# Live2D语音对话系统使用指南

## 🎯 快速开始

### 1. 安装依赖
```bash
# 安装基础依赖
pip install -r requirements.txt

# 如果PyAudio安装失败，尝试：
# Windows: 下载预编译的wheel文件
# 或安装Visual Studio构建工具
```

### 2. 配置系统
1. **STT模型**: 确保faster-whisper模型已下载到指定路径
2. **TTS服务**: 启动GPT-SoVITS服务 (localhost:9880)
3. **OpenAI API**: 在config.json中配置API密钥

### 3. 启动Live2D应用
```bash
cd dev
python main_window.py
```

### 4. 使用语音功能
1. 右键点击Live2D模型
2. 选择"🎤 语音对话"菜单
3. 选择语音输入模式并开始对话

## 🎤 语音功能详解

### 语音输入模式

#### 按键触发模式 (⌨️ 按键语音输入)
- **使用方法**: 按住空格键开始录音，松开停止
- **适用场景**: 精确控制录音时机
- **配置项**: 最大录音时长、最小录音时长、静音超时

#### 实时语音模式 (🔄 实时语音输入)
- **使用方法**: 自动检测语音活动，开始说话即录音
- **适用场景**: 自然对话，无需手动控制
- **配置项**: 音量阈值、静音持续时间、敏感度

### 语音设置 (⚙️ 语音设置)

#### STT设置标签页
- **模型路径**: faster-whisper模型位置
- **设备**: 计算设备 (auto/cpu/cuda)
- **计算类型**: 精度设置 (float16/float32/int8)
- **语言**: 识别语言 (auto/zh/en/ja/ko)
- **识别参数**: 束搜索大小、静音阈值等

#### TTS设置标签页
- **API地址**: GPT-SoVITS服务地址
- **合成参数**: 语言、Top K/P、温度、语速
- **音频设置**: 自动播放、保存音频、输出目录

#### 麦克风设置标签页
- **设备选择**: 自动选择或指定设备索引
- **音频参数**: 采样率、缓冲区大小、声道数
- **语音检测**: 音量阈值、静音持续时间

#### 高级设置标签页
- **集成设置**: LLM集成、自动发送、TTS回复
- **界面设置**: 语音指示器、音量表、热键

## 🔧 配置文件说明

### config.json 语音配置节
```json
{
  "voice_dialogue": {
    "microphone_config": {
      "sample_rate": 16000,        // 采样率
      "channels": 1,               // 声道数
      "chunk_size": 1024,          // 缓冲区大小
      "volume_threshold": 0.01     // 音量阈值
    },
    "stt_config": {
      "model_path": "模型路径",
      "device": "auto",            // 计算设备
      "language": "auto"           // 识别语言
    },
    "tts_config": {
      "api_url": "http://localhost:9880",
      "default_params": {
        "text_lang": "zh",         // 文本语言
        "speed_factor": 1.0        // 语速
      }
    }
  }
}
```

## 🚀 使用流程

### 完整对话流程
1. **启动语音输入**: 选择按键或实时模式
2. **语音识别**: 系统自动将语音转为文字
3. **LLM处理**: 文字发送给AI模型处理
4. **语音合成**: AI回复转换为语音
5. **播放回复**: 自动播放合成的语音

### 状态指示
- 🎤 **录音中**: 正在录制语音
- 🔄 **处理中**: 正在识别或合成
- 🎵 **播放中**: 正在播放AI回复
- ⏹️ **已停止**: 语音功能已停止

## 🛠️ 故障排除

### 常见问题

#### PyAudio安装失败
```bash
# Windows解决方案
pip install pipwin
pipwin install pyaudio

# 或下载预编译wheel
# https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio
```

#### 麦克风无法使用
1. 检查麦克风权限设置
2. 确认音频设备正常工作
3. 尝试不同的设备索引
4. 检查采样率兼容性

#### STT识别失败
1. 确认模型路径正确
2. 检查模型文件完整性
3. 尝试不同的计算设备
4. 调整音量阈值

#### TTS合成失败
1. 确认GPT-SoVITS服务运行
2. 检查API地址配置
3. 验证网络连接
4. 查看服务日志

#### 语音延迟过高
1. 使用GPU加速STT
2. 调整音频缓冲区大小
3. 优化TTS参数
4. 检查系统性能

### 性能优化

#### STT优化
- 使用GPU设备 (cuda)
- 选择合适的计算类型
- 调整束搜索大小
- 启用VAD过滤

#### TTS优化
- 调整合成参数
- 使用预设配置
- 缓存常用语音
- 异步处理

#### 系统优化
- 关闭不必要的程序
- 增加系统内存
- 使用SSD存储
- 优化网络连接

## 📊 测试和验证

### 运行测试
```bash
# 完整功能测试
python test_voice_dialogue_complete.py

# 简单集成测试
python test_voice_integration_simple.py

# 基础语音测试
python simple_voice_test.py
```

### 测试检查项
- ✅ 依赖包安装
- ✅ 配置文件完整
- ✅ 模块导入成功
- ✅ API连接正常
- ✅ UI集成完整
- ✅ 基础功能可用

## 🎯 使用技巧

### 最佳实践
1. **环境准备**: 安静的环境，清晰的发音
2. **设备选择**: 使用质量好的麦克风
3. **参数调整**: 根据环境调整音量阈值
4. **模式选择**: 按需选择按键或实时模式
5. **性能监控**: 关注识别准确率和响应时间

### 高级功能
1. **预设管理**: 配置多个TTS语音预设
2. **快捷键**: 使用热键快速启动语音
3. **批量处理**: 处理多段语音输入
4. **历史记录**: 查看对话历史
5. **自定义配置**: 调整个性化参数

## 📚 相关文档

- **集成报告**: `VOICE_DIALOGUE_INTEGRATION_COMPLETE.md`
- **需求文档**: `VOICE_DIALOGUE_REQUIREMENTS.md`
- **配置文件**: `config.json`
- **依赖列表**: `requirements.txt`

## 🆘 获取帮助

如果遇到问题：
1. 查看测试报告和错误信息
2. 检查配置文件设置
3. 运行诊断测试脚本
4. 查看相关文档
5. 检查依赖安装情况

---

🎉 **享受与Live2D桌面宠物的语音对话吧！**
