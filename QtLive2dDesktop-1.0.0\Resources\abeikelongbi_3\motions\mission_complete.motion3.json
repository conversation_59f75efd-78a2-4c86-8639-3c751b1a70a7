{"Version": 3, "Meta": {"Duration": 6.133, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 244, "TotalSegmentCount": 10160, "TotalPointCount": 12420, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.5, 1, 2, 0.533, 1, 0, 0.6, 0, 0, 0.767, 0.9, 0, 0.867, 0.7, 2, 1.933, 0.7, 0, 2.133, 1, 2, 2.8, 1, 0, 2.933, 0, 0, 3.1, 0.621, 0, 3.233, 0.5, 0, 6.133, 1]}, {"Target": "Parameter", "Id": "ParamEyeSmileL", "Segments": [0, 0, 2, 0.5, 0, 0, 0.733, 1, 2, 2.633, 1, 0, 2.8, 0, 2, 2.9, 0, 0, 2.933, 1, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.5, 1, 2, 0.533, 1, 0, 0.6, 0, 0, 0.767, 0.9, 0, 0.867, 0.7, 2, 1.933, 0.7, 0, 2.133, 1, 2, 2.8, 1, 0, 2.933, 0, 0, 3.1, 0.621, 0, 3.233, 0.5, 0, 6.133, 1]}, {"Target": "Parameter", "Id": "ParamEyeSmileR", "Segments": [0, 0, 2, 0.5, 0, 0, 0.733, 1, 2, 2.633, 1, 0, 2.8, 0, 2, 2.9, 0, 0, 2.933, 1, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 2, 0.367, 0, 0, 0.6, 1, 1, 0.722, 1, 0.845, 0.848, 0.967, 0.5, 1, 1.078, 0.183, 1.189, 0, 1.3, 0, 2, 1.6, 0, 0, 1.833, -0.5, 2, 2.067, -0.5, 2, 2.167, -0.5, 0, 2.667, 0, 1, 2.734, 0, 2.8, -0.551, 2.867, -0.7, 1, 2.989, -0.974, 3.111, -1, 3.233, -1, 1, 3.311, -1, 3.389, -0.964, 3.467, -0.8, 1, 3.489, -0.753, 3.511, -0.13, 3.533, 0, 1, 3.6, 0.39, 3.666, 0.5, 3.733, 0.5, 1, 3.811, 0.5, 3.889, 0.357, 3.967, 0, 1, 4.034, -0.306, 4.1, -0.5, 4.167, -0.5, 0, 4.433, 0, 1, 4.489, 0, 4.544, -0.167, 4.6, -0.5, 1, 4.656, -0.833, 4.711, -1, 4.767, -1, 1, 4.9, -1, 5.034, -0.444, 5.167, 0, 1, 5.234, 0.222, 5.3, 0.2, 5.367, 0.2, 0, 5.733, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.3, 0, 1, 0.344, 0, 0.389, 0.268, 0.433, 0.3, 1, 0.555, 0.387, 0.678, 0.407, 0.8, 0.5, 1, 0.856, 0.542, 0.911, 1, 0.967, 1, 0, 1.3, 0, 2, 1.6, 0, 0, 1.833, 0.5, 0, 1.967, 0.422, 1, 2, 0.422, 2.034, 0.41, 2.067, 0.5, 1, 2.122, 0.65, 2.178, 0.9, 2.233, 0.9, 0, 2.333, 0.775, 0, 2.433, 0.881, 0, 2.5, 0.775, 0, 2.6, 0.867, 0, 2.667, 0.5, 0, 2.933, 0.8, 2, 3, 0.8, 1, 3.056, 0.8, 3.111, 0.822, 3.167, 0.9, 1, 3.2, 0.947, 3.234, 1, 3.267, 1, 0, 3.4, 0.8, 0, 3.533, 0.881, 0, 3.6, 0.775, 0, 3.733, 0.841, 0, 3.9, 0.5, 0, 4.067, 0.782, 0, 4.167, 0.657, 0, 4.267, 0.763, 0, 4.333, 0.457, 1, 4.366, 0.457, 4.4, 0.497, 4.433, 0.549, 1, 4.633, 0.858, 4.833, 1, 5.033, 1, 0, 5.167, 0, 2, 5.6, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamMouthSize", "Segments": [0, 0, 2, 0.367, 0, 2, 1.6, 0, 0, 1.833, -0.8, 2, 2.067, -0.8, 2, 2.167, -0.8, 0, 2.667, 0, 0, 2.9, -0.401, 2, 4.933, -0.401, 0, 5.333, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamMouthY", "Segments": [0, 0, 2, 0.5, 0, 2, 2.733, 0, 0, 3, 0.3, 0, 3.3, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeEmotion", "Segments": [0, 0, 2, 0.5, 0, 0, 1.2, 1, 0, 1.833, 0, 0, 2.2, 1, 2, 2.867, 1, 0, 3.1, -1, 1, 3.667, -1, 4.233, -0.929, 4.8, -0.6, 1, 4.978, -0.497, 5.155, 0, 5.333, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamMarkShake", "Segments": [0, 0, 2, 3.567, 0, 0, 3.633, -0.8, 0, 3.933, 0.9, 0, 4.033, -0.638, 0, 4.267, 0.638, 0, 4.4, -0.2, 0, 4.533, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLX", "Segments": [0, 0, 2, 2.067, 0, 0, 2.367, -0.625, 1, 2.467, -0.625, 2.567, -0.626, 2.667, -0.625, 1, 2.767, -0.624, 2.867, 0, 2.967, 0, 2, 3.267, 0, 0, 3.733, -0.625, 2, 4.5, -0.625, 0, 5.3, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRX", "Segments": [0, 0, 2, 2.067, 0, 1, 2.167, 0, 2.267, -0.474, 2.367, -0.475, 2, 2.667, -0.475, 0, 2.967, 0, 2, 3.267, 0, 0, 3.733, -0.475, 2, 4.5, -0.475, 0, 5.3, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPHYInputX", "Segments": [0, 0, 2, 0.3, 0, 2, 0.5, 0, 0, 0.7, -8, 0, 1, 4, 0, 1.333, -6.749, 0, 1.533, -4.233, 0, 1.767, -5, 0, 2.1, -4.312, 0, 2.467, -5, 0, 2.7, 4.323, 0, 3.133, -12, 0, 3.5, -4.284, 0, 3.733, -10.284, 2, 4.033, -10.284, 0, 4.633, 8.232, 0, 4.9, -8.232, 0, 5.3, 6.661, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.4, 0, 2, 0.5, 0, 0, 1.233, 8, 0, 1.533, 6.637, 0, 1.9, 8.454, 0, 2.5, 8, 0, 3.033, 8.135, 0, 3.8, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.3, 0, 2, 0.5, 0, 0, 0.7, -8, 0, 1, 4, 0, 1.333, -6.749, 0, 1.533, -4.233, 0, 1.767, -5, 0, 2.1, -4.312, 0, 2.467, -5, 1, 2.556, -5, 2.644, 1.166, 2.733, 3, 1, 2.822, 4.834, 2.911, 5.08, 3, 6.513, 1, 3.133, 8.662, 3.267, 10.319, 3.4, 10.319, 0, 3.667, 7.263, 0, 4, 9, 0, 4.133, 7.263, 1, 4.211, 7.263, 4.289, 7.183, 4.367, 7.396, 1, 4.489, 7.73, 4.611, 13, 4.733, 13, 0, 5.033, -3, 0, 5.367, 4, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -5, 0, 1.1, 22, 0, 1.367, 18, 0, 1.667, 20, 1, 2, 20, 2.334, 13.329, 2.667, 1.036, 1, 2.911, -7.979, 3.156, -12, 3.4, -12, 0, 3.7, -11.263, 0, 3.9, -12, 2, 4.467, -12, 0, 5.133, 5.777, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "Segments": [0, 0, 2, 0.333, 0, 0, 1.1, 30, 0, 3.4, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamAngleH", "Segments": [0, 0, 2, 0.5, 0, 0, 2.8, 2, 0, 3.1, -3, 2, 3.367, -3, 2, 4, -3, 0, 4.7, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamAngleS", "Segments": [0, 0, 2, 0.5, 0, 2, 2.8, 0, 0, 3.267, 1, 2, 4.233, 1, 0, 4.6, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamArm<PERSON>pperRAngle", "Segments": [0, 0, 2, 0.133, 0, 0, 0.333, -1.053, 0, 0.767, 2, 0, 0.967, 1, 0, 1.133, 2.097, 0, 1.333, 1.37, 0, 1.567, 2, 0, 2.067, 0, 0, 2.533, 1, 0, 2.967, -2.016, 1, 3.511, -2.016, 4.056, -1.896, 4.6, -1.076, 1, 4.822, -0.741, 5.045, 1, 5.267, 1, 0, 5.767, -0.254, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRAngle", "Segments": [0, 7.4, 2, 0.033, 7.4, 0, 0.167, 8.501, 1, 0.256, 8.501, 0.344, 7.348, 0.433, 3.965, 1, 0.455, 3.119, 0.478, 0.072, 0.5, -1.517, 1, 0.544, -4.695, 0.589, -6, 0.633, -6, 0, 0.933, -0.767, 0, 1.1, -5.438, 1, 1.2, -5.438, 1.3, -3.083, 1.4, -2.004, 1, 1.511, -0.805, 1.622, -0.767, 1.733, -0.767, 0, 2, -8, 1, 2.078, -8, 2.155, -3.319, 2.233, -2.004, 1, 2.3, -0.877, 2.366, -0.135, 2.433, 0, 1, 2.644, 0.428, 2.856, 0.514, 3.067, 0.514, 0, 3.333, 0.452, 2, 4.667, 0.452, 0, 5.167, 7.856, 0, 5.4, 7.4, 2, 6.133, 7.4]}, {"Target": "Parameter", "Id": "ParamArmHandRAngle", "Segments": [0, 0, 0, 0.067, -2.29, 0, 0.367, 10, 2, 0.533, 10, 0, 0.833, -10, 0, 1.033, 4.588, 0, 1.2, -10, 0, 1.467, 2.369, 1, 1.556, 2.369, 1.644, 2.147, 1.733, -0.452, 1, 1.789, -2.076, 1.844, -10, 1.9, -10, 2, 1.933, -7.5, 0, 2.033, -10, 1, 2.066, -10, 2.1, -6.724, 2.133, -6, 1, 2.211, -4.311, 2.289, -4, 2.367, -4, 1, 2.467, -4, 2.567, -4.534, 2.667, -5, 1, 2.745, -5.363, 2.822, -5.393, 2.9, -5.393, 0, 3.2, -4.757, 0, 3.533, -5.268, 2, 4.767, -5.268, 0, 5.233, 2, 0, 5.567, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRH", "Segments": [0, 0, 1, 0.078, 0, 0.155, 6.493, 0.233, 6.915, 1, 0.3, 7.276, 0.366, 7.187, 0.433, 7.187, 1, 0.544, 7.187, 0.656, -10.296, 0.767, -10.876, 1, 1.378, -14.067, 1.989, -15, 2.6, -15, 0, 3.2, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperRH", "Segments": [0, 0, 2, 0.367, 0, 0, 1.633, 3, 2, 2.5, 3, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamHandR", "Segments": [0, 0, 2, 0.533, 0, 2, 0.567, 0.5, 2, 1.9, 0.5, 2, 1.933, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRAngle", "Segments": [0, 0, 2, 0.2, 0, 0, 0.533, -10, 0, 0.833, 10, 0, 1.067, -10, 0, 1.3, 10, 1, 1.489, 10, 1.678, 7.857, 1.867, 0, 1, 1.934, -2.773, 2, -10, 2.067, -10, 0, 2.267, -6, 0, 2.7, -10, 2, 5.3, -10, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamHandT2R", "Segments": [0, 0, 2, 0.267, 0, 0, 0.533, 1, 0, 1, 0, 2, 1.867, 0, 2, 2.067, 0, 0, 2.267, 1, 1, 2.367, 1, 2.467, 0.742, 2.567, 0.7, 1, 3.756, 0.202, 4.944, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamHandRCup", "Segments": [0, 0, 2, 0.4, 0, 2, 1.867, 0, 2, 2.1, 0, 0, 2.333, 1, 2, 4.833, 1, 0, 4.933, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY1", "Segments": [0, 0, 2, 2.833, 0, 0, 3.133, 7.311, 0, 3.633, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY2", "Segments": [0, 0, 0, 2, 15, 2, 2.033, 0, 2, 2.867, 0, 1, 3, 0, 3.134, 3.329, 3.267, 9.641, 1, 3.345, 13.323, 3.422, 15, 3.5, 15, 2, 3.533, 0, 0, 4, 15, 0, 4.033, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY3", "Segments": [0, 0, 0, 1.7, 15, 2, 1.733, 0, 2, 2.933, 0, 0, 3.333, 15, 2, 3.367, 0, 0, 3.867, 15, 0, 3.9, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamHandCupZ", "Segments": [0, 0, 0, 1.833, 0.564, 0, 2.2, -0.474, 0, 2.633, 0.564, 0, 3.067, -0.474, 0, 3.467, 0.564, 0, 3.867, -0.474, 0, 4.267, 0.564, 0, 4.667, -0.474, 0, 4.967, 0.608, 0, 5.4, -0.127, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamHandCupY", "Segments": [0, 0, 0, 1.967, 0.586, 0, 2.333, -0.544, 0, 2.767, 0.586, 0, 3.167, -0.544, 0, 3.567, 0.586, 0, 3.967, -0.544, 0, 4.367, 0.586, 0, 4.767, -0.544, 0, 5.033, 0.586, 0, 5.567, -0.124, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 0.5, 5, 0, 1.033, -4, 0, 1.267, -3, 0, 1.633, -4.469, 0, 1.933, -3.762, 1, 2.089, -3.762, 2.244, -3.674, 2.4, -4.131, 1, 2.578, -4.653, 2.755, -7.37, 2.933, -7.37, 0, 3.367, -5, 0, 4, -6.776, 0, 4.333, -6.557, 0, 5.033, -7, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 0, 1.233, -7, 0, 1.433, -5.065, 0, 1.7, -6.34, 0, 1.933, -5.4, 0, 2.367, -6, 1, 2.422, -6, 2.478, -5.889, 2.533, -4, 1, 2.589, -2.111, 2.644, 1.441, 2.7, 1.441, 0, 3.1, -4, 0, 3.467, -1.428, 0, 3.733, -3.428, 2, 4.033, -3.428, 0, 4.7, 3.234, 0, 4.967, -3.234, 0, 5.367, 2.617, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.167, 0, 0, 0.633, 0.219, 1, 0.878, 0.219, 1.122, -2.388, 1.367, -3, 1, 1.545, -3.445, 1.722, -3.338, 1.9, -3.338, 2, 2.367, -3.338, 1, 2.478, -3.338, 2.589, -2.223, 2.7, 0, 1, 2.867, 3.335, 3.033, 5, 3.2, 5, 2, 4.1, 5, 0, 4.967, -1, 0, 5.5, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX2", "Segments": [0, 0, 2, 0.5, 0, 0, 0.7, -2, 0, 1.233, 2.436, 0, 1.567, -1.19, 0, 1.833, 0, 2, 2.8, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY2", "Segments": [0, 0, 2, 0.167, 0, 2, 0.4, 0, 0, 1.3, -8.342, 0, 1.6, -5.405, 0, 1.833, -6.548, 0, 2.033, -6, 2, 2.267, -6, 1, 2.367, -6, 2.467, -5.991, 2.567, -5.405, 1, 2.622, -5.08, 2.678, 1, 2.733, 1, 0, 3.3, -2.638, 0, 3.6, -0.034, 0, 3.767, -2.034, 2, 4.133, -2.034, 0, 4.633, 3.234, 0, 4.9, -3.234, 0, 5.333, 2.617, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechX", "Segments": [0, 0, 2, 0.4, 0, 0, 0.7, 1, 0, 1.233, -8, 0, 1.5, -6.944, 0, 1.867, -7.423, 0, 2.433, -6.713, 0, 2.6, -8, 1, 2.722, -8, 2.845, -2.615, 2.967, 0, 1, 3.134, 3.565, 3.3, 4, 3.467, 4, 2, 4.633, 4, 0, 5.167, 6.162, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, 0, 2, 0.2, 0, 0, 0.7, 0.502, 1, 0.878, 0.502, 1.055, -1.121, 1.233, -2.26, 1, 1.3, -2.687, 1.366, -2.58, 1.433, -2.58, 2, 2.3, -2.58, 1, 2.911, -2.58, 3.522, -1.578, 4.133, 0, 1, 4.266, 0.344, 4.4, 0.375, 4.533, 0.375, 0, 4.767, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamShoulderR", "Segments": [0, 0, 2, 0.5, 0, 0, 0.767, -6.964, 0, 1.2, 4.976, 0, 1.6, 0, 0, 2.8, 9, 0, 3.1, -14, 0, 3.333, 0, 2, 5.1, 0, 0, 5.4, 11, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamShoulderL", "Segments": [0, 0, 2, 0.5, 0, 2, 5.1, 0, 0, 5.4, 9.243, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Z", "Segments": [0, 0, 2, 0.5, 0, 0, 0.967, -1.596, 0, 1.7, 0.595, 0, 2.533, -0.415, 0, 3.067, 0, 0, 3.733, -1.596, 0, 4.1, -1.339, 0, 4.533, -1.596, 2, 5, -1.596, 0, 5.933, 0.595, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLegR1X", "Segments": [0, 0, 2, 0.7, 0, 0, 1.1, -3, 0, 1.8, 1.509, 0, 2.6, 0, 2, 2.933, 0, 0, 3.667, -3, 2, 4.833, -3, 0, 5.667, 13, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Z", "Segments": [0, 0, 2, 0.5, 0, 2, 0.6, 0, 2, 0.767, 0, 0, 1.2, -2, 0, 1.9, 0.138, 0, 2.733, -1, 0, 3.267, 0, 0, 3.8, -1.675, 0, 4.3, -1.211, 0, 4.767, -1.675, 2, 5.2, -1.675, 0, 6.067, 0.701, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Z", "Segments": [0, 0, 0, 0.5, -0.632, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyX", "Segments": [0, 0, 2, 0.6, 0, 0, 0.9, 7, 0, 1.267, -3, 0, 1.567, 4, 0, 2.967, -7.178, 0, 4.967, 1.049, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyY", "Segments": [0, 0, 2, 0.633, 0, 0, 0.933, 7, 0, 1.3, -3, 0, 1.6, 4, 0, 2.633, -11.811, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyZ", "Segments": [0, 0, 2, 0.5, 0, 0, 0.9, 2, 0, 1.267, -11.745, 0, 1.833, -5.725, 0, 2.167, -7, 0, 4.133, 1.845, 1, 4.322, 1.845, 4.511, 1.729, 4.7, 0.15, 1, 4.822, -0.871, 4.945, -4.109, 5.067, -4.109, 2, 5.167, -4.109, 0, 5.733, 3, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRPositionZ", "Segments": [0, 0, 2, 0.5, 0, 2, 0.6, 0, 0, 1, 2, 0, 1.367, -9.745, 0, 1.933, -5.725, 0, 2.267, -7, 0, 4.033, 0.584, 1, 4.222, 0.584, 4.411, 0.512, 4.6, -0.539, 1, 4.722, -1.219, 4.845, -3.362, 4.967, -3.362, 2, 5.067, -3.362, 0, 5.633, 3, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyX", "Segments": [0, 0, 2, 0.5, 0, 2, 0.667, 0, 0, 0.9, 7, 0, 1.267, -3, 0, 1.567, 4, 1, 1.656, 4, 1.744, 1.258, 1.833, 0, 1, 2.211, -5.346, 2.589, -7.178, 2.967, -7.178, 0, 3.767, 4.747, 1, 3.945, 4.747, 4.122, 3.402, 4.3, 0, 1, 4.511, -4.04, 4.722, -6.474, 4.933, -6.474, 0, 5.367, 4.059, 0, 5.767, -5.438, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyY", "Segments": [0, 0, 2, 0.5, 0, 2, 0.533, 0, 2, 0.7, 0, 0, 0.933, 7, 0, 1.3, -3, 0, 1.6, 4, 1, 1.689, 4, 1.778, 2.72, 1.867, 0, 1, 2.122, -7.819, 2.378, -11.811, 2.633, -11.811, 0, 3.833, 10.277, 1, 4, 10.277, 4.166, 5.3, 4.333, 0, 1, 4.544, -6.714, 4.756, -8.618, 4.967, -8.618, 0, 5.433, 6.618, 0, 5.8, -5.438, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyZ", "Segments": [0, 0, 2, 0.5, 0, 2, 0.567, 0, 0, 0.733, -6, 0, 1.133, 3, 0, 1.433, -1.059, 0, 1.767, 0, 0, 2.433, -5.624, 2, 2.533, -5.624, 0, 4.2, 7.585, 1, 4.289, 7.585, 4.378, 3.74, 4.467, 0, 1, 4.622, -6.545, 4.778, -8.618, 4.933, -8.618, 2, 5.033, -8.618, 0, 5.633, 3.857, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUEyesForm", "Segments": [0, 0, 2, 0.333, 0, 2, 0.533, 0, 2, 1.1, 0, 0, 1.667, -1, 2, 2, -1, 0, 2.533, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyY", "Segments": [0, 0, 2, 0.333, 0, 2, 0.7, 0, 0, 1.067, 6.26, 0, 1.333, -1.62, 0, 1.467, 2.925, 0, 1.6, -2.598, 0, 1.7, 3.787, 0, 1.8, -3.047, 0, 1.933, 2.925, 0, 2.033, -2.703, 0, 2.167, 2, 1, 2.367, 2, 2.567, 1.275, 2.767, 0, 1, 2.945, -1.133, 3.122, -1.62, 3.3, -1.62, 0, 4.333, 2, 0, 4.967, -12.621, 0, 5.433, 9.177, 0, 5.8, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyZ", "Segments": [0, 0, 2, 0.333, 0, 2, 0.633, 0, 0, 1.033, 20.858, 2, 1.1, 20.858, 0, 1.3, -27.181, 0, 1.433, 27.181, 0, 1.567, -27.181, 0, 1.667, 27.181, 0, 1.767, -27.181, 0, 1.9, 27.181, 0, 2.067, -10, 0, 2.2, 4, 0, 2.467, -2.382, 0, 2.767, 2.816, 0, 3.2, -1.3, 0, 3.6, 1.227, 0, 4.233, -2.382, 0, 4.6, 1.227, 0, 5.033, -12.621, 0, 5.667, 7.576, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUArmZ", "Segments": [0, 0, 2, 0.333, 0, 2, 0.567, 0, 1, 0.634, 0, 0.7, 0.236, 0.767, -2, 1, 0.956, -8.336, 1.144, -15, 1.333, -15, 0, 1.467, 15.881, 0, 1.6, -17.598, 0, 1.7, 16.598, 0, 1.8, -18.598, 0, 1.933, 16.598, 0, 2.133, -12, 0, 2.3, 11, 0, 2.633, -5.118, 0, 2.9, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineU", "Segments": [0, 0, 2, 0.333, 0, 2, 1.367, 0, 1, 1.411, 0.333, 1.456, 0.667, 1.5, 1, 2, 1.533, 0, 2, 1.633, 0, 1, 1.666, 0.333, 1.7, 0.667, 1.733, 1, 2, 1.767, 0, 2, 1.833, 0, 2, 1.933, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineD", "Segments": [0, 0, 2, 0.333, 0, 2, 1.367, 0, 2, 1.5, 0, 1, 1.544, 0.333, 1.589, 0.667, 1.633, 1, 2, 1.667, 0, 2, 1.733, 0, 1, 1.766, 0.333, 1.8, 0.667, 1.833, 1, 2, 1.867, 0, 2, 1.933, 0, 1, 1.978, 0.333, 2.022, 0.667, 2.067, 1, 2, 2.1, 0, 2, 2.133, 0, 2, 2.433, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyW", "Segments": [0, 0, 2, 0.333, 0, 2, 0.933, 0, 0, 1.3, -0.6, 0, 1.433, 0.4, 0, 1.567, -0.5, 0, 1.7, 0.6, 0, 1.767, -0.5, 0, 1.867, 0.2, 1, 1.911, 0.2, 1.956, 0.103, 2, -0.029, 1, 2.067, -0.226, 2.133, -0.3, 2.2, -0.3, 0, 2.467, 0.2, 0, 2.7, -0.2, 0, 2.967, 0.2, 0, 3.3, -0.1, 0, 3.6, 0.1, 0, 3.967, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.6, 0, 0, 1, 1, 2, 4.067, 1, 0, 4.467, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyX", "Segments": [0, 0, 0, 0.6, -16.773, 0, 1, 16.773, 0, 1.533, -16.773, 0, 2.167, 16.773, 0, 2.8, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyY", "Segments": [0, 0, 2, 0.767, 0, 0, 1, -7, 0, 1.367, 3, 0, 1.767, -5.561, 0, 2.033, 3, 0, 2.333, -3, 0, 2.767, 1.169, 0, 3.2, 0, 2, 4.267, 0, 0, 4.6, -4.535, 0, 4.933, 3, 0, 5.267, -3, 0, 5.7, 1.169, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyZ", "Segments": [0, 0, 0, 0.5, 19.655, 0, 0.733, -21, 0, 1.167, 6.396, 0, 1.5, -3.586, 0, 1.8, 4.589, 0, 2.167, -2.649, 0, 2.567, 1.337, 0, 2.867, 0, 0, 3.1, 2.231, 0, 3.533, 0, 0, 4.033, 19.655, 0, 4.267, -21, 0, 4.7, 6.396, 0, 5.033, -3.586, 0, 5.467, 3.698, 0, 6.067, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyX", "Segments": [0, 0, 0, 0.633, -8, 0, 1.3, 7, 0, 2.133, -9, 0, 2.867, 5, 0, 3.667, -6, 0, 4.4, 6, 0, 5.067, -8, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyY", "Segments": [0, 0, 2, 0.5, 0, 2, 0.667, 0, 0, 0.9, -7, 0, 1.267, 3, 0, 1.567, -4, 0, 1.833, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamCannonZ", "Segments": [0, 0, 2, 0.533, 0, 0, 0.7, 6, 0, 1.1, -3, 0, 1.4, 1.059, 0, 1.733, 0, 2, 2.767, 0, 0, 3, 2.231, 0, 3.433, -13, 0, 3.667, -11.678, 0, 3.967, -12.606, 2, 4.533, -12.606, 0, 5.367, 3.698, 0, 5.967, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamCannonGaY", "Segments": [0, 0, 2, 0.6, 0, 0, 0.767, -8.384, 0, 1.167, 3.845, 0, 1.467, -3.618, 0, 1.767, 3.37, 0, 2.067, -1.16, 0, 2.4, 1.234, 1, 2.544, 1.234, 2.689, 1.071, 2.833, -0.231, 1, 2.911, -0.933, 2.989, -3.248, 3.067, -3.248, 0, 3.5, 11.418, 0, 3.733, 6, 0, 4.033, 10.888, 0, 4.3, 6.475, 0, 4.6, 10.888, 0, 5.433, -3.248, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupY", "Segments": [0, 0, 2, 0.633, 0, 0, 0.8, 0.2, 0, 1.2, -0.1, 0, 1.5, 0.035, 0, 1.833, 0, 2, 2.867, 0, 0, 3.1, 0.074, 0, 3.533, -0.433, 0, 3.767, -0.389, 0, 4.067, -0.42, 2, 4.633, -0.42, 0, 5.467, 0.39, 0, 6.067, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupZ", "Segments": [0, 0, 2, 0.5, 0, 0, 0.667, 0.2, 0, 1.067, -0.1, 0, 1.367, 0.035, 0, 1.7, 0, 2, 2.733, 0, 0, 2.967, 0.074, 0, 3.4, -0.433, 0, 3.633, -0.389, 0, 3.933, -0.42, 2, 4.5, -0.42, 0, 5.333, 0.39, 0, 5.933, 0, 2, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamParamStrongCatZ", "Segments": [0, 0, 2, 0.5, 0, 0, 0.9, 2, 0, 1.267, -11.745, 0, 1.833, -5.725, 0, 2.167, -7, 0, 4.033, 1.845, 1, 4.222, 1.845, 4.411, 1.435, 4.6, 0.15, 1, 4.722, -0.682, 4.845, -1.436, 4.967, -1.436, 2, 5.067, -1.436, 0, 5.633, 1.223, 2, 5.733, 1.223, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamParamSCBodyZ", "Segments": [0, 0, 2, 0.5, 0, 2, 0.6, 0, 0, 1, 2, 0, 1.367, -9.745, 0, 1.933, -5.725, 0, 2.267, -7, 0, 4.133, 1.845, 1, 4.322, 1.845, 4.511, 1.435, 4.7, 0.15, 1, 4.822, -0.682, 4.945, -1.436, 5.067, -1.436, 2, 5.167, -1.436, 0, 5.733, 1.223, 2, 5.833, 1.223, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamSCDishY", "Segments": [0, 0, 2, 0.5, 0, 2, 0.7, 0, 0, 1.1, 2, 0, 1.467, -2.745, 0, 2, 1.275, 0, 2.333, 0, 0, 4.133, 1.845, 1, 4.322, 1.845, 4.511, 1.435, 4.7, 0.15, 1, 4.822, -0.682, 4.945, -1.436, 5.067, -1.436, 2, 5.167, -1.436, 0, 5.733, 1.223, 2, 5.833, 1.223, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamSCDishZ", "Segments": [0, 0, 2, 0.5, 0, 2, 0.733, 0, 0, 1.133, 2, 0, 1.5, -2.745, 0, 2.033, 1.275, 0, 2.367, 0, 0, 4.267, 1.845, 1, 4.456, 1.845, 4.644, 1.435, 4.833, 0.15, 1, 4.955, -0.682, 5.078, -1.436, 5.2, -1.436, 2, 5.3, -1.436, 0, 5.867, 1.223, 2, 5.967, 1.223, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamSCCupZ", "Segments": [0, 0, 2, 0.5, 0, 2, 0.8, 0, 0, 1.233, 5.028, 0, 1.6, -5.773, 0, 2.167, 3.378, 0, 2.433, -1.036, 0, 4.333, 1.845, 1, 4.522, 1.845, 4.711, 1.435, 4.9, 0.15, 1, 5.022, -0.682, 5.145, -1.436, 5.267, -1.436, 2, 5.367, -1.436, 0, 5.933, 1.223, 2, 6.033, 1.223, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "MB_yanwubaozha", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "MB_DRFWXZKTMD", "Segments": [0, 1, 0, 6.133, 1]}, {"Target": "Parameter", "Id": "ParamAllSizeFix", "Segments": [0, 1, 0, 6.133, 1]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBGHide", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBG2Hide", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBGX", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBGY", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN2", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN3", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBlackY", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBlackCollar", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBlackOrder", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamWhiteIN", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamCHHide", "Segments": [0, 1, 0, 6.133, 1]}, {"Target": "Parameter", "Id": "ParamDeskHide", "Segments": [0, 1, 0, 6.133, 1]}, {"Target": "Parameter", "Id": "ParamStoolHide", "Segments": [0, 1, 0, 6.133, 1]}, {"Target": "Parameter", "Id": "ParamCupDesk", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamCHX", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamCHY", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamCHZ", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamChaSize", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamCcharacterZ", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionX", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionY", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionX", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionY", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamAllZ", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamAllSize", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamALLSize2", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamDeskShow", "Segments": [0, 10, 0, 6.133, 10]}, {"Target": "Parameter", "Id": "ParamStrongCatShow", "Segments": [0, 10, 0, 6.133, 10]}, {"Target": "Parameter", "Id": "ParamCannonShow", "Segments": [0, 10, 0, 6.133, 10]}, {"Target": "Parameter", "Id": "ParamLightPositionX", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamFixT", "Segments": [0, 1, 0, 6.133, 1]}, {"Target": "Parameter", "Id": "ParamFlap", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamScare", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPupilExp", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpen2", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamMouthType", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBlackFace", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamTeethLight", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamHeart2", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamMark", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamShameLine", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLY", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeL", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRY", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeR", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeRLightOpen", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLightLine1", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLightLine2", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLightLine3", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLightShine", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo1", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo2", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo3", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1Y", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle2", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow1", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow2", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamTearLight", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamTears", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperLAngle", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamArmHandLAngle", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerLAngle", "Segments": [0, 10, 0, 6.133, 10]}, {"Target": "Parameter", "Id": "ParamArmLowerLH", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerLAngle", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamHandT2L", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamFanOpenR", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamChili", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamChiliX", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRY", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamHand_Cl", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamHandT1R", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamHandRMail", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "Segments": [0, 1, 0, 6.133, 1]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechW", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Y", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Y", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Y", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamFootRX", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Z", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Z", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Z", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Y", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Y", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Y", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamFootLX", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLegLF", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRHide", "Segments": [0, 1, 0, 6.133, 1]}, {"Target": "Parameter", "Id": "ParamMJRFlap", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuR", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuR", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuR", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuR", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRInput", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamMRCupSet", "Segments": [0, 1, 0, 6.133, 1]}, {"Target": "Parameter", "Id": "ParamMalpositionManjuuR", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuREyeOpen", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRArmB", "Segments": [0, 30, 0, 6.133, 30]}, {"Target": "Parameter", "Id": "ParamManjuuRMouth", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRSigh", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow1", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow2", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow3", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow4", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowB", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW2", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamMRCupFZ", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX1", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX2", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqH", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX1", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX2", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX3", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLHide", "Segments": [0, 1, 0, 6.133, 1]}, {"Target": "Parameter", "Id": "ParamMJLSigh", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuL", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuL", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuL", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamMjLFlip", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionZManjuuL", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLEyeOpen", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyW", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuL", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamClawFX", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamClawFY", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamClawBX", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamClawBY", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUHide", "Segments": [0, 1, 0, 6.133, 1]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuU", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuU", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuU", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyX", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPandaHide", "Segments": [0, 1, 0, 6.133, 1]}, {"Target": "Parameter", "Id": "ParamPositionXPanda", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionYPanda", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamSizePanda", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2Panda", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegFZ", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegBZ", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamCannonHandZ", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY1", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY2", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY3", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupIce", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupZ", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupInput", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamSCDishRO", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamSCCupRO", "Segments": [0, 0, 0, 6.133, 0]}, {"Target": "Parameter", "Id": "ParamSCCupY", "Segments": [0, 0, 0, 6.133, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.6, "Value": ""}, {"Time": 5.633, "Value": ""}]}