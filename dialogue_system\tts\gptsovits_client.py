#!/usr/bin/env python3
"""
Live2D语音对话系统 - GPT-SoVITS客户端

这个模块提供了GPT-SoVITS API调用功能，包括：
- HTTP API调用
- 语音预设管理
- 参数配置和优化
- 错误处理和重试

使用示例：
    from dialogue_system.tts.gptsovits_client import GPTSoVITSClient
    
    # 创建客户端
    client = GPTSoVITSClient("http://localhost:9880")
    
    # 合成语音
    result = client.synthesize("你好，世界！", preset="shizuru")
    
    # 获取音频数据
    audio_data = result.get("audio_data")
"""

import requests
import json
import time
import io
from typing import Optional, Dict, Any, Union
from pathlib import Path


class GPTSoVITSClient:
    """GPT-SoVITS API客户端"""
    
    def __init__(self, api_url: str = "http://localhost:9880", timeout: float = 30.0):
        """初始化GPT-SoVITS客户端"""
        self.api_url = api_url.rstrip('/')
        self.timeout = timeout
        
        # API端点
        self.endpoints = {
            "synthesize": f"{self.api_url}/",
            "health": f"{self.api_url}/health",
            "models": f"{self.api_url}/models"
        }
        
        # 默认参数
        self.default_params = {
            "text_lang": "zh",
            "cut_punc": ",.;?!、，。？！；：",
            "top_k": 20,
            "top_p": 0.6,
            "temperature": 0.6,
            "speed_factor": 1.0
        }
        
        # 预设配置
        self.presets = {}
        
        print(f"🎵 GPT-SoVITS客户端初始化完成")
        print(f"   API地址: {api_url}")
        
    def check_health(self) -> bool:
        """检查API服务状态"""
        try:
            response = requests.get(
                self.endpoints["health"], 
                timeout=5.0
            )
            if response.status_code == 200:
                print("✅ GPT-SoVITS API服务正常")
                return True
            else:
                print(f"⚠️ GPT-SoVITS API服务异常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 无法连接GPT-SoVITS API: {e}")
            return False
            
    def load_preset(self, preset_name: str, preset_config: Dict[str, Any]):
        """加载语音预设"""
        self.presets[preset_name] = preset_config
        print(f"📝 已加载语音预设: {preset_name}")
        
    def load_presets_from_file(self, preset_file: str):
        """从文件加载语音预设"""
        try:
            with open(preset_file, 'r', encoding='utf-8') as f:
                preset_data = json.load(f)
                
            if "config" in preset_data:
                # 单个预设文件
                preset_name = preset_data.get("name", Path(preset_file).stem)
                self.load_preset(preset_name, preset_data["config"])
            else:
                # 多个预设文件
                for name, config in preset_data.items():
                    self.load_preset(name, config)
                    
            print(f"📁 已从文件加载预设: {preset_file}")
            
        except Exception as e:
            print(f"❌ 加载预设文件失败: {e}")
            
    def synthesize(self, 
                  text: str,
                  preset: Optional[str] = None,
                  **kwargs) -> Dict[str, Any]:
        """合成语音"""
        try:
            # 准备参数
            params = self.default_params.copy()
            
            # 应用预设
            if preset and preset in self.presets:
                preset_config = self.presets[preset]
                params.update(preset_config)
                print(f"🎭 使用预设: {preset}")
            elif preset:
                print(f"⚠️ 预设不存在: {preset}，使用默认参数")
                
            # 应用自定义参数
            params.update(kwargs)
            
            # 添加文本
            params["text"] = text
            
            print(f"🎵 开始合成语音: {text[:50]}...")
            start_time = time.time()
            
            # 发送请求
            response = requests.post(
                self.endpoints["synthesize"],
                json=params,
                timeout=self.timeout
            )
            
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                # 检查响应内容类型
                content_type = response.headers.get('content-type', '')
                
                if 'audio' in content_type or 'application/octet-stream' in content_type:
                    # 音频数据响应
                    audio_data = response.content
                    
                    result = {
                        "success": True,
                        "audio_data": audio_data,
                        "content_type": content_type,
                        "processing_time": processing_time,
                        "text": text,
                        "preset": preset,
                        "params": params
                    }
                    
                    print(f"✅ 语音合成完成 (时长: {processing_time:.2f}秒, "
                          f"大小: {len(audio_data)} 字节)")
                    return result
                    
                else:
                    # JSON响应
                    try:
                        json_data = response.json()
                        if "audio_data" in json_data or "audio_url" in json_data:
                            result = {
                                "success": True,
                                "processing_time": processing_time,
                                "text": text,
                                "preset": preset,
                                "params": params
                            }
                            result.update(json_data)
                            
                            print(f"✅ 语音合成完成 (时长: {processing_time:.2f}秒)")
                            return result
                        else:
                            return {
                                "success": False,
                                "error": f"响应中没有音频数据: {json_data}",
                                "response": json_data
                            }
                    except json.JSONDecodeError:
                        return {
                            "success": False,
                            "error": f"无法解析响应: {response.text[:200]}",
                            "status_code": response.status_code
                        }
            else:
                error_msg = f"API请求失败: {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f" - {error_detail}"
                except:
                    error_msg += f" - {response.text[:200]}"
                    
                print(f"❌ {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "status_code": response.status_code
                }
                
        except requests.exceptions.Timeout:
            error_msg = f"请求超时 (>{self.timeout}秒)"
            print(f"❌ {error_msg}")
            return {"success": False, "error": error_msg}
            
        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求失败: {e}"
            print(f"❌ {error_msg}")
            return {"success": False, "error": error_msg}
            
        except Exception as e:
            error_msg = f"语音合成异常: {e}"
            print(f"❌ {error_msg}")
            return {"success": False, "error": error_msg}
            
    def synthesize_to_file(self, 
                          text: str,
                          output_file: str,
                          preset: Optional[str] = None,
                          **kwargs) -> Dict[str, Any]:
        """合成语音并保存到文件"""
        result = self.synthesize(text, preset=preset, **kwargs)
        
        if result.get("success", False) and "audio_data" in result:
            try:
                with open(output_file, 'wb') as f:
                    f.write(result["audio_data"])
                    
                result["output_file"] = output_file
                print(f"💾 语音已保存到: {output_file}")
                return result
                
            except Exception as e:
                error_msg = f"保存音频文件失败: {e}"
                print(f"❌ {error_msg}")
                result["success"] = False
                result["error"] = error_msg
                return result
        else:
            return result
            
    def get_available_presets(self) -> list:
        """获取可用的预设列表"""
        return list(self.presets.keys())
        
    def get_preset_info(self, preset_name: str) -> Optional[Dict[str, Any]]:
        """获取预设信息"""
        return self.presets.get(preset_name)
        
    def set_default_params(self, **params):
        """设置默认参数"""
        self.default_params.update(params)
        print(f"🔧 已更新默认参数: {params}")
        
    def test_synthesis(self) -> bool:
        """测试语音合成功能"""
        try:
            # 检查服务状态
            if not self.check_health():
                return False
                
            # 测试合成
            test_text = "测试语音合成功能"
            result = self.synthesize(test_text)
            
            if result.get("success", False):
                print("✅ TTS功能测试通过")
                return True
            else:
                print(f"❌ TTS功能测试失败: {result.get('error', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"❌ TTS功能测试异常: {e}")
            return False
            
    def get_status(self) -> Dict[str, Any]:
        """获取客户端状态"""
        return {
            "api_url": self.api_url,
            "timeout": self.timeout,
            "available_presets": self.get_available_presets(),
            "default_params": self.default_params,
            "health": self.check_health()
        }
