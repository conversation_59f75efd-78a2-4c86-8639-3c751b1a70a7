#!/usr/bin/env python3
"""
Live2D语音对话系统 - STT管理器

这个模块提供了STT功能的统一管理，包括：
- 配置管理和加载
- 多种STT引擎支持
- 异步识别处理
- 结果缓存和优化

使用示例：
    from dialogue_system.stt.stt_manager import STTManager
    
    # 创建STT管理器
    stt_manager = STTManager(config_manager)
    
    # 异步识别
    result = await stt_manager.transcribe_async(audio_data)
    
    # 同步识别
    result = stt_manager.transcribe(audio_data)
"""

import asyncio
import threading
import time
from typing import Optional, Dict, Any, Callable, Union
import numpy as np
from .faster_whisper_client import FasterWhisperClient


class STTManager:
    """STT管理器 - 统一管理语音识别功能"""
    
    def __init__(self, config_manager=None):
        """初始化STT管理器"""
        self.config_manager = config_manager
        
        # 加载配置
        self.stt_config = self._load_stt_config()
        
        # STT客户端
        self.whisper_client: Optional[FasterWhisperClient] = None
        
        # 状态控制
        self.is_initialized = False
        self.is_busy = False
        
        # 回调函数
        self.on_transcription_start: Optional[Callable] = None
        self.on_transcription_complete: Optional[Callable] = None
        self.on_transcription_error: Optional[Callable] = None
        
        # 性能统计
        self.transcription_count = 0
        self.total_processing_time = 0.0
        self.last_transcription_time = 0.0
        
        print("🎤 STT管理器初始化完成")
        
    def _load_stt_config(self) -> Dict[str, Any]:
        """加载STT配置"""
        default_config = {
            "model_path": "D:/huggingface_cache/hub/models--Systran--faster-whisper-large-v3",
            "device": "auto",
            "compute_type": "float16",
            "language": "auto",
            "task": "transcribe",
            "beam_size": 5,
            "temperature": [0.0, 0.2, 0.4, 0.6, 0.8, 1.0],
            "compression_ratio_threshold": 2.4,
            "log_prob_threshold": -1.0,
            "no_speech_threshold": 0.6
        }
        
        if self.config_manager:
            voice_config = self.config_manager.config.get("voice_dialogue", {})
            stt_config = voice_config.get("stt_config", {})
            
            config = default_config.copy()
            config.update(stt_config)
            return config
        else:
            return default_config
            
    def initialize(self) -> bool:
        """初始化STT引擎"""
        if self.is_initialized:
            print("✅ STT引擎已初始化")
            return True
            
        try:
            print("🔄 正在初始化STT引擎...")
            
            # 创建faster-whisper客户端
            self.whisper_client = FasterWhisperClient(
                model_path=self.stt_config["model_path"],
                device=self.stt_config["device"],
                compute_type=self.stt_config["compute_type"]
            )
            
            # 设置默认参数
            self.whisper_client.set_default_params(
                language=self.stt_config["language"],
                task=self.stt_config["task"],
                beam_size=self.stt_config["beam_size"],
                temperature=self.stt_config["temperature"],
                compression_ratio_threshold=self.stt_config["compression_ratio_threshold"],
                log_prob_threshold=self.stt_config["log_prob_threshold"],
                no_speech_threshold=self.stt_config["no_speech_threshold"]
            )
            
            # 加载模型
            if self.whisper_client.load_model():
                self.is_initialized = True
                print("✅ STT引擎初始化成功")
                return True
            else:
                print("❌ STT模型加载失败")
                return False
                
        except Exception as e:
            print(f"❌ STT引擎初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    def set_callbacks(self,
                     on_transcription_start: Optional[Callable] = None,
                     on_transcription_complete: Optional[Callable] = None,
                     on_transcription_error: Optional[Callable] = None):
        """设置回调函数"""
        self.on_transcription_start = on_transcription_start
        self.on_transcription_complete = on_transcription_complete
        self.on_transcription_error = on_transcription_error
        
    def transcribe(self, 
                  audio_data: Union[np.ndarray, bytes, str],
                  language: Optional[str] = None,
                  **kwargs) -> Dict[str, Any]:
        """同步语音识别"""
        if not self.is_initialized:
            if not self.initialize():
                return {"error": "STT引擎未初始化", "success": False}
                
        if self.is_busy:
            return {"error": "STT引擎忙碌中", "success": False}
            
        try:
            self.is_busy = True
            start_time = time.time()
            
            # 触发开始回调
            if self.on_transcription_start:
                self.on_transcription_start()
                
            print("🎤 开始语音识别...")
            
            # 根据数据类型选择识别方法
            if isinstance(audio_data, np.ndarray):
                result = self.whisper_client.transcribe_audio_data(
                    audio_data, 
                    sample_rate=kwargs.get("sample_rate", 16000),
                    language=language,
                    **kwargs
                )
            elif isinstance(audio_data, bytes):
                result = self.whisper_client.transcribe_audio_bytes(
                    audio_data,
                    language=language,
                    **kwargs
                )
            elif isinstance(audio_data, str):
                result = self.whisper_client.transcribe_file(
                    audio_data,
                    language=language,
                    **kwargs
                )
            else:
                result = {"error": "不支持的音频数据类型", "success": False}
                
            # 更新统计信息
            processing_time = time.time() - start_time
            self.last_transcription_time = processing_time
            self.total_processing_time += processing_time
            self.transcription_count += 1
            
            if result.get("success", False):
                print(f"✅ 识别完成: {result.get('text', '')}")
                print(f"⏱️ 处理时间: {processing_time:.2f}秒")
                
                # 触发完成回调
                if self.on_transcription_complete:
                    self.on_transcription_complete(result)
            else:
                print(f"❌ 识别失败: {result.get('error', '未知错误')}")
                
                # 触发错误回调
                if self.on_transcription_error:
                    self.on_transcription_error(result)
                    
            return result
            
        except Exception as e:
            error_msg = f"语音识别异常: {e}"
            print(f"❌ {error_msg}")
            
            result = {"error": error_msg, "success": False}
            
            # 触发错误回调
            if self.on_transcription_error:
                self.on_transcription_error(result)
                
            return result
        finally:
            self.is_busy = False
            
    async def transcribe_async(self, 
                              audio_data: Union[np.ndarray, bytes, str],
                              language: Optional[str] = None,
                              **kwargs) -> Dict[str, Any]:
        """异步语音识别"""
        loop = asyncio.get_event_loop()
        
        # 在线程池中执行同步识别
        result = await loop.run_in_executor(
            None, 
            self.transcribe, 
            audio_data, 
            language,
            **kwargs
        )
        
        return result
        
    def transcribe_in_background(self,
                                audio_data: Union[np.ndarray, bytes, str],
                                callback: Callable[[Dict[str, Any]], None],
                                language: Optional[str] = None,
                                **kwargs):
        """后台语音识别"""
        def background_task():
            result = self.transcribe(audio_data, language=language, **kwargs)
            callback(result)
            
        thread = threading.Thread(target=background_task)
        thread.daemon = True
        thread.start()
        
    def set_language(self, language: str):
        """设置识别语言"""
        self.stt_config["language"] = language
        if self.whisper_client:
            self.whisper_client.set_default_params(language=language)
        print(f"🌐 已设置识别语言: {language}")
        
    def get_supported_languages(self) -> list:
        """获取支持的语言列表"""
        if self.whisper_client:
            return self.whisper_client.get_supported_languages()
        else:
            return ["auto", "zh", "en", "ja", "ko"]
            
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        avg_time = (self.total_processing_time / self.transcription_count 
                   if self.transcription_count > 0 else 0.0)
                   
        return {
            "transcription_count": self.transcription_count,
            "total_processing_time": self.total_processing_time,
            "average_processing_time": avg_time,
            "last_transcription_time": self.last_transcription_time,
            "is_busy": self.is_busy
        }
        
    def test_stt(self) -> bool:
        """测试STT功能"""
        if not self.is_initialized:
            if not self.initialize():
                return False
                
        if self.whisper_client:
            return self.whisper_client.test_recognition()
        else:
            return False
            
    def get_status(self) -> Dict[str, Any]:
        """获取STT状态"""
        return {
            "initialized": self.is_initialized,
            "busy": self.is_busy,
            "model_info": self.whisper_client.get_model_info() if self.whisper_client else None,
            "config": self.stt_config,
            "performance": self.get_performance_stats()
        }
        
    def cleanup(self):
        """清理资源"""
        if self.whisper_client:
            self.whisper_client.cleanup()
            self.whisper_client = None
            
        self.is_initialized = False
        self.is_busy = False
        print("🧹 STT管理器资源已清理")
        
    def __del__(self):
        """析构函数"""
        self.cleanup()
