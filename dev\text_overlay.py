#!/usr/bin/env python3
"""
文本叠加显示组件
在Live2D模型上显示对话文本
"""

import sys
from PySide6.QtWidgets import QLabel, QWidget, QVBoxLayout, QTextEdit, QFrame
from PySide6.QtCore import Qt, QTimer, Signal, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QFont, QPalette, QColor, QPainter, QPen, QBrush


class TextOverlay(QLabel):
    """文本叠加显示组件"""
    
    # 信号：文本显示完成
    text_finished = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_widget = parent
        self.current_text = ""
        self.display_text = ""
        self.char_index = 0
        self.is_animating = False
        
        # 动画定时器
        self.type_timer = QTimer()
        self.type_timer.timeout.connect(self.show_next_char)
        
        # 自动隐藏定时器
        self.hide_timer = QTimer()
        self.hide_timer.timeout.connect(self.hide_text)
        
        # 配置
        self.typing_speed = 50  # 毫秒/字符
        self.auto_hide_delay = 5000  # 5秒后自动隐藏
        self.max_chars_per_line = 20  # 每行最大字符数
        self.max_lines = 3  # 最大行数
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 设置窗口标志，确保不受父窗口透明模式影响
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool
        )

        # 设置窗口属性，确保始终可见
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, False)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, False)
        self.setAttribute(Qt.WidgetAttribute.WA_OpaquePaintEvent, True)

        # 设置基本属性
        self.setWordWrap(True)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setTextInteractionFlags(Qt.TextInteractionFlag.NoTextInteraction)

        # 设置样式
        self.setStyleSheet("""
            QLabel {
                background-color: rgba(0, 0, 0, 200);
                color: white;
                border: 2px solid rgba(255, 255, 255, 150);
                border-radius: 12px;
                padding: 12px 16px;
                font-size: 16px;
                font-weight: bold;
                font-family: "Microsoft YaHei", "SimHei", sans-serif;
            }
        """)

        # 初始隐藏
        self.hide()
        
        # 设置字体
        font = QFont()
        font.setFamily("Microsoft YaHei")
        font.setPointSize(14)
        font.setBold(True)
        self.setFont(font)
    
    def update_position(self):
        """更新位置到父窗口底部"""
        if not self.parent_widget:
            return

        # 获取父窗口的全局位置和大小
        parent_global_pos = self.parent_widget.mapToGlobal(self.parent_widget.rect().topLeft())
        parent_rect = self.parent_widget.rect()

        # 计算文本大小
        self.adjustSize()
        text_width = self.width()
        text_height = self.height()

        # 位置：水平居中，距离底部20%的位置
        x = parent_global_pos.x() + (parent_rect.width() - text_width) // 2
        y = parent_global_pos.y() + int(parent_rect.height() * 0.8 - text_height // 2)

        # 确保不超出屏幕边界
        from PySide6.QtGui import QGuiApplication
        screen = QGuiApplication.primaryScreen().geometry()
        x = max(10, min(x, screen.width() - text_width - 10))
        y = max(10, min(y, screen.height() - text_height - 10))

        self.move(x, y)
    
    def format_text(self, text: str) -> str:
        """格式化文本，处理换行"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = text.strip()
        
        # 按最大字符数分行
        lines = []
        current_line = ""
        
        for char in text:
            if char == '\n':
                if current_line:
                    lines.append(current_line)
                    current_line = ""
                continue
            
            current_line += char
            
            # 检查是否需要换行
            if len(current_line) >= self.max_chars_per_line:
                lines.append(current_line)
                current_line = ""
        
        # 添加最后一行
        if current_line:
            lines.append(current_line)
        
        # 限制最大行数
        if len(lines) > self.max_lines:
            lines = lines[:self.max_lines]
            # 在最后一行末尾添加省略号
            if lines:
                lines[-1] = lines[-1][:self.max_chars_per_line-3] + "..."
        
        return '\n'.join(lines)
    
    def show_text(self, text: str, typing_animation: bool = True):
        """显示文本"""
        if not text:
            return
        
        # 停止当前动画
        self.stop_animation()
        
        # 格式化文本
        self.current_text = self.format_text(text)
        
        if typing_animation and self.current_text:
            # 打字机效果
            self.display_text = ""
            self.char_index = 0
            self.is_animating = True
            self.setText("")
            self.show()
            self.update_position()
            self.type_timer.start(self.typing_speed)
        else:
            # 直接显示
            self.display_text = self.current_text
            self.setText(self.display_text)
            self.show()
            self.update_position()
            self.start_hide_timer()
    
    def show_next_char(self):
        """显示下一个字符（打字机效果）"""
        if self.char_index < len(self.current_text):
            self.display_text += self.current_text[self.char_index]
            self.setText(self.display_text)
            self.char_index += 1
            self.update_position()  # 每次更新文本后重新定位
        else:
            # 动画完成
            self.type_timer.stop()
            self.is_animating = False
            self.text_finished.emit()
            self.start_hide_timer()
    
    def start_hide_timer(self):
        """开始自动隐藏计时器"""
        if self.auto_hide_delay > 0:
            self.hide_timer.start(self.auto_hide_delay)
    
    def hide_text(self):
        """隐藏文本"""
        self.hide()
        self.hide_timer.stop()
    
    def stop_animation(self):
        """停止所有动画"""
        self.type_timer.stop()
        self.hide_timer.stop()
        self.is_animating = False
    
    def set_typing_speed(self, speed_ms: int):
        """设置打字速度（毫秒/字符）"""
        self.typing_speed = max(10, speed_ms)
    
    def set_auto_hide_delay(self, delay_ms: int):
        """设置自动隐藏延迟（毫秒）"""
        self.auto_hide_delay = max(0, delay_ms)
    
    def set_max_chars_per_line(self, chars: int):
        """设置每行最大字符数"""
        self.max_chars_per_line = max(10, chars)
    
    def set_max_lines(self, lines: int):
        """设置最大行数"""
        self.max_lines = max(1, lines)
    
    def is_visible_and_animating(self) -> bool:
        """检查是否正在显示和动画"""
        return self.isVisible() and self.is_animating


class QuickInputOverlay(QFrame):
    """快速输入叠加层"""

    # 信号：发送消息、预设切换
    message_sent = Signal(str)
    preset_changed = Signal(str)  # 预设ID

    def __init__(self, parent=None, config_manager=None):
        super().__init__(parent)
        self.parent_widget = parent
        self.config_manager = config_manager
        self.current_preset_id = "default"
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 设置窗口标志，确保不受父窗口透明模式影响
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool
        )

        # 设置窗口属性，确保始终可见
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, False)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, False)
        self.setAttribute(Qt.WidgetAttribute.WA_OpaquePaintEvent, True)

        self.setFrameStyle(QFrame.Shape.StyledPanel)
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(250, 250, 250, 245),
                    stop:1 rgba(240, 240, 240, 245));
                border: 2px solid rgba(120, 120, 120, 180);
                border-radius: 12px;
            }
            QLabel {
                color: #333;
                font-size: 12px;
                font-weight: bold;
                background: transparent;
                padding: 2px;
            }
            QComboBox {
                background-color: rgba(255, 255, 255, 250);
                border: 1px solid #ddd;
                border-radius: 6px;
                padding: 4px 8px;
                font-size: 12px;
                min-width: 120px;
            }
            QComboBox:hover {
                border-color: #007acc;
                background-color: rgba(255, 255, 255, 255);
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #666;
                margin-right: 4px;
            }
            QTextEdit {
                background-color: rgba(255, 255, 255, 250);
                border: 1px solid #ddd;
                border-radius: 6px;
                padding: 6px;
                font-size: 14px;
                selection-background-color: #007acc;
            }
            QTextEdit:focus {
                border-color: #007acc;
                background-color: rgba(255, 255, 255, 255);
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007acc,
                    stop:1 #005a9e);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
                min-width: 60px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0088dd,
                    stop:1 #006bb3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #005a9e,
                    stop:1 #004578);
            }
            QPushButton#cancelBtn {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #888,
                    stop:1 #666);
            }
            QPushButton#cancelBtn:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #999,
                    stop:1 #777);
            }
        """)
        
        # 布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)

        # 预设选择区域
        from PySide6.QtWidgets import QPushButton, QHBoxLayout, QLabel, QComboBox
        preset_layout = QHBoxLayout()
        preset_layout.setSpacing(8)

        self.preset_label = QLabel("当前预设:")
        self.preset_combo = QComboBox()
        self.preset_combo.setToolTip("选择对话预设")
        self.preset_combo.currentTextChanged.connect(self.on_preset_changed)

        preset_layout.addWidget(self.preset_label)
        preset_layout.addWidget(self.preset_combo)
        preset_layout.addStretch()

        # 输入框
        self.input_edit = QTextEdit()
        self.input_edit.setMaximumHeight(60)
        self.input_edit.setPlaceholderText("快速输入消息...")
        self.input_edit.installEventFilter(self)

        # 按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)

        self.send_btn = QPushButton("发送")
        self.send_btn.clicked.connect(self.send_message)
        self.send_btn.setDefault(True)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setObjectName("cancelBtn")
        self.cancel_btn.clicked.connect(self.hide)

        button_layout.addStretch()
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.send_btn)

        # 添加到主布局
        layout.addLayout(preset_layout)
        layout.addWidget(self.input_edit)
        layout.addLayout(button_layout)

        # 初始化预设列表
        self.load_presets()

        # 初始隐藏
        self.hide()

        # 设置固定大小（增加高度以容纳预设选择）
        self.setFixedSize(320, 140)
    
    def show_input(self):
        """显示输入框"""
        self.update_position()
        self.show()
        self.input_edit.setFocus()
        self.input_edit.clear()
    
    def update_position(self):
        """更新位置"""
        if not self.parent_widget:
            return

        # 获取父窗口的全局位置和大小
        parent_global_pos = self.parent_widget.mapToGlobal(self.parent_widget.rect().topLeft())
        parent_rect = self.parent_widget.rect()

        # 位置：居中显示
        x = parent_global_pos.x() + (parent_rect.width() - self.width()) // 2
        y = parent_global_pos.y() + (parent_rect.height() - self.height()) // 2

        # 确保不超出屏幕边界
        from PySide6.QtGui import QGuiApplication
        screen = QGuiApplication.primaryScreen().geometry()
        x = max(10, min(x, screen.width() - self.width() - 10))
        y = max(10, min(y, screen.height() - self.height() - 10))

        self.move(x, y)

    def load_presets(self):
        """加载预设列表"""
        if not self.config_manager:
            return

        self.preset_combo.clear()
        presets = self.config_manager.config.get("llm_presets", {})

        # 添加预设到下拉框
        for preset_id, preset_data in presets.items():
            name = preset_data.get("name", preset_id)
            model = preset_data.get("api_config", {}).get("model", "")
            display_text = f"{name}"
            if model:
                # 简化模型名称显示
                model_short = model.split("/")[-1] if "/" in model else model
                if len(model_short) > 15:
                    model_short = model_short[:12] + "..."
                display_text += f" ({model_short})"

            self.preset_combo.addItem(display_text, preset_id)

        # 设置当前预设
        self.set_current_preset(self.current_preset_id)

    def set_current_preset(self, preset_id):
        """设置当前预设"""
        self.current_preset_id = preset_id

        # 在下拉框中选择对应项
        for i in range(self.preset_combo.count()):
            if self.preset_combo.itemData(i) == preset_id:
                self.preset_combo.setCurrentIndex(i)
                break

    def on_preset_changed(self):
        """预设切换处理"""
        current_index = self.preset_combo.currentIndex()
        if current_index >= 0:
            preset_id = self.preset_combo.itemData(current_index)
            if preset_id and preset_id != self.current_preset_id:
                self.current_preset_id = preset_id
                self.preset_changed.emit(preset_id)
                print(f"快速输入预设切换到: {preset_id}")

    def send_message(self):
        """发送消息"""
        text = self.input_edit.toPlainText().strip()
        if text:
            self.message_sent.emit(text)
            self.input_edit.clear()
        self.hide()
    
    def eventFilter(self, obj, event):
        """事件过滤器"""
        if obj == self.input_edit and event.type() == event.Type.KeyPress:
            if event.key() == Qt.Key.Key_Return and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                self.send_message()
                return True
            elif event.key() == Qt.Key.Key_Escape:
                self.hide()
                return True
        return super().eventFilter(obj, event)


class TextDisplayManager:
    """文本显示管理器"""

    def __init__(self, parent_widget):
        """初始化管理器"""
        self.parent_widget = parent_widget
        self.text_overlay = TextOverlay(parent_widget)

        # 获取配置管理器
        config_manager = getattr(parent_widget, 'config_manager', None)
        self.quick_input = QuickInputOverlay(parent_widget, config_manager)
        
        # 配置
        self.config = {
            "typing_speed": 50,
            "auto_hide_delay": 5000,
            "max_chars_per_line": 20,
            "max_lines": 3,
            "typing_animation": True
        }
        
        self.apply_config()
    
    def apply_config(self):
        """应用配置"""
        self.text_overlay.set_typing_speed(self.config["typing_speed"])
        self.text_overlay.set_auto_hide_delay(self.config["auto_hide_delay"])
        self.text_overlay.set_max_chars_per_line(self.config["max_chars_per_line"])
        self.text_overlay.set_max_lines(self.config["max_lines"])
    
    def update_config(self, new_config: dict):
        """更新配置"""
        self.config.update(new_config)
        self.apply_config()
    
    def show_message(self, message: str):
        """显示消息"""
        typing_animation = self.config.get("typing_animation", True)
        self.text_overlay.show_text(message, typing_animation)
    
    def show_quick_input(self):
        """显示快速输入"""
        self.quick_input.show_input()
    
    def hide_all(self):
        """隐藏所有显示"""
        self.text_overlay.hide_text()
        self.quick_input.hide()
    
    def update_positions(self):
        """更新所有组件位置"""
        self.text_overlay.update_position()
        self.quick_input.update_position()
    
    def connect_signals(self, message_handler=None, preset_handler=None):
        """连接信号"""
        if message_handler:
            self.quick_input.message_sent.connect(message_handler)
        if preset_handler:
            self.quick_input.preset_changed.connect(preset_handler)
