{"Version": 3, "Meta": {"Duration": 4.067, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 244, "TotalSegmentCount": 12500, "TotalPointCount": 18600, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAllPositionY", "Segments": [0, 0, 2, 0.2, 0, 0, 0.333, -0.06, 0, 0.5, 0.021, 0, 0.6, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamAllZ", "Segments": [0, 0, 2, 0.033, 0, 0, 0.1, 0.12, 0, 0.2, -0.33, 0, 0.333, 0.371, 0, 0.467, 0, 2, 0.6, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamAllSize", "Segments": [0, 0, 2, 0.033, 0, 0, 0.167, 0.046, 0, 0.3, -0.023, 0, 0.433, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamFlap", "Segments": [0, 0, 1, 0.022, 0, 0.045, 0.861, 0.067, 2, 2, 0.133, 5, 2, 0.2, 8, 1, 0.222, 9.139, 0.245, 10, 0.267, 10, 2, 1.833, 10, 2, 1.867, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.1, 1, 0, 0.233, 0, 2, 0.267, 0, 0, 0.4, 0.8, 0, 0.467, 0.2, 0, 0.667, 1, 0, 0.867, 0.632, 0, 0.933, 0.752, 0, 0.967, 0.62, 0, 1.033, 0.729, 0, 1.1, 0.622, 0, 1.2, 0.717, 1, 1.222, 0.717, 1.245, 0.739, 1.267, 0.618, 1, 1.3, 0.438, 1.334, 0, 1.367, 0, 1, 1.389, 0, 1.411, 0.747, 1.433, 0.752, 1, 1.466, 0.761, 1.5, 0.76, 1.533, 0.76, 0, 1.633, 0, 0, 1.733, 0.7, 2, 1.767, 0.7, 0, 1.867, 0, 1, 1.9, 0, 1.934, 0.777, 1.967, 0.82, 1, 1.978, 0.835, 1.989, 0.83, 2, 0.832, 1, 2.022, 0.835, 2.045, 0.835, 2.067, 0.839, 1, 2.089, 0.844, 2.111, 0.939, 2.133, 0.939, 0, 2.2, 0, 0, 2.3, 0.558, 0, 2.533, 0, 2, 2.833, 0, 2, 2.967, 0, 2, 3.067, 0, 0, 3.3, 1, 2, 4.067, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.1, 1, 0, 0.233, 0, 2, 0.267, 0, 0, 0.4, 0.8, 0, 0.467, 0.2, 0, 0.667, 1, 0, 0.867, 0.632, 0, 0.933, 0.752, 0, 0.967, 0.618, 0, 1.033, 0.729, 0, 1.1, 0.622, 0, 1.2, 0.717, 1, 1.222, 0.717, 1.245, 0.738, 1.267, 0.616, 1, 1.3, 0.433, 1.334, 0, 1.367, 0, 1, 1.389, 0, 1.411, 0.743, 1.433, 0.75, 1, 1.466, 0.761, 1.5, 0.76, 1.533, 0.76, 0, 1.633, 0, 0, 1.733, 0.7, 2, 1.767, 0.7, 0, 1.867, 0, 1, 1.9, 0, 1.934, 0.777, 1.967, 0.82, 1, 1.978, 0.835, 1.989, 0.83, 2, 0.832, 1, 2.022, 0.835, 2.045, 0.835, 2.067, 0.839, 1, 2.089, 0.844, 2.111, 0.939, 2.133, 0.939, 0, 2.2, 0, 0, 2.3, 0.558, 0, 2.533, 0, 2, 2.833, 0, 2, 2.967, 0, 2, 3.067, 0, 0, 3.3, 1, 2, 4.067, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 0, 0.167, -0.823, 0, 0.333, -0.774, 0, 0.533, -1, 2, 0.833, -1, 0, 0.9, -0.823, 0, 0.967, -1, 0, 1.033, -0.916, 0, 1.167, -1, 0, 1.233, -0.91, 0, 1.3, -1, 0, 1.367, -0.903, 0, 1.433, -0.993, 0, 1.567, -0.903, 0, 1.667, -1, 2, 2.833, -1, 2, 2.967, -1, 2, 3.4, -1, 0, 4, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0.256, 0.133, 0.4, 1, 0.211, 0.652, 0.289, 0.9, 0.367, 1, 2, 0.767, 1, 1, 0.978, 1, 1.189, 0.668, 1.4, 0.391, 1, 1.522, 0.23, 1.645, 0.26, 1.767, 0.26, 2, 2.233, 0.26, 1, 2.355, 0.26, 2.478, 0.218, 2.6, 0.096, 1, 2.622, 0.074, 2.645, 0, 2.667, 0, 2, 2.833, 0, 2, 2.967, 0, 2, 3.067, 0, 2, 3.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamMouthSize", "Segments": [0, 0, 2, 0.067, 0, 0, 0.3, -1, 0, 0.667, -0.1, 0, 1.833, -0.5, 2, 2.533, -0.5, 0, 2.667, 0.5, 2, 2.833, 0.5, 2, 2.967, 0.5, 2, 3.067, 0.5, 2, 3.467, 0.5, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamMouthY", "Segments": [0, 0, 2, 0.133, 0, 0, 0.467, 0.3, 0, 0.733, 0, 2, 2.833, 0, 2, 2.967, 0, 2, 3.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamEyeEmotion", "Segments": [0, 0, 0, 0.3, -0.6, 0, 0.867, 1, 2, 1.833, 1, 2, 2.067, 1, 2, 2.233, 1, 2, 2.6, 1, 1, 2.678, 1, 2.755, 0.711, 2.833, 0.664, 1, 2.878, 0.637, 2.922, 0.646, 2.967, 0.63, 1, 3, 0.618, 3.034, 0.616, 3.067, 0.557, 1, 3.2, 0.32, 3.334, 0, 3.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 0.8, 1, 2, 1.833, 1, 2, 2.067, 1, 2, 2.233, 1, 2, 2.6, 1, 2, 2.833, 1, 2, 2.967, 1, 2, 3.067, 1, 2, 3.133, 1, 0, 3.867, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamMark", "Segments": [0, 0, 0, 0.8, 1.2, 2, 1.833, 1.2, 2, 2.067, 1.2, 2, 2.2, 1.2, 0, 2.8, 0, 2, 2.833, 0, 2, 2.967, 0, 2, 3.067, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamMarkShake", "Segments": [0, 0, 2, 0.867, 0, 0, 0.933, 1, 0, 1.233, -1, 0, 1.467, 1, 0, 1.6, 0, 2, 1.833, 0, 2, 2.067, 0, 2, 2.233, 0, 2, 2.6, 0, 2, 2.767, 0, 0, 2.833, 1, 1, 2.878, 1, 2.922, 1, 2.967, 0.999, 1, 3, 0.998, 3.034, 1, 3.067, 0.953, 1, 3.2, 0.696, 3.334, -1, 3.467, -1, 0, 3.7, 1, 0, 3.967, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLX", "Segments": [0, 0, 2, 1.133, 0, 0, 1.267, 1, 2, 1.367, 1, 0, 1.5, 0, 2, 1.633, 0, 1, 1.666, 0, 1.7, -0.702, 1.733, -0.9, 1, 1.755, -1, 1.778, -1, 1.8, -1, 0, 1.867, 0, 2, 2.833, 0, 2, 2.967, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeL", "Segments": [0, 0, 2, 0.2, 0, 0, 0.567, 1, 1, 0.645, 1, 0.722, 0.633, 0.8, 0.063, 1, 0.822, -0.1, 0.845, -0.124, 0.867, -0.124, 0, 0.933, 0.171, 0, 0.967, -0.152, 0, 1.067, 0.146, 0, 1.133, -0.116, 0, 1.233, 0.125, 0, 1.3, -0.118, 0, 1.4, 0.032, 0, 1.533, -0.064, 1, 1.578, -0.064, 1.622, -0.064, 1.667, -0.059, 1, 1.722, -0.053, 1.778, -0.029, 1.833, -0.027, 1, 1.911, -0.025, 1.989, -0.024, 2.067, -0.023, 0, 2.1, -0.022, 0, 2.2, -0.1, 1, 2.333, -0.1, 2.467, -0.099, 2.6, -0.09, 1, 2.678, -0.085, 2.755, -0.058, 2.833, -0.055, 1, 2.878, -0.053, 2.922, -0.054, 2.967, -0.053, 1, 3, -0.052, 3.034, -0.053, 3.067, -0.048, 1, 3.2, -0.03, 3.334, 0, 3.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRX", "Segments": [0, 0, 2, 1.133, 0, 0, 1.267, 0.6, 2, 1.367, 0.6, 0, 1.5, 0, 2, 1.633, 0, 1, 1.666, 0, 1.7, -0.607, 1.733, -0.8, 1, 1.755, -0.929, 1.778, -0.9, 1.8, -0.9, 0, 1.867, 0, 2, 2.833, 0, 2, 2.967, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRY", "Segments": [0, 0, 0, 0.8, -0.396, 2, 2.2, -0.396, 1, 2.211, -0.396, 2.222, -0.394, 2.233, -0.393, 1, 2.355, -0.382, 2.478, -0.376, 2.6, -0.36, 1, 2.678, -0.35, 2.755, -0.232, 2.833, -0.221, 1, 2.878, -0.215, 2.922, -0.217, 2.967, -0.213, 1, 3, -0.21, 3.034, -0.211, 3.067, -0.192, 1, 3.2, -0.117, 3.334, 0, 3.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeR", "Segments": [0, 0, 2, 0.2, 0, 0, 0.567, 1, 1, 0.645, 1, 0.722, 0.633, 0.8, 0.063, 1, 0.822, -0.1, 0.845, -0.124, 0.867, -0.124, 0, 0.933, 0.171, 0, 0.967, -0.152, 0, 1.067, 0.146, 0, 1.133, -0.116, 0, 1.233, 0.125, 0, 1.3, -0.118, 0, 1.4, 0.032, 0, 1.533, -0.064, 1, 1.578, -0.064, 1.622, -0.064, 1.667, -0.059, 1, 1.722, -0.053, 1.778, -0.029, 1.833, -0.027, 1, 1.911, -0.025, 1.989, -0.024, 2.067, -0.023, 0, 2.1, -0.022, 0, 2.2, -0.1, 1, 2.333, -0.1, 2.467, -0.099, 2.6, -0.09, 1, 2.678, -0.085, 2.755, -0.058, 2.833, -0.055, 1, 2.878, -0.053, 2.922, -0.054, 2.967, -0.053, 1, 3, -0.052, 3.034, -0.053, 3.067, -0.048, 1, 3.2, -0.03, 3.334, 0, 3.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow1", "Segments": [0, 0, 2, 0.867, 0, 1, 1.111, 0.333, 1.356, 0.667, 1.6, 1, 2, 1.633, 0, 2, 1.667, 0, 1, 1.911, 0.333, 2.156, 0.667, 2.4, 1, 2, 2.433, 0, 2, 2.967, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow2", "Segments": [0, 0, 2, 1, 0, 1, 1.233, 0.333, 1.467, 0.667, 1.7, 1, 2, 1.733, 0, 2, 1.767, 0, 1, 2, 0.333, 2.234, 0.667, 2.467, 1, 2, 2.5, 0, 2, 2.967, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamTearLight", "Segments": [0, 0, 2, 0.6, 0, 0, 1.2, 1, 2, 1.5, 0, 2, 2.967, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamTears", "Segments": [0, 0, 2, 0.4, 0, 0, 0.8, 1, 2, 2.7, 1, 0, 2.833, 0, 2, 2.967, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPHYInputX", "Segments": [0, 0, 2, 0.1, 0, 0, 0.433, -11, 0, 0.7, 10, 0, 0.9, -3, 0, 1.1, 1.51, 0, 1.3, -0.081, 1, 1.344, -0.081, 1.389, -0.133, 1.433, 0.396, 1, 1.544, 1.718, 1.656, 3.128, 1.767, 3.128, 1, 1.867, 3.128, 1.967, -1.182, 2.067, -3.125, 1, 2.122, -4.205, 2.178, -4, 2.233, -4, 0, 2.467, 6.455, 1, 2.522, 6.455, 2.578, 1.554, 2.633, 1, 1, 2.7, 0.336, 2.766, -0.182, 2.833, -0.22, 1, 2.911, -0.264, 2.989, -0.257, 3.067, -0.257, 1, 3.078, -0.257, 3.089, -0.297, 3.1, -0.251, 1, 3.289, 0.539, 3.478, 3, 3.667, 3, 0, 4, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.133, 0, 0, 0.467, 11, 0, 0.833, -24.174, 0, 0.967, -21.244, 0, 1.1, -24.922, 1, 1.2, -24.922, 1.3, -24.715, 1.4, -23.83, 1, 1.433, -23.535, 1.467, -22.457, 1.5, -22.457, 1, 1.544, -22.457, 1.589, -22.513, 1.633, -23.43, 1, 1.666, -24.118, 1.7, -28.048, 1.733, -29, 1, 1.766, -29.952, 1.8, -30, 1.833, -30, 0, 2, -17.747, 0, 2.133, -19, 0, 2.233, -11, 1, 2.355, -11, 2.478, -17.866, 2.6, -18, 1, 2.633, -18.036, 2.667, -18, 2.7, -18.024, 1, 2.767, -18.072, 2.833, -21.521, 2.9, -21.521, 1, 2.944, -21.521, 2.989, -22.165, 3.033, -19.26, 1, 3.189, -9.093, 3.344, 3, 3.5, 3, 0, 3.867, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.1, 0, 0, 0.433, -30, 0, 0.8, -4, 0, 1, -30, 0, 1.167, -25.49, 0, 1.3, -27.081, 1, 1.344, -27.081, 1.389, -26.856, 1.433, -26.604, 1, 1.566, -25.848, 1.7, -25.381, 1.833, -24.481, 1, 1.878, -24.181, 1.922, -20.854, 1.967, -20.854, 1, 2.022, -20.854, 2.078, -28.142, 2.133, -29.125, 1, 2.189, -30, 2.244, -30, 2.3, -30, 0, 2.533, -9.107, 1, 2.578, -9.107, 2.622, -12.69, 2.667, -13.147, 1, 2.722, -13.719, 2.778, -14.194, 2.833, -14.22, 1, 2.878, -14.241, 2.922, -14.241, 2.967, -14.249, 1, 3, -14.255, 3.034, -14.257, 3.067, -14.257, 1, 3.078, -14.257, 3.089, -14.313, 3.1, -14.251, 1, 3.289, -13.205, 3.478, 3, 3.667, 3, 0, 4, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.067, 0, 0, 0.5, -12, 0, 1.033, 12.408, 0, 1.267, 7.51, 0, 1.433, 8.51, 0, 1.733, 8.082, 0, 2, 8.51, 1, 2.078, 8.51, 2.155, -5.921, 2.233, -6.419, 1, 2.344, -7.131, 2.456, -7.233, 2.567, -7.75, 1, 2.589, -7.853, 2.611, -9.819, 2.633, -9.86, 1, 2.7, -9.982, 2.766, -10, 2.833, -10, 1, 2.911, -10, 2.989, -9.453, 3.067, -6.82, 1, 3.211, -1.93, 3.356, 1.685, 3.5, 1.685, 0, 3.967, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamAngleH", "Segments": [0, 0, 2, 0.033, 0, 0, 0.367, -2, 0, 0.8, 15, 2, 2.833, 15, 2, 2.967, 15, 2, 3.133, 15, 0, 3.567, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamAngleS", "Segments": [0, 0, 0, 0.4, 1, 0, 0.667, 0, 2, 1.833, 0, 2, 2.067, 0, 2, 2.833, 0, 2, 2.967, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperLAngle", "Segments": [0, 0, 0, 0.367, -2, 0, 0.533, 0.733, 1, 0.578, 0.733, 0.622, -0.16, 0.667, -1, 1, 0.711, -1.84, 0.756, -2, 0.8, -2, 2, 2, -2, 0, 2.167, -1.661, 1, 2.267, -1.661, 2.367, -2.662, 2.467, -3.66, 1, 2.545, -4.436, 2.622, -4.544, 2.7, -4.544, 2, 2.9, -4.544, 1, 2.944, -4.544, 2.989, -4.683, 3.033, -4.285, 1, 3.111, -3.588, 3.189, 1.492, 3.267, 1.492, 0, 3.4, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamArmHandLAngle", "Segments": [0, 0, 2, 0.633, 0, 0, 0.833, 3, 0, 0.967, 1.6, 0, 1.133, 2.02, 0, 2.167, 2, 0, 2.367, 3, 0, 2.6, 0, 0, 2.833, 0.408, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerLAngle", "Segments": [0, 10, 0, 0.267, 9.874, 0, 0.433, 12, 0, 0.8, -3.362, 0, 0.9, -3, 2, 1, -3, 2, 2.133, -3, 0, 2.267, -3.362, 0, 2.567, 0.217, 0, 2.733, 0, 2, 3.133, 0, 1, 3.211, 0, 3.289, 0.245, 3.367, 2.964, 1, 3.434, 5.295, 3.5, 10.053, 3.567, 10.053, 0, 3.667, 9.96, 0, 3.8, 10, 2, 4.067, 10]}, {"Target": "Parameter", "Id": "ParamArmLowerLH", "Segments": [0, 0, 0, 0.8, -12, 1, 1.144, -12, 1.489, -11.497, 1.833, -11.262, 1, 1.911, -11.209, 1.989, -11.237, 2.067, -11.214, 1, 2.111, -11.201, 2.156, -11.082, 2.2, -11, 1, 2.211, -10.979, 2.222, -10.961, 2.233, -10.838, 1, 2.3, -10.1, 2.366, -9.194, 2.433, -8, 1, 2.522, -6.408, 2.611, -5, 2.7, -5, 2, 2.833, -5, 1, 2.911, -5, 2.989, -5, 3.067, -5.003, 1, 3.122, -5.005, 3.178, -5.036, 3.233, -5.036, 0, 3.567, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerLAngle", "Segments": [0, 0, 0, 0.233, -3, 0, 0.7, 10, 2, 1, 10, 0, 1.167, 4, 2, 2.2, 4, 1, 2.211, 4, 2.222, 3.975, 2.233, 3.968, 1, 2.411, 3.852, 2.589, 3.757, 2.767, 3.634, 1, 2.789, 3.619, 2.811, 3.368, 2.833, 3.349, 1, 2.878, 3.311, 2.922, 3.31, 2.967, 3.268, 1, 3, 3.237, 3.034, 3.224, 3.067, 2.937, 1, 3.2, 1.789, 3.334, 0, 3.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamHandT2L", "Segments": [0, 0, 2, 0.6, 0, 0, 0.733, 0.545, 0, 1, 0.249, 0, 1.2, 0.3, 1, 1.533, 0.3, 1.867, 0.298, 2.2, 0.286, 1, 2.3, 0.283, 2.4, 0, 2.5, 0, 2, 2.767, 0, 2, 2.833, 0, 2, 2.967, 0, 2, 3.067, 0, 2, 3.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamArm<PERSON>pperRAngle", "Segments": [0, 0, 0, 0.267, -2, 0, 0.667, 4, 0, 0.933, 3.328, 2, 2, 3.328, 2, 2.1, 3.328, 0, 2.433, 0, 1, 2.5, 0, 2.566, 0.666, 2.633, 1, 1, 2.7, 1.334, 2.766, 1.373, 2.833, 1.67, 1, 2.878, 1.868, 2.922, 2.264, 2.967, 2.264, 0, 3.333, -2.628, 0, 3.8, 0.632, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRAngle", "Segments": [0, 7.4, 2, 0.067, 7.4, 0, 0.4, 5.32, 0, 0.833, 29.256, 0, 0.933, 29, 2, 1.933, 29, 0, 2.067, 29.23, 1, 2.122, 29.23, 2.178, 29.476, 2.233, 28.915, 1, 2.355, 27.68, 2.478, 3.887, 2.6, 3.016, 1, 2.667, 2.541, 2.733, 2.745, 2.8, 2.447, 1, 2.856, 2.198, 2.911, 1.2, 2.967, 1.2, 1, 3.045, 1.2, 3.122, 1.355, 3.2, 3.016, 1, 3.289, 4.914, 3.378, 7.4, 3.467, 7.4, 2, 4.067, 7.4]}, {"Target": "Parameter", "Id": "ParamArmHandRAngle", "Segments": [0, 0, 0, 0.367, 3.289, 0, 0.633, -8.871, 0, 0.833, -5.126, 0, 1.033, -6.871, 2, 1.267, -6.871, 2, 1.833, -6.871, 2, 2, -6.871, 0, 2.367, -10, 0, 2.6, -6, 1, 2.678, -6, 2.755, -8.513, 2.833, -8.871, 1, 2.878, -9.075, 2.922, -9, 2.967, -9, 0, 3.467, 0.811, 0, 3.667, -0.224, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRH", "Segments": [0, 0, 0, 0.3, -23, 0, 0.8, -16, 2, 2.2, -16, 1, 2.211, -16, 2.222, -15.912, 2.233, -15.871, 1, 2.355, -15.425, 2.478, -15.225, 2.6, -14.594, 1, 2.678, -14.192, 2.755, -9.536, 2.833, -9.007, 1, 2.878, -8.705, 2.922, -8.778, 2.967, -8.595, 1, 3, -8.457, 3.034, -8.053, 3.067, -7.843, 1, 3.234, -6.793, 3.4, -6.32, 3.567, -4.792, 1, 3.734, -3.264, 3.9, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperRH", "Segments": [0, 0, 0, 0.667, -3, 2, 2.2, -3, 1, 2.211, -3, 2.222, -2.984, 2.233, -2.976, 1, 2.355, -2.889, 2.478, -2.85, 2.6, -2.725, 1, 2.678, -2.646, 2.755, -1.754, 2.833, -1.67, 1, 2.878, -1.622, 2.922, -1.638, 2.967, -1.608, 1, 3, -1.586, 3.034, -1.59, 3.067, -1.449, 1, 3.2, -0.886, 3.334, 0, 3.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRAngle", "Segments": [0, 0, 2, 0.367, 0, 0, 0.667, 8, 0, 0.767, 5, 2, 0.833, 5, 0, 1.067, 9.96, 1, 1.156, 9.96, 1.244, 4.365, 1.333, 4.329, 1, 1.644, 4.205, 1.956, 4.169, 2.267, 4.037, 1, 2.378, 3.99, 2.489, 0, 2.6, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamHandT2R", "Segments": [0, 0, 2, 0.1, 0, 0, 0.333, 0.7, 0, 0.567, 0, 2, 0.6, 0, 0, 0.9, 1, 2, 2.2, 1, 0, 2.433, 0, 2, 2.6, 0, 2, 2.833, 0, 2, 2.967, 0, 2, 3.067, 0, 2, 3.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.067, 0, 0, 0.4, 8, 0, 0.8, -7.13, 0, 1.033, -6.528, 0, 1.267, -6.602, 0, 1.5, -6.528, 0, 1.7, -6.602, 1, 1.744, -6.602, 1.789, -6.392, 1.833, -6.35, 1, 1.911, -6.277, 1.989, -6.254, 2.067, -6.196, 1, 2.078, -6.188, 2.089, -6.081, 2.1, -6.002, 1, 2.144, -5.686, 2.189, -5.033, 2.233, -5, 1, 2.355, -4.908, 2.478, -4.885, 2.6, -4.792, 1, 2.656, -4.75, 2.711, -4.537, 2.767, -4.537, 1, 2.834, -4.537, 2.9, -5.014, 2.967, -5.229, 1, 3, -5.336, 3.034, -5.313, 3.067, -5.385, 1, 3.089, -5.433, 3.111, -5.553, 3.133, -5.553, 0, 3.7, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.067, 0, 0, 0.367, -10, 0, 0.7, 6, 0, 0.933, -7.55, 1, 0.955, -7.55, 0.978, -4.403, 1, -4.277, 1, 1.067, -3.899, 1.133, -3.837, 1.2, -3.837, 0, 1.367, -4.277, 0, 1.533, -3.837, 0, 1.667, -4.31, 0, 1.8, -3.833, 1, 1.811, -3.833, 1.822, -3.882, 1.833, -3.888, 1, 1.911, -3.929, 1.989, -3.941, 2.067, -3.998, 1, 2.111, -4.031, 2.156, -6.376, 2.2, -7.55, 1, 2.211, -7.844, 2.222, -7.976, 2.233, -7.976, 0, 2.433, -2, 0, 2.6, -6, 1, 2.678, -6, 2.755, -5.113, 2.833, -4.934, 1, 2.878, -4.832, 2.922, -4.867, 2.967, -4.801, 1, 3, -4.752, 3.034, -4.804, 3.067, -4.391, 1, 3.222, -2.465, 3.378, 0.993, 3.533, 0.993, 0, 3.867, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.067, 0, 0, 0.3, -0.988, 0, 0.867, 8, 0, 1.067, 6.749, 0, 1.333, 7.339, 0, 1.567, 7, 2, 2.067, 7, 2, 2.2, 7, 1, 2.211, 7, 2.222, 7.216, 2.233, 6.749, 1, 2.355, 1.612, 2.478, -2.851, 2.6, -2.851, 2, 2.8, -2.851, 1, 2.856, -2.851, 2.911, -2.838, 2.967, -2.762, 1, 3, -2.717, 3.034, -2.733, 3.067, -2.511, 1, 3.234, -1.401, 3.4, 0, 3.567, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX2", "Segments": [0, 0, 0, 0.4, 5, 0, 0.8, -10, 0, 1.067, -8.825, 0, 1.367, -10, 0, 1.567, -9.363, 0, 1.933, -10, 2, 2.2, -10, 1, 2.211, -10, 2.222, -9.946, 2.233, -9.92, 1, 2.355, -9.636, 2.478, -9.511, 2.6, -9.107, 1, 2.678, -8.85, 2.755, -5.881, 2.833, -5.604, 1, 2.878, -5.446, 2.922, -5.497, 2.967, -5.399, 1, 3, -5.326, 3.034, -5.332, 3.067, -4.874, 1, 3.2, -3.041, 3.334, 0, 3.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY2", "Segments": [0, 0, 0, 0.233, -10, 0, 0.667, 10, 0, 0.867, -3.981, 0, 1.033, 1.4, 0, 1.267, 1.064, 2, 2.067, 1.064, 1, 2.122, 1.064, 2.178, -2.648, 2.233, -3.009, 1, 2.366, -3.876, 2.5, -3.981, 2.633, -3.981, 1, 2.7, -3.981, 2.766, -1.821, 2.833, -1.558, 1, 2.878, -1.383, 2.922, -1.428, 2.967, -1.321, 1, 3, -1.241, 3.034, -1.129, 3.067, -0.872, 1, 3.111, -0.529, 3.156, 0.292, 3.2, 0.292, 0, 3.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechX", "Segments": [0, 0, 0, 0.4, 10, 0, 0.767, 1.087, 0, 0.9, 1.154, 0, 1.167, 1.049, 0, 1.333, 1.082, 0, 1.567, 1.049, 0, 1.767, 1.154, 1, 1.789, 1.154, 1.811, 1.139, 1.833, 1.136, 1, 1.911, 1.127, 1.989, 1.122, 2.067, 1.113, 1, 2.078, 1.112, 2.089, 1.095, 2.1, 1.086, 1, 2.133, 1.058, 2.167, 1.034, 2.2, 1, 1, 2.211, 0.989, 2.222, 0.926, 2.233, 0.923, 1, 2.355, 0.888, 2.478, 0.873, 2.6, 0.825, 1, 2.678, 0.795, 2.755, 0.521, 2.833, 0.495, 1, 2.878, 0.48, 2.922, 0.485, 2.967, 0.476, 1, 3, 0.469, 3.034, 0.47, 3.067, 0.428, 1, 3.2, 0.259, 3.334, 0, 3.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechW", "Segments": [0, 0, 0, 0.4, 4, 0, 0.667, -5, 0, 0.867, 1, 0, 1.033, -2.123, 0, 1.167, -0.001, 0, 1.467, -1, 2, 1.833, -1, 2, 2.067, -1, 2, 2.233, -1, 2, 2.6, -1, 2, 2.833, -1, 2, 2.967, -1, 2, 3.067, -1, 2, 3.133, -1, 0, 3.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, 0, 0, 0.4, -1, 0, 0.8, 2, 0, 1.833, 1.995, 2, 2.067, 1.995, 2, 2.133, 1.995, 0, 2.2, 2, 1, 2.211, 2, 2.222, 1.989, 2.233, 1.984, 1, 2.355, 1.925, 2.478, 1.9, 2.6, 1.816, 1, 2.678, 1.763, 2.755, 1.194, 2.833, 1.113, 1, 2.911, 1.032, 2.989, 1.075, 3.067, 0.966, 1, 3.2, 0.78, 3.334, 0, 3.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamShoulderR", "Segments": [0, 0, 0, 0.4, -30, 0, 0.8, 30, 2, 2.2, 30, 1, 2.211, 30, 2.222, 29.833, 2.233, 29.759, 1, 2.355, 28.941, 2.478, 28.56, 2.6, 27.418, 1, 2.678, 26.691, 2.755, 17.795, 2.833, 17, 1, 2.878, 16.546, 2.922, 16.679, 2.967, 16.388, 1, 3, 16.17, 3.034, 16.126, 3.067, 14.819, 1, 3.2, 9.59, 3.334, 0, 3.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamShoulderL", "Segments": [0, 0, 0, 0.4, -30, 0, 0.8, 30, 2, 2.233, 30, 1, 2.355, 30, 2.478, 30, 2.6, 29.731, 1, 2.678, 29.55, 2.755, 20.243, 2.833, 19.43, 1, 2.878, 18.965, 2.922, 19.101, 2.967, 18.8, 1, 3, 18.574, 3.034, 18.552, 3.067, 17.119, 1, 3.2, 11.387, 3.334, 0, 3.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Z", "Segments": [0, 0, 0, 0.4, -3, 0, 0.8, 8, 0, 1.167, 7, 2, 1.833, 7, 2, 2.067, 7, 2, 2.167, 7, 1, 2.189, 7, 2.211, 6.781, 2.233, 6.734, 1, 2.355, 6.476, 2.478, 6.393, 2.6, 6.051, 1, 2.678, 5.833, 2.755, 2.971, 2.833, 2.802, 1, 2.966, 2.513, 3.1, 2.5, 3.233, 2.219, 1, 3.3, 2.078, 3.366, 0, 3.433, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Z", "Segments": [0, 0, 0, 0.3, 1.756, 0, 0.833, -10, 0, 1.033, -8, 0, 1.267, -10, 0, 1.467, -9.202, 0, 1.733, -10, 0, 1.967, -9.403, 1, 2.067, -9.403, 2.167, -10.305, 2.267, -10.366, 1, 2.378, -10.434, 2.489, -10.422, 2.6, -10.422, 1, 2.633, -10.422, 2.667, -10.532, 2.7, -10, 1, 2.744, -9.291, 2.789, -5.108, 2.833, -5.108, 0, 3.233, -10.36, 0, 3.6, 0.564, 0, 3.767, -0.17, 0, 3.967, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Z", "Segments": [0, 0, 0, 0.467, -2.27, 2, 0.533, -2.27, 0, 0.733, 1.08, 0, 0.967, -1.793, 0, 1.167, 0.557, 0, 1.433, -1.712, 0, 1.767, -0.273, 0, 2.067, -0.632, 2, 2.6, -0.632, 2, 2.833, -0.632, 2, 3.233, -0.632, 2, 3.567, -0.632, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRInput", "Segments": [0, 0, 0, 0.167, -18.055, 0, 0.4, 12.238, 0, 0.767, -14.803, 0, 0.967, 8.791, 0, 1.333, -11.356, 0, 1.533, 5.33, 0, 1.933, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuREyeOpen", "Segments": [0, 0, 2, 0.9, 0, 0, 1.1, 1, 2, 1.633, 1, 2, 3, 1, 0, 3.233, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyZ", "Segments": [0, 0, 0, 0.267, 1, 0, 0.6, -6.885, 0, 1.1, 18.387, 0, 1.6, -15.215, 1, 1.678, -15.215, 1.755, -15.383, 1.833, -15.014, 1, 2.166, -13.435, 2.5, -10.98, 2.833, -10.98, 0, 3.067, -12.645, 0, 3.567, 4.335, 0, 3.933, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRPositionZ", "Segments": [0, 0, 0, 0.333, -3, 0, 0.767, 18.645, 0, 1.4, -18, 1, 1.544, -18, 1.689, -18.197, 1.833, -17.545, 1, 2.178, -15.991, 2.522, -13, 2.867, -13, 0, 3, -14.853, 0, 3.333, 1.275, 0, 3.767, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRArmB", "Segments": [0, 30, 2, 0.5, 30, 2, 0.767, 30, 0, 1.3, 4, 0, 1.5, 8, 0, 1.767, 3.932, 0, 2.067, 6.877, 0, 2.433, 4.74, 0, 2.733, 6.099, 0, 3.067, 4, 0, 3.567, 30, 2, 4.067, 30]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX1", "Segments": [0, 0, 2, 0.133, 0, 0, 0.3, -0.829, 0, 0.533, 0.635, 0, 0.9, -0.77, 0, 1.1, 0.565, 0, 1.467, -0.601, 0, 1.667, 0.394, 0, 2.067, -0.5, 0, 2.4, 0.223, 0, 2.733, -0.379, 0, 3.033, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX2", "Segments": [0, 0, 2, 0.133, 0, 1, 0.178, 0, 0.222, 0.004, 0.267, -0.014, 1, 0.311, -0.033, 0.356, -0.829, 0.4, -0.829, 0, 0.633, 0.635, 0, 0.967, -0.77, 0, 1.233, 0.565, 0, 1.6, -0.601, 0, 1.8, 0.394, 0, 2.2, -0.4, 0, 2.5, 0.223, 0, 2.867, -0.3, 0, 3.133, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX3", "Segments": [0, 0, 2, 0.133, 0, 1, 0.211, 0, 0.289, 0.002, 0.367, -0.018, 1, 0.422, -0.033, 0.478, -0.829, 0.533, -0.829, 0, 0.767, 0.635, 0, 1.133, -0.77, 0, 1.333, 0.565, 0, 1.7, -0.601, 0, 1.933, 0.394, 0, 2.3, -0.3, 0, 2.667, 0.209, 0, 3.033, -0.278, 0, 3.233, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuL", "Segments": [0, 0, 2, 0.7, 0, 0, 1.167, -0.05, 1, 1.189, -0.05, 1.211, 0.434, 1.233, 0.751, 1, 1.278, 1.385, 1.322, 1.548, 1.367, 1.548, 1, 1.411, 1.548, 1.456, 1.378, 1.5, 0.751, 1, 1.522, 0.438, 1.545, 0, 1.567, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLEyeOpen", "Segments": [0, 0, 0, 1.6, 1, 0, 1.833, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyX", "Segments": [0, 0, 0, 0.467, -2.167, 0, 1.1, 10, 0, 1.633, -8, 0, 1.8, 3.267, 0, 2.067, 0, 0, 2.267, 1.701, 0, 2.667, -1.339, 0, 3.533, 1.02, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyY", "Segments": [0, 0, 0, 0.3, -1.912, 0, 0.967, 7, 0, 1.167, -5, 0, 1.367, 3.76, 0, 1.667, -3, 0, 1.833, 1.133, 0, 2, -0.543, 0, 2.2, 1.133, 0, 2.467, -0.956, 0, 3.233, 0.956, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyZ", "Segments": [0, 0, 0, 0.233, 1.084, 0, 0.8, -18, 0, 1.067, 30, 0, 1.233, -10, 0, 1.533, 4.303, 0, 1.833, -6.425, 0, 2.267, 1.084, 0, 3, -1.211, 0, 3.733, 1.084, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyW", "Segments": [0, 0, 2, 0.733, 0, 0, 1.033, 15, 0, 1.367, -15, 0, 1.7, 9.3, 1, 1.778, 9.3, 1.855, 7.023, 1.933, 2.598, 1, 2.011, -1.827, 2.089, -3.976, 2.167, -3.976, 0, 2.633, 3.11, 0, 3.4, -0.765, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuL", "Segments": [0, 0, 2, 1.167, 0, 0, 1.367, -1, 0, 1.567, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyX", "Segments": [0, 0, 0, 0.1, -4, 0, 0.3, 11.391, 0, 0.533, 5.735, 0, 0.933, 10.18, 0, 1.233, 7, 0, 1.667, 9.817, 1, 1.722, 9.817, 1.778, 6.569, 1.833, 3.409, 1, 1.855, 2.145, 1.878, 1.621, 1.9, 1, 1, 2, -1.796, 2.1, -3, 2.2, -3, 0, 2.533, 0.391, 0, 2.8, -5.265, 0, 3.367, 1, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyY", "Segments": [0, 0, 0, 0.3, 20.883, 0, 0.467, 17.101, 0, 0.667, 17.995, 0, 0.867, 13, 0, 1.133, 17.995, 0, 1.433, -30, 1, 1.544, -30, 1.656, -17.775, 1.767, -4, 1, 1.811, 1.51, 1.856, 1.603, 1.9, 1.603, 0, 2.067, -2.395, 1, 2.134, -2.395, 2.2, -2.037, 2.267, 0, 1, 2.389, 3.734, 2.511, 6.662, 2.633, 6.662, 0, 3, 3.331, 0, 3.267, 4.737, 0, 3.6, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyZ", "Segments": [0, 0, 0, 0.3, -17, 1, 0.4, -17, 0.5, -5.084, 0.6, 7, 1, 0.667, 15.056, 0.733, 16, 0.8, 16, 0, 1.067, 7, 0, 1.3, 25.737, 0, 1.567, -7, 1, 1.656, -7, 1.744, 1.355, 1.833, 4.658, 1, 1.922, 7.961, 2.011, 8, 2.1, 8, 0, 2.867, -4, 0, 3.533, 2.443, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUArmZ", "Segments": [0, 0, 0, 0.167, 2, 0, 0.367, -1, 1, 0.445, -1, 0.522, 2.131, 0.6, 4.897, 1, 0.633, 6.082, 0.667, 6, 0.7, 6, 0, 0.967, 3, 0, 1.3, 12, 0, 1.567, -18, 1, 1.656, -18, 1.744, -5.074, 1.833, 1.32, 1, 1.889, 5.316, 1.944, 4.897, 2, 4.897, 0, 2.4, -5, 0, 2.767, -2.147, 0, 3.1, -5, 0, 3.567, 1, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyW", "Segments": [0, 0, 2, 0.433, 0, 2, 0.9, 0, 0, 1.2, 0.5, 0, 1.533, -0.5, 0, 1.833, 0.287, 0, 2.2, -0.046, 0, 2.667, 0, 2, 3.6, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPositionYPanda", "Segments": [0, 0, 2, 0.567, 0, 2, 1.067, 0, 1, 1.078, 0, 1.089, 0.142, 1.1, 0.256, 1, 1.133, 0.597, 1.167, 1.071, 1.2, 1.177, 1, 1.211, 1.212, 1.222, 1.195, 1.233, 1.195, 1, 1.278, 1.195, 1.322, 0.893, 1.367, 0.256, 1, 1.378, 0.097, 1.389, 0, 1.4, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyX", "Segments": [0, 0, 1, 0.256, 0, 0.511, -0.505, 0.767, -3.76, 1, 0.956, -6.166, 1.144, -10, 1.333, -10, 0, 1.9, 8, 0, 2.367, -1.084, 0, 3, 0, 0, 3.567, -1.084, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyY", "Segments": [0, 0, 0, 0.567, -8, 1, 0.778, -8, 0.989, -7.858, 1.2, -7, 1, 1.267, -6.729, 1.333, 5, 1.4, 5, 0, 1.6, -3.76, 1, 1.678, -3.76, 1.755, 0.189, 1.833, 2.144, 1, 1.878, 3.261, 1.922, 3.095, 1.967, 3.095, 0, 2.267, -1.275, 0, 2.567, 1.11, 0, 3.1, -1.275, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2Panda", "Segments": [0, 0, 2, 0.733, 0, 0, 1.033, -20, 0, 1.3, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyZ", "Segments": [0, 0, 0, 0.733, -21, 0, 1.033, 18, 0, 1.3, -30, 0, 1.467, 10, 0, 1.767, -4.303, 1, 1.834, -4.303, 1.9, -2.357, 1.967, 0, 1, 2.034, 2.357, 2.1, 3.024, 2.167, 3.024, 0, 2.567, 0, 0, 2.867, 1, 0, 3.4, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegFZ", "Segments": [0, 0, 2, 0.567, 0, 2, 0.733, 0, 0, 1.033, 0.3, 0, 1.3, -1, 0, 1.4, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegBZ", "Segments": [0, 0, 2, 0.567, 0, 2, 0.733, 0, 0, 1.033, 0.3, 0, 1.3, -1, 0, 1.4, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyX", "Segments": [0, 0, 1, 0.344, 0, 0.689, -0.306, 1.033, -2.651, 1, 1.166, -3.558, 1.3, -19.881, 1.433, -19.881, 1, 1.522, -19.881, 1.611, -18.859, 1.7, -18.555, 1, 1.744, -18.403, 1.789, -18.4, 1.833, -18.357, 1, 2.111, -18.085, 2.389, -18.12, 2.667, -17.584, 1, 2.989, -16.962, 3.311, 0.686, 3.633, 0.686, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyY", "Segments": [0, 0, 0, 0.3, 8.132, 0, 1.067, -19.881, 0, 1.3, 19.881, 0, 1.633, 0, 1, 1.7, 0, 1.766, 2.686, 1.833, 3.896, 1, 1.844, 4.098, 1.856, 3.976, 1.867, 3.976, 0, 2.4, -1.304, 0, 3, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamCannonZ", "Segments": [0, 0, 2, 0.7, 0, 0, 1, 11.928, 0, 1.333, -19.881, 0, 1.667, 1.743, 0, 1.833, -0.317, 0, 2.3, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamCannonGaY", "Segments": [0, 0, 2, 0.7, 0, 0, 0.9, 10.56, 0, 1.133, -19.881, 0, 1.4, 19.881, 0, 1.667, -3.221, 1, 1.722, -3.221, 1.778, 0.289, 1.833, 1.69, 1, 1.866, 2.531, 1.9, 2.387, 1.933, 2.387, 0, 2.3, -3.221, 0, 2.633, 0.887, 0, 3, -0.783, 0, 3.233, 0.887, 0, 3.633, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamCannonHandZ", "Segments": [0, 0, 2, 0.767, 0, 0, 1.033, -6.627, 0, 1.467, 6.627, 0, 1.733, -1.988, 1, 1.766, -1.988, 1.8, -1.093, 1.833, -0.475, 1, 1.889, 0.554, 1.944, 0.792, 2, 0.792, 0, 2.167, -1.162, 0, 2.467, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY1", "Segments": [0, 0, 2, 1.167, 0, 1, 1.234, 0, 1.3, 1.745, 1.367, 4.741, 1, 1.522, 11.731, 1.678, 15, 1.833, 15, 2, 1.867, 0, 1, 1.956, 0, 2.044, 4.841, 2.133, 7.271, 1, 2.355, 13.347, 2.578, 15, 2.8, 15, 2, 2.867, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY2", "Segments": [0, 0, 2, 1.3, 0, 1, 1.333, 0, 1.367, 1.911, 1.4, 4.9, 1, 1.478, 11.874, 1.555, 15, 1.633, 15, 2, 1.667, 0, 1, 1.722, 0, 1.778, 2.187, 1.833, 4.2, 1, 1.889, 6.213, 1.944, 8.015, 2, 9, 1, 2.244, 13.333, 2.489, 15, 2.733, 15, 2, 2.767, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY3", "Segments": [0, 0, 2, 1.367, 0, 2, 1.433, 0, 1, 1.489, 0, 1.544, 3.825, 1.6, 6, 1, 1.678, 9.046, 1.755, 11.642, 1.833, 13.099, 1, 1.922, 14.765, 2.011, 15, 2.1, 15, 2, 2.133, 0, 1, 2.266, 0, 2.4, 3.932, 2.533, 9, 1, 2.655, 13.645, 2.778, 15, 2.9, 15, 2, 2.933, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupInput", "Segments": [0, 0, 0, 0.167, -18.055, 0, 0.4, 12.238, 0, 0.767, -14.803, 0, 0.967, 8.791, 0, 1.333, -11.356, 0, 1.533, 5.33, 0, 1.933, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamParamStrongCatZ", "Segments": [0, 0, 0, 0.333, 2.624, 0, 0.7, -1.004, 0, 1.133, 5, 0, 1.5, -11.745, 1, 1.611, -11.745, 1.722, -8.57, 1.833, -7.95, 1, 1.911, -7.516, 1.989, -7.654, 2.067, -7.312, 1, 2.134, -7.019, 2.2, -5.725, 2.267, -5.725, 0, 2.633, -7, 0, 3.4, 2.624, 0, 3.767, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamParamSCBodyZ", "Segments": [0, 0, 0, 0.2, -1.047, 0, 0.5, 2.361, 0, 1.033, -1, 0, 1.233, 5, 0, 1.6, -9.745, 1, 1.678, -9.745, 1.755, -8.519, 1.833, -8.26, 1, 1.911, -8.001, 1.989, -8.1, 2.067, -7.811, 1, 2.167, -7.44, 2.267, -5.725, 2.367, -5.725, 0, 2.767, -7, 0, 3.533, 1.379, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamSCDishY", "Segments": [0, 0, 0, 0.633, 0.145, 0, 0.933, 0, 0, 1.333, 2, 0, 1.7, -2.745, 1, 1.744, -2.745, 1.789, -2.241, 1.833, -2.117, 1, 1.911, -1.9, 1.989, -1.99, 2.067, -1.652, 1, 2.2, -1.072, 2.334, 1.275, 2.467, 1.275, 0, 2.833, 0, 2, 3.533, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamSCDishZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.767, 0.113, 0, 0.967, 0, 0, 1.367, 2, 0, 1.733, -2.745, 1, 1.766, -2.745, 1.8, -2.455, 1.833, -2.374, 1, 1.911, -2.185, 1.989, -2.27, 2.067, -1.933, 1, 2.211, -1.308, 2.356, 1.275, 2.5, 1.275, 0, 2.867, 0, 2, 3.533, 0, 2, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamSCCupZ", "Segments": [0, 0, 2, 0.733, 0, 2, 1.033, 0, 0, 1.467, 5.028, 0, 1.833, -5.773, 1, 1.911, -5.773, 1.989, -5.875, 2.067, -5.682, 1, 2.256, -5.212, 2.444, 3.378, 2.633, 3.378, 0, 3.033, -1.002, 0, 3.533, 2, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "MB_yanwubaozha", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "MB_DRFWXZKTMD", "Segments": [0, 1, 0, 4.067, 1]}, {"Target": "Parameter", "Id": "ParamAllSizeFix", "Segments": [0, 1, 0, 4.067, 1]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBGHide", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBG2Hide", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBGX", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBGY", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN2", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN3", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBlackY", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBlackCollar", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBlackOrder", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamWhiteIN", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamCHHide", "Segments": [0, 1, 0, 4.067, 1]}, {"Target": "Parameter", "Id": "ParamDeskHide", "Segments": [0, 1, 0, 4.067, 1]}, {"Target": "Parameter", "Id": "ParamStoolHide", "Segments": [0, 1, 0, 4.067, 1]}, {"Target": "Parameter", "Id": "ParamCupDesk", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamCHX", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamCHY", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamCHZ", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamChaSize", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamCcharacterZ", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionX", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionY", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionX", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamALLSize2", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamDeskShow", "Segments": [0, 10, 0, 4.067, 10]}, {"Target": "Parameter", "Id": "ParamStrongCatShow", "Segments": [0, 10, 0, 4.067, 10]}, {"Target": "Parameter", "Id": "ParamCannonShow", "Segments": [0, 10, 0, 4.067, 10]}, {"Target": "Parameter", "Id": "ParamLightPositionX", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamFixT", "Segments": [0, 1, 0, 4.067, 1]}, {"Target": "Parameter", "Id": "ParamScare", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPupilExp", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamEyeSmileL", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamEyeSmileR", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpen2", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamMouthType", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBlackFace", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamTeethLight", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamHeart2", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamShameLine", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLY", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamEyeRLightOpen", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLightLine1", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLightLine2", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLightLine3", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLightShine", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo1", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo2", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo3", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1Y", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle2", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamFanOpenR", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamChili", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamChiliX", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamHandR", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRY", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamHand_Cl", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamHandT1R", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamHandRCup", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamHandRMail", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "Segments": [0, 1, 0, 4.067, 1]}, {"Target": "Parameter", "Id": "ParamHandLIQY1", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY2", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY3", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamHandCupZ", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamHandCupY", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLegR1X", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Y", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Y", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Y", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamFootRX", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Z", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Z", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Z", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Y", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Y", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Y", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamFootLX", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLegLF", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRHide", "Segments": [0, 1, 0, 4.067, 1]}, {"Target": "Parameter", "Id": "ParamMJRFlap", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuR", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuR", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuR", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuR", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamMRCupSet", "Segments": [0, 1, 0, 4.067, 1]}, {"Target": "Parameter", "Id": "ParamMalpositionManjuuR", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyX", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyY", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRMouth", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRSigh", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow1", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow2", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow3", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow4", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowB", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW2", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamMRCupFZ", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX1", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX2", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamLiqH", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLHide", "Segments": [0, 1, 0, 4.067, 1]}, {"Target": "Parameter", "Id": "ParamMJLSigh", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuL", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuL", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamMjLFlip", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPositionZManjuuL", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamClawFX", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamClawFY", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamClawBX", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamClawBY", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUHide", "Segments": [0, 1, 0, 4.067, 1]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuU", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuU", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuU", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUEyesForm", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineU", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineD", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamPandaHide", "Segments": [0, 1, 0, 4.067, 1]}, {"Target": "Parameter", "Id": "ParamPositionXPanda", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamSizePanda", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupY", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupZ", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupIce", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupZ", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamSCDishRO", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamSCCupRO", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamSCCupY", "Segments": [0, 0, 0, 4.067, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.3, "Value": ""}, {"Time": 3.567, "Value": ""}]}