#!/usr/bin/env python3
"""
测试配置文件值
验证config.json中的参数设置
"""

import json

def test_config_file():
    """测试配置文件"""
    print("🔍 测试配置文件")
    print("=" * 60)
    
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        # 检查llm配置
        llm_config = config.get("llm", {})
        api_config = llm_config.get("api_config", {})
        conversation_settings = llm_config.get("conversation_settings", {})
        default_params = llm_config.get("default_params", {})
        
        print("📋 llm.api_config:")
        for key, value in api_config.items():
            if key == "api_key":
                value = "***已设置***" if value else "未设置"
            print(f"   - {key}: {value}")
        
        print("\n📋 llm.conversation_settings:")
        for key, value in conversation_settings.items():
            print(f"   - {key}: {value}")
        
        print("\n📋 llm.default_params:")
        for key, value in default_params.items():
            print(f"   - {key}: {value}")
        
        # 检查关键参数
        conv_max_tokens = conversation_settings.get("max_tokens")
        default_max_tokens = default_params.get("max_tokens")
        
        print(f"\n🔍 max_tokens 检查:")
        print(f"   - conversation_settings.max_tokens: {conv_max_tokens}")
        print(f"   - default_params.max_tokens: {default_max_tokens}")
        
        # 检查是否需要更新conversation_settings
        if conv_max_tokens != default_max_tokens:
            print("⚠️ conversation_settings和default_params中的max_tokens不一致")
            if default_max_tokens == 65535:
                print("💡 需要更新conversation_settings.max_tokens")
                return False, "需要更新conversation_settings"
        
        if default_max_tokens == 65535:
            print("✅ default_params.max_tokens 配置正确")
            if conv_max_tokens == 65535 or conv_max_tokens is None:
                print("✅ 配置文件检查通过")
                return True, "配置正确"
            else:
                return False, "conversation_settings.max_tokens需要更新"
        else:
            print("❌ default_params.max_tokens 配置错误")
            return False, "default_params.max_tokens需要更新"
            
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False, str(e)

def fix_conversation_settings():
    """修复conversation_settings中的max_tokens"""
    print("\n🔧 修复conversation_settings")
    print("=" * 60)
    
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        # 更新conversation_settings中的max_tokens
        if "llm" not in config:
            config["llm"] = {}
        
        if "conversation_settings" not in config["llm"]:
            config["llm"]["conversation_settings"] = {}
        
        # 从default_params获取正确的max_tokens值
        default_max_tokens = config["llm"].get("default_params", {}).get("max_tokens", 65535)
        
        # 更新conversation_settings
        config["llm"]["conversation_settings"]["max_tokens"] = default_max_tokens
        
        # 保存配置文件
        with open("config.json", "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 已更新conversation_settings.max_tokens = {default_max_tokens}")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

if __name__ == "__main__":
    # 测试配置文件
    success, message = test_config_file()
    
    if not success and "conversation_settings" in message:
        print(f"\n🔧 检测到问题: {message}")
        print("正在自动修复...")
        
        if fix_conversation_settings():
            print("\n🔄 重新检查配置...")
            success, message = test_config_file()
    
    if success:
        print("\n🎉 配置文件检查通过！")
        print("💡 现在重新启动Live2D应用应该能正确使用65535 max_tokens")
        print("🚀 请重新启动Live2D应用并测试快速输入功能")
    else:
        print(f"\n❌ 配置仍有问题: {message}")
        print("💡 请手动检查config.json文件")
