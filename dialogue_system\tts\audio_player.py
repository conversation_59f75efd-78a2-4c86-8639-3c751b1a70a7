#!/usr/bin/env python3
"""
Live2D语音对话系统 - 音频播放器

这个模块提供了音频播放功能，包括：
- 多种音频格式支持
- 异步播放控制
- 音量和速度调节
- 播放状态管理

使用示例：
    from dialogue_system.tts.audio_player import AudioPlayer
    
    # 创建播放器
    player = AudioPlayer()
    
    # 播放音频数据
    player.play_audio_data(audio_data)
    
    # 播放音频文件
    player.play_audio_file("output.wav")
"""

import io
import wave
import threading
import time
from typing import Optional, Callable, Dict, Any, Union
from pathlib import Path
from PySide6.QtCore import QObject, Signal, QTimer
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
from PySide6.QtCore import QUrl, QIODevice
import tempfile
import os

try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    PYAUDIO_AVAILABLE = False
    print("⚠️ PyAudio未安装，将使用Qt音频播放")


class AudioPlayer(QObject):
    """音频播放器"""
    
    # 信号定义
    playback_started = Signal()
    playback_finished = Signal()
    playback_error = Signal(str)
    position_changed = Signal(float)  # 播放位置变化
    
    def __init__(self):
        """初始化音频播放器"""
        super().__init__()
        
        # Qt媒体播放器
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.media_player.setAudioOutput(self.audio_output)
        
        # 连接信号
        self.media_player.mediaStatusChanged.connect(self._on_media_status_changed)
        self.media_player.errorOccurred.connect(self._on_error_occurred)
        self.media_player.positionChanged.connect(self._on_position_changed)
        
        # PyAudio播放器（备用）
        self.pyaudio_instance = None
        self.pyaudio_stream = None
        
        # 播放状态
        self.is_playing = False
        self.current_file = None
        self.current_duration = 0.0
        
        # 播放设置
        self.volume = 1.0
        self.playback_rate = 1.0
        
        # 回调函数
        self.on_playback_start: Optional[Callable] = None
        self.on_playback_finish: Optional[Callable] = None
        self.on_playback_error: Optional[Callable] = None
        
        # 临时文件管理
        self.temp_files = []
        
        print("🔊 音频播放器初始化完成")
        
    def set_volume(self, volume: float):
        """设置音量 (0.0 - 1.0)"""
        self.volume = max(0.0, min(1.0, volume))
        self.audio_output.setVolume(self.volume)
        print(f"🔊 音量设置为: {self.volume:.2f}")
        
    def set_playback_rate(self, rate: float):
        """设置播放速度 (0.5 - 2.0)"""
        self.playback_rate = max(0.5, min(2.0, rate))
        self.media_player.setPlaybackRate(self.playback_rate)
        print(f"⏩ 播放速度设置为: {self.playback_rate:.2f}")
        
    def set_callbacks(self,
                     on_playback_start: Optional[Callable] = None,
                     on_playback_finish: Optional[Callable] = None,
                     on_playback_error: Optional[Callable] = None):
        """设置回调函数"""
        self.on_playback_start = on_playback_start
        self.on_playback_finish = on_playback_finish
        self.on_playback_error = on_playback_error
        
    def play_audio_file(self, file_path: str) -> bool:
        """播放音频文件"""
        try:
            if not os.path.exists(file_path):
                error_msg = f"音频文件不存在: {file_path}"
                print(f"❌ {error_msg}")
                self._emit_error(error_msg)
                return False
                
            print(f"🔊 播放音频文件: {file_path}")
            
            # 停止当前播放
            self.stop()
            
            # 设置媒体源
            file_url = QUrl.fromLocalFile(os.path.abspath(file_path))
            self.media_player.setSource(file_url)
            
            # 开始播放
            self.media_player.play()
            self.current_file = file_path
            
            return True
            
        except Exception as e:
            error_msg = f"播放音频文件失败: {e}"
            print(f"❌ {error_msg}")
            self._emit_error(error_msg)
            return False
            
    def play_audio_data(self, audio_data: bytes, format_hint: str = "wav") -> bool:
        """播放音频数据"""
        try:
            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(
                suffix=f".{format_hint}",
                delete=False
            )
            temp_path = temp_file.name
            
            # 写入音频数据
            temp_file.write(audio_data)
            temp_file.close()
            
            # 记录临时文件
            self.temp_files.append(temp_path)
            
            # 播放临时文件
            success = self.play_audio_file(temp_path)
            
            if success:
                print(f"🔊 播放音频数据 ({len(audio_data)} 字节)")
            
            return success
            
        except Exception as e:
            error_msg = f"播放音频数据失败: {e}"
            print(f"❌ {error_msg}")
            self._emit_error(error_msg)
            return False
            
    def play_audio_data_pyaudio(self, audio_data: bytes, 
                               sample_rate: int = 16000,
                               channels: int = 1,
                               sample_width: int = 2) -> bool:
        """使用PyAudio播放音频数据"""
        if not PYAUDIO_AVAILABLE:
            print("⚠️ PyAudio不可用，使用Qt播放器")
            return self.play_audio_data(audio_data)
            
        try:
            print(f"🔊 使用PyAudio播放音频数据 ({len(audio_data)} 字节)")
            
            # 停止当前播放
            self.stop()
            
            # 初始化PyAudio
            if not self.pyaudio_instance:
                self.pyaudio_instance = pyaudio.PyAudio()
                
            # 创建音频流
            self.pyaudio_stream = self.pyaudio_instance.open(
                format=pyaudio.paInt16,
                channels=channels,
                rate=sample_rate,
                output=True
            )
            
            # 标记播放开始
            self.is_playing = True
            self._emit_start()
            
            # 在后台线程播放
            def play_thread():
                try:
                    # 分块播放
                    chunk_size = 1024
                    for i in range(0, len(audio_data), chunk_size):
                        if not self.is_playing:
                            break
                        chunk = audio_data[i:i + chunk_size]
                        self.pyaudio_stream.write(chunk)
                        
                    # 播放完成
                    self.is_playing = False
                    self._emit_finish()
                    
                except Exception as e:
                    self.is_playing = False
                    self._emit_error(f"PyAudio播放错误: {e}")
                finally:
                    # 清理资源
                    if self.pyaudio_stream:
                        self.pyaudio_stream.stop_stream()
                        self.pyaudio_stream.close()
                        self.pyaudio_stream = None
                        
            thread = threading.Thread(target=play_thread)
            thread.daemon = True
            thread.start()
            
            return True
            
        except Exception as e:
            error_msg = f"PyAudio播放失败: {e}"
            print(f"❌ {error_msg}")
            self._emit_error(error_msg)
            return False
            
    def pause(self):
        """暂停播放"""
        if self.is_playing:
            self.media_player.pause()
            print("⏸️ 播放已暂停")
            
    def resume(self):
        """恢复播放"""
        if not self.is_playing:
            self.media_player.play()
            print("▶️ 播放已恢复")
            
    def stop(self):
        """停止播放"""
        self.media_player.stop()
        
        # 停止PyAudio播放
        if self.pyaudio_stream:
            try:
                self.pyaudio_stream.stop_stream()
                self.pyaudio_stream.close()
                self.pyaudio_stream = None
            except:
                pass
                
        self.is_playing = False
        print("⏹️ 播放已停止")
        
    def get_position(self) -> float:
        """获取播放位置（秒）"""
        return self.media_player.position() / 1000.0
        
    def get_duration(self) -> float:
        """获取音频时长（秒）"""
        return self.media_player.duration() / 1000.0
        
    def seek(self, position: float):
        """跳转到指定位置（秒）"""
        self.media_player.setPosition(int(position * 1000))
        
    def is_currently_playing(self) -> bool:
        """检查是否正在播放"""
        return (self.media_player.playbackState() == QMediaPlayer.PlaybackState.PlayingState or 
                self.is_playing)
                
    def get_status(self) -> Dict[str, Any]:
        """获取播放器状态"""
        return {
            "is_playing": self.is_currently_playing(),
            "current_file": self.current_file,
            "position": self.get_position(),
            "duration": self.get_duration(),
            "volume": self.volume,
            "playback_rate": self.playback_rate,
            "pyaudio_available": PYAUDIO_AVAILABLE
        }
        
    def _on_media_status_changed(self, status):
        """媒体状态变化处理"""
        if status == QMediaPlayer.MediaStatus.LoadedMedia:
            self.current_duration = self.get_duration()
        elif status == QMediaPlayer.MediaStatus.EndOfMedia:
            self.is_playing = False
            self._emit_finish()
            
    def _on_error_occurred(self, error, error_string):
        """错误处理"""
        self.is_playing = False
        error_msg = f"媒体播放错误: {error_string}"
        print(f"❌ {error_msg}")
        self._emit_error(error_msg)
        
    def _on_position_changed(self, position):
        """播放位置变化"""
        self.position_changed.emit(position / 1000.0)
        
    def _emit_start(self):
        """发送播放开始信号"""
        self.playback_started.emit()
        if self.on_playback_start:
            self.on_playback_start()
            
    def _emit_finish(self):
        """发送播放完成信号"""
        self.playback_finished.emit()
        if self.on_playback_finish:
            self.on_playback_finish()
            
    def _emit_error(self, error_msg: str):
        """发送错误信号"""
        self.playback_error.emit(error_msg)
        if self.on_playback_error:
            self.on_playback_error(error_msg)
            
    def cleanup(self):
        """清理资源"""
        # 停止播放
        self.stop()
        
        # 清理PyAudio
        if self.pyaudio_instance:
            self.pyaudio_instance.terminate()
            self.pyaudio_instance = None
            
        # 清理临时文件
        for temp_file in self.temp_files:
            try:
                os.unlink(temp_file)
            except:
                pass
        self.temp_files.clear()
        
        print("🧹 音频播放器资源已清理")
        
    def __del__(self):
        """析构函数"""
        self.cleanup()
