#!/usr/bin/env python3
"""
Live2D语音对话系统 - 按键触发语音输入

这个模块提供了按键触发的语音输入功能，包括：
- 全局按键监听
- 按键按下时开始录音
- 按键松开时停止录音并处理
- 可自定义触发键

使用示例：
    from dialogue_system.voice.key_triggered_input import KeyTriggeredInput
    from dialogue_system.voice.voice_processor import VoiceProcessor
    
    # 创建按键触发输入
    key_input = KeyTriggeredInput(voice_processor)
    
    # 开始监听
    key_input.start()
    
    # 停止监听
    key_input.stop()
"""

import threading
import time
from typing import Optional, Callable, Any
from PySide6.QtCore import QObject, Signal, QTimer
from PySide6.QtWidgets import QApplication
from .voice_processor import VoiceProcessor

try:
    import keyboard
    KEYBOARD_AVAILABLE = True
except ImportError:
    KEYBOARD_AVAILABLE = False
    print("⚠️ keyboard库未安装，按键触发功能将受限")


class KeyTriggeredInput(QObject):
    """按键触发语音输入管理器"""
    
    # 信号定义
    recording_started = Signal()
    recording_stopped = Signal()
    audio_processed = Signal(object)  # 传递音频数据
    status_changed = Signal(str)  # 状态变化
    
    def __init__(self, voice_processor: VoiceProcessor, config_manager=None):
        """初始化按键触发输入"""
        super().__init__()
        
        self.voice_processor = voice_processor
        self.config_manager = config_manager
        
        # 默认配置
        self.default_config = {
            "trigger_key": "space",
            "max_recording_duration": 30.0,
            "show_recording_indicator": True,
            "visual_feedback": True
        }
        
        # 加载配置
        self.key_config = self._load_key_config()
        
        # 状态控制
        self.is_active = False
        self.is_recording = False
        self.monitoring_thread = None
        
        # 回调函数
        self.on_audio_ready: Optional[Callable] = None
        self.on_status_change: Optional[Callable] = None
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_status)
        
        print("⌨️ 按键触发输入管理器初始化完成")
        
    def _load_key_config(self) -> dict:
        """加载按键配置"""
        if self.config_manager:
            voice_config = self.config_manager.config.get("voice_dialogue", {})
            key_config = voice_config.get("key_triggered_config", {})
            
            config = self.default_config.copy()
            config.update(key_config)
            return config
        else:
            return self.default_config.copy()
            
    def set_trigger_key(self, key: str):
        """设置触发键"""
        self.key_config["trigger_key"] = key
        print(f"⌨️ 触发键已设置为: {key}")
        
        # 如果正在运行，重启监听
        if self.is_active:
            self.stop()
            self.start()
            
    def set_callbacks(self, 
                     on_audio_ready: Optional[Callable] = None,
                     on_status_change: Optional[Callable] = None):
        """设置回调函数"""
        self.on_audio_ready = on_audio_ready
        self.on_status_change = on_status_change
        
    def start(self) -> bool:
        """开始按键监听"""
        if self.is_active:
            print("⚠️ 按键监听已在运行")
            return True
            
        if not KEYBOARD_AVAILABLE:
            print("❌ keyboard库不可用，无法启动按键监听")
            return False
            
        try:
            # 启动监听线程
            self.is_active = True
            self.monitoring_thread = threading.Thread(target=self._key_monitoring_loop)
            self.monitoring_thread.daemon = True
            self.monitoring_thread.start()
            
            # 启动状态更新定时器
            self.status_timer.start(100)  # 100ms更新一次
            
            self._emit_status("按住空格键开始录音")
            print(f"⌨️ 按键监听已启动 (触发键: {self.key_config['trigger_key']})")
            return True
            
        except Exception as e:
            print(f"❌ 启动按键监听失败: {e}")
            self.is_active = False
            return False
            
    def stop(self):
        """停止按键监听"""
        if not self.is_active:
            return
            
        self.is_active = False
        
        # 停止状态定时器
        self.status_timer.stop()
        
        # 如果正在录音，停止录音
        if self.is_recording:
            self._stop_recording()
            
        # 等待监听线程结束
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=1.0)
            
        self._emit_status("按键监听已停止")
        print("⌨️ 按键监听已停止")
        
    def _key_monitoring_loop(self):
        """按键监听循环"""
        trigger_key = self.key_config["trigger_key"]
        
        try:
            while self.is_active:
                # 检查按键状态
                if keyboard.is_pressed(trigger_key):
                    if not self.is_recording:
                        self._start_recording()
                else:
                    if self.is_recording:
                        self._stop_recording()
                        
                time.sleep(0.05)  # 50ms检查间隔
                
        except Exception as e:
            print(f"❌ 按键监听循环出错: {e}")
            self.is_active = False
            
    def _start_recording(self):
        """开始录音"""
        if self.is_recording:
            return
            
        print("🎙️ 按键按下，开始录音")
        self.is_recording = True
        
        # 开始录音
        if self.voice_processor.start_recording():
            self.recording_started.emit()
            self._emit_status("正在录音... (松开停止)")
        else:
            print("❌ 无法开始录音")
            self.is_recording = False
            
    def _stop_recording(self):
        """停止录音"""
        if not self.is_recording:
            return
            
        print("🎙️ 按键松开，停止录音")
        self.is_recording = False
        
        # 停止录音并获取数据
        audio_data = self.voice_processor.stop_recording()
        
        self.recording_stopped.emit()
        self._emit_status("正在识别语音...")
        
        # 处理音频数据
        if audio_data is not None:
            self._process_audio_data(audio_data)
        else:
            self._emit_status("按住空格键开始录音")
            
    def _process_audio_data(self, audio_data):
        """处理音频数据"""
        # 验证音频数据
        if not self.voice_processor.is_valid_speech(audio_data):
            self._emit_status("录音无效，请重试")
            QTimer.singleShot(2000, lambda: self._emit_status("按住空格键开始录音"))
            return
            
        # 获取音频信息
        audio_info = self.voice_processor.get_audio_info(audio_data)
        print(f"📊 音频信息: 时长 {audio_info['duration']:.2f}秒, "
              f"音量 {audio_info['normalized_rms']:.3f}")
              
        # 发送音频数据信号
        self.audio_processed.emit(audio_data)
        
        # 触发回调
        if self.on_audio_ready:
            self.on_audio_ready(audio_data)
            
        # 重置状态
        QTimer.singleShot(1000, lambda: self._emit_status("按住空格键开始录音"))
        
    def _emit_status(self, status: str):
        """发送状态信号"""
        self.status_changed.emit(status)
        
        if self.on_status_change:
            self.on_status_change(status)
            
    def _update_status(self):
        """更新状态显示"""
        if not self.is_active:
            return
            
        if self.is_recording:
            # 显示录音时长
            if hasattr(self.voice_processor, 'speech_start_time'):
                duration = time.time() - self.voice_processor.speech_start_time
                self._emit_status(f"正在录音... {duration:.1f}秒 (松开停止)")
                
                # 检查最大录音时长
                if duration > self.key_config['max_recording_duration']:
                    print(f"⏰ 录音时长超过限制，自动停止")
                    self._stop_recording()
                    
    def get_current_status(self) -> str:
        """获取当前状态"""
        if not self.is_active:
            return "按键监听未启动"
        elif self.is_recording:
            return "正在录音..."
        else:
            return "按住空格键开始录音"
            
    def is_key_triggered_active(self) -> bool:
        """检查按键触发是否激活"""
        return self.is_active
        
    def is_currently_recording(self) -> bool:
        """检查是否正在录音"""
        return self.is_recording


# 备用实现：如果keyboard库不可用，使用Qt的键盘事件
class QtKeyTriggeredInput(QObject):
    """基于Qt的按键触发输入（备用方案）"""
    
    recording_started = Signal()
    recording_stopped = Signal()
    audio_processed = Signal(object)
    status_changed = Signal(str)
    
    def __init__(self, voice_processor: VoiceProcessor, config_manager=None):
        super().__init__()
        self.voice_processor = voice_processor
        self.config_manager = config_manager
        self.is_active = False
        self.is_recording = False
        
        print("⌨️ Qt按键触发输入管理器初始化完成（备用方案）")
        
    def start(self) -> bool:
        """启动Qt按键监听"""
        print("⚠️ 使用Qt按键监听（功能受限）")
        print("💡 建议安装keyboard库以获得完整功能: pip install keyboard")
        self.is_active = True
        return True
        
    def stop(self):
        """停止Qt按键监听"""
        self.is_active = False
        
    def handle_key_press(self, key):
        """处理按键按下事件（需要在主窗口中调用）"""
        if key == "space" and not self.is_recording:
            self._start_recording()
            
    def handle_key_release(self, key):
        """处理按键松开事件（需要在主窗口中调用）"""
        if key == "space" and self.is_recording:
            self._stop_recording()
            
    def _start_recording(self):
        """开始录音"""
        self.is_recording = True
        self.voice_processor.start_recording()
        self.recording_started.emit()
        
    def _stop_recording(self):
        """停止录音"""
        self.is_recording = False
        audio_data = self.voice_processor.stop_recording()
        self.recording_stopped.emit()
        if audio_data is not None:
            self.audio_processed.emit(audio_data)
