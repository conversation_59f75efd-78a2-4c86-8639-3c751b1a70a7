{"Version": 3, "Meta": {"Duration": 10.3, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 251, "TotalSegmentCount": 20580, "TotalPointCount": 23920, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamBGHide", "Segments": [0, 0, 0, 1.033, 0.5, 2, 7.067, 0.5, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN3", "Segments": [0, 0, 2, 0.7, 0, 1, 0.856, 0, 1.011, 0.008, 1.167, 0.028, 1, 1.578, 0.08, 1.989, 0.122, 2.4, 0.188, 1, 2.533, 0.21, 2.667, 0.3, 2.8, 0.3, 0, 3.2, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBlackY", "Segments": [0, 0, 2, 0.3, 0, 0, 0.633, 30, 2, 1.9, 30, 1, 2.211, 30, 2.522, 28.543, 2.833, 21, 1, 2.955, 18.036, 3.078, 0, 3.2, 0, 2, 3.5, 0, 0, 3.833, 30, 2, 5.1, 30, 0, 6.033, 21, 0, 8.733, 30, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBlackCollar", "Segments": [0, 0, 0, 2.833, 0.1, 0, 3.133, 0, 2, 3.2, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamWhiteIN", "Segments": [0, 0, 2, 2.867, 0, 0, 3.033, 0.485, 0, 3.2, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionY", "Segments": [0, 0, 0, 1.933, -0.207, 2, 2.667, -0.207, 0, 3.1, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamAllSize", "Segments": [0, 0, 0, 1.933, 0.188, 2, 2.567, 0.188, 0, 3.1, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 0, 0.2, 0, 2, 1.133, 0, 2, 1.3, 0, 2, 3.067, 0, 0, 3.333, 1, 2, 3.733, 1, 0, 3.967, 0, 0, 4.233, 0.886, 0, 4.433, 0.6, 2, 5, 0.6, 0, 5.467, 1, 0, 5.567, 0, 0, 5.733, 1, 2, 6.2, 1, 0, 6.333, 0, 2, 6.633, 0, 0, 6.833, 1, 2, 10.3, 1]}, {"Target": "Parameter", "Id": "ParamEyeSmileL", "Segments": [0, 0, 2, 0.2, 0, 2, 0.633, 0, 0, 0.833, 1, 2, 2.367, 1, 0, 2.6, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 0.2, 0, 2, 0.867, 0, 0, 1.167, 0.5, 2, 1.233, 0.5, 0, 1.467, 0.4, 2, 1.7, 0.4, 2, 1.9, 0.4, 0, 2.533, 0, 2, 3.067, 0, 0, 3.333, 1, 2, 3.733, 1, 0, 3.967, 0, 0, 4.233, 0.886, 0, 4.433, 0.6, 2, 5, 0.6, 0, 5.467, 1, 0, 5.567, 0, 0, 5.733, 1, 2, 6.2, 1, 0, 6.333, 0, 2, 6.633, 0, 0, 6.833, 1, 2, 10.3, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 0, 0.567, 0.515, 0, 1.4, -1, 0, 1.633, 0.1, 0, 2.1, -0.498, 0, 2.533, 0.6, 0, 2.833, -0.2, 0, 3, 0.076, 0, 3.433, -1, 1, 3.466, -1, 3.5, -0.843, 3.533, -0.711, 1, 3.578, -0.535, 3.622, -0.498, 3.667, -0.498, 0, 3.833, -1, 2, 4.6, -1, 2, 5.667, -1, 2, 6.467, -1, 0, 6.9, -0.498, 0, 7.133, -1, 2, 7.367, -1, 2, 7.7, -1, 0, 7.833, -0.5, 0, 8.067, -1, 2, 8.333, -1, 0, 8.467, -0.5, 2, 8.633, -0.5, 0, 8.967, -0.498, 0, 9.333, -1, 2, 9.767, -1, 0, 10.267, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.567, 0, 0, 1.067, 1, 0, 1.2, 0.8, 0, 1.333, 1, 0, 1.567, 0.839, 0, 1.833, 1, 0, 2.067, 0.8, 1, 2.145, 0.8, 2.222, 0.829, 2.3, 0.9, 1, 2.367, 0.961, 2.433, 1, 2.5, 1, 0, 2.567, 0.963, 0, 2.667, 1, 0, 3, 0, 0, 3.333, 1, 2, 3.6, 1, 2, 3.7, 1, 2, 3.833, 1, 2, 3.867, 1, 0, 4.267, 0.6, 2, 5.1, 0.6, 0, 5.5, 1, 0, 5.633, 0.531, 0, 5.833, 1, 0, 6, 0.468, 2, 6.133, 0.468, 0, 6.267, 1, 0, 6.433, 0.6, 0, 6.6, 1, 0, 6.733, 0.5, 0, 6.9, 1, 0, 7.167, 0.656, 0, 7.367, 0.872, 2, 7.7, 0.872, 0, 7.933, 0.5, 1, 7.989, 0.5, 8.044, 0.602, 8.1, 0.752, 1, 8.167, 0.932, 8.233, 1, 8.3, 1, 0, 8.467, 0.5, 1, 8.522, 0.5, 8.578, 0.661, 8.633, 0.8, 1, 8.7, 0.967, 8.766, 1, 8.833, 1, 0, 9.533, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamMouthSize", "Segments": [0, 0, 2, 2.933, 0, 0, 3.333, 0.8, 1, 3.455, 0.8, 3.578, 0.647, 3.7, 0, 1, 3.789, -0.47, 3.878, -1, 3.967, -1, 2, 4.1, -1, 0, 4.467, 0, 2, 5, 0, 2, 8.033, 0, 0, 8.333, -0.5, 0, 8.6, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamEyeEmotion", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 1, 0, 1.133, -1, 2, 2.667, -1, 0, 2.9, 0, 0, 3.3, -1, 0, 3.667, 1, 2, 3.9, 1, 1, 3.922, 1, 3.945, 0.106, 3.967, 0, 1, 4.122, -0.744, 4.278, -1, 4.433, -1, 2, 4.767, -1, 0, 5, 0, 2, 6.867, 0, 0, 7.5, -1, 1, 8, -1, 8.5, -1, 9, -0.999, 0, 9.533, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBlackFace", "Segments": [0, 0, 2, 5, 0, 2, 6.367, 0, 0, 7.133, 1, 2, 9.267, 1, 0, 9.567, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamShameLine", "Segments": [0, 0, 2, 5, 0, 2, 6.567, 0, 1, 6.634, 0, 6.7, 0.194, 6.767, 0.5, 2, 7, 1, 2, 7.233, 0.5, 2, 7.5, 1, 2, 7.733, 0.5, 2, 8, 1, 2, 8.233, 0.5, 2, 8.567, 0.5, 0, 8.7, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLX", "Segments": [0, 0, 2, 6.733, 0, 0, 7.2, 1, 2, 8.4, 1, 0, 8.767, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeL", "Segments": [0, 0, 2, 6.733, 0, 0, 7.067, -0.5, 2, 8.267, -0.5, 0, 8.767, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRX", "Segments": [0, 0, 2, 2.833, 0, 2, 6.733, 0, 0, 7.2, 0.5, 2, 8.4, 0.5, 0, 8.767, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeR", "Segments": [0, 0, 2, 2.833, 0, 2, 6.733, 0, 0, 7.067, -0.6, 2, 8.267, -0.6, 0, 8.767, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamEyeRLightOpen", "Segments": [0, 0, 2, 0.6, 0, 1, 0.722, 0, 0.845, 0.288, 0.967, 0.296, 1, 1.056, 0.302, 1.144, 0.3, 1.233, 0.3, 0, 1.4, 0, 2, 2.033, 0, 2, 2.533, 0, 0, 3.033, 1, 2, 3.5, 1, 0, 4.167, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLightLine1", "Segments": [0, 0, 2, 0.967, 0, 0, 1.333, -2, 2, 1.867, -2, 2, 2.833, -2, 2, 3.067, -2, 0, 3.867, 10, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLightLine2", "Segments": [0, 0, 2, 0.967, 0, 0, 1.333, -2, 0, 1.567, 3.506, 0, 1.733, 1.895, 0, 1.8, 3.213, 0, 1.9, 1.089, 0, 1.933, 3.213, 0, 2, 0.613, 0, 2.033, 1.14, 0, 2.067, -2, 2, 2.5, -2, 0, 2.567, 7.012, 0, 2.7, 3.665, 0, 2.733, 8.765, 1, 2.766, 8.765, 2.8, 7.876, 2.833, 4, 1, 2.855, 1.416, 2.878, -2, 2.9, -2, 0, 3.433, 2, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLightLine3", "Segments": [0, 0, 2, 1.167, 0, 0, 1.8, 0.533, 0, 2.033, -2, 2, 2.067, -2, 2, 2.267, -2, 2, 2.433, -2, 2, 2.567, -2, 0, 2.767, 1.378, 0, 2.833, -0.311, 0, 3.1, 1.569, 0, 3.433, -2, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLightShine", "Segments": [0, 0, 2, 0.9, 0, 0, 1.133, 0.9, 0, 1.533, 0, 2, 2.733, 0, 1, 2.766, 0, 2.8, 0, 2.833, 0.127, 1, 3.255, 4.853, 3.678, 10, 4.1, 10, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo1", "Segments": [0, 0, 2, 0.4, 0, 0, 0.833, -1, 0, 1.2, 1, 0, 1.6, -1, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0.081, 2.833, -0.252, 1, 3.066, -1.651, 3.3, -3, 3.533, -3, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo2", "Segments": [0, 0, 2, 1.367, 0, 0, 1.533, 0.525, 1, 1.566, 0.525, 1.6, 0.31, 1.633, 0, 1, 1.955, -3.001, 2.278, -4.45, 2.6, -4.45, 2, 2.833, -4.45, 0, 2.9, -30, 0, 3.9, 30, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo3", "Segments": [0, 0, 2, 1.167, 0, 0, 1.333, 0.525, 0, 1.4, 0.026, 0, 1.6, 0.591, 0, 1.767, 0, 0, 2, 0.576, 0, 2.367, 0, 0, 2.567, 1, 1, 2.656, 1, 2.744, 0.875, 2.833, 0.544, 1, 2.922, 0.213, 3.011, 0, 3.1, 0, 0, 3.233, 0.525, 0, 3.3, 0.026, 0, 3.533, 0.591, 0, 3.733, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1", "Segments": [0, 0, 0, 0.933, -30, 2, 2.167, -30, 1, 2.389, -30, 2.611, -25.545, 2.833, -16.656, 1, 3.111, -5.545, 3.389, 0, 3.667, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1Y", "Segments": [0, 0, 2, 0.467, 0, 2, 2.733, 0, 1, 2.766, 0, 2.8, -0.009, 2.833, -5.428, 1, 2.911, -18.071, 2.989, -30, 3.067, -30, 2, 3.433, -30, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle2", "Segments": [0, 0, 0, 0.5, 6.971, 0, 0.6, 1.671, 0, 0.667, 9.915, 0, 0.8, 1.671, 0, 0.933, 9.915, 0, 1.033, 1.671, 0, 1.167, 9.915, 0, 1.367, 1.615, 0, 1.433, 9.915, 0, 1.567, 1.615, 0, 1.667, 12.223, 0, 1.833, 1.671, 0, 1.967, 12.223, 0, 2.1, 1.67, 0, 2.267, 12.223, 0, 2.367, 1.67, 0, 2.5, 12.223, 0, 2.6, 1.67, 0, 2.7, 19.223, 0, 2.833, 9.711, 0, 2.967, 19.223, 0, 3.133, 8.616, 0, 3.267, 19.223, 0, 3.467, 8.67, 0, 3.733, 30, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPHYInputX", "Segments": [0, 0, 0, 0.233, -6.71, 0, 0.6, 28, 1, 0.756, 28, 0.911, -9.5, 1.067, -13.141, 1, 1.289, -18.342, 1.511, -19.567, 1.733, -23.652, 1, 1.8, -24.877, 1.866, -28.383, 1.933, -28.383, 0, 2.233, 22, 0, 2.567, -30, 0, 2.8, 13, 0, 2.9, -30, 0, 3.3, 30, 0, 3.767, -10, 0, 4.1, 6.295, 0, 4.7, -6.71, 0, 5.367, 0, 0, 5.767, -6.295, 0, 6.033, 12.47, 1, 6.111, 12.47, 6.189, 10.16, 6.267, -1.73, 1, 6.356, -15.318, 6.444, -28.383, 6.533, -28.383, 0, 7.033, 27, 1, 7.122, 27, 7.211, 23.356, 7.3, 15, 1, 7.433, 2.466, 7.567, -4.383, 7.7, -4.383, 0, 8.033, 11, 0, 8.3, -22, 0, 8.633, 28, 0, 9.333, -13.141, 0, 9.6, 6.295, 0, 10.233, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.1, 0, 0, 0.367, 3, 0, 0.667, 0, 2, 5.067, 0, 2, 6.767, 0, 0, 7.167, 30, 2, 9.033, 30, 0, 9.733, 0, 2, 10.133, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 0, 0.233, -5, 0, 0.6, 15, 1, 0.756, 15, 0.911, -4.534, 1.067, -7.945, 1, 1.322, -13.548, 1.578, -15.447, 1.833, -20, 1, 1.9, -21.188, 1.966, -24, 2.033, -24, 2, 2.333, -24, 0, 2.667, -22, 0, 2.867, -29.363, 0, 3.3, 30, 0, 3.767, -0.478, 0, 4.1, 3.94, 0, 4.7, -17, 0, 5.367, 0, 0, 5.767, -6.295, 0, 6.033, 0.837, 1, 6.111, 0.837, 6.189, 1.156, 6.267, -1.73, 1, 6.356, -5.028, 6.444, -23, 6.533, -23, 0, 7.033, 20.478, 1, 7.122, 20.478, 7.211, 16.814, 7.3, 15, 1, 7.422, 12.506, 7.545, 12.191, 7.667, 12.191, 0, 8.033, 16.574, 0, 8.3, 10.757, 0, 8.6, 24.861, 0, 8.933, 0, 0, 9.167, 4, 0, 9.333, 0.837, 0, 9.6, 6.295, 0, 10.233, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 0, 0.567, 2, 0, 1.067, -8, 2, 2.1, -8, 0, 2.667, 2, 2, 2.8, 2, 0, 2.933, -14, 0, 3.567, 0.57, 1, 3.845, 0.57, 4.122, 0.557, 4.4, 0, 1, 4.578, -0.357, 4.755, -2, 4.933, -2, 0, 5.3, 2, 0, 5.667, -7.251, 0, 5.9, -3, 2, 6.767, -3, 0, 7.3, 13, 0, 7.633, 9, 0, 7.967, 13.501, 0, 8.033, 13.44, 0, 8.333, 22, 0, 8.533, 18, 2, 8.833, 18, 0, 9.333, -2.212, 0, 9.7, 2, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperLAngle", "Segments": [0, 0, 0, 0.267, -0.053, 0, 0.567, 0.984, 0, 0.967, -6, 0, 1.167, -5, 0, 1.333, -5.266, 2, 2, -5.266, 0, 2.8, 1, 0, 3.033, -1.456, 0, 3.4, 4, 0, 3.633, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamArmHandLAngle", "Segments": [0, 0, 2, 0.267, 0, 0, 0.967, 6.6, 0, 1.533, 4.8, 2, 1.8, 4.8, 0, 3.367, 6.661, 0, 3.867, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerLAngle", "Segments": [0, 10, 2, 0.267, 10, 0, 0.467, 11, 0, 0.967, -10, 0, 1.5, -9, 2, 2.1, -9, 2, 2.433, -9, 2, 3.067, -9, 0, 3.667, 10, 2, 3.9, 10, 2, 10.3, 10]}, {"Target": "Parameter", "Id": "ParamArmLowerLH", "Segments": [0, 0, 2, 0.267, 0, 2, 0.967, 0, 2, 1.933, 0, 2, 2.267, 0, 2, 3.167, 0, 0, 3.467, -30, 0, 3.633, 0, 2, 4.933, 0, 2, 5.467, 0, 2, 5.5, 0, 2, 6.3, 0, 2, 8.667, 0, 2, 9.6, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamArm<PERSON>pperRAngle", "Segments": [0, 0, 0, 0.433, 3, 0, 0.8, 0, 0, 1.1, 4, 2, 2.033, 4, 2, 2.833, 4, 0, 3.2, 5, 0, 5.033, 2.582, 0, 5.4, 4, 2, 6.833, 4, 0, 7.2, 2, 0, 7.733, 2.582, 0, 8.033, -1, 0, 8.267, 0, 2, 9.167, 0, 2, 9.6, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRAngle", "Segments": [0, 7.4, 2, 0.167, 7.4, 0, 0.367, 8.324, 0, 0.7, -1, 1, 0.811, -1, 0.922, 0.728, 1.033, 1, 1, 1.111, 1.19, 1.189, 1.134, 1.267, 1.134, 2, 1.967, 1.134, 1, 2.234, 1.134, 2.5, 1.144, 2.767, 1, 1, 2.934, 0.91, 3.1, -3.686, 3.267, -3.686, 0, 3.7, -2, 2, 4.933, -2, 0, 5.167, -0.535, 1, 5.311, -0.535, 5.456, -13.372, 5.6, -22, 1, 5.622, -23.327, 5.645, -23.128, 5.667, -23.34, 1, 5.722, -23.869, 5.778, -24, 5.833, -24, 2, 6.4, -24, 1, 6.522, -24, 6.645, -6.005, 6.767, -0.765, 1, 6.8, 0.664, 6.834, 0, 6.867, 0, 0, 7.233, -5.895, 0, 7.5, -4.755, 0, 7.833, -6.713, 0, 8.133, 15.905, 0, 8.4, 14.9, 1, 8.511, 14.9, 8.622, 14.9, 8.733, 14.905, 1, 8.778, 14.907, 8.822, 16.07, 8.867, 16.07, 0, 9.267, 7.039, 0, 9.567, 7.4, 2, 10.3, 7.4]}, {"Target": "Parameter", "Id": "ParamArmHandRAngle", "Segments": [0, 0, 0, 0.167, -0.204, 0, 0.5, 1, 0, 0.7, -8.571, 1, 0.778, -8.571, 0.855, -0.375, 0.933, 0, 1, 1.266, 1.607, 1.6, 2, 1.933, 2, 2, 2.367, 2, 0, 2.567, 4, 1, 2.622, 4, 2.678, 3.868, 2.733, 2, 1, 2.944, -5.098, 3.156, -10, 3.367, -10, 0, 3.633, -7.584, 0, 4, -8.571, 2, 4.933, -8.571, 1, 5.022, -8.571, 5.111, -4.578, 5.2, 0, 1, 5.344, 7.44, 5.489, 10, 5.633, 10, 0, 5.767, 8.308, 0, 5.933, 10, 2, 6.4, 10, 0, 6.6, -10, 0, 6.967, 2, 0, 7.3, -10, 0, 7.567, 10, 0, 7.967, -10, 0, 8.3, -1, 0, 8.5, -2.117, 0, 8.667, -1.273, 0, 9, -2.117, 1, 9.111, -2.117, 9.222, -0.728, 9.333, -0.292, 1, 9.422, 0.057, 9.511, 0, 9.6, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRH", "Segments": [0, 0, 1, 0.056, 0, 0.111, -0.035, 0.167, -0.389, 1, 0.2, -0.601, 0.234, -1.23, 0.267, -1.23, 0, 0.667, 16, 0, 1.933, 11, 0, 3.367, 16, 0, 3.933, 0, 2, 6.833, 0, 0, 7, 4.887, 0, 7.267, 0, 2, 7.5, 0, 0, 7.767, -10, 0, 8, 0, 0, 8.267, -9, 0, 8.533, 0, 2, 9.6, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperRH", "Segments": [0, 0, 2, 0.167, 0, 2, 0.4, 0, 0, 0.6, -9.566, 0, 1.067, -6, 2, 4.8, -6, 0, 5.533, 0, 2, 6.3, 0, 2, 9.6, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamHandR", "Segments": [0, 0, 2, 0.167, 0, 2, 4.933, 0, 2, 6.967, 0, 2, 7, 0.4, 2, 7.9, 0.4, 2, 7.933, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRAngle", "Segments": [0, 0, 2, 0.167, 0, 2, 0.8, 0, 0, 1.367, 4, 1, 1.567, 4, 1.767, 3.87, 1.967, 2.855, 1, 2.056, 2.404, 2.144, 0, 2.233, 0, 2, 2.533, 0, 0, 2.633, -1, 1, 2.689, -1, 2.744, -1.313, 2.8, 0, 1, 2.9, 2.364, 3, 10, 3.1, 10, 2, 4.933, 10, 0, 5.367, 0, 2, 6.3, 0, 2, 9.6, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamHand_Cl", "Segments": [0, 0, 2, 0.167, 0, 2, 5.333, 0, 2, 5.367, 30, 2, 6.533, 30, 2, 6.567, 0, 2, 8.533, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamHandT1R", "Segments": [0, 0, 2, 0.167, 0, 2, 0.4, 0, 2, 4.933, 0, 2, 5.4, 0, 0, 5.8, 1, 2, 8.8, 1, 0, 8.833, 0, 2, 9.6, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamHandT2R", "Segments": [0, 0, 2, 0.167, 0, 2, 0.667, 0, 0, 0.9, 1, 2, 2.033, 1, 2, 2.367, 1, 0, 2.6, 0.928, 0, 2.833, 1, 2, 3.267, 1, 0, 3.633, 0, 2, 4.933, 0, 2, 6.3, 0, 2, 9.6, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 0.2, 3, 0, 0.733, -10, 0, 1.033, -8.569, 0, 1.5, -10, 2, 2.233, -10, 0, 2.8, -2.337, 0, 3.033, -4.42, 0, 3.733, 1, 0, 4.1, 0, 1, 4.344, 0, 4.589, 0.002, 4.833, 0.017, 1, 4.922, 0.023, 5.011, 2, 5.1, 2, 0, 5.633, -9.19, 2, 6.7, -9.19, 0, 7.333, -1.089, 0, 7.833, -2.628, 0, 8.233, 4, 1, 8.289, 4, 8.344, 3.053, 8.4, 2.99, 1, 8.733, 2.61, 9.067, 2.446, 9.4, 2, 1, 9.533, 1.822, 9.667, -0.26, 9.8, -0.26, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 0, 0.167, -1.819, 0, 0.533, 10, 2, 0.6, 10, 0, 1, -8, 0, 1.4, -5.526, 0, 1.633, -6.98, 0, 1.833, -6.045, 0, 2.067, -6.605, 0, 2.3, 0, 0, 2.6, -8.727, 0, 3.1, 10, 0, 3.533, -0.398, 0, 3.7, 1.016, 0, 4, -0.896, 0, 4.5, 0.311, 0, 4.933, -0.758, 0, 5.4, 4.17, 0, 5.833, -1.461, 0, 6.167, 0, 0, 6.433, -6.98, 0, 6.667, 2, 0, 6.867, 0, 0, 7.267, 10, 0, 7.567, 7.703, 0, 7.9, 10, 0, 8.2, -1.819, 0, 8.467, 3, 2, 9.4, 3, 0, 9.833, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 0.367, -0.729, 0, 0.833, 7.481, 0, 1.167, 6.803, 0, 1.533, 7.092, 2, 2.067, 7.092, 0, 2.367, 7, 0, 3.267, 10, 1, 3.4, 10, 3.534, 9.069, 3.667, 9.062, 1, 4.089, 9.039, 4.511, 9.027, 4.933, 9.01, 1, 5.011, 9.007, 5.089, 9.01, 5.167, 9, 1, 5.334, 8.978, 5.5, -1.23, 5.667, -1.23, 0, 6, 0, 2, 6.367, 0, 0, 6.767, 7.481, 0, 7.033, 5, 0, 7.333, 7.481, 0, 7.633, 6.017, 0, 8.033, 7, 1, 8.333, 7, 8.633, 6.962, 8.933, 6, 1, 9.089, 5.501, 9.244, 0, 9.4, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX2", "Segments": [0, 0, 2, 0.533, 0, 1, 0.722, 0, 0.911, -2.955, 1.1, -3, 1, 2.378, -3.304, 3.655, -3.676, 4.933, -3.796, 1, 5.089, -3.811, 5.244, -3.8, 5.4, -3.8, 1, 5.767, -3.8, 6.133, -3.736, 6.5, -3.555, 1, 6.567, -3.522, 6.633, -3.436, 6.7, -3.436, 0, 7, -10, 0, 8.2, -2.912, 0, 9, -3.246, 0, 9.533, 1, 0, 9.833, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY2", "Segments": [0, 0, 2, 0.533, 0, 2, 2.2, 0, 0, 2.767, -10, 0, 3.333, 10, 0, 3.7, -1.116, 0, 3.867, 1.594, 0, 4.467, -0.51, 0, 4.833, 0.351, 1, 4.866, 0.351, 4.9, 0.359, 4.933, 0.325, 1, 5.111, 0.146, 5.289, 0, 5.467, 0, 2, 6.5, 0, 2, 6.7, 0, 2, 8, 0, 0, 8.3, -6, 0, 8.533, 2.944, 0, 8.933, 0, 0, 9.467, 2, 0, 9.833, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechX", "Segments": [0, 0, 0, 0.5, -1.09, 0, 1.133, 10, 2, 1.967, 10, 0, 2.267, 7.41, 0, 2.6, 10, 0, 3.033, -1.379, 0, 3.5, 10, 0, 3.867, -1.09, 0, 4.267, 0.993, 0, 4.733, -1.039, 1, 4.8, -1.039, 4.866, -0.838, 4.933, -0.416, 1, 5.033, 0.216, 5.133, 0.542, 5.233, 0.542, 0, 5.833, 0, 2, 6.7, 0, 0, 7.233, -2.239, 0, 7.833, 2, 0, 8.667, 0, 2, 8.767, 0, 0, 9.4, -1.09, 0, 9.7, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, 0, 0, 0.533, 1, 0, 1.067, -0.246, 0, 1.567, 0, 2, 1.933, 0, 0, 2.467, 1.519, 0, 3.033, -3, 0, 3.433, 5, 0, 3.767, 2.886, 0, 4.2, 3.866, 0, 4.767, 2.886, 2, 5.167, 2.886, 0, 5.533, 0, 2, 6.7, 0, 2, 9.833, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamShoulderR", "Segments": [0, 0, 2, 0.533, 0, 0, 1.033, 19, 2, 4.933, 19, 2, 5.467, 19, 2, 6.5, 19, 2, 6.7, 19, 2, 8.2, 19, 0, 9.167, 21, 0, 9.6, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamShoulderL", "Segments": [0, 0, 2, 0.533, 0, 0, 1.033, 14, 2, 4.933, 14, 2, 5.467, 14, 2, 6.5, 14, 2, 6.7, 14, 2, 8.2, 14, 0, 9.167, 14.729, 0, 9.6, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuREyeOpen", "Segments": [0, 0, 2, 0.333, 0, 2, 2, 0, 0, 2.1, 1, 2, 5.633, 1, 0, 5.8, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyX", "Segments": [0, 0, 0, 0.4, 15.695, 0, 0.9, -30, 2, 2.867, -30, 1, 3.378, -30, 3.889, -5.596, 4.4, -1.812, 1, 4.744, 0.738, 5.089, 0, 5.433, 0, 0, 6.167, -5.239, 0, 8.3, 3.347, 0, 9.967, -1.812, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0.843, 0.167, -0.706, 1, 0.422, -7.833, 0.678, -30, 0.933, -30, 2, 2.2, -30, 0, 2.433, 30, 0, 3.233, -30, 1, 3.8, -30, 4.366, -13.791, 4.933, -2.34, 1, 5.1, 1.028, 5.266, 0, 5.433, 0, 1, 5.578, 0, 5.722, 0.176, 5.867, -0.706, 1, 6.111, -2.198, 6.356, -5.43, 6.6, -5.43, 0, 8.767, 2.312, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyZ", "Segments": [0, 0, 2, 0.267, 0, 0, 0.967, 30, 2, 1.567, 30, 2, 2.733, 30, 0, 3.367, -1.682, 0, 3.967, 6.107, 0, 4.633, -3.28, 0, 5.433, 6, 0, 6.9, -2.263, 0, 7.433, -1.28, 0, 7.967, -2.031, 0, 8.467, 5.861, 0, 9.367, -1.28, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRPositionZ", "Segments": [0, 0, 0, 0.667, -2.311, 1, 1.211, -2.311, 1.756, -1.355, 2.3, -1.238, 1, 2.6, -1.173, 2.9, -1.201, 3.2, -1.161, 1, 3.367, -1.139, 3.533, 5.929, 3.7, 5.929, 0, 4.267, -2.854, 0, 5.2, 7, 0, 6.433, -1.479, 0, 8.3, 0.929, 0, 9.5, -0.854, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRMouth", "Segments": [0, 0, 0, 0.5, 10.12, 0, 1.267, -7.41, 0, 1.967, 2.681, 0, 2.7, -4.354, 1, 2.867, -4.354, 3.033, -4.483, 3.2, -4.096, 1, 3.433, -3.554, 3.667, 4.763, 3.9, 4.763, 0, 4.967, -2.684, 1, 5.122, -2.684, 5.278, -2.778, 5.433, 0, 1, 5.622, 3.374, 5.811, 10.12, 6, 10.12, 0, 6.733, -7.41, 0, 7.4, 2.681, 0, 8.167, -4.354, 1, 8.322, -4.354, 8.478, -4.522, 8.633, -4.096, 1, 8.9, -3.366, 9.166, 4.763, 9.433, 4.763, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuL", "Segments": [0, 0, 2, 1.333, 0, 2, 1.567, 0, 0, 3.6, 20, 2, 5.367, 20, 0, 9.033, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamMjLFlip", "Segments": [0, 0, 2, 0.367, 0, 0, 1, 1, 2, 3.3, 1, 0, 4.033, 0, 2, 9.033, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPositionZManjuuL", "Segments": [0, 0, 1, 0.211, 0, 0.422, -9.106, 0.633, -15, 1, 0.689, -16.551, 0.744, -16, 0.8, -16, 0, 1.233, -8, 1, 1.289, -8, 1.344, -20.652, 1.4, -21, 1, 1.511, -21.697, 1.622, -21.728, 1.733, -21.728, 0, 2.033, 2, 0, 2.267, 0, 2, 8.2, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLEyeOpen", "Segments": [0, 0, 0, 1.333, 1, 2, 2.267, 1, 2, 2.633, 1, 2, 3.2, 1, 0, 8.2, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyX", "Segments": [0, 0, 0, 0.267, 1.434, 0, 0.633, -30, 0, 1.2, 30, 1, 1.244, 30, 1.289, 10.133, 1.333, 0, 1, 1.411, -17.733, 1.489, -21, 1.567, -21, 0, 1.933, 10.631, 0, 2.267, -17, 0, 2.633, 0, 2, 8.2, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyY", "Segments": [0, 0, 0, 0.433, 2.709, 0, 1.4, -22.948, 0, 2.067, 13, 0, 2.367, -12, 0, 2.933, 0, 2, 5.467, 0, 0, 5.567, 21.076, 1, 5.589, 21.076, 5.611, -21.076, 5.633, -21.076, 0, 5.733, 21.076, 0, 5.833, -21.076, 0, 5.933, 21.076, 0, 6.033, -21.076, 0, 6.1, 21.076, 0, 6.167, -21.076, 0, 6.267, 21.076, 0, 6.367, -21.076, 1, 6.389, -21.076, 6.411, 21.076, 6.433, 21.076, 0, 6.533, -21.076, 0, 6.633, 21.076, 0, 6.733, -21.076, 0, 6.833, 21.076, 0, 6.933, -21.076, 0, 7.033, 21.076, 0, 7.1, -21.076, 0, 7.167, 21.076, 0, 7.267, -21.076, 0, 7.367, 21.076, 0, 7.467, -21.076, 0, 7.567, 21.076, 0, 7.7, -21.076, 0, 7.8, 21.076, 0, 7.967, -21.076, 1, 7.978, -21.076, 7.989, 21.076, 8, 21.076, 0, 8.1, -21.076, 0, 8.2, 21.076, 0, 8.3, -21.076, 0, 8.4, 21.076, 0, 8.5, -21.076, 0, 8.6, 21.076, 0, 8.7, -21.076, 0, 8.8, 21.076, 0, 8.867, -21.076, 1, 8.889, -21.076, 8.911, 21.076, 8.933, 21.076, 0, 9.033, -21.076, 0, 9.133, 21.076, 0, 9.233, -21.076, 0, 9.333, 21.076, 0, 9.633, -3.527, 0, 9.967, 3, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyZ", "Segments": [0, 0, 0, 0.3, -4, 0, 0.867, 17, 0, 1.4, -7.258, 1, 1.5, -7.258, 1.6, 9.953, 1.7, 10, 1, 2.622, 10.435, 3.545, 10.589, 4.467, 10.589, 0, 5.133, 0, 0, 5.767, 30, 2, 8.467, 30, 0, 8.933, 0, 0, 9.233, 30, 0, 9.567, -7.258, 0, 9.867, 6.105, 0, 10.233, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyW", "Segments": [0, 0, 2, 0.2, 0, 0, 0.6, 12.12, 0, 0.8, -30, 0, 1.233, 19, 0, 1.533, 0, 2, 8.2, 0, 2, 8.9, 0, 0, 9.133, 11, 0, 9.433, -8, 0, 9.7, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuL", "Segments": [0, 0, 2, 5.433, 0, 0, 5.533, 1, 0, 5.6, -1, 0, 5.7, 1, 0, 5.767, -1, 0, 5.867, 1, 0, 5.967, -1, 0, 6.067, 1, 0, 6.133, -1, 0, 6.233, 1, 0, 6.3, -1, 0, 6.4, 1, 0, 6.5, -1, 0, 6.6, 1, 0, 6.667, -1, 0, 6.767, 1, 0, 6.867, -1, 0, 6.933, 1, 0, 7.067, -1, 0, 7.167, 1, 0, 7.233, -1, 0, 7.333, 1, 0, 7.433, -1, 0, 7.533, 1, 0, 7.667, -1, 0, 7.767, 1, 0, 7.9, -1, 0, 8, 1, 0, 8.067, -1, 0, 8.167, 1, 0, 8.267, -1, 0, 8.367, 1, 0, 8.467, -1, 0, 8.533, 1, 0, 8.633, -1, 0, 8.733, 1, 0, 8.8, -1, 0, 8.9, 1, 0, 9, -1, 0, 9.1, 1, 0, 9.2, -1, 0, 9.3, 1, 0, 9.333, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamClawFX", "Segments": [0, 0, 2, 1.333, 0, 0, 1.533, 1, 0, 1.633, -1, 2, 1.7, -1, 0, 1.833, 1, 0, 2, -1, 2, 2.033, -1, 0, 2.167, 1, 0, 2.333, -1, 2, 2.367, -1, 0, 2.467, 1, 0, 2.633, -1, 2, 2.667, -1, 0, 2.833, 1, 0, 2.967, -1, 2, 3, -1, 0, 3.167, 1, 0, 3.233, -1, 0, 3.367, 1, 0, 3.5, 0, 2, 5.433, 0, 0, 5.6, 1, 0, 5.733, -1, 2, 5.767, -1, 0, 5.933, 1, 0, 6.067, -1, 2, 6.1, -1, 0, 6.233, 1, 0, 6.367, -1, 2, 6.4, -1, 0, 6.533, 1, 0, 6.667, -1, 2, 6.733, -1, 0, 6.867, 1, 0, 7.033, -1, 2, 7.067, -1, 0, 7.167, 1, 0, 7.333, -1, 2, 7.367, -1, 0, 7.533, 1, 0, 7.667, -1, 2, 7.7, -1, 0, 7.867, 1, 0, 8, -1, 0, 8.167, 1, 0, 8.3, -1, 2, 8.367, -1, 0, 8.5, 1, 0, 8.633, -1, 2, 8.7, -1, 0, 8.8, 1, 0, 8.933, -1, 2, 9, -1, 0, 9.133, 1, 0, 9.3, -1, 0, 9.4, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamClawFY", "Segments": [0, 0, 2, 1.333, 0, 0, 1.4, -1, 0, 1.533, 1, 0, 1.7, -1, 0, 2, 1, 0, 2.033, -1, 0, 2.333, 1, 0, 2.367, -1, 2, 2.667, -1, 0, 2.967, 1, 0, 3, -1, 2, 3.233, -1, 0, 3.5, 0, 2, 5.433, 0, 0, 5.533, -1, 0, 5.633, 1, 0, 5.767, -1, 2, 5.867, -1, 0, 5.967, 1, 0, 6.1, -1, 1, 6.122, -1, 6.145, -1, 6.167, -0.999, 1, 6.2, -0.997, 6.234, 1, 6.267, 1, 0, 6.4, -1, 2, 6.5, -1, 0, 6.6, 1, 0, 6.733, -1, 2, 6.833, -1, 0, 6.933, 1, 0, 7.067, -1, 2, 7.167, -1, 0, 7.233, 1, 0, 7.367, -1, 2, 7.467, -1, 0, 7.567, 1, 0, 7.7, -1, 2, 7.8, -1, 0, 7.9, 1, 0, 8, -1, 2, 8.1, -1, 0, 8.2, 1, 0, 8.367, -1, 2, 8.467, -1, 0, 8.533, 1, 0, 8.7, -1, 2, 8.8, -1, 0, 8.867, 1, 0, 9, -1, 2, 9.1, -1, 0, 9.2, 1, 0, 9.333, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamClawBX", "Segments": [0, 0, 2, 1.333, 0, 0, 1.533, -1, 0, 1.633, 1, 2, 1.7, 1, 0, 1.833, -1, 0, 2, 1, 2, 2.033, 1, 0, 2.167, -1, 0, 2.333, 1, 2, 2.367, 1, 0, 2.467, -1, 0, 2.633, 1, 2, 2.667, 1, 0, 2.833, -1, 0, 2.967, 1, 2, 3, 1, 0, 3.167, -1, 0, 3.233, 1, 0, 3.367, -1, 0, 3.5, 0, 2, 5.433, 0, 0, 5.6, -1, 0, 5.733, 1, 2, 5.767, 1, 0, 5.933, -1, 0, 6.067, 1, 2, 6.1, 1, 0, 6.233, -1, 0, 6.367, 1, 2, 6.4, 1, 0, 6.533, -1, 0, 6.667, 1, 2, 6.733, 1, 0, 6.867, -1, 0, 7.033, 1, 2, 7.067, 1, 0, 7.167, -1, 0, 7.333, 1, 2, 7.367, 1, 0, 7.533, -1, 0, 7.667, 1, 2, 7.7, 1, 0, 7.867, -1, 0, 8, 1, 0, 8.167, -1, 0, 8.3, 1, 2, 8.367, 1, 0, 8.5, -1, 0, 8.633, 1, 2, 8.7, 1, 0, 8.8, -1, 0, 8.933, 1, 2, 9, 1, 0, 9.133, -1, 0, 9.3, 1, 0, 9.4, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamClawBY", "Segments": [0, 0, 2, 1.333, 0, 0, 1.4, 1, 0, 1.533, -1, 0, 1.7, 1, 0, 2, -1, 0, 2.033, 1, 0, 2.333, -1, 0, 2.367, 1, 2, 2.667, 1, 0, 2.967, -1, 0, 3, 1, 2, 3.233, 1, 0, 3.5, 0, 2, 5.433, 0, 0, 5.533, 1, 0, 5.633, -1, 0, 5.767, 1, 2, 5.867, 1, 0, 5.967, -1, 0, 6.1, 1, 1, 6.122, 1, 6.145, 1, 6.167, 0.999, 1, 6.2, 0.997, 6.234, -1, 6.267, -1, 0, 6.4, 1, 2, 6.5, 1, 0, 6.6, -1, 0, 6.733, 1, 2, 6.833, 1, 0, 6.933, -1, 0, 7.067, 1, 2, 7.167, 1, 0, 7.233, -1, 0, 7.367, 1, 2, 7.467, 1, 0, 7.567, -1, 0, 7.7, 1, 2, 7.8, 1, 0, 7.9, -1, 0, 8, 1, 2, 8.1, 1, 0, 8.2, -1, 0, 8.367, 1, 2, 8.467, 1, 0, 8.533, -1, 0, 8.7, 1, 2, 8.8, 1, 0, 8.867, -1, 0, 9, 1, 2, 9.1, 1, 0, 9.2, -1, 0, 9.333, 0, 2, 9.467, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUEyesForm", "Segments": [0, 0, 2, 0.533, 0, 0, 1.133, -1, 2, 2.667, -1, 0, 2.8, 1, 0, 3.167, 0, 2, 8.2, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyX", "Segments": [0, 0, 2, 0.067, 0, 2, 0.533, 0, 0, 0.867, -30, 2, 2.167, -30, 0, 2.833, 9.817, 0, 2.933, 7, 0, 3.233, 9.817, 0, 3.733, -3, 0, 5.267, 2, 0, 5.7, -30, 0, 6.133, 29.528, 0, 6.633, 0, 0, 7.133, 4.882, 1, 7.411, 4.882, 7.689, 4.495, 7.967, 0, 1, 8.111, -2.337, 8.256, -28.032, 8.4, -28.032, 0, 8.8, 12.598, 0, 9.233, -6.457, 0, 9.7, 8.346, 0, 10.133, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyY", "Segments": [0, 0, 2, 0.067, 0, 2, 0.3, 0, 0, 0.533, 4, 0, 1, -30, 2, 2.3, -30, 1, 2.367, -30, 2.433, -2.802, 2.5, 0, 1, 2.578, 3.269, 2.655, 3.567, 2.733, 6.26, 1, 2.766, 7.414, 2.8, 17.995, 2.833, 17.995, 0, 3.067, -30, 1, 3.167, -30, 3.267, -6.27, 3.367, -4, 1, 3.534, -0.216, 3.7, 0, 3.867, 0, 2, 4.667, 0, 0, 5.333, -1.211, 0, 6.4, 0.896, 0, 7.7, 0, 0, 8.533, 0.896, 0, 9.667, -1.211, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyZ", "Segments": [0, 0, 2, 0.067, 0, 2, 0.533, 0, 2, 0.633, 0, 1, 0.822, 0, 1.011, -4.453, 1.2, -5, 1, 1.289, -5.257, 1.378, -5.127, 1.467, -5.281, 1, 1.534, -5.397, 1.6, -6.565, 1.667, -6.565, 0, 1.967, 0, 2, 2.267, 0, 2, 2.433, 0, 1, 2.522, 0, 2.611, 16.53, 2.7, 20.858, 1, 2.8, 25.727, 2.9, 25.737, 3, 25.737, 0, 3.2, -7, 0, 3.667, 6, 0, 5.367, 2.64, 0, 5.767, 30, 0, 6.133, -30, 0, 6.4, 2.64, 0, 6.933, -2.746, 1, 7.178, -2.746, 7.422, -2.045, 7.667, 0.945, 1, 7.811, 2.712, 7.956, 5.197, 8.1, 5.197, 0, 8.667, -15, 0, 9.233, 16, 0, 9.867, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUArmZ", "Segments": [0, 0, 2, 0.067, 0, 2, 0.633, 0, 0, 1, -12, 0, 1.167, -9.283, 0, 1.4, -10, 2, 1.467, -10, 0, 2, -2.641, 0, 2.5, -5.118, 0, 3, 12, 0, 3.2, -18, 0, 3.633, 2.009, 0, 5.2, -2.641, 2, 5.5, -2.641, 0, 5.867, 30, 0, 6.2, -30, 1, 6.422, -30, 6.645, -3.049, 6.867, -1, 1, 7.2, 2.073, 7.534, 2.009, 7.867, 2.009, 0, 8.733, -1, 0, 9.133, 1.024, 0, 9.567, -0.6, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyW", "Segments": [0, 0, 2, 0.067, 0, 2, 0.733, 0, 0, 0.867, -0.4, 0, 1.067, -0.26, 0, 1.867, -0.3, 0, 2.167, 0.27, 0, 2.5, -0.1, 0, 2.7, 0, 2, 2.767, 0, 0, 2.9, 0.5, 0, 3.167, -0.5, 0, 3.6, 0.31, 0, 4.633, -0.123, 2, 4.8, -0.123, 0, 5.233, 0.1, 0, 5.733, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPositionXPanda", "Segments": [0, 0, 2, 0.1, 0, 1, 1.522, 4.333, 2.945, 8.667, 4.367, 13, 2, 4.8, 13, 1, 6.522, 8.667, 8.245, 4.333, 9.967, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPositionYPanda", "Segments": [0, 0, 2, 0.1, 0, 0, 4.367, -0.679, 2, 4.8, -0.679, 0, 9.967, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.3, 0, 2, 4.467, 0, 0, 4.833, 1, 2, 9.8, 1, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyX", "Segments": [0, 0, 2, 0.067, 0, 2, 0.2, 0, 0, 0.333, 2.538, 0, 0.5, -2.538, 0, 0.667, 2.538, 0, 0.8, -2.538, 0, 1, 2.538, 0, 1.2, -2.538, 0, 1.467, 2.538, 0, 1.567, -2.538, 0, 1.767, 2.538, 0, 1.9, -2.538, 0, 2.1, 2.538, 0, 2.3, -2.538, 0, 2.433, 2.538, 0, 2.667, -2.538, 0, 2.8, 2.538, 0, 2.967, -2.538, 0, 3.2, 2.538, 0, 3.3, -2.538, 0, 3.433, 2.538, 0, 3.6, -2.538, 0, 3.8, 2.538, 0, 4, -2.538, 0, 4.167, 2.538, 0, 4.367, -2.538, 0, 4.567, 2.538, 0, 4.767, 0, 2, 5, 0, 0, 5.167, 2.538, 0, 5.333, -2.538, 0, 5.467, 2.538, 0, 5.6, -2.538, 0, 5.833, 2.538, 0, 6.033, -2.538, 0, 6.267, 2.538, 0, 6.367, -2.538, 0, 6.533, 2.538, 0, 6.7, -2.538, 0, 6.9, 2.538, 0, 7.1, -2.538, 0, 7.233, 2.538, 0, 7.433, -2.538, 0, 7.6, 2.538, 0, 7.767, -2.538, 0, 8, 2.538, 0, 8.2, -2.538, 0, 8.333, 2.538, 0, 8.5, -2.538, 0, 8.7, 2.538, 0, 8.867, -2.538, 0, 9.067, 2.538, 0, 9.233, -2.538, 0, 9.4, 2.538, 0, 9.633, -2.538, 0, 9.767, 2.538, 0, 9.9, -2.538, 0, 10.133, 2.538, 0, 10.233, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyY", "Segments": [0, 0, 2, 0.1, 0, 0, 0.267, 3.183, 0, 0.433, -3.183, 0, 0.567, 3.183, 0, 0.733, -3.183, 0, 0.933, 3.183, 0, 1.133, -3.183, 0, 1.4, 3.183, 0, 1.5, -3.183, 0, 1.667, 3.183, 0, 1.833, -3.183, 0, 2.033, 3.183, 0, 2.233, -3.183, 0, 2.4, 3.183, 0, 2.567, -3.183, 0, 2.733, 3.183, 0, 2.9, -3.183, 0, 3.167, 3.183, 0, 3.233, -3.183, 0, 3.367, 3.183, 0, 3.533, -3.183, 0, 3.733, 3.183, 0, 3.933, -3.183, 0, 4.1, 3.183, 0, 4.267, -3.183, 0, 4.5, 3.183, 0, 4.6, 0, 2, 4.933, 0, 0, 5.1, 3.183, 0, 5.267, -3.183, 0, 5.4, 3.183, 0, 5.567, -3.183, 0, 5.767, 3.183, 0, 5.967, -3.183, 0, 6.2, 3.183, 0, 6.3, -3.183, 0, 6.467, 3.183, 0, 6.633, -3.183, 0, 6.833, 3.183, 0, 7.033, -3.183, 0, 7.167, 3.183, 0, 7.367, -3.183, 0, 7.533, 3.183, 0, 7.667, -3.183, 0, 7.967, 3.183, 0, 8.1, -3.183, 0, 8.267, 3.183, 0, 8.433, -3.183, 0, 8.633, 3.183, 0, 8.767, -3.183, 0, 9, 3.183, 0, 9.167, -3.183, 0, 9.333, 3.183, 0, 9.567, -3.183, 0, 9.7, 3.183, 0, 9.833, -3.183, 0, 10.067, 3.183, 0, 10.2, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2Panda", "Segments": [0, 0, 2, 0.1, 0, 0, 0.2, 6.229, 0, 0.267, -6.213, 0, 0.333, 6.229, 0, 0.433, -6.229, 0, 0.533, 6.229, 0, 0.6, -6.229, 0, 0.7, 6.229, 0, 0.8, -6.229, 0, 0.867, 6.229, 0, 0.967, -6.229, 0, 1.067, 6.229, 0, 1.167, -6.229, 0, 1.233, 6.229, 0, 1.333, -6.213, 0, 1.4, 6.229, 0, 1.5, -6.229, 0, 1.567, 6.229, 0, 1.633, -6.229, 0, 1.733, 6.229, 0, 1.833, -6.229, 0, 1.9, 6.229, 0, 2, -6.229, 0, 2.067, 6.229, 0, 2.167, -6.229, 0, 2.267, 6.229, 0, 2.333, -6.229, 0, 2.4, 6.229, 0, 2.5, -6.229, 0, 2.567, 6.229, 0, 2.667, -6.229, 0, 2.767, 6.229, 0, 2.867, -6.229, 0, 2.933, 6.229, 0, 3.033, -6.229, 0, 3.133, 6.229, 0, 3.2, -6.229, 2, 3.233, -6.229, 0, 3.333, 6.229, 0, 3.467, -6.213, 0, 3.533, 6.229, 0, 3.6, -6.229, 0, 3.7, 6.229, 0, 3.8, -6.213, 0, 3.867, 6.229, 0, 3.933, -6.213, 0, 4, 6.229, 0, 4.133, -6.229, 0, 4.233, 6.229, 0, 4.333, -6.213, 0, 4.4, 6.229, 0, 4.433, 0, 2, 4.8, 0, 0, 4.867, -6.229, 0, 4.967, 6.229, 0, 5.033, -6.229, 0, 5.133, 6.229, 0, 5.233, -6.229, 0, 5.333, 6.229, 0, 5.4, -6.229, 0, 5.5, 6.229, 0, 5.567, -6.229, 0, 5.633, 6.229, 0, 5.7, -6.229, 0, 5.833, 6.229, 0, 5.9, -6.229, 0, 6, 6.229, 0, 6.067, -6.229, 0, 6.133, 6.229, 0, 6.233, -6.229, 0, 6.333, 6.229, 0, 6.4, -6.229, 0, 6.467, 6.229, 0, 6.567, -6.229, 0, 6.633, 6.229, 0, 6.7, -6.229, 0, 6.833, 6.229, 0, 6.933, -6.229, 0, 7, 6.229, 0, 7.1, -6.229, 0, 7.167, 6.229, 0, 7.233, -6.229, 0, 7.333, 6.229, 0, 7.433, -6.229, 0, 7.533, 6.229, 0, 7.6, -6.229, 0, 7.667, 6.229, 0, 7.767, -6.229, 0, 7.867, 6.229, 0, 7.967, -6.229, 0, 8.033, 6.229, 0, 8.1, -6.229, 0, 8.2, 6.229, 0, 8.267, -6.229, 0, 8.367, 6.229, 0, 8.467, -6.229, 0, 8.567, 6.229, 0, 8.667, -6.229, 0, 8.733, 6.229, 0, 8.767, -6.229, 0, 8.867, 6.229, 0, 8.967, -6.229, 0, 9.067, 6.229, 0, 9.133, -6.229, 0, 9.233, 6.229, 0, 9.333, -6.229, 0, 9.4, 6.229, 0, 9.5, -6.229, 0, 9.567, 6.229, 0, 9.667, -6.229, 0, 9.733, 6.229, 0, 9.8, -6.229, 0, 9.9, 6.229, 0, 10, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyZ", "Segments": [0, 0, 2, 0.067, 0, 0, 0.2, 1.431, 0, 0.4, -1.431, 0, 0.533, 1.431, 0, 0.667, -1.431, 0, 0.867, 1.431, 0, 1.1, -1.431, 0, 1.333, 1.431, 0, 1.467, -1.431, 0, 1.633, 1.431, 0, 1.767, -1.431, 0, 2, 1.431, 0, 2.2, -1.431, 0, 2.367, 1.431, 0, 2.533, -1.431, 0, 2.7, 1.431, 0, 2.833, -1.431, 0, 3.133, 1.431, 0, 3.233, -1.431, 0, 3.3, 1.431, 0, 3.5, -1.431, 0, 3.7, 1.431, 0, 3.867, -1.431, 0, 4.033, 1.431, 0, 4.233, -1.431, 0, 4.433, 1.431, 1, 4.478, 1.431, 4.522, 1.977, 4.567, 0, 1, 4.645, -3.46, 4.722, -30, 4.8, -30, 0, 5.033, 1.431, 0, 5.233, -1.431, 0, 5.367, 1.431, 0, 5.5, -1.431, 0, 5.7, 1.431, 0, 5.9, -1.431, 0, 6.133, 1.431, 0, 6.267, -1.431, 0, 6.433, 1.431, 0, 6.567, -1.431, 0, 6.767, 1.431, 0, 7, -1.431, 0, 7.167, 1.431, 0, 7.333, -1.431, 0, 7.5, 1.431, 0, 7.633, -1.431, 0, 7.9, 1.431, 0, 8.067, -1.431, 0, 8.2, 1.431, 0, 8.4, -1.431, 0, 8.567, 1.431, 0, 8.767, -1.431, 0, 8.933, 1.431, 0, 9.133, -1.431, 0, 9.267, 1.431, 0, 9.567, -1.431, 0, 9.667, 1.431, 0, 9.8, -1.431, 0, 10.033, 1.431, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegFZ", "Segments": [0, 0, 2, 0.1, 0, 0, 0.267, -1, 0, 0.467, 1, 0, 0.633, -1, 0, 0.833, 1, 0, 1.033, -1, 0, 1.267, 1, 0, 1.433, -1, 0, 1.6, 1, 0, 1.767, -1, 0, 2, 1, 0, 2.167, -1, 0, 2.367, 1, 0, 2.5, -1, 0, 2.733, 1, 0, 2.933, -1, 0, 3.133, 1, 0, 3.233, -1, 0, 3.367, 1, 0, 3.567, -1, 0, 3.767, 1, 0, 3.967, -1, 0, 4.133, 1, 0, 4.3, -1, 0, 4.4, 0, 2, 4.8, 0, 0, 4.9, 1, 0, 5.067, -1, 0, 5.3, 1, 0, 5.5, -1, 0, 5.7, 1, 0, 5.867, -1, 0, 6.067, 1, 0, 6.167, -1, 0, 6.4, 1, 0, 6.567, -1, 0, 6.767, 1, 0, 6.933, -1, 0, 7.167, 1, 0, 7.333, -1, 0, 7.533, 1, 0, 7.7, -1, 0, 7.9, 1, 0, 8.067, -1, 0, 8.3, 1, 0, 8.5, -1, 0, 8.667, 1, 0, 8.833, -1, 0, 9.033, 1, 0, 9.3, -1, 0, 9.533, 1, 0, 9.633, -1, 0, 9.9, 1, 0, 10, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegBZ", "Segments": [0, 0, 2, 0.1, 0, 0, 0.267, -1, 0, 0.467, 1, 0, 0.633, -1, 0, 0.833, 1, 0, 1.033, -1, 0, 1.267, 1, 0, 1.433, -1, 0, 1.6, 1, 0, 1.767, -1, 0, 2, 1, 0, 2.167, -1, 0, 2.367, 1, 0, 2.5, -1, 0, 2.733, 1, 0, 2.933, -1, 0, 3.133, 1, 0, 3.233, -1, 0, 3.367, 1, 0, 3.567, -1, 0, 3.767, 1, 0, 3.967, -1, 0, 4.133, 1, 0, 4.3, -1, 0, 4.4, 0, 2, 4.8, 0, 0, 4.9, 1, 0, 5.067, -1, 0, 5.3, 1, 0, 5.5, -1, 0, 5.7, 1, 0, 5.867, -1, 0, 6.067, 1, 0, 6.167, -1, 0, 6.4, 1, 0, 6.567, -1, 0, 6.767, 1, 0, 6.933, -1, 0, 7.167, 1, 0, 7.333, -1, 0, 7.533, 1, 0, 7.7, -1, 0, 7.9, 1, 0, 8.067, -1, 0, 8.3, 1, 0, 8.5, -1, 0, 8.667, 1, 0, 8.833, -1, 0, 9.033, 1, 0, 9.3, -1, 0, 9.533, 1, 0, 9.633, -1, 0, 9.9, 1, 0, 10, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyX", "Segments": [0, 0, 0, 0.333, 2, 0, 1.267, -30, 2, 2.733, -30, 0, 4.933, 3.384, 0, 5.433, 0, 0, 6.5, 5.186, 0, 8.5, -4.826, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyY", "Segments": [0, 0, 0, 0.2, -3.665, 0, 0.567, 30, 1, 0.734, 30, 0.9, 2.747, 1.067, 0, 1, 1.245, -2.93, 1.422, -2.629, 1.6, -2.629, 1, 1.689, -2.629, 1.778, 0.327, 1.867, 1.177, 1, 2.056, 2.983, 2.244, 3.267, 2.433, 3.267, 0, 2.7, -17.291, 0, 3.133, 30, 0, 3.433, -1.599, 0, 4.4, 1.177, 0, 5.433, 0, 0, 6.233, 1.561, 0, 8.4, -1.599, 0, 9.967, 1.177, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamCannonZ", "Segments": [0, 0, 2, 0.067, 0, 0, 0.967, 18.725, 2, 2.667, 18.725, 0, 3.1, -30, 0, 3.6, 3.187, 0, 4.8, -2.048, 1, 5.011, -2.048, 5.222, -1.712, 5.433, 0, 1, 6.166, 5.948, 6.9, 9.63, 7.633, 9.63, 0, 10.033, -2.048, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamCannonGaY", "Segments": [0, 0, 0, 1.4, -20, 1, 1.833, -20, 2.267, -19.839, 2.7, -17.769, 1, 2.889, -16.867, 3.078, 25.1, 3.267, 25.1, 0, 3.633, -14.821, 0, 3.867, 9.562, 0, 4.2, -5.578, 0, 4.767, 3.807, 0, 5.433, 0, 0, 6.867, 3.807, 0, 8.6, -1.392, 0, 10.133, 3.53, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupY", "Segments": [0, 0, 0, 1.9, -0.17, 0, 3.233, 0.126, 0, 4.867, -0.04, 0, 5.433, 0, 0, 7.333, -0.17, 0, 8.8, 0.126, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupZ", "Segments": [0, 0, 2, 0.333, 0, 0, 2.8, -0.149, 0, 4, 0.113, 0, 5.433, 0, 2, 5.767, 0, 0, 8.233, -0.149, 0, 9.567, 0.113, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamCannonHandZ", "Segments": [0, 0, 1, 0.733, 0, 1.467, -0.361, 2.2, -1.699, 1, 2.367, -2.003, 2.533, -3.984, 2.7, -3.984, 0, 3.3, 7, 0, 3.9, -1.699, 0, 4.5, 1.258, 1, 4.811, 1.258, 5.122, 0.551, 5.433, 0, 1, 6.166, -1.298, 6.9, -1.699, 7.633, -1.699, 0, 8.867, 1.258, 0, 10.2, -0.396, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupInput", "Segments": [0, 0, 2, 0.967, 0, 0, 1.167, 12.003, 0, 1.433, -12.118, 0, 1.667, 17.333, 0, 2, -14.401, 0, 2.367, 12.003, 0, 2.667, -18.472, 0, 2.933, 12.003, 0, 3.2, -12.118, 0, 3.333, 17.333, 0, 3.667, -14.401, 0, 4.133, 12.003, 0, 4.4, -12.118, 0, 4.767, 17.333, 0, 5.067, -14.401, 0, 5.433, 12.003, 0, 5.733, -17.749, 0, 6, 4, 1, 6.156, 4, 6.311, 3.34, 6.467, 0, 1, 6.522, -1.193, 6.578, -13, 6.633, -13, 0, 6.8, 12.81, 0, 7.2, -15, 0, 7.567, 4.515, 0, 8.533, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamParamStrongCatZ", "Segments": [0, 0, 2, 0.2, 0, 0, 0.533, 2, 0, 1, -11.745, 0, 1.467, -5.725, 0, 1.933, -7, 2, 2.067, -7, 0, 2.3, -11.487, 0, 2.767, 7.269, 0, 3.2, -11.653, 0, 3.767, -8.731, 0, 4.933, -11.511, 0, 6.433, -7.238, 0, 7.3, -13.101, 0, 7.967, 2.702, 0, 8.633, -4.522, 0, 9.567, 0, 2, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamParamSCBodyZ", "Segments": [0, 0, 2, 0.2, 0, 2, 0.3, 0, 0, 0.633, 2, 0, 1.1, -9.745, 0, 1.567, -5.725, 1, 1.7, -5.725, 1.834, -5.669, 1.967, -7, 1, 2.134, -8.664, 2.3, -11.712, 2.467, -11.712, 0, 2.967, 7.269, 0, 3.367, -11.814, 0, 4.067, -8.023, 0, 5.567, -10.558, 0, 7.033, -7.731, 0, 7.567, -12.893, 0, 8.367, 2.999, 0, 9.733, -1, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamSCDishY", "Segments": [0, 0, 2, 0.2, 0, 2, 0.367, 0, 0, 0.7, 2, 0, 1.133, -2.745, 0, 1.8, 1.275, 0, 2.033, 0, 2, 2.367, 0, 0, 2.567, -6.809, 0, 3.067, 8.004, 0, 3.467, -2.967, 0, 4, 2.348, 0, 4.8, -1.652, 0, 6.5, 2.86, 0, 8.4, -0.982, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamSCDishZ", "Segments": [0, 0, 2, 0.2, 0, 2, 0.367, 0, 0, 0.733, 2, 0, 1.167, -2.745, 0, 1.833, 1.275, 0, 2.067, 0, 2, 2.367, 0, 0, 2.733, -4.423, 0, 3.233, 8.057, 0, 3.8, -3.543, 0, 4.367, 1.437, 0, 5.333, -1.686, 0, 7.233, 2.177, 0, 9.2, -1.686, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamSCCupZ", "Segments": [0, 0, 2, 0.2, 0, 2, 0.433, 0, 0, 0.8, 5.028, 0, 1.267, -5.773, 0, 1.933, 3.378, 0, 2.133, -1.036, 0, 2.367, 0, 0, 2.4, -5.646, 0, 4.5, 3.433, 1, 4.811, 3.433, 5.122, 1.71, 5.433, 0, 1, 6.189, -4.154, 6.944, -5.646, 7.7, -5.646, 0, 10.067, 3.433, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "MB_yanwubaozha", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "MB_DRFWXZKTMD", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "Parameter", "Id": "ParamAllSizeFix", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBG2Hide", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBGX", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBGY", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN2", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBlackOrder", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamCHHide", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "Parameter", "Id": "ParamDeskHide", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "Parameter", "Id": "ParamStoolHide", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "Parameter", "Id": "ParamCupDesk", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamCHX", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamCHY", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamCHZ", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamChaSize", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamCcharacterZ", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionX", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionY", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionX", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamAllZ", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamALLSize2", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamDeskShow", "Segments": [0, 10, 0, 10.3, 10]}, {"Target": "Parameter", "Id": "ParamStrongCatShow", "Segments": [0, 10, 0, 10.3, 10]}, {"Target": "Parameter", "Id": "ParamCannonShow", "Segments": [0, 10, 0, 10.3, 10]}, {"Target": "Parameter", "Id": "ParamLightPositionX", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamFixT", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "Parameter", "Id": "ParamFlap", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamScare", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPupilExp", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamEyeSmileR", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpen2", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamMouthType", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamMouthY", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamTeethLight", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamHeart2", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamMark", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamMarkShake", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLY", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRY", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow1", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow2", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamTearLight", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamTears", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamAngleH", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamAngleS", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerLAngle", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamHandT2L", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamFanOpenR", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamChili", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamChiliX", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRY", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamHandRCup", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamHandRMail", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "Parameter", "Id": "ParamHandLIQY1", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY2", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY3", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamHandCupZ", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamHandCupY", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechW", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Z", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLegR1X", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Z", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Z", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Y", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Y", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Y", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamFootRX", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Z", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Z", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Z", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Y", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Y", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Y", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamFootLX", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLegLF", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRHide", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "Parameter", "Id": "ParamMJRFlap", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuR", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuR", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuR", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuR", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRInput", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamMRCupSet", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "Parameter", "Id": "ParamMalpositionManjuuR", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRArmB", "Segments": [0, 30, 0, 10.3, 30]}, {"Target": "Parameter", "Id": "ParamManjuuRSigh", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow1", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow2", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow3", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow4", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowB", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW2", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamMRCupFZ", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX1", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX2", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLiqH", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX1", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX2", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX3", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLHide", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "Parameter", "Id": "ParamMJLSigh", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuL", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuL", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUHide", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuU", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuU", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuU", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineU", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineD", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamPandaHide", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "Parameter", "Id": "ParamSizePanda", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY1", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY2", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY3", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupIce", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupZ", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamSCDishRO", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamSCCupRO", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "Parameter", "Id": "ParamSCCupY", "Segments": [0, 0, 0, 10.3, 0]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 10.3, 1]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 0, 10.3, 1]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 1.3, "Value": ""}, {"Time": 9.8, "Value": ""}]}