#!/usr/bin/env python3
"""
快速输入组件模块
为Live2D桌面宠物提供快速输入界面
"""

from PySide6.QtWidgets import (QFrame, QVBoxLayout, QHBoxLayout, QTextEdit, 
                              QPushButton, QLabel, QComboBox)
from PySide6.QtCore import Qt, Signal


class QuickInputOverlay(QFrame):
    """快速输入叠加层"""

    # 信号：发送消息、预设切换
    message_sent = Signal(str)
    preset_changed = Signal(str)  # 预设ID

    def __init__(self, parent=None, config_manager=None):
        super().__init__(parent)
        self.parent_widget = parent
        self.config_manager = config_manager
        self.current_preset_id = "default"
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 设置窗口标志，确保不受父窗口透明模式影响
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool
        )

        # 设置窗口属性，确保始终可见
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, False)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, False)
        self.setAttribute(Qt.WidgetAttribute.WA_OpaquePaintEvent, True)

        self.setFrameStyle(QFrame.Shape.StyledPanel)
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(250, 250, 250, 245),
                    stop:1 rgba(240, 240, 240, 245));
                border: 2px solid rgba(120, 120, 120, 180);
                border-radius: 12px;
            }
            QLabel {
                color: #333;
                font-size: 12px;
                font-weight: bold;
                background: transparent;
                padding: 2px;
            }
            QComboBox {
                background-color: rgba(255, 255, 255, 250);
                border: 1px solid #ddd;
                border-radius: 6px;
                padding: 4px 8px;
                font-size: 12px;
                min-width: 120px;
            }
            QComboBox:hover {
                border-color: #007acc;
                background-color: rgba(255, 255, 255, 255);
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #666;
                margin-right: 4px;
            }
            QTextEdit {
                background-color: rgba(255, 255, 255, 250);
                border: 1px solid #ddd;
                border-radius: 6px;
                padding: 6px;
                font-size: 14px;
                selection-background-color: #007acc;
            }
            QTextEdit:focus {
                border-color: #007acc;
                background-color: rgba(255, 255, 255, 255);
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007acc,
                    stop:1 #005a9e);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
                min-width: 60px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0088dd,
                    stop:1 #006bb3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #005a9e,
                    stop:1 #004578);
            }
            QPushButton#cancelBtn {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #888,
                    stop:1 #666);
            }
            QPushButton#cancelBtn:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #999,
                    stop:1 #777);
            }
        """)
        
        # 布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)

        # 预设选择区域
        preset_layout = QHBoxLayout()
        preset_layout.setSpacing(8)

        self.preset_label = QLabel("当前预设:")
        self.preset_combo = QComboBox()
        self.preset_combo.setToolTip("选择对话预设")
        self.preset_combo.currentTextChanged.connect(self.on_preset_changed)

        preset_layout.addWidget(self.preset_label)
        preset_layout.addWidget(self.preset_combo)
        preset_layout.addStretch()

        # 输入框
        self.input_edit = QTextEdit()
        self.input_edit.setMaximumHeight(60)
        self.input_edit.setPlaceholderText("快速输入消息...")
        self.input_edit.installEventFilter(self)

        # 按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)

        self.send_btn = QPushButton("发送")
        self.send_btn.clicked.connect(self.send_message)
        self.send_btn.setDefault(True)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setObjectName("cancelBtn")
        self.cancel_btn.clicked.connect(self.hide)

        button_layout.addStretch()
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.send_btn)

        # 添加到主布局
        layout.addLayout(preset_layout)
        layout.addWidget(self.input_edit)
        layout.addLayout(button_layout)

        # 初始化预设列表
        self.load_presets()

        # 初始隐藏
        self.hide()

        # 设置固定大小（增加高度以容纳预设选择）
        self.setFixedSize(320, 140)
    
    def show_input(self):
        """显示输入框"""
        self.update_position()
        self.show()
        self.input_edit.setFocus()
        self.input_edit.clear()
    
    def update_position(self):
        """更新位置"""
        if not self.parent_widget:
            return

        # 获取父窗口的全局位置和大小
        parent_global_pos = self.parent_widget.mapToGlobal(self.parent_widget.rect().topLeft())
        parent_rect = self.parent_widget.rect()

        # 位置：居中显示
        x = parent_global_pos.x() + (parent_rect.width() - self.width()) // 2
        y = parent_global_pos.y() + (parent_rect.height() - self.height()) // 2

        # 确保不超出屏幕边界
        from PySide6.QtGui import QGuiApplication
        screen = QGuiApplication.primaryScreen().geometry()
        x = max(10, min(x, screen.width() - self.width() - 10))
        y = max(10, min(y, screen.height() - self.height() - 10))

        self.move(x, y)

    def load_presets(self):
        """加载预设列表"""
        if not self.config_manager:
            return

        self.preset_combo.clear()
        presets = self.config_manager.config.get("llm_presets", {})

        # 添加预设到下拉框
        for preset_id, preset_data in presets.items():
            name = preset_data.get("name", preset_id)
            model = preset_data.get("api_config", {}).get("model", "")
            display_text = f"{name}"
            if model:
                # 简化模型名称显示
                model_short = model.split("/")[-1] if "/" in model else model
                if len(model_short) > 15:
                    model_short = model_short[:12] + "..."
                display_text += f" ({model_short})"

            self.preset_combo.addItem(display_text, preset_id)

        # 设置当前预设
        self.set_current_preset(self.current_preset_id)

    def set_current_preset(self, preset_id):
        """设置当前预设"""
        self.current_preset_id = preset_id

        # 在下拉框中选择对应项
        for i in range(self.preset_combo.count()):
            if self.preset_combo.itemData(i) == preset_id:
                self.preset_combo.setCurrentIndex(i)
                break

    def on_preset_changed(self):
        """预设切换处理"""
        current_index = self.preset_combo.currentIndex()
        if current_index >= 0:
            preset_id = self.preset_combo.itemData(current_index)
            if preset_id and preset_id != self.current_preset_id:
                self.current_preset_id = preset_id
                self.preset_changed.emit(preset_id)
                print(f"快速输入预设切换到: {preset_id}")

    def send_message(self):
        """发送消息"""
        text = self.input_edit.toPlainText().strip()
        if text:
            self.message_sent.emit(text)
            self.input_edit.clear()
        self.hide()
    
    def eventFilter(self, obj, event):
        """事件过滤器"""
        if obj == self.input_edit and event.type() == event.Type.KeyPress:
            if event.key() == Qt.Key.Key_Return and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                self.send_message()
                return True
            elif event.key() == Qt.Key.Key_Escape:
                self.hide()
                return True
        return super().eventFilter(obj, event)
