#!/usr/bin/env python3
"""
Live2D语音对话系统完整功能测试

这个脚本提供了语音对话系统的完整测试，包括：
- 依赖检查
- 模块导入测试
- 配置验证
- API连接测试
- 功能集成测试
- 性能测试

使用方法：
    python test_voice_dialogue_complete.py
"""

import sys
import os
import time
import json
from typing import Dict, Any, List

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'dev'))

class VoiceDialogueSystemTester:
    """语音对话系统测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = time.time()
        
    def log_result(self, test_name: str, success: bool, message: str = "", details: Any = None):
        """记录测试结果"""
        self.test_results[test_name] = {
            'success': success,
            'message': message,
            'details': details,
            'timestamp': time.time()
        }
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
        if details and isinstance(details, str):
            print(f"   详情: {details}")
    
    def test_dependencies(self) -> bool:
        """测试依赖包"""
        print("\n🔄 测试依赖包...")
        
        dependencies = {
            'numpy': 'numpy',
            'PySide6': 'PySide6.QtCore',
            'requests': 'requests',
            'faster_whisper': 'faster_whisper',
            'keyboard': 'keyboard',
            'threading': 'threading',
            'queue': 'queue',
            'json': 'json'
        }
        
        optional_dependencies = {
            'pyaudio': 'pyaudio'
        }
        
        all_success = True
        
        # 测试必需依赖
        for name, module in dependencies.items():
            try:
                __import__(module)
                self.log_result(f"依赖-{name}", True, "可用")
            except ImportError as e:
                self.log_result(f"依赖-{name}", False, "缺失", str(e))
                all_success = False
        
        # 测试可选依赖
        for name, module in optional_dependencies.items():
            try:
                __import__(module)
                self.log_result(f"可选依赖-{name}", True, "可用")
            except ImportError:
                self.log_result(f"可选依赖-{name}", False, "缺失（可选）", "语音功能需要此依赖")
        
        return all_success
    
    def test_configuration(self) -> bool:
        """测试配置文件"""
        print("\n🔄 测试配置文件...")
        
        try:
            # 检查主配置文件
            if not os.path.exists('config.json'):
                self.log_result("配置文件", False, "config.json不存在")
                return False
            
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查语音对话配置
            if 'voice_dialogue' not in config:
                self.log_result("语音配置", False, "缺少voice_dialogue配置节")
                return False
            
            voice_config = config['voice_dialogue']
            
            # 检查必需的配置节
            required_sections = [
                'microphone_config',
                'stt_config', 
                'tts_config',
                'ui_settings'
            ]
            
            missing_sections = []
            for section in required_sections:
                if section not in voice_config:
                    missing_sections.append(section)
            
            if missing_sections:
                self.log_result("配置完整性", False, f"缺少配置节: {missing_sections}")
                return False
            
            # 检查STT模型路径
            stt_config = voice_config['stt_config']
            model_path = stt_config.get('model_path', '')
            
            if not model_path:
                self.log_result("STT模型路径", False, "未配置模型路径")
            elif not os.path.exists(model_path):
                self.log_result("STT模型路径", False, f"模型路径不存在: {model_path}")
            else:
                self.log_result("STT模型路径", True, f"模型路径有效: {model_path}")
            
            # 检查TTS API配置
            tts_config = voice_config['tts_config']
            api_url = tts_config.get('api_url', '')
            
            if not api_url:
                self.log_result("TTS API配置", False, "未配置API地址")
            else:
                self.log_result("TTS API配置", True, f"API地址: {api_url}")
            
            self.log_result("配置文件", True, "配置文件完整")
            return True
            
        except Exception as e:
            self.log_result("配置文件", False, "配置文件读取失败", str(e))
            return False
    
    def test_module_imports(self) -> bool:
        """测试模块导入"""
        print("\n🔄 测试模块导入...")
        
        modules_to_test = [
            ('语音配置管理', 'dialogue_system.config.voice_dialogue_config', 'VoiceDialogueConfig'),
            ('STT客户端', 'dialogue_system.stt.faster_whisper_client', 'FasterWhisperClient'),
            ('STT管理器', 'dialogue_system.stt.stt_manager', 'STTManager'),
            ('TTS客户端', 'dialogue_system.tts.gptsovits_client', 'GPTSoVITSClient'),
            ('TTS音频播放器', 'dialogue_system.tts.audio_player', 'AudioPlayer'),
            ('TTS管理器', 'dialogue_system.tts.tts_manager', 'TTSManager'),
            ('语音设置界面', 'dialogue_system.ui.voice_settings_dialog', 'VoiceSettingsDialog'),
            ('配置管理器', 'settings_dialog', 'ConfigManager')
        ]
        
        # 语音模块可能因为PyAudio而失败
        optional_modules = [
            ('语音对话管理器', 'dialogue_system.voice', 'VoiceDialogueManager'),
            ('麦克风管理器', 'dialogue_system.voice.microphone_manager', 'MicrophoneManager'),
            ('语音处理器', 'dialogue_system.voice.voice_processor', 'VoiceProcessor'),
            ('按键触发输入', 'dialogue_system.voice.key_triggered_input', 'KeyTriggeredInput'),
            ('实时语音输入', 'dialogue_system.voice.realtime_input', 'RealtimeInput')
        ]
        
        all_success = True
        
        # 测试必需模块
        for name, module_path, class_name in modules_to_test:
            try:
                module = __import__(module_path, fromlist=[class_name])
                getattr(module, class_name)
                self.log_result(f"模块-{name}", True, "导入成功")
            except ImportError as e:
                self.log_result(f"模块-{name}", False, "导入失败", str(e))
                all_success = False
            except AttributeError as e:
                self.log_result(f"模块-{name}", False, "类不存在", str(e))
                all_success = False
        
        # 测试可选模块
        for name, module_path, class_name in optional_modules:
            try:
                module = __import__(module_path, fromlist=[class_name])
                getattr(module, class_name)
                self.log_result(f"可选模块-{name}", True, "导入成功")
            except ImportError as e:
                if "pyaudio" in str(e).lower():
                    self.log_result(f"可选模块-{name}", False, "PyAudio未安装（预期）")
                else:
                    self.log_result(f"可选模块-{name}", False, "导入失败", str(e))
        
        return all_success
    
    def test_api_connections(self) -> bool:
        """测试API连接"""
        print("\n🔄 测试API连接...")

        try:
            import requests

            # 测试TTS API
            try:
                with open('config.json', 'r', encoding='utf-8') as f:
                    config = json.load(f)

                api_url = config['voice_dialogue']['tts_config']['api_url']

                # 使用正确的GPT-SoVITS API端点进行测试
                test_data = {
                    "text": "测试",
                    "text_lang": "zh",
                    "ref_audio_path": "",
                    "prompt_text": "",
                    "prompt_lang": "zh",
                    "top_k": 5,
                    "top_p": 1.0,
                    "temperature": 1.0,
                    "speed_factor": 1.0
                }

                # 先测试服务是否运行（任何响应都表示服务在运行）
                try:
                    response = requests.post(f"{api_url}/tts", json=test_data, timeout=5)
                    # 任何HTTP响应（包括400, 404, 500等）都表示服务正在运行
                    self.log_result("TTS API连接", True, f"服务运行中 ({api_url}) - 状态码: {response.status_code}")
                except requests.exceptions.ConnectionError:
                    self.log_result("TTS API连接", False, "连接失败", "请确保GPT-SoVITS服务正在运行")
                except requests.exceptions.Timeout:
                    self.log_result("TTS API连接", False, "连接超时")

            except Exception as e:
                self.log_result("TTS API连接", False, "测试失败", str(e))

            return True

        except ImportError:
            self.log_result("API连接测试", False, "requests模块未安装")
            return False
    
    def test_ui_integration(self) -> bool:
        """测试UI集成"""
        print("\n🔄 测试UI集成...")
        
        try:
            # 检查主窗口文件
            main_window_path = os.path.join('dev', 'main_window.py')
            if not os.path.exists(main_window_path):
                self.log_result("主窗口文件", False, "main_window.py不存在")
                return False
            
            with open(main_window_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查语音功能集成
            required_elements = [
                ('语音菜单', '🎤 语音对话'),
                ('语音组件初始化', 'self.voice_dialogue_manager = None'),
                ('语音初始化方法', 'def init_voice_dialogue'),
                ('按键语音方法', 'def start_key_triggered_voice'),
                ('实时语音方法', 'def start_realtime_voice'),
                ('停止语音方法', 'def stop_voice_input'),
                ('语音设置方法', 'def show_voice_settings'),
                ('语音回调方法', 'def on_voice_input_received')
            ]
            
            missing_elements = []
            for name, pattern in required_elements:
                if pattern not in content:
                    missing_elements.append(name)
                else:
                    self.log_result(f"UI集成-{name}", True, "已集成")
            
            if missing_elements:
                self.log_result("UI集成完整性", False, f"缺少元素: {missing_elements}")
                return False
            else:
                self.log_result("UI集成完整性", True, "所有UI元素已集成")
                return True
                
        except Exception as e:
            self.log_result("UI集成测试", False, "测试失败", str(e))
            return False
    
    def test_basic_functionality(self) -> bool:
        """测试基础功能"""
        print("\n🔄 测试基础功能...")
        
        try:
            # 测试配置管理器创建
            from settings_dialog import ConfigManager
            config_manager = ConfigManager()
            self.log_result("配置管理器创建", True, "成功")
            
            # 测试语音配置管理器
            from dialogue_system.config import VoiceDialogueConfig
            voice_config = VoiceDialogueConfig(config_manager)
            self.log_result("语音配置管理器", True, "创建成功")
            
            # 测试配置读取
            stt_config = voice_config.get_stt_config()
            tts_config = voice_config.get_tts_config()
            mic_config = voice_config.get_microphone_config()
            
            self.log_result("配置读取", True, f"STT/TTS/麦克风配置读取成功")
            
            # 测试STT客户端创建（不加载模型）
            from dialogue_system.stt import FasterWhisperClient
            # 不实际创建客户端，只测试类导入
            self.log_result("STT客户端类", True, "类导入成功")
            
            # 测试TTS客户端创建
            from dialogue_system.tts import GPTSoVITSClient
            tts_client = GPTSoVITSClient(tts_config['api_url'])
            self.log_result("TTS客户端创建", True, "创建成功")
            
            return True
            
        except Exception as e:
            self.log_result("基础功能测试", False, "测试失败", str(e))
            return False
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*80)
        print("Live2D语音对话系统测试报告")
        print("="*80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"测试总数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        print(f"测试时间: {time.time() - self.start_time:.2f}秒")
        
        # 分类显示结果
        categories = {
            '依赖': [k for k in self.test_results.keys() if k.startswith('依赖')],
            '配置': [k for k in self.test_results.keys() if k.startswith('配置')],
            '模块': [k for k in self.test_results.keys() if k.startswith('模块')],
            'API': [k for k in self.test_results.keys() if 'API' in k],
            'UI': [k for k in self.test_results.keys() if k.startswith('UI')],
            '功能': [k for k in self.test_results.keys() if k.startswith('基础')]
        }
        
        for category, tests in categories.items():
            if tests:
                print(f"\n{category}测试:")
                for test in tests:
                    result = self.test_results[test]
                    status = "✅" if result['success'] else "❌"
                    print(f"  {status} {test}: {result['message']}")
        
        # 总结和建议
        print("\n" + "="*80)
        print("总结和建议:")
        print("="*80)
        
        if failed_tests == 0:
            print("🎉 所有测试通过！语音对话系统已准备就绪。")
        else:
            print("⚠️ 部分测试失败，请根据以下建议进行修复：")
            
            # 检查PyAudio
            pyaudio_failed = any('pyaudio' in str(result.get('details', '')).lower() 
                               for result in self.test_results.values() if not result['success'])
            if pyaudio_failed:
                print("  💡 安装PyAudio: pip install pyaudio")
                print("     如果安装失败，可能需要Visual Studio构建工具")
            
            # 检查模型路径
            model_failed = any('模型路径' in test for test, result in self.test_results.items() 
                             if not result['success'])
            if model_failed:
                print("  💡 检查STT模型路径配置")
            
            # 检查API连接
            api_failed = any('API' in test for test, result in self.test_results.items() 
                           if not result['success'])
            if api_failed:
                print("  💡 启动GPT-SoVITS服务: 确保localhost:9880可访问")
        
        print("\n📖 详细文档: VOICE_DIALOGUE_INTEGRATION_COMPLETE.md")
        print("🧪 简单测试: python simple_voice_test.py")
        print("🎤 开始使用: 右键Live2D模型 → 语音对话")

def main():
    """主函数"""
    print("Live2D语音对话系统完整测试")
    print("="*80)
    
    tester = VoiceDialogueSystemTester()
    
    # 执行所有测试
    tester.test_dependencies()
    tester.test_configuration()
    tester.test_module_imports()
    tester.test_api_connections()
    tester.test_ui_integration()
    tester.test_basic_functionality()
    
    # 生成报告
    tester.generate_report()

if __name__ == "__main__":
    main()
