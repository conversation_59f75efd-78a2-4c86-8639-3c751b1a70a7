#!/usr/bin/env python3
"""
Live2D语音对话系统 - 语音设置对话框

这个模块提供语音对话系统的设置界面，包括：
- STT设置
- TTS设置
- 麦克风设置
- 语音预设管理

使用示例：
    from dialogue_system.ui.voice_settings_dialog import VoiceSettingsDialog
    
    # 创建设置对话框
    dialog = VoiceSettingsDialog(config_manager, parent)
    dialog.show()
"""

import os
from typing import Dict, Any, Optional
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                              QWidget, QLabel, QLineEdit, QPushButton, QSpinBox,
                              QDoubleSpinBox, QCheckBox, QComboBox, QTextEdit,
                              QGroupBox, QFormLayout, QSlider, QProgressBar,
                              QListWidget, QListWidgetItem, QMessageBox,
                              QFileDialog, QSplitter)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont


class VoiceSettingsDialog(QDialog):
    """语音设置对话框"""
    
    # 信号
    settings_changed = Signal()
    
    def __init__(self, config_manager=None, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        
        # 导入配置管理器
        try:
            from dialogue_system.config import VoiceDialogueConfig
            self.voice_config = VoiceDialogueConfig(config_manager)
        except ImportError:
            self.voice_config = None
            
        self.setWindowTitle("语音对话设置")
        self.setModal(True)
        self.resize(800, 600)
        
        self.init_ui()
        self.load_settings()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # STT设置标签页
        self.create_stt_tab()
        
        # TTS设置标签页
        self.create_tts_tab()
        
        # 麦克风设置标签页
        self.create_microphone_tab()
        
        # 高级设置标签页
        self.create_advanced_tab()
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.test_btn = QPushButton("测试语音功能")
        self.test_btn.clicked.connect(self.test_voice_system)
        button_layout.addWidget(self.test_btn)
        
        button_layout.addStretch()
        
        self.reset_btn = QPushButton("重置为默认")
        self.reset_btn.clicked.connect(self.reset_to_default)
        button_layout.addWidget(self.reset_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_settings)
        button_layout.addWidget(self.save_btn)
        
        layout.addLayout(button_layout)
        
    def create_stt_tab(self):
        """创建STT设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # STT模型设置
        model_group = QGroupBox("STT模型设置")
        model_layout = QFormLayout(model_group)
        
        self.model_path_edit = QLineEdit()
        model_path_layout = QHBoxLayout()
        model_path_layout.addWidget(self.model_path_edit)
        
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self.browse_model_path)
        model_path_layout.addWidget(browse_btn)
        
        model_layout.addRow("模型路径:", model_path_layout)
        
        self.device_combo = QComboBox()
        self.device_combo.addItems(["auto", "cpu", "cuda"])
        model_layout.addRow("设备:", self.device_combo)
        
        self.compute_type_combo = QComboBox()
        self.compute_type_combo.addItems(["float16", "float32", "int8"])
        model_layout.addRow("计算类型:", self.compute_type_combo)
        
        layout.addWidget(model_group)
        
        # STT参数设置
        params_group = QGroupBox("识别参数")
        params_layout = QFormLayout(params_group)
        
        self.language_combo = QComboBox()
        self.language_combo.addItems(["auto", "zh", "en", "ja", "ko"])
        params_layout.addRow("语言:", self.language_combo)
        
        self.beam_size_spin = QSpinBox()
        self.beam_size_spin.setRange(1, 10)
        self.beam_size_spin.setValue(5)
        params_layout.addRow("束搜索大小:", self.beam_size_spin)
        
        self.no_speech_threshold_spin = QDoubleSpinBox()
        self.no_speech_threshold_spin.setRange(0.0, 1.0)
        self.no_speech_threshold_spin.setSingleStep(0.1)
        self.no_speech_threshold_spin.setValue(0.6)
        params_layout.addRow("静音阈值:", self.no_speech_threshold_spin)
        
        layout.addWidget(params_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "STT设置")
        
    def create_tts_tab(self):
        """创建TTS设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # TTS API设置
        api_group = QGroupBox("TTS API设置")
        api_layout = QFormLayout(api_group)
        
        self.api_url_edit = QLineEdit()
        api_layout.addRow("API地址:", self.api_url_edit)
        
        # 测试连接按钮
        test_api_btn = QPushButton("测试连接")
        test_api_btn.clicked.connect(self.test_tts_api)
        api_layout.addRow("", test_api_btn)
        
        layout.addWidget(api_group)
        
        # TTS参数设置
        params_group = QGroupBox("合成参数")
        params_layout = QFormLayout(params_group)
        
        self.text_lang_combo = QComboBox()
        self.text_lang_combo.addItems(["zh", "en", "ja", "ko"])
        params_layout.addRow("文本语言:", self.text_lang_combo)
        
        self.top_k_spin = QSpinBox()
        self.top_k_spin.setRange(1, 100)
        self.top_k_spin.setValue(20)
        params_layout.addRow("Top K:", self.top_k_spin)
        
        self.top_p_spin = QDoubleSpinBox()
        self.top_p_spin.setRange(0.0, 1.0)
        self.top_p_spin.setSingleStep(0.1)
        self.top_p_spin.setValue(0.6)
        params_layout.addRow("Top P:", self.top_p_spin)
        
        self.temperature_spin = QDoubleSpinBox()
        self.temperature_spin.setRange(0.0, 2.0)
        self.temperature_spin.setSingleStep(0.1)
        self.temperature_spin.setValue(0.6)
        params_layout.addRow("Temperature:", self.temperature_spin)
        
        self.speed_factor_spin = QDoubleSpinBox()
        self.speed_factor_spin.setRange(0.1, 3.0)
        self.speed_factor_spin.setSingleStep(0.1)
        self.speed_factor_spin.setValue(1.0)
        params_layout.addRow("语速:", self.speed_factor_spin)
        
        layout.addWidget(params_group)
        
        # 音频设置
        audio_group = QGroupBox("音频设置")
        audio_layout = QFormLayout(audio_group)
        
        self.auto_play_check = QCheckBox()
        self.auto_play_check.setChecked(True)
        audio_layout.addRow("自动播放:", self.auto_play_check)
        
        self.save_audio_check = QCheckBox()
        audio_layout.addRow("保存音频:", self.save_audio_check)
        
        self.output_dir_edit = QLineEdit()
        output_dir_layout = QHBoxLayout()
        output_dir_layout.addWidget(self.output_dir_edit)
        
        browse_output_btn = QPushButton("浏览...")
        browse_output_btn.clicked.connect(self.browse_output_dir)
        output_dir_layout.addWidget(browse_output_btn)
        
        audio_layout.addRow("输出目录:", output_dir_layout)
        
        layout.addWidget(audio_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "TTS设置")
        
    def create_microphone_tab(self):
        """创建麦克风设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 麦克风设备设置
        device_group = QGroupBox("麦克风设备")
        device_layout = QFormLayout(device_group)
        
        self.auto_select_device_check = QCheckBox()
        self.auto_select_device_check.setChecked(True)
        device_layout.addRow("自动选择设备:", self.auto_select_device_check)
        
        self.device_index_spin = QSpinBox()
        self.device_index_spin.setRange(-1, 99)
        self.device_index_spin.setValue(-1)
        device_layout.addRow("设备索引:", self.device_index_spin)
        
        # 刷新设备列表按钮
        refresh_devices_btn = QPushButton("刷新设备列表")
        refresh_devices_btn.clicked.connect(self.refresh_audio_devices)
        device_layout.addRow("", refresh_devices_btn)
        
        layout.addWidget(device_group)
        
        # 音频参数设置
        audio_params_group = QGroupBox("音频参数")
        audio_params_layout = QFormLayout(audio_params_group)
        
        self.sample_rate_combo = QComboBox()
        self.sample_rate_combo.addItems(["16000", "22050", "44100", "48000"])
        self.sample_rate_combo.setCurrentText("16000")
        audio_params_layout.addRow("采样率:", self.sample_rate_combo)
        
        self.chunk_size_spin = QSpinBox()
        self.chunk_size_spin.setRange(256, 8192)
        self.chunk_size_spin.setValue(1024)
        audio_params_layout.addRow("缓冲区大小:", self.chunk_size_spin)
        
        self.channels_spin = QSpinBox()
        self.channels_spin.setRange(1, 2)
        self.channels_spin.setValue(1)
        audio_params_layout.addRow("声道数:", self.channels_spin)
        
        layout.addWidget(audio_params_group)
        
        # 语音检测设置
        vad_group = QGroupBox("语音检测")
        vad_layout = QFormLayout(vad_group)
        
        self.volume_threshold_spin = QDoubleSpinBox()
        self.volume_threshold_spin.setRange(0.001, 0.1)
        self.volume_threshold_spin.setSingleStep(0.001)
        self.volume_threshold_spin.setValue(0.01)
        self.volume_threshold_spin.setDecimals(3)
        vad_layout.addRow("音量阈值:", self.volume_threshold_spin)
        
        self.silence_duration_spin = QDoubleSpinBox()
        self.silence_duration_spin.setRange(0.5, 10.0)
        self.silence_duration_spin.setSingleStep(0.1)
        self.silence_duration_spin.setValue(1.5)
        vad_layout.addRow("静音持续时间(秒):", self.silence_duration_spin)
        
        layout.addWidget(vad_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "麦克风设置")
        
    def create_advanced_tab(self):
        """创建高级设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 集成设置
        integration_group = QGroupBox("集成设置")
        integration_layout = QFormLayout(integration_group)
        
        self.llm_integration_check = QCheckBox()
        self.llm_integration_check.setChecked(True)
        integration_layout.addRow("LLM集成:", self.llm_integration_check)
        
        self.auto_send_to_llm_check = QCheckBox()
        self.auto_send_to_llm_check.setChecked(True)
        integration_layout.addRow("自动发送到LLM:", self.auto_send_to_llm_check)
        
        self.llm_response_tts_check = QCheckBox()
        self.llm_response_tts_check.setChecked(True)
        integration_layout.addRow("LLM回复TTS:", self.llm_response_tts_check)
        
        layout.addWidget(integration_group)
        
        # UI设置
        ui_group = QGroupBox("界面设置")
        ui_layout = QFormLayout(ui_group)
        
        self.show_voice_indicator_check = QCheckBox()
        self.show_voice_indicator_check.setChecked(True)
        ui_layout.addRow("显示语音指示器:", self.show_voice_indicator_check)
        
        self.show_volume_meter_check = QCheckBox()
        self.show_volume_meter_check.setChecked(True)
        ui_layout.addRow("显示音量表:", self.show_volume_meter_check)
        
        self.hotkey_enabled_check = QCheckBox()
        self.hotkey_enabled_check.setChecked(True)
        ui_layout.addRow("启用热键:", self.hotkey_enabled_check)
        
        layout.addWidget(ui_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "高级设置")
        
    def browse_model_path(self):
        """浏览STT模型路径"""
        path = QFileDialog.getExistingDirectory(self, "选择STT模型目录")
        if path:
            self.model_path_edit.setText(path)
            
    def browse_output_dir(self):
        """浏览音频输出目录"""
        path = QFileDialog.getExistingDirectory(self, "选择音频输出目录")
        if path:
            self.output_dir_edit.setText(path)
            
    def refresh_audio_devices(self):
        """刷新音频设备列表"""
        try:
            import pyaudio
            p = pyaudio.PyAudio()
            
            device_info = []
            for i in range(p.get_device_count()):
                info = p.get_device_info_by_index(i)
                if info['maxInputChannels'] > 0:
                    device_info.append(f"{i}: {info['name']}")
                    
            p.terminate()
            
            if device_info:
                QMessageBox.information(self, "音频设备", 
                    f"找到 {len(device_info)} 个输入设备:\n\n" + 
                    "\n".join(device_info))
            else:
                QMessageBox.warning(self, "音频设备", "未找到可用的音频输入设备")
                
        except ImportError:
            QMessageBox.warning(self, "错误", "PyAudio未安装，无法检测音频设备")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"检测音频设备失败: {e}")
            
    def test_tts_api(self):
        """测试TTS API连接"""
        try:
            import requests
            api_url = self.api_url_edit.text().strip()
            
            if not api_url:
                QMessageBox.warning(self, "错误", "请输入API地址")
                return
                
            response = requests.get(f"{api_url}/health", timeout=5)
            
            if response.status_code == 200:
                QMessageBox.information(self, "连接测试", "TTS API连接成功！")
            else:
                QMessageBox.warning(self, "连接测试", 
                    f"TTS API响应异常: {response.status_code}")
                    
        except requests.exceptions.ConnectionError:
            QMessageBox.critical(self, "连接测试", "无法连接到TTS API")
        except Exception as e:
            QMessageBox.critical(self, "连接测试", f"连接测试失败: {e}")
            
    def test_voice_system(self):
        """测试语音系统"""
        try:
            # 启动语音测试程序
            import subprocess
            import sys
            import os
            
            test_script = os.path.join(os.path.dirname(__file__), "..", "..", "simple_voice_test.py")
            if os.path.exists(test_script):
                subprocess.Popen([sys.executable, test_script])
                QMessageBox.information(self, "语音测试", "语音测试程序已启动")
            else:
                QMessageBox.warning(self, "语音测试", "语音测试程序未找到")
                
        except Exception as e:
            QMessageBox.critical(self, "语音测试", f"启动测试程序失败: {e}")
            
    def load_settings(self):
        """加载设置"""
        if not self.voice_config:
            return
            
        try:
            # 加载STT设置
            stt_config = self.voice_config.get_stt_config()
            self.model_path_edit.setText(stt_config.get("model_path", ""))
            self.device_combo.setCurrentText(stt_config.get("device", "auto"))
            self.compute_type_combo.setCurrentText(stt_config.get("compute_type", "float16"))
            self.language_combo.setCurrentText(stt_config.get("language", "auto"))
            self.beam_size_spin.setValue(stt_config.get("beam_size", 5))
            self.no_speech_threshold_spin.setValue(stt_config.get("no_speech_threshold", 0.6))
            
            # 加载TTS设置
            tts_config = self.voice_config.get_tts_config()
            self.api_url_edit.setText(tts_config.get("api_url", ""))
            
            default_params = tts_config.get("default_params", {})
            self.text_lang_combo.setCurrentText(default_params.get("text_lang", "zh"))
            self.top_k_spin.setValue(default_params.get("top_k", 20))
            self.top_p_spin.setValue(default_params.get("top_p", 0.6))
            self.temperature_spin.setValue(default_params.get("temperature", 0.6))
            self.speed_factor_spin.setValue(default_params.get("speed_factor", 1.0))
            
            audio_settings = tts_config.get("audio_settings", {})
            self.auto_play_check.setChecked(audio_settings.get("auto_play", True))
            self.save_audio_check.setChecked(audio_settings.get("save_audio", False))
            self.output_dir_edit.setText(audio_settings.get("output_dir", ""))
            
            # 加载麦克风设置
            mic_config = self.voice_config.get_microphone_config()
            self.auto_select_device_check.setChecked(mic_config.get("auto_select_device", True))
            self.device_index_spin.setValue(mic_config.get("device_index", -1) or -1)
            self.sample_rate_combo.setCurrentText(str(mic_config.get("sample_rate", 16000)))
            self.chunk_size_spin.setValue(mic_config.get("chunk_size", 1024))
            self.channels_spin.setValue(mic_config.get("channels", 1))
            self.volume_threshold_spin.setValue(mic_config.get("volume_threshold", 0.01))
            
            # 加载高级设置
            integration_settings = self.voice_config.get_integration_settings()
            self.llm_integration_check.setChecked(integration_settings.get("llm_integration", True))
            self.auto_send_to_llm_check.setChecked(integration_settings.get("auto_send_to_llm", True))
            self.llm_response_tts_check.setChecked(integration_settings.get("llm_response_tts", True))
            
            ui_settings = self.voice_config.get_ui_settings()
            self.show_voice_indicator_check.setChecked(ui_settings.get("show_voice_indicator", True))
            self.show_volume_meter_check.setChecked(ui_settings.get("show_volume_meter", True))
            self.hotkey_enabled_check.setChecked(ui_settings.get("hotkey_enabled", True))
            
        except Exception as e:
            print(f"❌ 加载语音设置失败: {e}")
            
    def save_settings(self):
        """保存设置"""
        if not self.voice_config:
            QMessageBox.warning(self, "错误", "语音配置管理器未初始化")
            return
            
        try:
            # 保存STT设置
            stt_updates = {
                "model_path": self.model_path_edit.text().strip(),
                "device": self.device_combo.currentText(),
                "compute_type": self.compute_type_combo.currentText(),
                "language": self.language_combo.currentText(),
                "beam_size": self.beam_size_spin.value(),
                "no_speech_threshold": self.no_speech_threshold_spin.value()
            }
            self.voice_config.update_stt_config(stt_updates)
            
            # 保存TTS设置
            tts_updates = {
                "api_url": self.api_url_edit.text().strip(),
                "default_params": {
                    "text_lang": self.text_lang_combo.currentText(),
                    "top_k": self.top_k_spin.value(),
                    "top_p": self.top_p_spin.value(),
                    "temperature": self.temperature_spin.value(),
                    "speed_factor": self.speed_factor_spin.value()
                },
                "audio_settings": {
                    "auto_play": self.auto_play_check.isChecked(),
                    "save_audio": self.save_audio_check.isChecked(),
                    "output_dir": self.output_dir_edit.text().strip()
                }
            }
            self.voice_config.update_tts_config(tts_updates)
            
            # 保存麦克风设置
            mic_updates = {
                "auto_select_device": self.auto_select_device_check.isChecked(),
                "device_index": self.device_index_spin.value() if self.device_index_spin.value() >= 0 else None,
                "sample_rate": int(self.sample_rate_combo.currentText()),
                "chunk_size": self.chunk_size_spin.value(),
                "channels": self.channels_spin.value(),
                "volume_threshold": self.volume_threshold_spin.value()
            }
            self.voice_config.update_microphone_config(mic_updates)
            
            # 保存高级设置
            integration_updates = {
                "llm_integration": self.llm_integration_check.isChecked(),
                "auto_send_to_llm": self.auto_send_to_llm_check.isChecked(),
                "llm_response_tts": self.llm_response_tts_check.isChecked()
            }
            self.voice_config.update_integration_settings(integration_updates)
            
            ui_updates = {
                "show_voice_indicator": self.show_voice_indicator_check.isChecked(),
                "show_volume_meter": self.show_volume_meter_check.isChecked(),
                "hotkey_enabled": self.hotkey_enabled_check.isChecked()
            }
            self.voice_config.update_ui_settings(ui_updates)
            
            # 发出设置变更信号
            self.settings_changed.emit()
            
            QMessageBox.information(self, "保存成功", "语音设置已保存")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存语音设置失败: {e}")
            
    def reset_to_default(self):
        """重置为默认设置"""
        reply = QMessageBox.question(self, "重置设置", 
            "确定要重置所有语音设置为默认值吗？\n此操作不可撤销。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            
        if reply == QMessageBox.StandardButton.Yes:
            if self.voice_config:
                self.voice_config.reset_to_default()
                self.load_settings()
                QMessageBox.information(self, "重置完成", "语音设置已重置为默认值")
