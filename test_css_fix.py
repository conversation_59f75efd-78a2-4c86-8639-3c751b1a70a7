#!/usr/bin/env python3
"""
测试CSS box-shadow错误修复
验证是否还有Unknown property box-shadow错误
"""

import sys
import os

def test_css_errors():
    """测试CSS错误修复"""
    print("🔍 测试CSS box-shadow错误修复")
    print("=" * 60)
    
    try:
        # 设置环境变量抑制Qt警告
        os.environ["QT_LOGGING_RULES"] = "qt.qpa.fonts.warning=false"
        
        from PySide6.QtWidgets import QApplication, QFrame
        from PySide6.QtCore import Qt
        
        print("✅ Qt模块导入成功")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        print("✅ QApplication创建成功")
        
        # 测试快速输入样式
        print("\n🧪 测试快速输入样式...")
        try:
            from dialogue_system.ui.quick_input import QuickInputWidget
            quick_input = QuickInputWidget()
            print("✅ QuickInputWidget创建成功，无CSS错误")
        except Exception as e:
            print(f"❌ QuickInputWidget创建失败: {e}")
        
        # 测试文本叠加层样式
        print("\n🧪 测试文本叠加层样式...")
        try:
            # 导入文本叠加层相关模块
            sys.path.append('dev')
            from text_overlay import QuickInputOverlay
            
            # 创建一个简单的父窗口用于测试
            parent_frame = QFrame()
            parent_frame.setGeometry(100, 100, 400, 300)
            
            overlay = QuickInputOverlay(parent_frame)
            print("✅ QuickInputOverlay创建成功，无CSS错误")
        except Exception as e:
            print(f"❌ QuickInputOverlay创建失败: {e}")
        
        print("\n🎉 CSS错误修复测试完成")
        print("💡 如果没有看到'Unknown property box-shadow'错误，说明修复成功")
        
        # 清理
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_files_for_box_shadow():
    """检查文件中是否还有box-shadow"""
    print("\n🔍 检查文件中是否还有box-shadow...")
    print("=" * 60)
    
    files_to_check = [
        "dialogue_system/ui/quick_input.py",
        "dev/text_overlay.py",
        "dev/快速输入栏优化说明.md"
    ]
    
    found_box_shadow = False
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'box-shadow' in content:
                        print(f"⚠️ {file_path}: 仍包含box-shadow")
                        found_box_shadow = True
                    else:
                        print(f"✅ {file_path}: 已移除box-shadow")
            except Exception as e:
                print(f"❌ 无法读取 {file_path}: {e}")
        else:
            print(f"⚠️ 文件不存在: {file_path}")
    
    if not found_box_shadow:
        print("\n🎉 所有文件都已移除box-shadow属性！")
    else:
        print("\n⚠️ 仍有文件包含box-shadow属性")
    
    return not found_box_shadow

if __name__ == "__main__":
    print("🚀 开始CSS box-shadow错误修复验证")
    print("=" * 80)
    
    # 检查文件
    files_clean = check_files_for_box_shadow()
    
    # 测试CSS
    css_test_success = test_css_errors()
    
    print("\n" + "=" * 80)
    if files_clean and css_test_success:
        print("🎉 CSS box-shadow错误修复成功！")
        print("💡 现在启动Live2D应用应该不会看到'Unknown property box-shadow'错误了")
    else:
        print("❌ CSS box-shadow错误修复可能不完整")
        if not files_clean:
            print("   - 仍有文件包含box-shadow属性")
        if not css_test_success:
            print("   - CSS测试失败")
