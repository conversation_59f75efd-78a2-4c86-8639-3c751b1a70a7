#!/usr/bin/env python3
"""
测试快速输入修复
验证LLM客户端现在能正确使用API配置
"""

import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_llm_client_config():
    """测试LLM客户端配置"""
    print("🔍 测试LLM客户端配置修复")
    print("=" * 60)
    
    try:
        # 导入配置管理器和LLM客户端
        from dialogue_system.config.config_manager import ConfigManager
        from dialogue_system.core.llm_client import LLMClient
        
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 创建LLM客户端
        llm_client = LLMClient(config_manager)
        
        print("✅ LLM客户端初始化成功")
        print(f"\n📋 当前LLM配置:")
        print(f"   - API地址: {llm_client.api_config.get('base_url')}")
        print(f"   - 模型: {llm_client.api_config.get('model')}")
        print(f"   - API密钥: {'已设置' if llm_client.api_config.get('api_key') else '未设置'}")
        print(f"   - 超时: {llm_client.api_config.get('timeout')}秒")
        
        print(f"\n📋 默认参数:")
        for key, value in llm_client.default_params.items():
            if value is not None:
                print(f"   - {key}: {value}")
        
        # 检查配置是否完整
        is_configured = llm_client.is_configured()
        print(f"\n🔧 配置状态: {'✅ 完整' if is_configured else '❌ 不完整'}")
        
        if not is_configured:
            print("❌ 配置不完整，无法进行测试")
            return False
        
        print("\n🚀 发送测试消息...")
        
        # 发送测试消息
        test_message = "你好"
        print(f"📤 发送消息: {test_message}")
        
        response = llm_client.chat(test_message)
        
        if response and not response.startswith("❌"):
            print(f"✅ 测试成功!")
            print(f"📥 收到回复: {response}")
            return True
        else:
            print(f"❌ 测试失败: {response}")
            return False
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保所有依赖已安装")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_configs():
    """比较两个配置节点"""
    print("\n🔍 比较配置节点")
    print("=" * 60)
    
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        llm_config = config.get("llm", {}).get("api_config", {})
        openai_config = config.get("openai_config", {})
        
        print("📋 llm.api_config:")
        for key, value in llm_config.items():
            if key == "api_key":
                value = "***已设置***" if value else "未设置"
            print(f"   - {key}: {value}")
        
        print("\n📋 openai_config:")
        for key, value in openai_config.items():
            if key == "api_key":
                value = "***已设置***" if value else "未设置"
            elif key == "default_params":
                continue  # 跳过参数详情
            print(f"   - {key}: {value}")
        
        # 检查关键配置是否一致
        key_fields = ["base_url", "api_key", "model"]
        all_match = True
        
        print("\n🔍 关键配置对比:")
        for field in key_fields:
            llm_val = llm_config.get(field)
            openai_val = openai_config.get(field)
            match = llm_val == openai_val
            all_match = all_match and match
            
            if field == "api_key":
                llm_val = "***已设置***" if llm_val else "未设置"
                openai_val = "***已设置***" if openai_val else "未设置"
            
            status = "✅" if match else "❌"
            print(f"   {status} {field}: llm='{llm_val}' vs openai='{openai_val}'")
        
        print(f"\n🎯 配置一致性: {'✅ 一致' if all_match else '❌ 不一致'}")
        return all_match
        
    except Exception as e:
        print(f"❌ 配置比较失败: {e}")
        return False

if __name__ == "__main__":
    # 比较配置
    config_consistent = compare_configs()
    
    # 测试LLM客户端
    if config_consistent:
        test_success = test_llm_client_config()
        
        if test_success:
            print("\n🎉 修复成功！快速输入现在应该能正常工作了！")
        else:
            print("\n❌ 仍有问题，需要进一步调试")
    else:
        print("\n⚠️ 配置不一致，可能仍有问题")
