#!/usr/bin/env python3
"""
Live2D语音对话系统 - 配置管理模块

这个模块提供语音对话系统的配置管理功能。

主要组件：
- VoiceDialogueConfig: 语音对话配置管理器

使用示例：
    from dialogue_system.config import VoiceDialogueConfig
    
    # 创建配置管理器
    config = VoiceDialogueConfig(config_manager)
    
    # 获取各种配置
    stt_config = config.get_stt_config()
    tts_config = config.get_tts_config()
    ui_settings = config.get_ui_settings()
"""

from .voice_dialogue_config import VoiceDialogueConfig

__all__ = [
    'VoiceDialogueConfig'
]

__version__ = '1.0.0'
__author__ = 'Live2D Voice Dialogue System'
