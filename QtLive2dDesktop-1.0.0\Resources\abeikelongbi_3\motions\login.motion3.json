{"Version": 3, "Meta": {"Duration": 15.167, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 245, "TotalSegmentCount": 29910, "TotalPointCount": 39170, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamBGHide", "Segments": [0, 0, 0, 0.767, 1, 2, 4.767, 1, 2, 4.8, 1, 2, 12.733, 1, 0, 13.4, 0, 2, 13.5, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBG2Hide", "Segments": [0, 1, 2, 4.767, 1, 2, 4.8, 1, 2, 9.433, 1, 0, 9.9, 0, 2, 13.4, 0, 2, 13.5, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN3", "Segments": [0, 0, 2, 4.733, 0, 0, 5.067, 0.201, 1, 5.789, 0.201, 6.511, 0.201, 7.233, 0.192, 1, 7.822, 0.185, 8.411, 0.161, 9, 0.134, 1, 9.256, 0.122, 9.511, 0, 9.767, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamCHHide", "Segments": [0, 0, 2, 3.567, 0, 2, 3.6, 1, 2, 4.767, 1, 2, 4.8, 1, 2, 13.4, 1, 2, 13.5, 1, 2, 15.167, 1]}, {"Target": "Parameter", "Id": "ParamCupDesk", "Segments": [0, 0, 0, 0.167, 1, 2, 3.567, 1, 2, 4.767, 1, 2, 4.8, 0, 2, 13.4, 0, 2, 13.5, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamCcharacterZ", "Segments": [0, 0, 0, 3.167, 25.586, 1, 3.234, 25.586, 3.3, 25.871, 3.367, 25, 1, 3.511, 23.113, 3.656, 16.138, 3.8, 9.992, 1, 3.867, 7.155, 3.933, 0, 4, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 9.9, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionX", "Segments": [0, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionY", "Segments": [0, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionX", "Segments": [0, 19.003, 1, 0.578, 19.003, 1.155, 18.743, 1.733, 18.003, 1, 1.8, 17.918, 1.866, 14.76, 1.933, 14.76, 0, 2.067, 15.87, 0, 3.433, 15.15, 1, 3.444, 15.15, 3.456, 15.956, 3.467, 18.074, 1, 3.478, 20.192, 3.489, 22.628, 3.5, 25.04, 1, 3.511, 27.452, 3.522, 30.09, 3.533, 32.568, 1, 3.544, 35.046, 3.556, 36.24, 3.567, 36.24, 2, 3.6, 2.458, 1, 3.667, 2.458, 3.733, 4.498, 3.8, 4.622, 1, 3.989, 4.972, 4.178, 5.13, 4.367, 5.419, 1, 4.411, 5.487, 4.456, 5.58, 4.5, 5.58, 2, 4.767, 5.58, 2, 4.8, 18.003, 2, 4.933, 18.003, 2, 5.1, 18.003, 2, 9.433, 18.003, 0, 9.833, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionY", "Segments": [0, 22, 1, 0.578, 22, 1.155, 21.754, 1.733, 21, 1, 1.811, 20.899, 1.889, -19.699, 1.967, -19.699, 1, 2.022, -19.699, 2.078, -17.333, 2.133, -17.276, 1, 2.4, -17.002, 2.666, -16.868, 2.933, -16.591, 1, 3.044, -16.475, 3.156, -16.183, 3.267, -15.886, 1, 3.311, -15.768, 3.356, -15.361, 3.4, -15.361, 1, 3.433, -15.361, 3.467, -17.141, 3.5, -22, 1, 3.522, -25.239, 3.545, -27.72, 3.567, -27.72, 2, 3.6, -4, 1, 3.678, -3.207, 3.755, -2.261, 3.833, -1.876, 1, 4.055, -0.775, 4.278, -0.45, 4.5, -0.45, 1, 4.589, -0.45, 4.678, -0.488, 4.767, -0.602, 2, 4.8, -12.332, 2, 4.933, -12.332, 2, 9.433, -12.332, 0, 9.833, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamAllSize", "Segments": [0, 20, 1, 0.422, 20, 0.845, 20.093, 1.267, 20.462, 1, 1.422, 20.598, 1.578, 20.905, 1.733, 20.905, 0, 1.933, 7, 0, 2.067, 7.528, 2, 3.433, 7.528, 0, 3.567, 30, 2, 3.6, 12.24, 2, 4.5, 12.24, 2, 4.767, 12.24, 2, 4.8, 30, 2, 4.867, 30, 2, 9.433, 30, 0, 9.833, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamALLSize2", "Segments": [0, 0, 2, 1.733, 0, 0, 1.933, 7, 2, 3.433, 7, 0, 3.567, 10, 2, 4.5, 10, 2, 4.767, 10, 2, 4.8, 2.16, 1, 4.844, 2.053, 4.889, 2.03, 4.933, 2.03, 2, 9.433, 2.03, 0, 9.833, 0, 2, 13.4, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamStrongCatShow", "Segments": [0, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 13.367, 0, 0, 13.933, 10, 2, 15.167, 10]}, {"Target": "Parameter", "Id": "ParamScare", "Segments": [0, 0, 2, 8, 0, 0, 8.067, 1, 2, 8.1, 1, 0, 8.167, 0, 0, 8.233, 1, 2, 8.267, 1, 0, 8.333, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 5, 1, 0, 5.467, 0.013, 2, 6.533, 0.013, 0, 7.833, 0.003, 0, 8.133, 1, 0, 8.433, 0.937, 0, 8.933, 1, 2, 9.033, 1, 0, 9.167, 0, 2, 9.2, 0, 0, 9.3, 0.8, 0, 9.367, 0.2, 0, 9.567, 1, 0, 9.633, 0.938, 0, 9.7, 1, 0, 9.767, 0, 0, 9.833, 0.062, 0, 9.9, 0.002, 0, 9.967, 0.067, 0, 10.033, 0.002, 0, 10.1, 0.049, 0, 10.133, 0, 0, 10.2, 0.062, 0, 10.233, 0, 0, 10.267, 0.067, 0, 10.333, 0.002, 0, 10.367, 0.061, 0, 10.4, 0, 0, 10.467, 0.062, 0, 10.533, 0.002, 0, 10.6, 0.067, 0, 10.667, 0.002, 0, 10.733, 0.049, 0, 10.767, 0, 0, 10.833, 0.062, 0, 10.867, 0, 1, 10.878, 0, 10.889, 0, 10.9, 0.067, 1, 10.922, 0.201, 10.945, 0.537, 10.967, 0.602, 1, 10.989, 0.667, 11.011, 0.662, 11.033, 0.662, 0, 11.1, 0.602, 0, 11.167, 0.666, 0, 11.233, 0.602, 1, 11.255, 0.602, 11.278, 0.599, 11.3, 0.649, 1, 11.389, 0.848, 11.478, 1, 11.567, 1, 0, 11.833, 0.9, 2, 12.233, 0.9, 0, 12.5, 1, 0, 12.867, 0.607, 2, 13.167, 0.607, 0, 13.667, 0, 2, 14.3, 0, 0, 14.567, 1, 2, 14.7, 1, 2, 15.1, 1, 2, 15.167, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 5, 1, 0, 5.467, 0.013, 2, 6.533, 0.013, 0, 7.833, 0.003, 0, 8.133, 1, 0, 8.433, 0.937, 0, 8.933, 1, 2, 9.033, 1, 0, 9.167, 0, 2, 9.2, 0, 0, 9.3, 0.8, 0, 9.367, 0.2, 0, 9.567, 1, 0, 9.633, 0.938, 0, 9.7, 1, 0, 9.767, 0, 0, 9.833, 0.062, 0, 9.9, 0.002, 0, 9.967, 0.067, 0, 10.033, 0.002, 0, 10.1, 0.049, 0, 10.133, 0, 0, 10.2, 0.062, 0, 10.233, 0, 0, 10.267, 0.067, 0, 10.333, 0.002, 0, 10.367, 0.061, 0, 10.4, 0, 0, 10.467, 0.062, 0, 10.533, 0.002, 0, 10.6, 0.067, 0, 10.667, 0.002, 0, 10.733, 0.049, 0, 10.767, 0, 0, 10.833, 0.062, 0, 10.867, 0, 1, 10.878, 0, 10.889, 0, 10.9, 0.067, 1, 10.922, 0.201, 10.945, 0.537, 10.967, 0.602, 1, 10.989, 0.667, 11.011, 0.662, 11.033, 0.662, 0, 11.1, 0.602, 0, 11.167, 0.667, 0, 11.233, 0.602, 1, 11.255, 0.602, 11.278, 0.599, 11.3, 0.649, 1, 11.389, 0.848, 11.478, 1, 11.567, 1, 0, 11.833, 0.9, 2, 12.233, 0.9, 0, 12.5, 1, 0, 12.867, 0.607, 2, 13.167, 0.607, 0, 13.667, 0, 2, 14.3, 0, 0, 14.567, 1, 2, 14.7, 1, 2, 15.1, 1, 2, 15.167, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 2, 7.833, 0, 0, 8.033, -1, 2, 8.633, -1, 0, 8.867, -0.5, 1, 8.911, -0.5, 8.956, -0.632, 9, -0.774, 1, 9.056, -0.951, 9.111, -1, 9.167, -1, 2, 9.867, -1, 2, 10.733, -1, 0, 11.267, -0.5, 0, 11.8, -1, 0, 12.067, -0.699, 2, 12.467, -0.699, 0, 13, -1, 2, 13.367, -1, 2, 13.567, -1, 0, 13.8, -0.5, 0, 13.967, -0.7, 1, 14.056, -0.7, 14.144, -0.675, 14.233, -0.5, 1, 14.366, -0.237, 14.5, 0, 14.633, 0, 2, 14.7, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 7.833, 0, 0, 8.033, 1, 0, 8.3, 0.762, 0, 8.5, 0.9, 0, 8.633, 0.7, 0, 8.9, 0.773, 0, 9.033, 0.5, 2, 9.167, 0.5, 1, 9.234, 0.5, 9.3, 0.517, 9.367, 0.661, 1, 9.4, 0.733, 9.434, 1, 9.467, 1, 1, 9.489, 1, 9.511, 1, 9.533, 0.995, 1, 9.6, 0.957, 9.666, 0, 9.733, 0, 0, 9.933, 1, 0, 10.1, 0.632, 0, 10.267, 0.988, 0, 10.433, 0.768, 0, 10.6, 0.805, 0, 11, 0, 2, 11.267, 0, 1, 11.367, 0, 11.467, 0.184, 11.567, 0.5, 1, 11.645, 0.746, 11.722, 0.845, 11.8, 0.845, 0, 12.233, 0.3, 2, 12.467, 0.3, 0, 12.967, 1, 1, 13.022, 1, 13.078, 0.971, 13.133, 0.843, 1, 13.178, 0.741, 13.222, 0.632, 13.267, 0.632, 0, 13.4, 1, 1, 13.456, 1, 13.511, 0.932, 13.567, 0.843, 1, 13.634, 0.736, 13.7, 0.7, 13.767, 0.7, 0, 14.033, 1, 1, 14.1, 1, 14.166, 0.435, 14.233, 0.3, 1, 14.366, 0.029, 14.5, 0, 14.633, 0, 2, 14.7, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamMouthSize", "Segments": [0, 0, 2, 7.833, 0, 0, 8, 0.5, 0, 8.2, 0, 2, 9.733, 0, 0, 10.267, -0.6, 0, 10.567, 0, 2, 11.267, 0, 0, 11.8, -0.5, 0, 12.2, -0.4, 2, 12.967, -0.4, 2, 13.3, -0.4, 0, 13.4, -0.653, 0, 13.533, -0.587, 0, 13.633, -1, 1, 13.689, -1, 13.744, -0.948, 13.8, -0.944, 1, 13.944, -0.933, 14.089, -0.936, 14.233, -0.92, 1, 14.389, -0.902, 14.544, 0, 14.7, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeEmotion", "Segments": [0, 0, 2, 7.833, 0, 2, 8.267, 0, 0, 8.4, -1, 2, 8.833, -1, 0, 8.933, 0, 0, 9.233, -0.6, 0, 9.733, 1, 2, 12.633, 1, 2, 12.933, 1, 2, 13.2, 1, 0, 13.5, -1, 0, 13.8, 0, 2, 14.7, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 2, 7.833, 0, 2, 8.033, 0, 2, 8.933, 0, 2, 9.5, 0, 0, 9.8, 1, 2, 12.933, 1, 0, 13.5, 0, 2, 14.7, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBlackFace", "Segments": [0, 0, 2, 8.267, 0, 0, 8.4, 1, 2, 8.767, 1, 0, 8.933, 0, 2, 12.033, 0, 2, 14.7, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamMark", "Segments": [0, 0, 2, 7.833, 0, 2, 8.033, 0, 2, 8.833, 0, 2, 8.933, 0, 0, 9.667, 1.2, 2, 11.533, 1.2, 0, 11.8, 1.5, 2, 11.833, 0, 2, 14.7, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamShameLine", "Segments": [0, 0, 2, 7.833, 0, 2, 8.033, 0, 2, 8.833, 0, 2, 8.933, 0, 2, 9.267, 0, 1, 9.334, 0, 9.4, 0.194, 9.467, 0.5, 2, 9.7, 1, 2, 9.933, 0.5, 2, 10.2, 1, 2, 10.433, 0.5, 2, 10.7, 1, 2, 10.933, 0.5, 2, 11.267, 0.5, 0, 11.4, 0, 2, 14.7, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamMarkShake", "Segments": [0, 0, 2, 7.833, 0, 2, 8.033, 0, 2, 8.833, 0, 2, 8.933, 0, 2, 9.733, 0, 0, 9.8, 1, 0, 10.067, -1, 0, 10.267, 1, 0, 10.4, 0, 2, 14.7, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLY", "Segments": [0, 0, 2, 9.333, 0, 0, 9.667, -0.252, 0, 9.833, 0, 2, 15.067, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeL", "Segments": [0, 0, 2, 9.3, 0, 0, 9.667, -0.8, 2, 10.767, -0.8, 0, 11.233, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRY", "Segments": [0, 0, 2, 9.333, 0, 0, 9.667, -0.396, 0, 9.833, 0, 2, 15.067, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeR", "Segments": [0, 0, 2, 9.3, 0, 0, 9.667, -0.7, 2, 10.767, -0.7, 0, 11.233, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow1", "Segments": [0, 0, 2, 9.733, 0, 1, 9.955, 0.333, 10.178, 0.667, 10.4, 1, 2, 10.433, 0, 2, 10.533, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow2", "Segments": [0, 0, 2, 9.867, 0, 1, 10.078, 0.333, 10.289, 0.667, 10.5, 1, 2, 10.533, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamTearLight", "Segments": [0, 0, 2, 9.5, 0, 0, 10.033, 1, 2, 10.3, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamTears", "Segments": [0, 0, 2, 9.3, 0, 0, 9.667, 1, 2, 10.767, 1, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPHYInputX", "Segments": [0, 0, 2, 9.333, 0, 0, 9.6, 13.791, 1, 9.633, 13.791, 9.667, 14.015, 9.7, 12.61, 1, 9.744, 10.737, 9.789, -4.297, 9.833, -10.791, 1, 9.866, -15.662, 9.9, -16.806, 9.933, -16.806, 0, 10.133, 16.986, 0, 10.333, -13.356, 0, 10.533, 16.113, 0, 10.733, -15.006, 1, 10.789, -15.006, 10.844, -13.148, 10.9, -10.791, 1, 11.078, -3.247, 11.255, 0, 11.433, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 2, 0, 2, 2.2, 0, 0, 3.5, 12, 1, 3.667, 12, 3.833, 11.632, 4, 7.213, 1, 4.122, 3.973, 4.245, -15.336, 4.367, -17.743, 1, 4.5, -20.369, 4.634, -21.005, 4.767, -22.803, 1, 4.778, -22.953, 4.789, -29.462, 4.8, -29.499, 1, 4.911, -29.864, 5.022, -30, 5.133, -30, 0, 5.4, -27.666, 0, 5.733, -30, 0, 6.033, -27.717, 0, 6.3, -30, 0, 6.767, -27.563, 0, 6.933, -30, 0, 7.267, -29.739, 2, 7.833, -29.739, 2, 8.033, -29.739, 2, 8.1, -29.739, 1, 8.244, -29.739, 8.389, -29.48, 8.533, -28.966, 1, 8.544, -28.926, 8.556, -28.857, 8.567, -28.851, 1, 8.656, -28.802, 8.744, -28.816, 8.833, -28.719, 1, 8.944, -28.597, 9.056, -7.369, 9.167, -2.57, 1, 9.234, 0.31, 9.3, 0.183, 9.367, 2.717, 1, 9.456, 6.096, 9.544, 10.791, 9.633, 10.791, 1, 9.666, 10.791, 9.7, 11.015, 9.733, 9.61, 1, 9.778, 7.737, 9.822, -7.297, 9.867, -13.791, 1, 9.9, -18.662, 9.934, -19.806, 9.967, -19.806, 0, 10.167, 13.986, 0, 10.367, -16.356, 0, 10.567, 13.113, 0, 10.767, -18.006, 1, 10.822, -18.006, 10.878, -16.649, 10.933, -13.791, 1, 11.111, -4.646, 11.289, 0, 11.467, 0, 2, 13.2, 0, 0, 14, -5.81, 1, 14.156, -5.81, 14.311, -5.874, 14.467, -5.02, 1, 14.589, -4.349, 14.711, 0.737, 14.833, 0.737, 0, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 2, 0, 0, 2.333, -10, 0, 2.833, 0, 1, 3.144, 0, 3.456, -0.539, 3.767, -3.926, 1, 3.889, -5.257, 4.011, -16, 4.133, -16, 0, 4.567, 4.184, 1, 4.634, 4.184, 4.7, 4.098, 4.767, 3.814, 1, 4.778, 3.767, 4.789, -1.856, 4.8, -1.962, 1, 4.933, -3.239, 5.067, -3.753, 5.2, -3.753, 0, 5.467, 3.031, 0, 5.767, -2.72, 0, 6.1, 3.225, 0, 6.433, -2.72, 0, 6.667, 2.575, 0, 6.967, -2.72, 1, 7.067, -2.72, 7.167, -1.884, 7.267, -1.758, 1, 7.456, -1.521, 7.644, -1.444, 7.833, -1.258, 1, 7.9, -1.192, 7.966, -1.11, 8.033, -1.11, 1, 8.111, -1.11, 8.189, -1.559, 8.267, -1.73, 1, 8.356, -1.926, 8.444, -1.992, 8.533, -2.119, 1, 8.566, -2.167, 8.6, -2.238, 8.633, -2.254, 1, 8.7, -2.286, 8.766, -2.29, 8.833, -2.29, 0, 9.033, 0, 0, 9.333, -4, 0, 9.667, 30, 2, 9.767, 30, 1, 9.811, 30, 9.856, 5.457, 9.9, 0, 1, 9.978, -9.55, 10.055, -20.774, 10.133, -21, 1, 10.311, -21.516, 10.489, -21.649, 10.667, -22.101, 1, 10.711, -22.214, 10.756, -26.011, 10.8, -26.011, 0, 11.233, 0, 0, 11.567, -2.72, 0, 12.2, 0, 0, 12.467, -2.72, 0, 12.933, 19.265, 1, 13.044, 19.265, 13.156, 18.227, 13.267, 14, 1, 13.411, 8.504, 13.556, -5.04, 13.7, -5.777, 1, 13.844, -6.514, 13.989, -6.418, 14.133, -6.977, 1, 14.2, -7.235, 14.266, -10.466, 14.333, -10.466, 0, 14.7, 1.228, 0, 15.067, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 3.433, 0, 1, 3.622, 0, 3.811, -7.383, 4, -10.036, 1, 4.189, -12.689, 4.378, -12.598, 4.567, -12.598, 1, 4.634, -12.598, 4.7, -12.564, 4.767, -12.448, 1, 4.778, -12.429, 4.789, -9.073, 4.8, -8.941, 1, 4.967, -6.967, 5.133, -5.575, 5.3, -3.311, 1, 5.422, -1.65, 5.545, 0.199, 5.667, 0.199, 0, 5.933, -2.312, 0, 6.3, 0.473, 0, 6.6, -2.312, 0, 6.867, 0.199, 1, 6.956, 0.199, 7.044, -1.246, 7.133, -2.312, 1, 7.178, -2.845, 7.222, -3.088, 7.267, -3.254, 1, 7.456, -3.959, 7.644, -4.207, 7.833, -4.207, 1, 7.9, -4.207, 7.966, -3.811, 8.033, -3.319, 1, 8.055, -3.155, 8.078, -3.155, 8.1, -3.155, 0, 8.3, -3.311, 1, 8.378, -3.311, 8.455, -3.293, 8.533, -3.244, 1, 8.633, -3.181, 8.733, -3.143, 8.833, -3.143, 0, 9.4, -3.24, 0, 9.7, 0.688, 0, 9.833, -0.668, 0, 10, 0, 2, 11.067, 0, 0, 11.867, -3.951, 0, 12.133, -2, 0, 12.433, -2.601, 0, 13.067, 4, 0, 13.767, -1, 2, 14.333, -1, 0, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamAngleS", "Segments": [0, 0, 2, 8.833, 0, 0, 9.3, 1, 0, 9.567, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperLAngle", "Segments": [0, 0, 2, 2.833, 0, 0, 3.1, -1.624, 1, 3.211, -1.624, 3.322, -0.629, 3.433, 0.526, 1, 3.611, 2.373, 3.789, 3, 3.967, 3, 0, 4.733, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 8.833, 0, 0, 9.267, -2, 1, 9.311, -2, 9.356, 0.159, 9.4, 1, 1, 9.489, 2.682, 9.578, 3, 9.667, 3, 0, 9.967, 0, 2, 10.9, 0, 0, 11.267, -1.624, 0, 11.567, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamArmHandLAngle", "Segments": [0, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 9.5, 0, 1, 9.556, 0, 9.611, 0.155, 9.667, 1.006, 1, 9.734, 2.027, 9.8, 3, 9.867, 3, 0, 10.167, 0, 0, 10.333, 0.462, 0, 10.633, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerLAngle", "Segments": [0, 10, 2, 2.9, 10, 0, 3.267, 11, 0, 3.567, 10, 0, 4.067, 12, 1, 4.3, 12, 4.534, 11.359, 4.767, 10.086, 1, 4.778, 10.025, 4.789, 10, 4.8, 10, 2, 8.833, 10, 0, 9.2, 9.874, 0, 9.333, 12, 0, 9.667, 2.111, 0, 10, 3.796, 0, 10.2, 3.06, 0, 11.133, 3.233, 0, 11.3, 2.659, 0, 11.7, 10, 2, 15.167, 10]}, {"Target": "Parameter", "Id": "ParamArmLowerLH", "Segments": [0, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 8.833, 0, 0, 9.233, -11.711, 0, 9.567, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerLAngle", "Segments": [0, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 8.833, 0, 0, 9.667, 8, 2, 10.767, 8, 0, 12.2, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamChili", "Segments": [0, 0, 2, 4.767, 0, 0, 4.8, 1, 2, 5.1, 1, 2, 7.033, 1, 0, 7.167, 1.9, 2, 9.033, 1.9, 2, 9.067, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamChiliX", "Segments": [0, 0, 2, 4.767, 0, 0, 4.9, 0.306, 0, 5.2, -0.307, 0, 5.5, 0.208, 0, 5.833, -0.244, 0, 6.133, 0.306, 0, 6.4, -0.26, 0, 6.7, 0.295, 1, 6.767, 0.295, 6.833, 0.27, 6.9, 0.105, 1, 6.944, -0.004, 6.989, -0.195, 7.033, -0.195, 0, 7.367, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamArm<PERSON>pperRAngle", "Segments": [0, 0, 2, 2.7, 0, 0, 3.1, -1.715, 1, 3.267, -1.715, 3.433, -0.904, 3.6, 0.565, 1, 3.811, 2.426, 4.022, 3.285, 4.233, 3.285, 0, 4.6, 2.497, 0, 4.767, 3.285, 2, 4.8, 2, 2, 4.833, 2, 0, 5.033, 2.09, 0, 5.267, 1.85, 0, 5.567, 2.15, 0, 5.867, 1.85, 0, 6.233, 2.12, 0, 6.567, 2, 0, 6.833, 2.106, 0, 7.167, 2, 0, 7.3, 2.329, 0, 7.567, 2.149, 0, 7.833, 2.327, 0, 8.033, 2.288, 0, 8.167, 2.487, 0, 8.367, 1.861, 1, 8.467, 1.861, 8.567, 1.879, 8.667, 1.91, 1, 8.722, 1.927, 8.778, 1.934, 8.833, 1.934, 0, 9.5, 1, 1, 9.578, 1, 9.655, 1.001, 9.733, 2, 1, 9.822, 3.141, 9.911, 5.17, 10, 5.17, 0, 10.167, 5, 2, 11.233, 5, 0, 11.7, 4, 2, 12.2, 4, 0, 12.633, 1.003, 1, 12.689, 1.003, 12.744, 2.069, 12.8, 2.329, 1, 12.9, 2.796, 13, 2.835, 13.1, 2.835, 0, 13.433, 1.275, 1, 13.478, 1.275, 13.522, 1.411, 13.567, 1.448, 1, 13.722, 1.579, 13.878, 1.613, 14.033, 1.613, 0, 14.667, -0.11, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRAngle", "Segments": [0, 7.4, 2, 1.9, 7.4, 1, 2.189, 7.4, 2.478, -6.257, 2.767, -23.651, 1, 2.867, -29.672, 2.967, -30, 3.067, -30, 2, 3.2, -30, 1, 3.322, -30, 3.445, -26.33, 3.567, -24.799, 1, 3.667, -23.546, 3.767, -23.651, 3.867, -23.651, 1, 4, -23.651, 4.134, -24.2, 4.267, -25.064, 1, 4.367, -25.712, 4.467, -25.939, 4.567, -25.939, 1, 4.634, -25.939, 4.7, -25.531, 4.767, -24.281, 2, 4.8, -11.912, 0, 5.167, -12.573, 0, 5.5, -11.865, 0, 5.833, -12.573, 0, 6.133, -11.865, 0, 6.433, -12.68, 0, 6.767, -11.925, 1, 6.834, -11.925, 6.9, -12.026, 6.967, -12.173, 1, 7.045, -12.344, 7.122, -12.548, 7.2, -12.641, 1, 7.222, -12.668, 7.245, -12.657, 7.267, -12.674, 1, 7.356, -12.743, 7.444, -12.794, 7.533, -12.794, 0, 7.833, -12.674, 2, 8.033, -12.674, 0, 8.167, -12.914, 0, 8.333, -12.554, 0, 8.667, -12.914, 0, 9.067, -12.8, 0, 9.367, -13.33, 1, 9.4, -13.33, 9.434, -12.708, 9.467, -11.12, 1, 9.5, -9.532, 9.534, -7.241, 9.567, -5.945, 1, 9.645, -2.921, 9.722, -1.823, 9.8, -1.823, 0, 10, -3.26, 0, 10.2, -1.823, 0, 11.067, -2, 0, 11.233, -1.323, 0, 11.667, -5.246, 0, 11.867, -5.02, 0, 12.133, -5.021, 1, 12.211, -5.021, 12.289, -5.088, 12.367, -4.889, 1, 12.5, -4.547, 12.634, -1.966, 12.767, -0.9, 1, 12.867, -0.101, 12.967, 0.001, 13.067, 1, 1, 13.145, 1.777, 13.222, 7.4, 13.3, 7.4, 0, 13.467, 7.22, 0, 13.633, 7.4, 0, 13.8, 7.22, 2, 13.967, 7.22, 1, 14.078, 7.22, 14.189, 7.217, 14.3, 7.31, 1, 14.411, 7.403, 14.522, 7.618, 14.633, 7.618, 0, 15, 7.4, 2, 15.167, 7.4]}, {"Target": "Parameter", "Id": "ParamArmHandRAngle", "Segments": [0, 0, 2, 2.7, 0, 1, 3.033, 0, 3.367, 0.217, 3.7, 1.753, 1, 3.822, 2.316, 3.945, 10, 4.067, 10, 1, 4.256, 10, 4.444, 9.534, 4.633, 8, 1, 4.678, 7.639, 4.722, 6.018, 4.767, 4.741, 2, 4.8, -2.684, 1, 4.844, -3.598, 4.889, -4.71, 4.933, -5, 1, 5.022, -5.58, 5.111, -5.667, 5.2, -5.667, 0, 5.533, -4.667, 0, 5.833, -5.667, 0, 6.1, -5, 0, 6.4, -5.667, 1, 6.467, -5.667, 6.533, -5.381, 6.6, -5, 1, 6.644, -4.746, 6.689, -4.526, 6.733, -4.372, 1, 6.789, -4.18, 6.844, -4.134, 6.9, -4.134, 0, 7.2, -7.331, 0, 7.5, -3.772, 2, 8.133, -3.772, 0, 8.3, -1.363, 1, 8.378, -1.363, 8.455, -1.778, 8.533, -1.934, 1, 8.578, -2.023, 8.622, -2, 8.667, -2, 2, 8.833, -2, 0, 9.2, 4, 1, 9.267, 4, 9.333, 3.403, 9.4, 0, 1, 9.467, -3.403, 9.533, -7.331, 9.6, -7.331, 0, 9.867, -4.667, 0, 10.167, -10, 0, 10.333, -9, 0, 10.567, -10, 2, 11.067, -10, 0, 11.4, -6.983, 0, 11.733, -10, 0, 11.967, -9.637, 0, 12.5, -10, 1, 12.589, -10, 12.678, -6.132, 12.767, -3, 1, 12.8, -1.825, 12.834, -2, 12.867, -2, 0, 13.033, -4.462, 1, 13.089, -4.462, 13.144, -3.081, 13.2, -2, 1, 13.233, -1.351, 13.267, -1.363, 13.3, -1.363, 0, 13.467, -2, 0, 13.633, -1.363, 0, 13.8, -2, 0, 13.967, -1.363, 2, 14.267, -1.363, 0, 14.633, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRH", "Segments": [0, 0, 2, 2.7, 0, 1, 2.833, 0, 2.967, -7.159, 3.1, -8, 1, 3.389, -9.823, 3.678, -10, 3.967, -10, 0, 4.767, -0.344, 2, 4.8, -15, 2, 4.867, -15, 0, 5.167, -15.754, 0, 5.467, -14.434, 0, 5.8, -15.754, 0, 6.033, -14.874, 0, 6.333, -15.554, 0, 6.633, -14.874, 2, 6.8, -14.874, 0, 7.267, -10.424, 2, 7.833, -10.424, 2, 8.033, -10.424, 2, 8.667, -10.424, 2, 8.833, -10.424, 0, 9.233, -23, 0, 9.567, -16, 0, 9.933, -27, 2, 12.167, -27, 0, 12.467, -10, 2, 13.067, -10, 0, 13.433, -13, 1, 13.655, -13, 13.878, -13.095, 14.1, -12.329, 1, 14.278, -11.716, 14.455, 0, 14.633, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperRH", "Segments": [0, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 6.8, 0, 0, 7.267, 1.665, 2, 8.833, 1.665, 0, 9.567, -5, 0, 9.933, 14, 2, 11.567, 14, 2, 12.2, 14, 0, 12.767, 5.194, 1, 12.9, 5.194, 13.034, 5.25, 13.167, 6.449, 1, 13.311, 7.748, 13.456, 9.755, 13.6, 9.755, 2, 14.067, 9.755, 0, 14.633, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamHandR", "Segments": [0, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRAngle", "Segments": [0, 0, 2, 4.767, 0, 2, 4.8, -6.213, 0, 5.1, 7.899, 0, 5.4, -4.343, 0, 5.733, 6.879, 0, 6.033, -6.213, 0, 6.3, 7.139, 0, 6.6, -6, 1, 6.667, -6, 6.733, -4.67, 6.8, -2.39, 1, 6.889, 0.65, 6.978, 2, 7.067, 2, 0, 7.267, -4, 1, 7.456, -4, 7.644, -3.951, 7.833, -3.603, 1, 7.9, -3.48, 7.966, -1.789, 8.033, -1.789, 0, 8.233, -2.39, 1, 8.3, -2.39, 8.366, -0.479, 8.433, 0.345, 1, 8.511, 1.306, 8.589, 1.345, 8.667, 1.345, 1, 8.722, 1.345, 8.778, 1.42, 8.833, 1.301, 1, 9.111, 0.707, 9.389, -1, 9.667, -1, 0, 12.2, 0.496, 0, 12.633, 0, 0, 12.967, 4, 1, 13.056, 4, 13.144, 2.898, 13.233, 2.009, 1, 13.322, 1.12, 13.411, 1, 13.5, 1, 0, 13.667, 1.116, 0, 13.833, 0.345, 2, 14.167, 0.345, 0, 14.3, 2.683, 0, 14.633, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRY", "Segments": [0, 0, 2, 2.767, 0, 0, 4, 10, 0, 4.3, -10, 0, 4.767, -0.827, 2, 4.8, -7.356, 1, 4.822, -8.12, 4.845, -8.203, 4.867, -8.203, 0, 5.167, 7.711, 0, 5.5, -7.711, 0, 5.8, 8.203, 0, 6.2, -5.783, 0, 6.467, 7.711, 0, 6.7, -7.711, 1, 6.856, -7.711, 7.011, -5.669, 7.167, 0, 1, 7.211, 1.62, 7.256, 4, 7.3, 4, 0, 7.867, -10, 2, 8.2, -10, 0, 8.433, 10, 2, 8.667, 10, 2, 8.833, 10, 2, 12.2, 10, 0, 12.7, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamHand_Cl", "Segments": [0, 0, 2, 2.7, 0, 2, 2.733, 30, 2, 9.5, 30, 2, 9.533, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamHandT1R", "Segments": [0, 0, 2, 2.767, 0, 0, 3.333, 1, 2, 4.767, 1, 2, 4.8, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamHandT2R", "Segments": [0, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 9.433, 0, 0, 9.667, 0.799, 1, 9.734, 0.799, 9.8, 0.681, 9.867, 0.415, 1, 9.934, 0.149, 10, 0, 10.067, 0, 2, 12.433, 0, 1, 12.6, 0, 12.766, 0.124, 12.933, 0.522, 1, 13.011, 0.708, 13.089, 0.927, 13.167, 0.927, 2, 13.333, 0.927, 0, 13.5, 1, 0, 13.667, 0.9, 0, 13.833, 1, 0, 14.1, 0.9, 2, 14.3, 0.9, 0, 14.633, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 2, 0, 0, 2.3, 5, 2, 3.5, 5, 1, 3.744, 5, 3.989, 0.672, 4.233, -4, 1, 4.355, -6.336, 4.478, -6.466, 4.6, -6.466, 0, 4.767, -5.806, 0, 4.8, -6.345, 1, 4.833, -6.345, 4.867, -4.717, 4.9, -4.263, 1, 5, -2.902, 5.1, -2.569, 5.2, -2.569, 0, 5.633, -4, 0, 6, -2.874, 0, 6.3, -3.849, 0, 6.633, -2.765, 1, 6.8, -2.765, 6.966, -4.046, 7.133, -5.155, 1, 7.178, -5.451, 7.222, -5.364, 7.267, -5.364, 2, 8.033, -5.364, 0, 8.3, -5.155, 0, 8.533, -5.364, 1, 8.689, -5.364, 8.844, -3.377, 9, 0, 1, 9.1, 2.171, 9.2, 3, 9.3, 3, 0, 9.667, -7.13, 0, 9.8, -4.597, 0, 9.933, -6.466, 0, 10.167, 5, 0, 10.4, -5.606, 0, 10.6, 5.783, 0, 10.8, -1.869, 0, 11.067, 1.383, 0, 11.5, -5.534, 0, 11.633, -5, 1, 11.844, -5, 12.056, -5.102, 12.267, -5.397, 1, 12.322, -5.475, 12.378, -5.606, 12.433, -5.606, 0, 13.033, 1.383, 0, 13.733, -4, 2, 14.267, -4, 0, 14.833, 0.228, 0, 15.033, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 2, 0, 2, 2.1, 0, 0, 2.433, -1.819, 1, 2.566, -1.819, 2.7, -1.4, 2.833, 0, 1, 3, 1.75, 3.166, 3, 3.333, 3, 0, 3.767, -8, 2, 3.9, -8, 0, 4.467, 1, 0, 4.767, 0.087, 1, 4.778, 0.087, 4.789, 0.739, 4.8, 0.762, 1, 4.933, 1.036, 5.067, 1.144, 5.2, 1.144, 0, 5.5, -0.751, 0, 5.8, 0.887, 0, 6.067, -1.205, 0, 6.4, 1.45, 0, 6.7, -1.205, 0, 7.267, 0.049, 2, 7.833, 0.049, 2, 8.033, 0.049, 0, 8.133, -0.671, 0, 8.333, 2.814, 0, 8.533, 1.221, 0, 9.067, 4, 0, 9.367, -1, 0, 9.7, 8.668, 0, 9.967, 0, 2, 10.567, 0, 0, 11, -2.058, 0, 11.4, 0, 0, 12.2, -4, 0, 12.833, 9, 2, 13.033, 9, 0, 13.433, -5.777, 0, 13.767, -4, 0, 13.967, -5.777, 1, 14.078, -5.777, 14.189, -5.718, 14.3, -4, 1, 14.411, -2.282, 14.522, 1, 14.633, 1, 0, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 2, 0, 0, 2.467, 1.033, 0, 3.567, 0, 0, 3.867, 3, 1, 3.989, 3, 4.111, 0.91, 4.233, -1, 1, 4.278, -1.694, 4.322, -1.599, 4.367, -1.599, 0, 4.767, 0.656, 1, 4.778, 0.656, 4.789, -2.836, 4.8, -2.997, 1, 4.867, -3.961, 4.933, -4.276, 5, -4.276, 0, 5.367, -3.564, 0, 5.667, -4.167, 0, 5.967, -3.186, 0, 6.3, -4.167, 0, 6.533, -3.36, 0, 7.267, -5.431, 2, 7.833, -5.431, 2, 8.033, -5.431, 0, 8.367, -4.276, 0, 8.533, -4.431, 0, 9, 0, 0, 9.233, -0.988, 0, 9.733, 2.065, 1, 10.078, 2.065, 10.422, 2.115, 10.767, 1.653, 1, 11.045, 1.281, 11.322, -2, 11.6, -2, 2, 11.833, -2, 0, 12.833, -1, 0, 13.5, -2, 2, 14.033, -2, 0, 15, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX2", "Segments": [0, 0, 2, 2.833, 0, 0, 3.267, 10, 0, 4.067, -7, 0, 4.467, 2.709, 1, 4.567, 2.709, 4.667, 2, 4.767, 0.216, 1, 4.778, 0.018, 4.789, -2.136, 4.8, -2.157, 1, 4.956, -2.457, 5.111, -2.605, 5.267, -3, 1, 5.422, -3.395, 5.578, -4, 5.733, -4, 0, 6.133, -2.912, 1, 6.244, -2.912, 6.356, -2.761, 6.467, -3.8, 1, 6.7, -5.983, 6.934, -9, 7.167, -9, 0, 7.267, -8.841, 2, 8.533, -8.841, 0, 9.3, 5, 0, 9.667, -10, 2, 9.833, -10, 0, 10.2, -2, 0, 10.433, -3, 0, 10.567, -1.129, 0, 10.8, -2.825, 1, 10.856, -2.825, 10.911, -2.172, 10.967, -2.164, 1, 11.378, -2.102, 11.789, -2.07, 12.2, -2, 1, 12.322, -1.979, 12.445, -1.787, 12.567, -1.129, 1, 12.756, -0.112, 12.944, 10, 13.133, 10, 0, 13.933, -4, 2, 14.467, -4, 0, 14.733, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY2", "Segments": [0, 0, 2, 2.833, 0, 0, 3.233, 10, 0, 3.733, -8, 2, 3.9, -8, 0, 4.367, 4.17, 0, 4.767, -1.089, 0, 4.8, 0, 2, 8.533, 0, 0, 9.167, -1, 0, 9.567, 9.064, 0, 11.367, -3, 0, 11.8, 0, 0, 12.3, -5.133, 0, 12.967, 10, 0, 13.433, -4, 2, 13.967, -4, 0, 14.667, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechX", "Segments": [0, 0, 2, 2, 0, 0, 2.6, -1.09, 1, 2.667, -1.09, 2.733, -0.815, 2.8, 0, 1, 2.844, 0.543, 2.889, 1, 2.933, 1, 0, 3.733, -10, 2, 3.933, -10, 0, 4.3, 1.77, 0, 4.533, -0.611, 1, 4.611, -0.611, 4.689, -0.026, 4.767, 0.895, 1, 4.778, 1.027, 4.789, 0.836, 4.8, 1.147, 1, 4.967, 5.805, 5.133, 10, 5.3, 10, 0, 5.767, 8.308, 0, 6.133, 9.83, 0, 6.433, 8.537, 0, 6.767, 9.975, 0, 7.267, 8.109, 2, 8.533, 8.109, 0, 9.3, 10, 1, 9.422, 10, 9.545, 7.863, 9.667, 5, 1, 9.678, 4.74, 9.689, 4.838, 9.7, 4.838, 0, 9.767, 5.528, 0, 9.833, 4.541, 0, 9.9, 5.602, 0, 9.967, 4.541, 0, 10.033, 5.318, 0, 10.067, 4.29, 0, 10.133, 5.528, 0, 10.167, 4.5, 0, 10.2, 5.602, 0, 10.267, 4.541, 0, 10.3, 5, 0, 10.333, 4.838, 0, 10.4, 5.528, 0, 10.467, 4.541, 0, 10.533, 5.602, 0, 10.6, 4.541, 0, 10.667, 5.318, 0, 10.7, 4.29, 0, 10.767, 5.528, 0, 10.8, 4.5, 0, 10.833, 5.602, 0, 10.9, 4.541, 0, 10.967, 5.528, 0, 11.033, 4.541, 0, 11.1, 5.602, 0, 11.167, 4.541, 0, 11.233, 5.318, 0, 11.267, 4.29, 0, 11.333, 5.528, 0, 11.4, 5, 2, 11.7, 5, 1, 11.767, 5, 11.833, 6.842, 11.9, 7.331, 1, 12, 8.065, 12.1, 8.096, 12.2, 8.096, 1, 12.233, 8.096, 12.267, 8.346, 12.3, 7.912, 1, 12.444, 6.029, 12.589, -0.266, 12.733, -0.266, 0, 13.833, 0, 0, 14.367, -2.708, 0, 14.7, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechW", "Segments": [0, 0, 2, 3.7, 0, 0, 3.833, -1, 0, 4, 4, 0, 4.233, -4, 0, 4.433, 1.514, 0, 4.7, -1, 1, 4.722, -1, 4.745, -0.963, 4.767, -0.687, 1, 4.778, -0.549, 4.789, 0, 4.8, 0, 2, 8.533, 0, 0, 9.3, 4, 0, 9.567, -6, 0, 9.733, -3, 0, 9.9, -5, 0, 10.067, -1.665, 0, 10.267, -2.256, 0, 10.7, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, 0, 2, 2, 0, 0, 2.633, 1, 0, 3.6, 0, 0, 3.9, 1.937, 0, 4.233, -1.725, 0, 4.333, -1.464, 0, 4.5, -2.171, 1, 4.589, -2.171, 4.678, -1.701, 4.767, -0.457, 1, 4.778, -0.301, 4.789, 0.725, 4.8, 0.725, 0, 5.233, -0.246, 0, 5.7, 0, 2, 8.533, 0, 0, 9.3, -1, 0, 9.667, 2, 0, 9.7, 1.848, 0, 9.733, 2.031, 0, 9.767, 1.885, 0, 9.833, 2.042, 0, 9.867, 1.885, 0, 9.9, 2, 0, 9.933, 1.848, 0, 10, 2.031, 0, 10.033, 1.879, 0, 10.067, 2.042, 0, 10.133, 1.885, 0, 10.167, 2, 0, 10.2, 1.848, 0, 10.233, 2.031, 0, 10.267, 1.885, 0, 10.333, 2.042, 0, 10.367, 1.885, 0, 10.8, 1.937, 0, 11.733, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamShoulderR", "Segments": [0, 0, 2, 4.767, 0, 1, 4.778, 0, 4.789, 12.286, 4.8, 12.66, 1, 4.933, 17.153, 5.067, 19, 5.2, 19, 2, 6.867, 19, 0, 7.267, 21.007, 2, 8.533, 21.007, 1, 8.666, 21.007, 8.8, 16.809, 8.933, 0, 1, 9.055, -15.409, 9.178, -30, 9.3, -30, 0, 9.667, 30, 0, 10.1, -20.637, 0, 10.5, 0, 2, 12.2, 0, 0, 12.8, 30, 1, 12.9, 30, 13, 30, 13.1, 29.976, 1, 13.222, 29.933, 13.345, -8, 13.467, -8, 0, 13.8, -4, 0, 14, -8, 1, 14.111, -8, 14.222, -6.798, 14.333, -4, 1, 14.433, -1.481, 14.533, 0, 14.633, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamShoulderL", "Segments": [0, 0, 2, 2.633, 0, 1, 3.344, 0, 4.056, 2.732, 4.767, 8.418, 1, 4.778, 8.507, 4.789, 13.375, 4.8, 13.41, 1, 4.933, 13.834, 5.067, 14, 5.2, 14, 2, 6.867, 14, 0, 7.267, 13.062, 2, 8.533, 13.062, 1, 8.666, 13.062, 8.8, 11.927, 8.933, 0, 1, 9.055, -10.933, 9.178, -30, 9.3, -30, 0, 9.667, 30, 0, 10.1, -20.637, 0, 10.5, 0, 2, 12.2, 0, 2, 14.167, 0, 2, 15.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Z", "Segments": [0, 0, 2, 2, 0, 0, 2.833, 4.689, 1, 2.878, 4.689, 2.922, 4.707, 2.967, 4.659, 1, 3.122, 4.491, 3.278, 4.24, 3.433, 4.24, 0, 3.833, 6.184, 0, 4.2, -0.749, 0, 4.3, 0.341, 0, 4.667, -0.464, 1, 4.7, -0.464, 4.734, -0.432, 4.767, -0.281, 1, 4.778, -0.231, 4.789, 0, 4.8, 0, 2, 7.267, 0, 2, 8.1, 0, 2, 9.1, 0, 0, 9.467, 7.446, 0, 9.6, 7, 2, 10.6, 7, 1, 10.978, 7, 11.355, 7.087, 11.733, 6.734, 1, 12.144, 6.35, 12.556, 0, 12.967, 0, 2, 13.833, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Z", "Segments": [0, 0, 2, 2, 0, 1, 2.278, 0, 2.555, -2.53, 2.833, -7.287, 1, 2.944, -9.19, 3.056, -10, 3.167, -10, 1, 3.289, -10, 3.411, -9.912, 3.533, -7.173, 1, 3.789, -1.447, 4.044, 4, 4.3, 4, 0, 4.567, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 7.267, 0, 2, 8.1, 0, 2, 9.2, 0, 0, 9.633, -17, 0, 9.833, -16.202, 0, 10.1, -17, 0, 10.233, -16.403, 0, 10.5, -16.629, 0, 10.7, -16.202, 1, 11.044, -16.202, 11.389, -16.4, 11.733, -17.422, 1, 11.844, -17.752, 11.956, -19.987, 12.067, -19.987, 0, 13.133, 0, 2, 13.833, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Z", "Segments": [0, 0, 2, 2, 0, 1, 2.278, 0, 2.555, 2.385, 2.833, 3.544, 1, 2.878, 3.729, 2.922, 3.611, 2.967, 3.611, 0, 3.833, -10, 0, 4.3, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 7.267, 0, 2, 8.1, 0, 1, 8.822, 0, 9.545, -0.398, 10.267, -0.615, 1, 10.345, -0.638, 10.422, -0.624, 10.5, -0.628, 1, 10.556, -0.631, 10.611, -0.632, 10.667, -0.632, 2, 11.033, -0.632, 2, 11.267, -0.632, 2, 11.4, -0.632, 2, 11.733, -0.632, 0, 12.967, 0, 2, 13.833, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Y", "Segments": [0, 0, 2, 2, 0, 2, 2.833, 0, 0, 3.333, 7.007, 1, 3.422, 7.007, 3.511, 7.423, 3.6, 6.052, 1, 3.767, 3.481, 3.933, -7.015, 4.1, -7.015, 0, 4.333, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 7.267, 0, 2, 8.1, 0, 2, 11.933, 0, 2, 12.967, 0, 2, 13.833, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Y", "Segments": [0, 0, 2, 2, 0, 1, 2.278, 0, 2.555, 2.327, 2.833, 12.935, 1, 2.966, 18.027, 3.1, 30, 3.233, 30, 1, 3.333, 30, 3.433, 28.85, 3.533, 26.6, 1, 3.611, 24.85, 3.689, 24, 3.767, 24, 0, 3.867, 25.66, 0, 4.3, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 7.267, 0, 2, 8.1, 0, 2, 11.933, 0, 2, 12.967, 0, 2, 13.833, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Y", "Segments": [0, 0, 2, 2, 0, 0, 2.833, -8.339, 1, 3.022, -8.339, 3.211, -7.96, 3.4, -4.157, 1, 3.567, -0.801, 3.733, 5, 3.9, 5, 0, 4.3, -3, 0, 4.633, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 7.267, 0, 2, 8.1, 0, 2, 10.267, 0, 2, 10.5, 0, 2, 10.667, 0, 2, 11.033, 0, 2, 11.267, 0, 2, 11.4, 0, 2, 11.733, 0, 2, 12.967, 0, 2, 13.833, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamFootRX", "Segments": [0, 0, 2, 2, 0, 0, 2.833, 9.972, 0, 4.3, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 7.267, 0, 2, 8.1, 0, 2, 10.267, 0, 2, 10.5, 0, 2, 10.667, 0, 2, 11.033, 0, 2, 11.267, 0, 2, 11.4, 0, 2, 11.733, 0, 2, 12.967, 0, 2, 13.833, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Z", "Segments": [0, 0, 2, 2, 0, 2, 2.933, 0, 0, 3.2, -0.755, 0, 3.633, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 7.267, 0, 2, 8.1, 0, 2, 12.967, 0, 2, 13.833, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Z", "Segments": [0, 0, 2, 2, 0, 2, 2.933, 0, 0, 3.2, 1.055, 0, 3.633, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 7.267, 0, 2, 8.1, 0, 2, 10.267, 0, 2, 10.5, 0, 2, 10.667, 0, 2, 11.033, 0, 2, 11.267, 0, 2, 11.4, 0, 2, 11.733, 0, 2, 12.967, 0, 2, 13.833, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Y", "Segments": [0, 0, 2, 2, 0, 2, 2.933, 0, 0, 3.267, -3, 0, 3.633, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 7.267, 0, 2, 8.1, 0, 2, 10.267, 0, 2, 10.5, 0, 2, 10.667, 0, 2, 11.033, 0, 2, 11.267, 0, 2, 11.4, 0, 2, 11.733, 0, 2, 12.967, 0, 2, 13.833, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamMJRFlap", "Segments": [0, 0, 2, 1.867, 0, 1, 1.889, 0, 1.911, 0.861, 1.933, 2, 2, 2, 5, 2, 2.033, 8, 1, 2.044, 9.14, 2.056, 10, 2.067, 10, 2, 2.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRInput", "Segments": [0, 0, 2, 1.333, 0, 0, 1.667, -30, 0, 1.933, 30, 0, 2.2, -30, 0, 2.733, 30, 0, 3.033, -30, 0, 3.5, 30, 0, 4.1, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamMRCupSet", "Segments": [0, 0, 2, 1.8, 0, 0, 1.867, 1, 2, 15.167, 1]}, {"Target": "Parameter", "Id": "ParamManjuuREyeOpen", "Segments": [0, 1, 2, 1.5, 1, 0, 1.767, 0, 2, 2.667, 0, 0, 2.767, 1, 2, 2.833, 1, 0, 2.9, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyX", "Segments": [0, -2.385, 0, 0.167, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyY", "Segments": [0, 0, 2, 1.133, 0, 0, 1.733, -30, 0, 2.1, 11.181, 0, 2.2, -3.665, 0, 2.633, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyZ", "Segments": [0, -14.451, 0, 0.833, -30, 0, 1.4, -25.896, 0, 1.633, -30, 0, 1.933, 30, 0, 2.133, -5, 0, 2.567, 2.39, 1, 2.6, 2.39, 2.634, 2.683, 2.667, 1, 1, 2.711, -1.244, 2.756, -16, 2.8, -16, 0, 3.133, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRPositionZ", "Segments": [0, -4.629, 0, 0.133, -7.409, 2, 0.3, -7.409, 0, 0.633, -13.236, 2, 0.833, -13.236, 0, 1.1, -19.236, 0, 1.3, -18.882, 0, 1.633, -25.737, 0, 1.8, 6, 0, 2.033, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRArmB", "Segments": [0, -5, 0, 0.967, -7.487, 0, 1.167, -6, 0, 1.5, -10, 1, 1.6, -10, 1.7, -4.071, 1.8, 14, 1, 1.822, 18.016, 1.845, 30, 1.867, 30, 2, 2.767, 30, 0, 2.833, 28.722, 1, 2.866, 28.722, 2.9, 29.023, 2.933, 29.442, 1, 2.966, 29.861, 3, 30, 3.033, 30, 2, 15.167, 30]}, {"Target": "Parameter", "Id": "ParamManjuuRMouth", "Segments": [0, 4.032, 0, 0.133, 30, 0, 0.4, -28.5, 0, 0.7, 30, 0, 1.033, -30, 0, 1.433, 23.103, 0, 1.633, -18, 0, 1.867, 5.118, 0, 2.033, 0, 2, 2.2, 0, 0, 2.667, 27, 0, 2.867, -30, 0, 3.067, 14, 0, 3.333, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRSigh", "Segments": [0, 0, 2, 2.033, 0, 1, 2.4, 0, 2.766, 0.324, 3.133, 0.8, 1, 3.266, 0.973, 3.4, 1, 3.533, 1, 0, 3.567, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow1", "Segments": [0, 2, 1, 0.233, 4.667, 0.467, 7.333, 0.7, 10, 1, 0.711, 6.667, 0.722, 3.333, 0.733, 0, 1, 1, 3.333, 1.266, 6.667, 1.533, 10, 2, 1.567, 0, 2, 1.667, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow2", "Segments": [0, 8, 1, 0.056, 8.667, 0.111, 9.333, 0.167, 10, 1, 0.178, 6.667, 0.189, 3.333, 0.2, 0, 1, 0.5, 3.333, 0.8, 6.667, 1.1, 10, 1, 1.111, 6.667, 1.122, 3.333, 1.133, 0, 1, 1.278, 3.333, 1.422, 6.667, 1.567, 10, 2, 1.6, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow3", "Segments": [0, 3.333, 1, 0.2, 2.222, 0.4, 1.111, 0.6, 0, 1, 0.9, 3.333, 1.2, 6.667, 1.5, 10, 2, 1.533, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow4", "Segments": [0, 9.429, 1, 0.011, 9.619, 0.022, 9.81, 0.033, 10, 1, 0.044, 6.667, 0.056, 3.333, 0.067, 0, 2, 0.3, 0, 1, 0.611, 3.333, 0.922, 6.667, 1.233, 10, 1, 1.244, 6.667, 1.256, 3.333, 1.267, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowB", "Segments": [0, 1, 2, 1.3, 1, 0, 1.5, 1.1, 0, 1.533, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW", "Segments": [0, 0.633, 0, 0.067, 1, 0, 0.333, -0.95, 0, 0.633, 1, 0, 0.967, -1, 0, 1.367, 0.77, 0, 1.567, -0.6, 1, 1.622, -0.6, 1.678, -0.55, 1.733, -0.2, 1, 1.755, -0.06, 1.778, 1, 1.8, 1, 0, 1.967, -1, 0, 2.1, 0, 2, 2.167, 0, 0, 2.667, 1, 0, 2.767, -1, 0, 2.867, 1, 0, 2.933, -0.2, 0, 3.033, 0.4, 0, 3.167, -0.154, 0, 3.367, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW2", "Segments": [0, -0.882, 0, 0.267, 1, 0, 0.5, -1, 0, 0.8, 0.904, 0, 1.133, -1, 0, 1.533, 0.733, 0, 1.767, -0.6, 0, 1.8, 1, 0, 2, -1, 0, 2.1, 0, 2, 2.2, 0, 0, 2.733, 1, 0, 2.8, -1, 0, 2.9, 1, 0, 3, -0.2, 0, 3.067, 0.363, 0, 3.233, -0.074, 0, 3.4, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamMRCupFZ", "Segments": [0, 0, 2, 1.833, 0, 0, 1.933, -30, 0, 2, 30, 0, 2.1, -24.436, 1, 2.111, -24.436, 2.122, 21.356, 2.133, 21.356, 1, 2.155, 21.356, 2.178, -15.099, 2.2, -15.099, 0, 2.567, 3.642, 0, 2.633, -5.151, 0, 2.733, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 2, 1.9, 0, 0, 2, -30, 1, 2.011, -30, 2.022, 30, 2.033, 30, 0, 2.1, -24.436, 1, 2.122, -24.436, 2.145, 21.356, 2.167, 21.356, 1, 2.189, 21.356, 2.211, -15.099, 2.233, -15.099, 0, 2.6, 3.642, 0, 2.667, -5.151, 0, 2.8, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX1", "Segments": [0, 0.853, 0, 0.033, 1, 0, 0.367, -1, 0, 0.733, 1, 0, 1.167, -1, 0, 1.5, 1, 0, 1.6, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX2", "Segments": [0, 0.25, 0, 0.133, 1, 0, 0.567, -1, 0, 0.9, 1, 0, 1.233, -1, 0, 1.6, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLiqH", "Segments": [0, 0.259, 0, 1.533, 1, 2, 2.967, 1, 0, 3, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX1", "Segments": [0, -0.672, 0, 0.267, 0.803, 0, 0.533, -0.803, 0, 0.8, 0.9, 0, 0.967, -0.9, 0, 1.233, 1, 0, 1.467, -1, 1, 1.522, -1, 1.578, 0.355, 1.633, 0.8, 1, 1.666, 1, 1.7, 1, 1.733, 1, 0, 1.933, -1, 0, 2.1, 1, 0, 2.133, -1, 0, 2.567, 1, 0, 2.733, -1, 0, 2.933, 1, 0, 2.967, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX2", "Segments": [0, -1, 0, 0.3, 1, 0, 0.6, -1, 0, 0.867, 1, 0, 1.033, -1, 0, 1.3, 1, 0, 1.5, -1, 1, 1.544, -1, 1.589, -0.336, 1.633, 0.2, 1, 1.689, 0.87, 1.744, 1, 1.8, 1, 0, 2, -1, 0, 2.1, 1, 0, 2.167, -1, 0, 2.633, 1, 0, 2.767, -1, 0, 2.967, 1, 0, 3.067, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX3", "Segments": [0, 0.088, 0, 0.1, -0.418, 0, 0.4, 0.418, 0, 0.667, -0.418, 0, 0.933, 0.418, 0, 1.133, -0.418, 0, 1.4, 0.418, 0, 1.567, -0.418, 1, 1.589, -0.418, 1.611, -0.442, 1.633, -0.4, 1, 1.711, -0.252, 1.789, 0.418, 1.867, 0.418, 0, 2.033, -1, 0, 2.1, 1, 0, 2.233, -1, 0, 2.667, 1, 0, 2.833, -1, 0, 3.033, 0.418, 0, 3.133, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuL", "Segments": [0, 0, 2, 2.1, 0, 0, 2.133, -0.325, 0, 2.167, 0.107, 0, 2.2, -0.181, 0, 2.233, 0.088, 0, 2.267, -0.125, 0, 2.3, 0.139, 0, 2.367, 0.005, 0, 2.4, 0.163, 0, 2.433, 0, 2, 3.1, 0, 2, 3.267, 0, 1, 3.767, 0, 4.267, 10.23, 4.767, 19.972, 1, 4.778, 20.188, 4.789, 20, 4.8, 20, 2, 11.6, 20, 0, 14.267, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuL", "Segments": [0, 0, 2, 1.667, 0, 0, 1.7, -0.18, 2, 1.733, -0.18, 2, 1.967, -0.18, 0, 2.1, 4.426, 0, 2.133, 4.102, 0, 2.167, 4.762, 0, 2.2, 4.246, 0, 2.233, 4.743, 0, 2.267, 4.53, 0, 2.3, 4.794, 0, 2.367, 4.432, 1, 2.378, 4.432, 2.389, 4.469, 2.4, 4.59, 1, 2.467, 5.318, 2.533, 5.71, 2.6, 5.71, 1, 2.656, 5.71, 2.711, 5.714, 2.767, 5.653, 1, 2.811, 5.604, 2.856, 5.365, 2.9, 5.016, 1, 2.944, 4.667, 2.989, 3.086, 3.033, 1.781, 1, 3.055, 1.129, 3.078, -0.06, 3.1, -0.06, 0, 3.167, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 13.667, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuL", "Segments": [0, 0, 2, 1.967, 0, 0, 2.1, 4.426, 0, 2.133, 4.102, 0, 2.167, 4.762, 0, 2.2, 4.246, 0, 2.233, 4.743, 0, 2.267, 4.53, 0, 2.3, 4.794, 0, 2.367, 4.432, 1, 2.378, 4.432, 2.389, 4.548, 2.4, 4.59, 1, 2.411, 4.632, 2.422, 4.63, 2.433, 4.63, 0, 2.5, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 13.667, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamMjLFlip", "Segments": [0, 0, 2, 2.433, 0, 0, 2.533, 1, 2, 3, 1, 2, 4.6, 1, 1, 4.656, 1, 4.711, 0.99, 4.767, 0.949, 1, 4.778, 0.941, 4.789, 0.009, 4.8, 0.004, 1, 4.811, 0, 4.822, 0, 4.833, 0, 2, 11.133, 0, 2, 14.267, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPositionZManjuuL", "Segments": [0, 0, 2, 1.6, 0, 0, 1.7, -13, 1, 1.833, -13, 1.967, -9.224, 2.1, 7, 1, 2.167, 15.112, 2.233, 30, 2.3, 30, 1, 2.344, 30, 2.389, 13.747, 2.433, 6.012, 1, 2.489, -3.657, 2.544, -13.515, 2.6, -15, 1, 2.644, -16.188, 2.689, -16, 2.733, -16, 0, 3.033, -8, 0, 3.167, -30, 2, 3.6, -30, 0, 3.767, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 13.667, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLEyeOpen", "Segments": [0, 0, 2, 1.7, 0, 0, 2.1, 1, 2, 2.433, 1, 2, 3.1, 1, 2, 3.767, 1, 2, 4.033, 1, 2, 4.433, 1, 1, 4.544, 1, 4.656, 1, 4.767, 0.999, 1, 4.778, 0.999, 4.789, 0.972, 4.8, 0.97, 1, 7.756, 0.322, 10.711, 0, 13.667, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyX", "Segments": [0, 0, 2, 2.1, 0, 0, 2.433, 30, 0, 2.6, -30, 0, 3, 30, 1, 3.033, 30, 3.067, 10.661, 3.1, 0, 1, 3.156, -17.768, 3.211, -21, 3.267, -21, 0, 3.533, 10.631, 0, 3.767, -17, 0, 4.033, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 13.667, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyY", "Segments": [0, 0, 2, 2.4, 0, 0, 3.133, -22.948, 0, 3.633, 30, 0, 3.833, -12, 0, 4.233, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 11.667, 0, 0, 11.733, 21.076, 0, 11.8, -21.076, 0, 11.867, 21.076, 1, 11.889, 21.076, 11.911, -21.076, 11.933, -21.076, 0, 12, 21.076, 0, 12.067, -21.076, 1, 12.089, -21.076, 12.111, 21.076, 12.133, 21.076, 0, 12.2, -21.076, 0, 12.267, 21.076, 1, 12.289, 21.076, 12.311, -21.076, 12.333, -21.076, 0, 12.4, 21.076, 0, 12.467, -21.076, 1, 12.489, -21.076, 12.511, 21.076, 12.533, 21.076, 0, 12.6, -21.076, 0, 12.667, 21.076, 1, 12.689, 21.076, 12.711, -21.076, 12.733, -21.076, 0, 12.8, 21.076, 0, 12.867, -21.076, 1, 12.889, -21.076, 12.911, 21.076, 12.933, 21.076, 0, 13, -21.076, 0, 13.067, 21.076, 1, 13.089, 21.076, 13.111, -21.076, 13.133, -21.076, 0, 13.2, 21.076, 0, 13.3, -21.076, 0, 13.367, 21.076, 0, 13.467, -21.076, 1, 13.489, -21.076, 13.511, 21.076, 13.533, 21.076, 0, 13.6, -21.076, 0, 13.667, 21.076, 1, 13.689, 21.076, 13.711, -21.076, 13.733, -21.076, 0, 13.8, 21.076, 0, 13.867, -21.076, 1, 13.889, -21.076, 13.911, 21.076, 13.933, 21.076, 0, 14, -21.076, 0, 14.067, 21.076, 1, 14.089, 21.076, 14.111, -21.076, 14.133, -21.076, 0, 14.2, 21.076, 0, 14.267, -21.076, 1, 14.289, -21.076, 14.311, 21.076, 14.333, 21.076, 0, 14.4, -21.076, 0, 14.467, 21.076, 0, 14.7, -3.527, 0, 14.933, 3, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyZ", "Segments": [0, 0, 2, 1.733, 0, 0, 1.833, -8, 1, 1.966, -8, 2.1, -2.41, 2.233, 10, 1, 2.3, 16.205, 2.366, 20, 2.433, 20, 2, 2.567, 20, 0, 2.767, 30, 0, 3.167, -18, 0, 3.367, 30, 0, 3.533, 0, 0, 3.667, 30, 2, 3.9, 30, 0, 4.167, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 11.433, 0, 0, 11.9, 30, 2, 13.833, 30, 0, 14.2, 0, 0, 14.4, 30, 0, 14.633, -7.258, 0, 14.867, 6.105, 0, 15.133, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyW", "Segments": [0, 0, 2, 1.6, 0, 0, 1.7, -22.148, 0, 1.833, 30, 2, 1.9, 30, 0, 2.133, -30, 2, 2.567, -30, 0, 2.833, 27.255, 0, 2.933, -19.148, 1, 2.978, -19.148, 3.022, -14.371, 3.067, 0, 1, 3.122, 17.963, 3.178, 30, 3.233, 30, 0, 3.4, -30, 0, 3.6, 19.49, 0, 3.8, -22.148, 0, 3.967, 8, 0, 4.1, -11.034, 0, 4.2, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 13.667, 0, 2, 14.167, 0, 0, 14.333, 11, 0, 14.533, -8, 0, 14.733, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuL", "Segments": [0, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 11.633, 0, 0, 11.7, 1, 0, 11.767, -1, 0, 11.833, 1, 0, 11.9, -1, 0, 11.967, 1, 0, 12.033, -1, 0, 12.1, 1, 0, 12.167, -1, 0, 12.233, 1, 0, 12.3, -1, 0, 12.367, 1, 0, 12.433, -1, 0, 12.5, 1, 0, 12.567, -1, 0, 12.633, 1, 0, 12.7, -1, 0, 12.767, 1, 0, 12.833, -1, 0, 12.9, 1, 0, 12.967, -1, 0, 13.033, 1, 0, 13.1, -1, 0, 13.167, 1, 0, 13.267, -1, 0, 13.333, 1, 0, 13.433, -1, 0, 13.5, 1, 0, 13.567, -1, 0, 13.633, 1, 0, 13.7, -1, 0, 13.767, 1, 0, 13.833, -1, 0, 13.9, 1, 0, 13.967, -1, 0, 14.033, 1, 0, 14.1, -1, 0, 14.167, 1, 0, 14.233, -1, 0, 14.3, 1, 0, 14.367, -1, 0, 14.433, 1, 0, 14.467, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamClawFX", "Segments": [0, 0, 2, 3.1, 0, 0, 3.233, 1, 0, 3.333, -1, 2, 3.367, -1, 0, 3.467, 1, 0, 3.567, -1, 2, 3.6, -1, 0, 3.7, 1, 0, 3.8, -1, 2, 3.833, -1, 0, 3.933, 1, 0, 4.033, -1, 2, 4.067, -1, 0, 4.167, 1, 0, 4.267, -1, 2, 4.3, -1, 0, 4.4, 1, 0, 4.5, -1, 2, 4.533, -1, 0, 4.633, 1, 0, 4.733, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 11.633, 0, 0, 11.767, 1, 0, 11.867, -1, 2, 11.9, -1, 0, 12, 1, 0, 12.1, -1, 2, 12.133, -1, 0, 12.233, 1, 0, 12.333, -1, 2, 12.367, -1, 0, 12.467, 1, 0, 12.567, -1, 2, 12.6, -1, 0, 12.7, 1, 0, 12.8, -1, 2, 12.833, -1, 0, 12.933, 1, 0, 13.033, -1, 2, 13.067, -1, 0, 13.167, 1, 0, 13.267, -1, 2, 13.3, -1, 0, 13.4, 1, 0, 13.5, -1, 2, 13.533, -1, 0, 13.633, 1, 0, 13.733, -1, 2, 13.767, -1, 0, 13.867, 1, 0, 13.967, -1, 2, 14, -1, 0, 14.1, 1, 0, 14.2, -1, 2, 14.233, -1, 0, 14.333, 1, 0, 14.433, -1, 0, 14.5, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamClawFY", "Segments": [0, 0, 2, 3.1, 0, 0, 3.167, -1, 0, 3.233, 1, 0, 3.367, -1, 0, 3.567, 1, 0, 3.6, -1, 0, 3.8, 1, 0, 3.833, -1, 2, 4.067, -1, 0, 4.267, 1, 0, 4.3, -1, 0, 4.5, 1, 0, 4.533, -1, 0, 4.733, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 11.633, 0, 0, 11.7, -1, 0, 11.8, 1, 0, 11.9, -1, 2, 11.967, -1, 0, 12.033, 1, 0, 12.133, -1, 1, 12.155, -1, 12.178, -1, 12.2, -0.999, 1, 12.222, -0.998, 12.245, 1, 12.267, 1, 0, 12.367, -1, 2, 12.433, -1, 0, 12.5, 1, 0, 12.6, -1, 2, 12.667, -1, 0, 12.733, 1, 0, 12.833, -1, 2, 12.9, -1, 0, 12.967, 1, 0, 13.067, -1, 2, 13.133, -1, 0, 13.2, 1, 0, 13.3, -1, 2, 13.367, -1, 0, 13.433, 1, 0, 13.533, -1, 2, 13.6, -1, 0, 13.667, 1, 0, 13.767, -1, 2, 13.833, -1, 0, 13.9, 1, 0, 14, -1, 2, 14.067, -1, 0, 14.133, 1, 0, 14.233, -1, 2, 14.3, -1, 0, 14.367, 1, 0, 14.467, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamClawBX", "Segments": [0, 0, 2, 3.1, 0, 0, 3.233, -1, 0, 3.333, 1, 2, 3.367, 1, 0, 3.467, -1, 0, 3.567, 1, 2, 3.6, 1, 0, 3.7, -1, 0, 3.8, 1, 2, 3.833, 1, 0, 3.933, -1, 0, 4.033, 1, 2, 4.067, 1, 0, 4.167, -1, 0, 4.267, 1, 2, 4.3, 1, 0, 4.4, -1, 0, 4.5, 1, 2, 4.533, 1, 0, 4.633, -1, 0, 4.733, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 11.633, 0, 0, 11.767, -1, 0, 11.867, 1, 2, 11.9, 1, 0, 12, -1, 0, 12.1, 1, 2, 12.133, 1, 0, 12.233, -1, 0, 12.333, 1, 2, 12.367, 1, 0, 12.467, -1, 0, 12.567, 1, 2, 12.6, 1, 0, 12.7, -1, 0, 12.8, 1, 2, 12.833, 1, 0, 12.933, -1, 0, 13.033, 1, 2, 13.067, 1, 0, 13.167, -1, 0, 13.267, 1, 2, 13.3, 1, 0, 13.4, -1, 0, 13.5, 1, 2, 13.533, 1, 0, 13.633, -1, 0, 13.733, 1, 2, 13.767, 1, 0, 13.867, -1, 0, 13.967, 1, 2, 14, 1, 0, 14.1, -1, 0, 14.2, 1, 2, 14.233, 1, 0, 14.333, -1, 0, 14.433, 1, 0, 14.5, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamClawBY", "Segments": [0, 0, 2, 3.1, 0, 0, 3.167, 1, 0, 3.233, -1, 0, 3.367, 1, 0, 3.567, -1, 0, 3.6, 1, 0, 3.8, -1, 0, 3.833, 1, 2, 4.067, 1, 0, 4.267, -1, 0, 4.3, 1, 0, 4.5, -1, 0, 4.533, 1, 0, 4.733, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 11.633, 0, 0, 11.7, 1, 0, 11.8, -1, 0, 11.9, 1, 2, 11.967, 1, 0, 12.033, -1, 0, 12.133, 1, 1, 12.155, 1, 12.178, 1, 12.2, 0.999, 1, 12.222, 0.998, 12.245, -1, 12.267, -1, 0, 12.367, 1, 2, 12.433, 1, 0, 12.5, -1, 0, 12.6, 1, 2, 12.667, 1, 0, 12.733, -1, 0, 12.833, 1, 2, 12.9, 1, 0, 12.967, -1, 0, 13.067, 1, 2, 13.133, 1, 0, 13.2, -1, 0, 13.3, 1, 2, 13.367, 1, 0, 13.433, -1, 0, 13.533, 1, 2, 13.6, 1, 0, 13.667, -1, 0, 13.767, 1, 2, 13.833, 1, 0, 13.9, -1, 0, 14, 1, 2, 14.067, 1, 0, 14.133, -1, 0, 14.233, 1, 2, 14.3, 1, 0, 14.367, -1, 0, 14.467, 0, 2, 14.567, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUHide", "Segments": [0, 1, 2, 0.967, 1, 2, 2.367, 1, 2, 3.567, 1, 0, 3.6, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 5.4, 0, 2, 8.567, 0, 0, 13.133, 1, 2, 13.8, 1, 2, 15.167, 1]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuU", "Segments": [0, 0, 2, 3.567, 0, 0, 3.6, -9, 1, 3.989, -9.004, 4.378, -9.009, 4.767, -9.013, 0, 4.8, -9.021, 1, 7.578, -9.049, 10.355, -9.063, 13.133, -9.063, 1, 13.244, -9.063, 13.356, -9.051, 13.467, -8.473, 1, 13.534, -8.126, 13.6, -5.124, 13.667, -2.978, 1, 13.734, -0.832, 13.8, 0.951, 13.867, 0.951, 0, 13.967, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUEyesForm", "Segments": [0, 0, 2, 0.967, 0, 2, 2.367, 0, 0, 3.867, -1, 0, 4.267, 0, 2, 4.4, 0, 2, 4.767, 0, 1, 4.778, 0, 4.789, -0.734, 4.8, -0.76, 1, 4.878, -0.941, 4.955, -1, 5.033, -1, 2, 5.433, -1, 0, 6.067, 0, 2, 8.567, 0, 2, 13.133, 0, 2, 14.2, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyX", "Segments": [0, 0, 2, 0.967, 0, 2, 2.367, 0, 2, 3.867, 0, 2, 4.267, 0, 1, 4.434, 0, 4.6, 1.084, 4.767, 3.31, 1, 4.778, 3.458, 4.789, 3.566, 4.8, 3.566, 0, 6.533, -5.239, 0, 8.567, 0, 2, 13.133, 0, 2, 14.2, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyY", "Segments": [0, 0, 0, 0.8, -3.677, 0, 0.967, 2, 0, 1.067, -2.703, 0, 1.233, 2, 1, 1.255, 2, 1.278, 1.214, 1.3, -1.62, 1, 1.311, -3.037, 1.322, -5, 1.333, -5, 0, 1.533, 2.925, 0, 1.633, -3.677, 0, 1.833, 2, 0, 1.9, -2.703, 0, 2.067, 2, 1, 2.1, 2, 2.134, 1.055, 2.167, -1.62, 1, 2.178, -2.512, 2.189, -3.677, 2.2, -3.677, 0, 2.367, 2, 0, 2.467, -2.703, 0, 2.633, 2, 1, 2.655, 2, 2.678, 1.214, 2.7, -1.62, 1, 2.711, -3.037, 2.722, -5, 2.733, -5, 0, 2.933, 2.925, 0, 3, -3.677, 0, 3.2, 2, 0, 3.267, -2.703, 0, 3.433, 2, 0, 3.533, -1.62, 0, 3.867, 0, 2, 4.267, 0, 2, 4.567, 0, 0, 4.767, 3.551, 0, 4.8, 2.583, 0, 4.833, 2.925, 0, 4.967, -2.598, 0, 5.2, 3.252, 0, 5.467, -0.937, 0, 5.9, 0.896, 0, 6.267, 0, 2, 8.567, 0, 2, 13.133, 0, 2, 13.167, 0, 0, 13.367, 30, 0, 13.633, -30, 0, 14.133, 0, 2, 14.2, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyZ", "Segments": [0, 0, 0, 0.767, -30, 0, 0.967, 30, 1, 0.989, 30, 1.011, -30, 1.033, -30, 0, 1.233, 30, 0, 1.3, -30, 0, 1.533, 30, 0, 1.6, -30, 0, 1.833, 30, 0, 1.9, -30, 0, 2.067, 30, 1, 2.1, 30, 2.134, -30, 2.167, -30, 0, 2.367, 30, 1, 2.389, 30, 2.411, -30, 2.433, -30, 0, 2.633, 30, 1, 2.655, 30, 2.678, -30, 2.7, -30, 0, 2.933, 30, 0, 2.967, -30, 0, 3.167, 30, 1, 3.189, 30, 3.211, -30, 3.233, -30, 0, 3.4, 30, 2, 3.467, 30, 1, 3.478, 30, 3.489, 5.858, 3.5, 0, 1, 3.544, -23.43, 3.589, -30, 3.633, -30, 0, 3.8, 0, 2, 3.867, 0, 2, 4.5, 0, 1, 4.589, 0, 4.678, 3.378, 4.767, 11.128, 1, 4.778, 12.097, 4.789, 13.476, 4.8, 13.476, 0, 4.9, -19.601, 0, 5.133, 19.601, 0, 5.267, -12, 0, 5.567, 2.426, 0, 6.233, -1.978, 0, 6.9, 2.426, 0, 8.567, 0, 2, 13.133, 0, 0, 13.333, -16, 0, 13.6, 23, 0, 14, -21, 0, 14.2, 8, 0, 14.433, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUArmZ", "Segments": [0, 0, 0, 0.8, -2.573, 0, 0.967, 19, 0, 1.067, -30, 0, 1.233, 30, 0, 1.333, -30, 1, 1.344, -30, 1.356, -30, 1.367, -28, 1, 1.422, -7.441, 1.478, 30, 1.533, 30, 0, 1.633, -30, 0, 1.833, 30, 0, 1.9, -30, 0, 2.067, 30, 0, 2.2, -30, 0, 2.367, 30, 0, 2.467, -30, 0, 2.633, 30, 1, 2.666, 30, 2.7, -30, 2.733, -30, 1, 2.744, -30, 2.756, -30, 2.767, -28, 1, 2.822, -7.441, 2.878, 30, 2.933, 30, 0, 3, -30, 0, 3.167, 30, 0, 3.267, -30, 0, 3.4, 30, 2, 3.467, 30, 1, 3.478, 30, 3.489, 20.598, 3.5, 14.24, 1, 3.556, -17.548, 3.611, -30, 3.667, -30, 0, 3.833, 0, 2, 3.867, 0, 2, 4.567, 0, 0, 4.767, -2.573, 1, 4.778, -2.573, 4.789, 14.007, 4.8, 15.073, 1, 4.811, 16.139, 4.822, 16, 4.833, 16, 0, 4.967, -20, 0, 5.2, 19, 0, 5.367, -9.782, 0, 5.633, 3.028, 0, 6.133, -1.374, 0, 6.533, 0, 0, 6.867, -1, 0, 8.567, 0, 2, 13.133, 0, 2, 13.233, 0, 0, 13.367, -12, 0, 13.733, 30, 0, 14.2, -7, 0, 14.433, 3.141, 0, 14.667, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineU", "Segments": [0, 0, 2, 1.067, 0, 0, 1.233, 1, 2, 1.267, 0, 2, 1.367, 0, 0, 1.533, 1, 2, 1.6, 0, 2, 1.667, 0, 0, 1.833, 1, 2, 1.867, 0, 2, 1.933, 0, 0, 2.067, 1, 2, 2.1, 0, 2, 2.233, 0, 0, 2.367, 1, 2, 2.4, 0, 2, 2.467, 0, 0, 2.633, 1, 2, 2.667, 0, 2, 2.767, 0, 0, 2.933, 1, 2, 2.967, 0, 2, 3.033, 0, 0, 3.2, 1, 2, 3.233, 0, 2, 3.3, 0, 0, 3.433, 1, 2, 3.467, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 8.567, 0, 2, 13.133, 0, 2, 14.2, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineD", "Segments": [0, 0, 2, 1.2, 0, 2, 1.233, 0, 1, 1.278, 0.333, 1.322, 0.667, 1.367, 1, 2, 1.4, 0, 2, 1.533, 0, 1, 1.578, 0.333, 1.622, 0.667, 1.667, 1, 2, 1.7, 0, 2, 1.833, 0, 1, 1.866, 0.333, 1.9, 0.667, 1.933, 1, 2, 1.967, 0, 2, 2.067, 0, 1, 2.111, 0.333, 2.156, 0.667, 2.2, 1, 2, 2.233, 0, 2, 2.367, 0, 1, 2.4, 0.333, 2.434, 0.667, 2.467, 1, 2, 2.5, 0, 2, 2.6, 0, 2, 2.633, 0, 1, 2.678, 0.333, 2.722, 0.667, 2.767, 1, 2, 2.8, 0, 2, 2.933, 0, 1, 2.966, 0.333, 3, 0.667, 3.033, 1, 2, 3.067, 0, 2, 3.2, 0, 1, 3.233, 0.333, 3.267, 0.667, 3.3, 1, 2, 3.333, 0, 2, 3.433, 0, 1, 3.478, 0.333, 3.522, 0.667, 3.567, 1, 2, 3.6, 0, 2, 3.867, 0, 2, 4.267, 0, 2, 4.767, 0, 2, 4.8, 0, 2, 8.567, 0, 2, 13.133, 0, 2, 14.2, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyW", "Segments": [0, 0, 0, 0.767, -0.582, 0, 0.967, 1, 0, 1.033, -0.5, 0, 1.233, 0.4, 0, 1.3, -0.6, 0, 1.533, 1, 0, 1.6, -0.582, 0, 1.833, 1, 0, 1.9, -0.5, 0, 2.067, 0.4, 0, 2.167, -0.6, 0, 2.367, 1, 0, 2.433, -0.5, 0, 2.633, 0.4, 0, 2.7, -0.6, 0, 2.933, 1, 0, 3, -0.797, 0, 3.167, 1, 0, 3.267, -0.5, 0, 3.433, 0.4, 0, 3.567, -0.6, 0, 3.867, 0, 2, 4.733, 0, 2, 4.767, 0, 0, 4.8, 0.3, 0, 4.967, 0, 0, 5.167, 0.4, 0, 5.367, 0, 0, 5.733, 0.132, 0, 6.267, -0.132, 0, 8.567, 0, 2, 13.133, 0, 0, 13.333, 0.5, 0, 13.567, -1, 0, 13.9, 1, 0, 14.133, -0.5, 0, 14.367, 0.5, 0, 14.633, -0.3, 0, 14.933, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPositionXPanda", "Segments": [0, 0, 2, 2.633, 0, 0, 5.1, 18, 2, 10.867, 18, 0, 14.167, 0, 2, 14.567, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPositionYPanda", "Segments": [0, 0, 2, 1.6, 0, 0, 1.8, -0.12, 1, 1.844, -0.12, 1.889, -0.131, 1.933, 0.216, 1, 1.966, 0.476, 2, 2, 2.033, 2, 0, 2.067, 1.82, 0, 2.1, 2.123, 0, 2.133, 1.883, 0, 2.167, 2.066, 0, 2.2, 1.887, 0, 2.233, 2.131, 0, 2.267, 1.893, 0, 2.3, 2.079, 0, 2.333, 1.962, 0, 2.367, 2.086, 0, 2.4, 1.855, 0, 2.433, 2.013, 0, 2.467, 1.904, 0, 2.533, 2, 1, 2.578, 2, 2.622, 1.794, 2.667, 0.984, 1, 2.689, 0.579, 2.711, -0.06, 2.733, -0.06, 0, 2.8, 0.018, 0, 3.233, 0, 2, 14.567, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamSizePanda", "Segments": [0, 0, 2, 1.6, 0, 2, 1.7, 0, 2, 1.967, 0, 0, 2.1, 1.178, 0, 2.133, 0.91, 0, 2.167, 1.456, 0, 2.2, 1.03, 0, 2.233, 1.44, 0, 2.267, 1.264, 0, 2.3, 1.482, 0, 2.367, 1.183, 1, 2.378, 1.183, 2.389, 1.279, 2.4, 1.314, 1, 2.411, 1.349, 2.422, 1.347, 2.433, 1.347, 0, 2.5, 0, 2, 3.1, 0, 2, 3.767, 0, 2, 4.033, 0, 2, 4.433, 0, 2, 14.567, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 10.8, 0, 0, 11, 1, 2, 14.033, 1, 0, 14.3, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyX", "Segments": [0, 0, 2, 1.8, 0, 0, 2.133, 30, 2, 2.433, 30, 0, 2.7, -23.554, 0, 2.933, 7.48, 0, 3.133, -5.045, 0, 3.3, 2.228, 0, 3.567, -2.785, 0, 3.8, 0, 2, 14.567, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyY", "Segments": [0, 0, 0, 1.867, -0.06, 0, 2.067, 30, 2, 2.533, 30, 0, 2.833, -30, 0, 3.033, 7.878, 0, 3.233, -5.889, 0, 3.467, 1.989, 0, 3.733, -2.865, 0, 3.933, 0, 2, 13.967, 0, 0, 14.2, -23, 0, 14.533, 5, 0, 14.767, -2.865, 0, 15.067, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2Panda", "Segments": [0, 0, 2, 3.267, 0, 0, 3.333, -6.213, 1, 3.344, -6.213, 3.356, 6.229, 3.367, 6.229, 0, 3.433, -6.213, 1, 3.444, -6.213, 3.456, 6.229, 3.467, 6.229, 0, 3.567, -6.229, 0, 3.6, 6.229, 0, 3.633, -6.229, 0, 3.733, 6.229, 0, 3.767, -6.229, 0, 3.867, 6.229, 0, 3.9, -6.229, 0, 3.933, 6.229, 0, 4.033, -6.229, 0, 4.067, 6.229, 0, 4.167, -6.213, 0, 4.2, 6.229, 0, 4.233, -6.229, 0, 4.3, 6.229, 0, 4.333, -6.229, 0, 4.433, 6.229, 0, 4.467, -6.229, 0, 4.567, 6.229, 0, 4.6, -6.229, 0, 4.633, 6.229, 0, 4.733, -6.229, 0, 4.767, 6.229, 0, 4.8, -6.229, 0, 4.867, 6.229, 0, 4.933, -6.229, 0, 5, 6.229, 0, 5.067, -6.229, 0, 5.1, 6.229, 0, 5.133, 0, 2, 10.967, 0, 0, 11, -6.213, 0, 11.1, 6.229, 0, 11.133, -6.213, 0, 11.233, 6.229, 0, 11.333, -6.229, 0, 11.433, 6.229, 0, 11.5, -6.229, 0, 11.6, 6.229, 0, 11.7, -6.229, 0, 11.8, 6.229, 0, 11.9, -6.229, 0, 11.967, 6.229, 0, 12.067, -6.229, 0, 12.167, 6.229, 0, 12.267, -6.213, 0, 12.367, 6.229, 0, 12.433, -6.229, 0, 12.5, 6.229, 0, 12.6, -6.229, 0, 12.667, 6.229, 1, 12.689, 6.229, 12.711, 4.954, 12.733, 0, 1, 12.744, -2.477, 12.756, -6.229, 12.767, -6.229, 0, 12.867, 6.229, 0, 12.967, -6.229, 0, 13.067, 6.229, 0, 13.133, -6.229, 0, 13.233, 6.229, 0, 13.3, -6.229, 0, 13.367, 6.229, 0, 13.467, -6.229, 0, 13.567, 6.229, 0, 13.667, -6.229, 0, 13.767, 6.229, 0, 13.833, -6.229, 0, 13.933, 6.229, 0, 13.967, 0, 2, 14.567, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyZ", "Segments": [0, 0, 2, 0.667, 0, 0, 1.333, -6.314, 1, 1.422, -6.314, 1.511, -6.185, 1.6, 0, 1, 1.667, 4.639, 1.733, 30, 1.8, 30, 1, 1.867, 30, 1.933, -1.045, 2, -12, 1, 2.1, -28.433, 2.2, -30, 2.3, -30, 2, 2.533, -30, 1, 2.644, -30, 2.756, -29.883, 2.867, -28.747, 1, 2.934, -28.066, 3, -26.155, 3.067, -23.157, 1, 3.156, -19.159, 3.244, -6.35, 3.333, 3.581, 1, 3.389, 9.788, 3.444, 21.689, 3.5, 26.569, 1, 3.533, 29.497, 3.567, 29.431, 3.6, 29.431, 0, 3.7, 26.569, 0, 3.867, 29.431, 0, 4, 26.569, 1, 4.022, 26.569, 4.045, 27.27, 4.067, 28, 1, 4.1, 29.095, 4.134, 29.431, 4.167, 29.431, 0, 4.233, 26.569, 0, 4.333, 29.431, 0, 4.467, 26.569, 0, 4.6, 29.431, 0, 4.733, 26.569, 0, 4.867, 29.431, 0, 4.933, 26.569, 0, 5.067, 29.431, 1, 5.089, 29.431, 5.111, 30, 5.133, 28, 1, 5.244, 16.147, 5.356, 0, 5.467, 0, 2, 10.967, 0, 0, 11.1, 1.431, 0, 11.267, -1.431, 0, 11.433, 1.431, 0, 11.567, -1.431, 0, 11.8, 1.431, 0, 12.033, -1.431, 1, 12.078, -1.431, 12.122, -1.066, 12.167, 0, 1, 12.2, 0.8, 12.234, 1.431, 12.267, 1.431, 0, 12.4, -1.431, 0, 12.6, 1.431, 0, 12.733, -1.431, 0, 12.967, 1.431, 0, 13.133, -1.431, 0, 13.333, 1.431, 0, 13.533, -1.431, 0, 13.7, 1.431, 1, 13.744, 1.431, 13.789, 2.337, 13.833, -1.431, 1, 13.922, -8.967, 14.011, -30, 14.1, -30, 0, 14.367, 11.918, 0, 14.6, -5, 0, 14.833, 3.581, 0, 15.033, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegFZ", "Segments": [0, 0, 2, 1.8, 0, 0, 1.933, 1, 0, 2.433, -1, 0, 2.7, 1, 0, 2.867, -1, 0, 2.9, 1, 0, 3, -1, 0, 3.1, 1, 0, 3.233, -1, 0, 3.333, 1, 0, 3.433, -1, 0, 3.567, 1, 0, 3.633, -1, 0, 3.767, 1, 0, 3.9, -1, 0, 4.033, 1, 0, 4.167, -1, 0, 4.233, 1, 0, 4.333, -1, 0, 4.467, 1, 0, 4.6, -1, 0, 4.733, 1, 0, 4.8, -1, 0, 4.933, 1, 0, 5.067, -1, 0, 5.133, 0, 2, 11, 0, 0, 11.133, -1, 0, 11.333, 1, 0, 11.5, -1, 0, 11.7, 1, 0, 11.9, -1, 0, 12.067, 1, 0, 12.267, -1, 0, 12.433, 1, 0, 12.6, -1, 0, 12.767, 1, 0, 12.967, -1, 0, 13.133, 1, 0, 13.3, -1, 0, 13.467, 1, 0, 13.667, -1, 0, 13.833, 1, 0, 13.967, 0, 2, 14.567, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegBZ", "Segments": [0, 0, 2, 1.8, 0, 0, 1.933, 1, 0, 2.433, -1, 0, 2.7, 1, 0, 2.867, -1, 0, 2.9, 1, 0, 3, -1, 0, 3.1, 1, 0, 3.233, -1, 0, 3.333, 1, 0, 3.433, -1, 0, 3.567, 1, 0, 3.633, -1, 0, 3.767, 1, 0, 3.9, -1, 0, 4.033, 1, 0, 4.167, -1, 0, 4.233, 1, 0, 4.333, -1, 0, 4.467, 1, 0, 4.6, -1, 0, 4.733, 1, 0, 4.8, -1, 0, 4.933, 1, 0, 5.067, -1, 0, 5.133, 0, 2, 11, 0, 0, 11.133, -1, 0, 11.333, 1, 0, 11.5, -1, 0, 11.7, 1, 0, 11.9, -1, 0, 12.067, 1, 0, 12.267, -1, 0, 12.433, 1, 0, 12.6, -1, 0, 12.767, 1, 0, 12.967, -1, 0, 13.133, 1, 0, 13.3, -1, 0, 13.467, 1, 0, 13.667, -1, 0, 13.833, 1, 0, 13.967, 0, 2, 14.567, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyX", "Segments": [0, 0, 0, 1, -4.826, 0, 2.867, 3.384, 1, 3.5, 3.384, 4.134, 1.411, 4.767, -2.713, 1, 4.778, -2.785, 4.789, -3.745, 4.8, -3.745, 0, 5.8, 4.761, 0, 7.467, -4.826, 0, 9.333, 3.384, 0, 13.4, -3.842, 0, 14.467, 4.761, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyY", "Segments": [0, 0, 0, 0.967, -1.599, 0, 2.467, 1.177, 0, 4.767, -1.145, 1, 4.778, -1.145, 4.789, -0.551, 4.8, -0.517, 1, 5.189, 0.681, 5.578, 1.236, 5.967, 1.236, 0, 7.433, -1.599, 0, 9.3, 1.177, 0, 10.733, -3.108, 0, 12.967, 1.833, 0, 13.333, -30, 0, 13.6, 30, 0, 14, -6.911, 0, 14.433, 3.347, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamCannonZ", "Segments": [0, 0, 0, 1.467, 8.73, 0, 2.733, -2.048, 0, 4.267, 7.986, 1, 4.434, 7.986, 4.6, 7.618, 4.767, 6.733, 1, 4.778, 6.674, 4.789, 1.771, 4.8, 1.685, 1, 5, 0.143, 5.2, -0.523, 5.4, -0.523, 0, 7.933, 8.73, 0, 9.2, -2.048, 0, 12.267, 7.986, 0, 14.067, -0.523, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamCannonGaY", "Segments": [0, 0, 0, 1.133, -1.392, 0, 2.6, 3.53, 0, 4.5, 0, 1, 4.589, 0, 4.678, 0.069, 4.767, 0.272, 1, 4.778, 0.297, 4.789, 2.954, 4.8, 3.004, 1, 4.956, 3.71, 5.111, 4, 5.267, 4, 0, 7.6, -1.392, 0, 9.867, 4, 0, 11.167, -3.108, 0, 13.167, 2.311, 0, 13.433, -30, 0, 13.7, 30, 0, 14.1, -4.706, 0, 14.6, 3.586, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupY", "Segments": [0, 0, 0, 1.3, 0.126, 0, 2.8, -0.04, 0, 4.067, 0.056, 1, 4.3, 0.056, 4.534, 0.026, 4.767, -0.044, 1, 4.778, -0.047, 4.789, -0.122, 4.8, -0.122, 0, 7.767, 0.126, 0, 9.267, -0.04, 0, 12.067, 0.056, 0, 13.367, -0.123, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupZ", "Segments": [0, 0, 0, 0.767, -0.149, 0, 2.067, 0.113, 0, 3.367, -0.04, 0, 4.767, 0.094, 1, 4.778, 0.094, 4.789, 0.033, 4.8, 0.031, 1, 5, -0.006, 5.2, -0.056, 5.4, -0.075, 1, 6.011, -0.132, 6.622, -0.149, 7.233, -0.149, 0, 8.533, 0.113, 0, 9.833, -0.04, 0, 12.9, 0.097, 0, 14.067, -0.075, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamCannonHandZ", "Segments": [0, 0, 0, 0.8, -1.213, 0, 1.4, 1.258, 0, 2.667, -0.396, 0, 3.733, 0.557, 1, 4.078, 0.557, 4.422, 0.362, 4.767, -0.066, 1, 4.778, -0.08, 4.789, -0.841, 4.8, -0.847, 1, 4.967, -0.944, 5.133, -1.1, 5.3, -1.119, 1, 5.956, -1.194, 6.611, -1.213, 7.267, -1.213, 0, 7.867, 1.258, 0, 9.133, -0.396, 0, 10.2, 0.557, 0, 13.967, -1.119, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 1.589, 0, 3.178, -5.25, 4.767, -15.943, 1, 4.778, -16.018, 4.789, -20.384, 4.8, -20.449, 1, 5.9, -26.873, 7, -30, 8.1, -30, 0, 8.567, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupIce", "Segments": [0, 0, 2, 0.967, 0, 2, 2.367, 0, 2, 3.867, 0, 2, 4.267, 0, 2, 4.633, 0, 2, 4.667, 0, 1, 4.7, 0, 4.734, 0.452, 4.767, 0.609, 1, 5.434, 3.752, 6.1, 6.681, 6.767, 9.761, 1, 6.778, 9.812, 6.789, 9.947, 6.8, 9.947, 2, 6.833, 0, 1, 6.911, 0.203, 6.989, 0.406, 7.067, 0.609, 1, 7.489, 2.445, 7.911, 4.264, 8.333, 5.953, 1, 8.622, 7.108, 8.911, 8.569, 9.2, 9.174, 1, 9.4, 9.594, 9.6, 9.862, 9.8, 9.947, 1, 9.956, 10, 10.111, 10, 10.267, 10, 2, 10.3, 0, 2, 11.767, 0, 2, 12.8, 0, 2, 13.133, 0, 2, 14.2, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupZ", "Segments": [0, 0, 2, 4.633, 0, 0, 4.733, -0.59, 0, 5, 0.59, 0, 5.333, -0.59, 0, 5.6, 0.59, 0, 5.867, -0.59, 0, 6.2, 0.59, 0, 6.467, -0.59, 0, 6.767, 0.552, 0, 7.067, -0.532, 0, 7.3, 0.478, 0, 7.6, -0.511, 0, 7.867, 0.368, 0, 8.133, -0.339, 0, 8.4, 0.369, 0, 8.733, -0.307, 0, 9.033, 0.277, 0, 9.433, -0.185, 0, 9.667, 0, 2, 10.333, 0, 2, 12.8, 0, 2, 13.4, 0, 2, 13.5, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupInput", "Segments": [0, 0, 2, 4.8, 0, 0, 5.033, -10.608, 0, 5.333, 10.493, 0, 5.633, -10.608, 0, 5.933, 10.493, 0, 6.2, -10.608, 0, 6.533, 10.493, 0, 6.8, -10.608, 0, 7.167, 10.493, 0, 7.433, -10.608, 0, 7.8, 10.493, 0, 8.1, -10.608, 0, 8.433, 10.493, 0, 8.833, -10.608, 0, 9.133, 10.493, 0, 9.833, -5.62, 0, 10.367, 4.889, 0, 11.133, -3.512, 0, 12.033, 1.264, 0, 12.733, -2.697, 0, 13.367, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamParamStrongCatZ", "Segments": [0, 0, 0, 1.367, -16.712, 0, 2.433, 20.517, 0, 3.3, -10.928, 0, 4.767, -0.413, 0, 5.6, -6.795, 0, 8.567, 0, 2, 12.033, 0, 2, 13.1, 0, 0, 13.533, -17, 0, 13.9, 8, 0, 14.2, -6, 1, 14.3, -6, 14.4, -1.608, 14.5, 0, 1, 14.578, 1.251, 14.655, 1.109, 14.733, 1.109, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamParamSCBodyZ", "Segments": [0, 0, 0, 1.7, -16.688, 0, 2.667, 20.635, 0, 3.667, -10.146, 0, 4.767, -2.334, 0, 6.167, -6.745, 0, 8.567, 0, 2, 13.1, 0, 2, 13.233, 0, 0, 13.6, -15, 0, 13.967, 5, 0, 14.267, -2, 0, 14.567, 0, 2, 14.967, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamSCDishY", "Segments": [0, 0, 0, 1.567, -5.456, 0, 2.933, 19.492, 0, 3.733, -16.1, 0, 4.767, -1.035, 0, 6.633, -5.456, 0, 8.567, 0, 2, 12.033, 0, 2, 13.167, 0, 0, 13.667, -30, 0, 14.1, 10, 0, 14.433, -6, 0, 14.767, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamSCDishZ", "Segments": [0, 0, 0, 1.1, -5, 0, 2.733, 20, 0, 3.533, -15.617, 0, 4.767, 3.081, 0, 6.2, -5, 0, 8.567, 0, 2, 12.033, 0, 2, 13.167, 0, 2, 13.333, 0, 0, 13.767, -15, 0, 14.167, 5, 0, 14.5, -2, 0, 14.833, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamSCCupRO", "Segments": [0, 0, 2, 6.533, 0, 2, 8.567, 0, 2, 12.033, 0, 2, 13.2, 0, 0, 13.867, 1, 2, 13.9, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamSCCupY", "Segments": [0, 0, 2, 6.533, 0, 2, 8.567, 0, 2, 12.033, 0, 2, 13.2, 0, 0, 13.533, 30, 0, 13.867, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamSCCupZ", "Segments": [0, 0, 0, 1.033, -3.653, 0, 2.567, 3.433, 0, 4.767, -3.981, 0, 5.7, 4.371, 0, 6.533, 0, 2, 8.567, 0, 2, 12.033, 0, 2, 13.2, 0, 2, 13.667, 0, 0, 14.133, 30, 0, 14.467, -16, 0, 14.767, 9, 0, 14.9, 0, 2, 15.167, 0]}, {"Target": "Parameter", "Id": "MB_yanwubaozha", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "MB_DRFWXZKTMD", "Segments": [0, 1, 0, 15.167, 1]}, {"Target": "Parameter", "Id": "ParamAllSizeFix", "Segments": [0, 1, 0, 15.167, 1]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBGX", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBGY", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN2", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBlackY", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBlackCollar", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBlackOrder", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamWhiteIN", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamDeskHide", "Segments": [0, 1, 0, 15.167, 1]}, {"Target": "Parameter", "Id": "ParamStoolHide", "Segments": [0, 1, 0, 15.167, 1]}, {"Target": "Parameter", "Id": "ParamCHX", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamCHY", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamCHZ", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamChaSize", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamAllZ", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamDeskShow", "Segments": [0, 10, 0, 15.167, 10]}, {"Target": "Parameter", "Id": "ParamCannonShow", "Segments": [0, 10, 0, 15.167, 10]}, {"Target": "Parameter", "Id": "ParamLightPositionX", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamFixT", "Segments": [0, 1, 0, 15.167, 1]}, {"Target": "Parameter", "Id": "ParamFlap", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPupilExp", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeSmileL", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeSmileR", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpen2", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamMouthType", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamMouthY", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamTeethLight", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamHeart2", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLX", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRX", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeRLightOpen", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLightLine1", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLightLine2", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLightLine3", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLightShine", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo1", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo2", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo3", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1Y", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle2", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamAngleH", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamHandT2L", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamFanOpenR", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamHandRCup", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamHandRMail", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "Segments": [0, 1, 0, 15.167, 1]}, {"Target": "Parameter", "Id": "ParamHandLIQY1", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY2", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY3", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamHandCupZ", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamHandCupY", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLegR1X", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Z", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Y", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Y", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamFootLX", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamLegLF", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRHide", "Segments": [0, 1, 0, 15.167, 1]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuR", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuR", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuR", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuR", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamMalpositionManjuuR", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLHide", "Segments": [0, 1, 0, 15.167, 1]}, {"Target": "Parameter", "Id": "ParamMJLSigh", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuU", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuU", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamPandaHide", "Segments": [0, 1, 0, 15.167, 1]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY1", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY2", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY3", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "ParamSCDishRO", "Segments": [0, 0, 0, 15.167, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 0, 15.167, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 1.233, "Value": ""}, {"Time": 14.667, "Value": ""}]}