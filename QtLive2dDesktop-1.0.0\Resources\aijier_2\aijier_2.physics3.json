{"Version": 3, "Meta": {"PhysicsSettingCount": 26, "TotalInputCount": 51, "TotalOutputCount": 71, "VertexCount": 221, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "Dummy1"}, {"Id": "PhysicsSetting2", "Name": "Dummy2"}, {"Id": "PhysicsSetting3", "Name": "Dummy3"}, {"Id": "PhysicsSetting4", "Name": "Dummy4"}, {"Id": "PhysicsSetting5", "Name": "Dummy5"}, {"Id": "PhysicsSetting6", "Name": "Dummy6"}, {"Id": "PhysicsSetting7", "Name": "Dummy7"}, {"Id": "PhysicsSetting8", "Name": "Dummy8"}, {"Id": "PhysicsSetting9", "Name": "Dummy9"}, {"Id": "PhysicsSetting10", "Name": "Dummy10"}, {"Id": "PhysicsSetting11", "Name": "Dummy11"}, {"Id": "PhysicsSetting12", "Name": "Dummy12"}, {"Id": "PhysicsSetting13", "Name": "Dummy13"}, {"Id": "PhysicsSetting14", "Name": "Dummy14"}, {"Id": "PhysicsSetting15", "Name": "Dummy15"}, {"Id": "PhysicsSetting16", "Name": "Dummy16"}, {"Id": "PhysicsSetting17", "Name": "Dummy17"}, {"Id": "PhysicsSetting18", "Name": "Dummy18"}, {"Id": "PhysicsSetting19", "Name": "Dummy19"}, {"Id": "PhysicsSetting20", "Name": "Dummy20"}, {"Id": "PhysicsSetting21", "Name": "Dummy21"}, {"Id": "PhysicsSetting22", "Name": "Dummy22"}, {"Id": "PhysicsSetting23", "Name": "Dummy23"}, {"Id": "PhysicsSetting24", "Name": "Dummy24"}, {"Id": "PhysicsSetting25", "Name": "Dummy25"}, {"Id": "PhysicsSetting26", "Name": "Dummy26"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 25, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "Hair_physics"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairFront1"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairFront2"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairFront3"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairFront4"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairFront5"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 25, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "Hair_physics"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairSide1"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairSide2"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairSide3"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairSide4"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairSide5"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Hair_physics"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBack1"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack2"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack3"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack4"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack5"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param86"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param87"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 80, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeLSmile"}, "Weight": 20, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeLsize"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamEyeLGG"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 80, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeRSmile"}, "Weight": 20, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeRsize"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamEyeRGG"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "OP_physics"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ZD_BustMotionY_L"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ZD_BustMotionY_X"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ZD_BustMotionX_L"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ZD_BustMotionX_X"}, "VertexIndex": 2, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "OP_physics"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ZD_BustLMotionY_L"}, "VertexIndex": 1, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ZD_BustLMotionY_X"}, "VertexIndex": 2, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "OP_physics"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ZD_BustRMotionY_L"}, "VertexIndex": 1, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ZD_BustRMotionY_X"}, "VertexIndex": 2, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ZD_BustLMotionX_L"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ZD_BustLMotionX_X"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ZD_BustRMotionX_L"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ZD_BustRMotionX_X"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting12", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param104"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param3"}, "VertexIndex": 2, "Scale": 5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param4"}, "VertexIndex": 3, "Scale": 5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param105"}, "VertexIndex": 2, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param107"}, "VertexIndex": 4, "Scale": 7, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting13", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param2"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting14", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param5"}, "VertexIndex": 1, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param6"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting15", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param11"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param12"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param13"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting16", "Input": [{"Source": {"Target": "Parameter", "Id": "null_a"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param22"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param23"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting17", "Input": [{"Source": {"Target": "Parameter", "Id": "Param14"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param55"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting18", "Input": [{"Source": {"Target": "Parameter", "Id": "Param61"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param15"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param16"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param17"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param18"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting19", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "null_b"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param86"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param87"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting20", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param108"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.8, "Delay": 0.8, "Acceleration": 2, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting21", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param106"}, "VertexIndex": 1, "Scale": 60, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 2, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting22", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamArmLAngle"}, "Weight": 80, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param7"}, "VertexIndex": 1, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param8"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param9"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param10"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting23", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param111"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting24", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param110"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.6, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting25", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param79"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param82"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param80"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param81"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting26", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param118"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param117"}, "VertexIndex": 2, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param116"}, "VertexIndex": 3, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param119"}, "VertexIndex": 4, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param121"}, "VertexIndex": 5, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}]}