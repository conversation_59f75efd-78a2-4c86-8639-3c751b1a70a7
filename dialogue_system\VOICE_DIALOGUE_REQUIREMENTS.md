# Live2D语音对话系统需求文档

## 📋 项目概述

### 项目名称
Live2D桌面宠物极低延迟语音对话系统

### 项目目标
在现有Live2D桌面宠物基础上，集成完整的语音对话功能，实现S-T-T-S（Speech-to-Text-to-Speech）全流程对话，追求极低延迟的用户体验。

### 技术架构
- **语音输入**: PyAudio直接麦克风监听
- **STT引擎**: faster-whisper (本地预加载模式)
- **TTS引擎**: GPT-SoVITS API调用
- **LLM引擎**: OpenAI API (基于现有实现)
- **UI框架**: PySide6 (Live2D集成)

## 🎯 核心功能需求

### 1. PyAudio麦克风管理 (优先级：最高)

**功能描述**: 使用PyAudio创建麦克风对象，持续监听音频流

**技术要求**:
- 持续音频流监听，16kHz采样率
- 支持两种语音输入模式切换
- 实时音频数据队列管理
- 低延迟音频处理

**参考配置**:
```json
{
  "microphone_config": {
    "sample_rate": 16000,
    "channels": 1,
    "chunk_size": 1024,
    "format": "paInt16",
    "input_device_index": null
  }
}
```

### 2. 按键触发语音输入模式 (优先级：高)

**功能描述**: 按住指定按键期间录音，松开按键停止并处理

**技术要求**:
- 默认空格键触发
- 按键期间收集音频数据
- 松开按键立即处理语音
- 支持自定义触发键

**用户体验**:
- 按下按键：Live2D切换到"倾听"表情（暂时无此表情，不需要设计）
- 录音期间：显示录音状态指示
- 松开按键：切换到"思考"表情，开始处理（暂时无此表情，不需要设计）

**参考配置**:
```json
{
  "key_triggered_config": {
    "trigger_key": "space",
    "max_recording_duration": 30.0,
    "show_recording_indicator": true,
    "visual_feedback": true
  }
}
```

### 3. 实时对话语音输入模式 (优先级：高)

**功能描述**: 持续监听麦克风，自动检测语音活动并处理

**技术要求**:
- 简化音量阈值检测
- 自动开始/结束录音
- 静音检测和语音分段
- 连续对话支持

**检测参数**:
- 音量阈值：0.01 (可调节)
- 静音持续时间：2.0秒
- 最小语音时长：0.5秒
- 最大语音时长：30.0秒

**参考配置**:
```json
{
  "realtime_config": {
    "volume_threshold": 0.01,
    "silence_duration": 2.0,
    "min_speech_duration": 0.5,
    "max_speech_duration": 30.0,
    "auto_restart": true
  }
}
```

### 4. Live2D右键菜单集成 (优先级：重要)

**功能描述**: 在Live2D右键菜单中添加语音对话控制选项

**菜单结构**:
```
🎤 语音对话
├── ⌨️ 按键触发模式
├── 🔄 实时对话模式
├── ▶️ 开始语音对话
├── ⏹️ 停止语音对话
├── ⚙️ 语音设置
└── 🌟 沉浸式模式
```

### 5. 设置界面功能控制 (优先级：重要)

**功能描述**: 在设置界面添加语音对话相关配置选项

**设置分组**:
```
语音输入设置
├── 输入模式选择 (按键触发/实时对话)
├── 触发键设置 (按键模式)
├── 音量阈值调节 (实时模式)
├── 麦克风设备选择
└── 音频参数配置

语音识别设置
├── STT模型路径
├── 识别语言设置
├── 模型精度选择
└── 处理超时设置

语音合成设置
├── TTS API地址
├── 语音预设选择
├── 合成参数调节
└── 播放设备选择
```

## 🔧 技术规格与参考配置

### STT配置 (faster-whisper)

**参考路径**: `D:\BaiduNetdiskDownload\SOVITS\LLM对话\scripts\stt_client\stt_config.json`

**直接使用配置**:
```json
{
  "model_config": {
    "model_path": "D:/huggingface_cache/hub/models--Systran--faster-whisper-large-v3",
    "device": "auto",
    "compute_type": "float16"
  },
  "audio_config": {
    "sample_rate": 16000,
    "channels": 1,
    "chunk_duration": 1.0,
    "overlap_duration": 0.1
  },
  "transcription_config": {
    "language": "auto",
    "task": "transcribe",
    "beam_size": 5,
    "temperature": [0.0, 0.2, 0.4, 0.6, 0.8, 1.0],
    "compression_ratio_threshold": 2.4,
    "log_prob_threshold": -1.0,
    "no_speech_threshold": 0.6
  }
}
```

### TTS配置 (GPT-SoVITS)

**参考路径**: `D:\BaiduNetdiskDownload\SOVITS\LLM对话\scripts\tts_client\tts_config.json`

**API端点**: `http://localhost:9880`

**基础API配置**:
```json
{
  "api_url": "http://localhost:9880",
  "default_params": {
    "text_lang": "zh",
    "cut_punc": ",.;?!、，。？！；：",
    "top_k": 20,
    "top_p": 0.6,
    "temperature": 0.6,
    "speed_factor": 1.0
  },
  "audio_settings": {
    "output_dir": "output/audio",
    "auto_play": true,
    "save_audio": false,
    "format": "wav"
  }
}
```

**完整语音预设配置示例** (直接复制粘贴使用):

**文件路径**: `D:\BaiduNetdiskDownload\SOVITS\LLM对话\scripts\tts_client\presets\shizuru.json`

```json
{
  "name": "shizuru",
  "description": "静流角色语音预设，使用特定的参考音频和提示文本",
  "author": "系统默认",
  "version": "1.0",
  "created_date": "2024-01-01",
  "tags": ["角色", "静流", "日文提示"],
  "config": {
    "gpt_weights_path": "D:/BaiduNetdiskDownload/SOVITS/GPT-SoVITS-v2pro-20250604/GPT_weights_v2Pro/静流-e15.ckpt",
    "sovits_weights_path": "D:/BaiduNetdiskDownload/SOVITS/GPT-SoVITS-v2pro-20250604/SoVITS_weights_v2Pro/静流_e8_s4720.pth",
    "text_lang": "zh",
    "prompt_lang": "ja",
    "ref_audio_path": "D:/BaiduNetdiskDownload/SOVITS/静流参考音频/z2053#00456.ogg",
    "prompt_text": "井上さん、葉っぱか何かを靴に巻いて、わざと足跡を消してるっぽい…",
    "text_split_method": "cut5",
    "top_k": 5,
    "top_p": 1.0,
    "temperature": 1.0,
    "speed_factor": 1.0,
    "batch_size": 1,
    "batch_threshold": 0.75,
    "split_bucket": true,
    "fragment_interval": 0.3,
    "seed": -1,
    "media_type": "wav",
    "streaming_mode": true,
    "parallel_infer": true,
    "repetition_penalty": 1.35,
    "sample_steps": 32,
    "super_sampling": false
  }
}
```

**⚠️ 重要提示**:
- 此配置必须完整使用，不可分离或部分使用
- 所有路径和参数都是必需的，缺少任何部分都会导致错误
- 可直接复制此配置文件到项目中使用

**其他预设参考**:
- `D:\BaiduNetdiskDownload\SOVITS\LLM对话\scripts\tts_client\presets\小鸟.json`

### LLM配置 (OpenAI)

**参考路径**: `D:\BaiduNetdiskDownload\SOVITS\LLM对话\config\integrated_config.json`

**直接使用配置**:
```json
{
  "openai_config": {
    "base_url": "https://api.openai.com/v1",
    "api_key": "your-api-key-here",
    "model": "gpt-3.5-turbo",
    "timeout": 30.0,
    "max_retries": 3
  },
  "default_params": {
    "temperature": 0.7,
    "max_tokens": 1000,
    "top_p": 1.0,
    "frequency_penalty": 0.0,
    "presence_penalty": 0.0
  },
  "conversation_settings": {
    "system_prompt": "你是一个友好的Live2D桌面宠物助手，请用简洁自然的语言回复。",
    "max_history": 20
  }
}
```

## 📁 项目结构

### 目标目录结构
```
Live2d-py/dialogue_system/
├── core/
│   ├── llm_client.py              # 现有，需增强
│   ├── voice_dialogue_manager.py  # 新增：统一语音对话管理器
│   └── ...
├── voice/                         # 新增：语音处理模块
│   ├── __init__.py
│   ├── microphone_manager.py      # PyAudio麦克风管理
│   ├── key_triggered_input.py     # 按键触发输入
│   ├── realtime_input.py          # 实时语音输入
│   └── voice_processor.py         # 语音处理器
├── stt/
│   ├── __init__.py
│   ├── faster_whisper_client.py   # 从scripts适配
│   └── stt_manager.py             # STT管理器
├── tts/
│   ├── __init__.py
│   ├── gptsovits_client.py        # 从scripts适配
│   ├── audio_player.py            # 从scripts适配
│   └── tts_manager.py             # TTS管理器
├── ui/
│   ├── voice_control_widget.py    # 语音控制界面
│   ├── voice_settings_dialog.py   # 语音设置对话框
│   └── ...
├── config/
│   ├── voice_dialogue_config.py   # 语音对话配置管理
│   └── default_voice_config.json  # 默认语音配置
└── test/
    ├── test_voice_dialogue.py     # 语音对话测试
    ├── test_microphone.py         # 麦克风测试
    └── ...
```

### 参考文件路径
- **STT客户端参考**: `D:\BaiduNetdiskDownload\SOVITS\LLM对话\scripts\stt_client\`
- **TTS客户端参考**: `D:\BaiduNetdiskDownload\SOVITS\LLM对话\scripts\tts_client\`
- **集成示例参考**: `D:\BaiduNetdiskDownload\SOVITS\LLM对话\scripts\integrated_chat_tts.py`
- **音频录制参考**: `D:\BaiduNetdiskDownload\SOVITS\LLM对话\scripts\stt_client\audio_recorder.py`

## 🎨 用户界面需求

### 1. Live2D状态同步

**功能描述**: 预留Live2D状态同步接口，支持根据语音对话状态更新Live2D表情和动画

**状态定义**:
```python
# 预留状态接口，暂不配置具体表情文件，因为对应模型没有相应的表情
voice_states = {
    "idle": None,        # 待机状态 - 预留接口
    "listening": None,   # 倾听状态 - 预留接口
    "recording": None,   # 录音状态 - 预留接口
    "thinking": None,    # 思考状态 - 预留接口
    "speaking": None,    # 说话状态 - 预留接口
    "error": None        # 错误状态 - 预留接口
}

# 状态更新接口设计
class Live2DStateManager:
    def update_voice_state(self, state: str):
        """更新语音对话状态 - 预留扩展接口"""
        print(f"🎭 语音状态变更: {state}")
        # 预留：未来可在此处添加具体的表情切换逻辑
        # if voice_states[state]:
        #     self.live2d_model.set_expression(voice_states[state])
```

### 2. 语音状态指示器

**功能描述**: 通过文字提示和控制台输出显示当前语音对话状态

**按键触发模式**:
- 待机：显示"按住空格键开始录音"
- 录音：显示"正在录音... (松开停止)" + Live2D状态预留接口调用
- 处理：显示"正在识别语音..." + Live2D状态预留接口调用

**实时对话模式**:
- 监听：显示"请开始说话" + Live2D状态预留接口调用
- 录音：显示"正在录音..." + Live2D状态预留接口调用
- 处理：显示"正在处理..." + Live2D状态预留接口调用

**实现方式**:
```python
def update_voice_status(self, mode: str, status: str):
    """更新语音状态显示"""
    status_messages = {
        "key_triggered": {
            "idle": "按住空格键开始录音",
            "recording": "正在录音... (松开停止)",
            "processing": "正在识别语音..."
        },
        "realtime": {
            "listening": "请开始说话",
            "recording": "正在录音...",
            "processing": "正在处理..."
        }
    }

    message = status_messages.get(mode, {}).get(status, "")
    print(f"📢 {message}")

    # 预留Live2D状态同步接口
    self.live2d_state_manager.update_voice_state(status)
```

## 🔄 开发阶段

### 阶段1：基础语音架构 (1周)
- [ ] 创建PyAudio麦克风管理器
- [ ] 实现按键触发语音输入
- [ ] 实现实时语音输入
- [ ] 适配faster-whisper和GPT-SoVITS客户端

### 阶段2：Live2D集成 (1周)
- [ ] 扩展右键菜单
- [ ] 实现语音模式切换
- [ ] Live2D状态同步
- [ ] 语音状态可视化

### 阶段3：用户体验优化 (3-5天)
- [ ] 完善设置界面
- [ ] 错误处理和用户反馈
- [ ] 性能监控和调试
- [ ] 操作指南和帮助

### 阶段4：测试和发布 (2-3天)
- [ ] 功能测试和性能测试
- [ ] 用户体验测试
- [ ] 文档完善
- [ ] 版本发布

## 📝 验收标准

### 功能验收
- [ ] 两种语音输入模式正常工作
- [ ] 语音识别准确率 > 90%
- [ ] S-T-T-S全流程正常运行
- [ ] Live2D状态同步接口预留完整
- [ ] 右键菜单功能完整

### 性能验收
- [ ] 连续对话30分钟无崩溃
- [ ] 内存使用稳定，无明显泄漏
- [ ] 麦克风响应及时，无卡顿
- [ ] 音频处理实时性良好

### 用户体验验收
- [ ] 操作流程简单直观
- [ ] 模式切换流畅
- [ ] 错误提示清晰友好
- [ ] 语音状态提示准确

### 扩展性验收
- [ ] Live2D状态同步接口设计合理
- [ ] 表情配置预留空间充足
- [ ] 代码结构支持后续扩展
- [ ] 配置文件支持动态更新

## 📚 直接可用的参考资源

### 代码参考文件
```
D:\BaiduNetdiskDownload\SOVITS\LLM对话\scripts\
├── integrated_chat_tts.py           # 完整集成示例
├── stt_client\
│   ├── whisper_client.py           # STT客户端实现
│   ├── audio_recorder.py           # 音频录制实现
│   └── stt_config.json             # STT配置文件
└── tts_client\
    ├── gptsovits_client.py         # TTS客户端实现
    ├── audio_player.py             # 音频播放实现
    ├── tts_config.json             # TTS配置文件
    └── presets\                    # 语音预设目录
        ├── shizuru.json            # 完整预设配置(推荐直接使用)
        └── 小鸟.json
```

### 配置文件路径
```
D:\BaiduNetdiskDownload\SOVITS\LLM对话\
├── config\integrated_config.json   # 集成配置示例
├── scripts\stt_client\stt_config.json
├── scripts\tts_client\tts_config.json
└── scripts\tts_client\presets\shizuru.json  # 完整TTS预设配置
```

### 模型和服务
- **Whisper模型路径**: `D:/huggingface_cache/hub/models--Systran--faster-whisper-large-v3`
- **GPT-SoVITS API**: `http://localhost:9880`
- **OpenAI API**: 配置中的base_url和api_key
- **TTS预设配置**: `scripts\tts_client\presets\shizuru.json` (完整配置，可直接复制使用)

### 重要使用说明
1. **TTS预设配置**: 必须使用完整的 `shizuru.json` 配置，不可分离或部分使用
2. **路径配置**: 所有文件路径都已在配置中指定，可直接使用
3. **模型权重**: GPT和SoVITS权重文件路径已在预设中配置
4. **参考音频**: 参考音频文件路径已在预设中指定
5. **配置完整性**: 缺少任何配置项都会导致TTS功能异常

---

**文档版本**: v2.0  
**创建日期**: 2025-07-29  
**最后更新**: 2025-07-29  
**负责人**: Live2D语音对话开发团队
