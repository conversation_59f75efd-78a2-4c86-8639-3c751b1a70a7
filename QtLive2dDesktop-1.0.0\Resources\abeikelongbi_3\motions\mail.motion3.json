{"Version": 3, "Meta": {"Duration": 11.833, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 244, "TotalSegmentCount": 17730, "TotalPointCount": 21670, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "MB_yanwubaozha", "Segments": [0, 0, 2, 0.033, 0, 0, 0.867, 1.5, 2, 0.9, 0, 2, 2.367, 0, 0, 3.1, 1.5, 2, 3.133, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.367, 1, 0, 0.433, 0, 0, 0.6, 0.9, 0, 0.7, 0.7, 2, 0.933, 0.7, 0, 1.067, 0, 2, 2.5, 0, 0, 2.667, 1, 2, 3, 1, 0, 3.167, 0.7, 2, 4.6, 0.7, 0, 4.8, 1, 2, 4.967, 1, 0, 5.2, 0, 2, 6.267, 0, 0, 6.467, 0.704, 0, 6.967, 0.459, 0, 7.267, 0.7, 0, 7.6, 0.5, 0, 7.933, 0.8, 0, 8.267, 0, 0, 8.533, 0.7, 0, 8.8, 0.5, 0, 9.1, 0.7, 0, 9.433, 0.4, 0, 9.7, 0.6, 0, 9.933, 0.5, 0, 10.333, 0.7, 0, 10.733, 0.5, 0, 10.933, 1, 0, 11.133, 0, 0, 11.267, 1, 2, 11.833, 1]}, {"Target": "Parameter", "Id": "ParamEyeSmileL", "Segments": [0, 0, 0, 0.567, 1, 2, 2.533, 1, 0, 2.7, 0, 2, 2.833, 0, 0, 2.867, 1, 2, 4.467, 1, 2, 10.9, 1, 0, 11.133, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.367, 1, 0, 0.433, 0, 0, 0.6, 0.9, 0, 0.7, 0.7, 2, 0.933, 0.7, 0, 1.067, 0, 2, 2.5, 0, 0, 2.667, 1, 2, 3, 1, 0, 3.167, 0.7, 2, 4.6, 0.7, 0, 4.8, 1, 2, 4.967, 1, 0, 5.2, 0, 2, 6.267, 0, 0, 6.467, 0.704, 0, 6.967, 0.459, 0, 7.267, 0.7, 0, 7.6, 0.5, 0, 7.933, 0.8, 0, 8.267, 0, 0, 8.533, 0.7, 0, 8.8, 0.5, 0, 9.1, 0.7, 0, 9.433, 0.4, 0, 9.7, 0.6, 0, 9.933, 0.5, 0, 10.333, 0.7, 0, 10.733, 0.5, 0, 10.933, 1, 0, 11.133, 0, 0, 11.267, 1, 2, 11.833, 1]}, {"Target": "Parameter", "Id": "ParamEyeSmileR", "Segments": [0, 0, 0, 0.567, 1, 2, 2.533, 1, 0, 2.7, 0, 2, 2.833, 0, 0, 2.867, 1, 2, 4.467, 1, 2, 10.9, 1, 0, 11.133, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 0, 0.233, 0.5, 0, 0.467, 0, 1, 0.556, 0, 0.644, 0.062, 0.733, 0.3, 1, 0.811, 0.508, 0.889, 0.7, 0.967, 0.7, 0, 1.233, 0.5, 2, 1.6, 0.5, 0, 1.733, 1, 0, 1.9, 0, 0, 2.067, 1, 2, 2.167, 1, 2, 2.333, 1, 0, 2.6, 0.5, 0, 2.8, 0.6, 0, 2.933, 0.495, 0, 3.067, 1, 2, 3.5, 1, 0, 3.7, 0, 0, 4, 1, 2, 4.3, 1, 2, 4.833, 1, 1, 4.944, 1, 5.056, 0.97, 5.167, 0.57, 1, 5.222, 0.37, 5.278, -1, 5.333, -1, 0, 5.567, 0, 2, 5.933, 0, 2, 6.4, 0, 2, 6.7, 0, 0, 7.167, 0.5, 2, 7.3, 0.5, 2, 8.1, 0.5, 2, 8.167, 0.5, 0, 8.767, 1, 2, 8.833, 1, 0, 9.333, -0.5, 1, 9.366, -0.5, 9.4, -0.093, 9.433, 0, 1, 9.622, 0.526, 9.811, 0.7, 10, 0.7, 2, 10.067, 0.7, 0, 10.567, 0.5, 0, 11.1, 1, 0, 11.6, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 0.233, 1, 0, 0.467, 0.5, 1, 0.534, 0.5, 0.6, 0.549, 0.667, 0.7, 1, 0.745, 0.877, 0.822, 1, 0.9, 1, 0, 1.267, 0.7, 2, 1.5, 0.7, 0, 1.733, 1, 2, 2.067, 1, 0, 2.2, 0.5, 0, 2.333, 0.993, 0, 2.467, 0.5, 0, 2.6, 1, 2, 2.8, 1, 0, 3.067, 0, 2, 3.5, 0, 0, 3.7, 1, 1, 3.789, 1, 3.878, 0.85, 3.967, 0.611, 1, 4, 0.522, 4.034, 0.5, 4.067, 0.5, 0, 4.367, 1, 2, 4.833, 1, 0, 5.033, 0.5, 0, 5.233, 1, 2, 5.4, 1, 0, 5.5, 0.907, 0, 5.633, 1, 0, 5.767, 0.9, 0, 5.933, 1, 0, 6.267, 0, 2, 6.4, 0, 0, 6.7, 1, 0, 7.167, 0.5, 2, 7.3, 0.5, 0, 8.1, 1, 2, 8.167, 1, 0, 8.767, 0.5, 2, 8.833, 0.5, 0, 9.333, 1, 0, 9.667, 0.5, 0, 10, 0.8, 2, 10.067, 0.8, 0, 10.567, 1, 1, 10.745, 1, 10.922, 0.854, 11.1, 0.5, 1, 11.256, 0.19, 11.411, 0, 11.567, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthSize", "Segments": [0, 0, 2, 2.333, 0, 0, 2.467, -0.7, 1, 2.545, -0.7, 2.622, -0.705, 2.7, -0.694, 1, 2.833, -0.676, 2.967, 0, 3.1, 0, 2, 3.333, 0, 0, 3.633, -0.3, 0, 4.667, -0.02, 0, 5.167, -0.407, 0, 5.567, 0, 2, 6.033, 0, 2, 8.833, 0, 0, 9.3, -0.6, 2, 9.467, -0.6, 1, 9.589, -0.6, 9.711, -0.312, 9.833, -0.3, 1, 10.133, -0.27, 10.433, -0.266, 10.733, -0.236, 1, 10.866, -0.222, 11, 0, 11.133, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthY", "Segments": [0, 0, 2, 2.633, 0, 0, 2.933, 0.3, 0, 3.233, 0, 2, 4.467, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeEmotion", "Segments": [0, 0, 0, 1.067, 1, 0, 1.733, 0, 0, 2.1, 1, 2, 2.933, 1, 0, 3.033, -1, 2, 4.767, -1, 0, 5.333, 1, 2, 11, 1, 0, 11.133, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 2, 4.467, 0, 2, 5.2, 0, 0, 5.7, 1, 2, 10.533, 1, 0, 11.133, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLightShine", "Segments": [0, 0, 0, 1.067, 10, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPHYInputX", "Segments": [0, 0, 2, 0.5, 0, 0, 0.633, -15, 0, 1.033, 16, 0, 1.233, -12, 0, 1.333, 13, 0, 1.433, -8.457, 0, 1.567, 8.052, 0, 1.667, -3, 0, 1.8, 2.89, 0, 1.9, -0.109, 1, 2, -0.109, 2.1, -0.131, 2.2, 0, 1, 2.289, 0.117, 2.378, 2, 2.467, 2, 0, 2.867, -6, 0, 3.067, 10.263, 0, 3.267, -3.702, 0, 3.467, 2.89, 0, 3.833, 0, 2, 4.133, 0, 0, 4.467, 23, 0, 5.133, -26, 0, 6.067, 24.977, 0, 6.933, -21.728, 0, 7.567, 23, 0, 8.233, -20.063, 0, 8.733, 19.305, 0, 9.467, -18.814, 0, 10, 17, 0, 10.8, -6.352, 0, 11.2, 3.697, 0, 11.6, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.333, 0, 2, 2.9, 0, 0, 3.167, 0.847, 0, 3.7, 0, 2, 4.133, 0, 2, 4.467, 0, 2, 8.067, 0, 0, 8.867, 0.63, 0, 9.467, -3.865, 0, 10.167, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 0, 0.633, -4, 0, 1.1, 7.263, 0, 1.233, 5.992, 0, 1.3, 7.742, 0, 1.433, 5.992, 0, 1.567, 7.929, 0, 1.667, 6.238, 0, 1.8, 7.668, 1, 1.833, 7.668, 1.867, 7.456, 1.9, 7, 1, 1.922, 6.696, 1.945, 6.074, 1.967, 6.054, 1, 2.022, 6.005, 2.078, 6, 2.133, 6, 0, 2.467, 11, 0, 2.867, 1, 0, 3.067, 6.513, 0, 3.367, 4.765, 0, 3.7, 10.319, 0, 3.9, 5.254, 0, 4.233, 9.988, 0, 4.467, 4.765, 0, 4.733, 8.583, 0, 4.933, 7.396, 0, 5.2, 17, 0, 5.633, -20, 0, 6.467, 4, 0, 6.967, -8, 0, 7.233, 7.614, 0, 7.567, -6, 0, 7.933, 6.508, 0, 8.233, -8, 0, 8.533, 3.668, 0, 8.8, -8, 0, 9.1, 4.765, 0, 9.433, -6, 0, 9.7, 2.683, 0, 9.933, -4, 0, 10.333, 2, 0, 10.733, -13, 0, 10.967, 2.683, 0, 11.4, -3, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.133, 0, 0, 0.5, -4, 0, 1.033, 19.431, 1, 1.111, 19.431, 1.189, 18.223, 1.267, 18.14, 1, 1.422, 17.975, 1.578, 17.969, 1.733, 17.969, 0, 2.233, 20.131, 0, 2.8, -4, 0, 3.067, 0, 1, 3.134, 0, 3.2, 0.292, 3.267, -1, 1, 3.478, -5.091, 3.689, -10.061, 3.9, -10.061, 0, 4.467, 3, 0, 5.133, -1, 0, 6.067, 5.777, 0, 6.967, -2.643, 0, 7.567, 9, 0, 8.2, -3.284, 0, 8.767, 7, 0, 9.433, -5, 0, 9.967, 4, 0, 10.767, -5, 0, 11.233, 1.121, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "Segments": [0, 0, 2, 0.333, 0, 2, 2.533, 0, 0, 2.7, -3, 0, 2.933, 0, 2, 4.467, 0, 2, 8.867, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleH", "Segments": [0, 0, 2, 0.333, 0, 2, 2.6, 0, 0, 2.933, 2, 0, 3.2, -3, 2, 3.767, -3, 2, 4.433, -3, 1, 4.444, -3, 4.456, -3.042, 4.467, -2.983, 1, 4.789, -1.283, 5.111, 0, 5.433, 0, 2, 8.867, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleS", "Segments": [0, 0, 2, 0.333, 0, 2, 2.933, 0, 0, 3.6, 1, 2, 4.467, 1, 2, 4.7, 1, 0, 5.333, 0, 2, 8.867, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamArm<PERSON>pperRAngle", "Segments": [0, 0, 0, 0.333, 0.834, 1, 0.411, 0.834, 0.489, 0.209, 0.567, 0.058, 1, 0.722, -0.243, 0.878, -0.276, 1.033, -0.276, 2, 1.4, -0.276, 2, 2.067, -0.276, 0, 2.233, 0, 0, 2.567, -1.81, 0, 2.767, -0.619, 0, 3, -1.453, 0, 3.267, -1, 2, 3.4, -1, 0, 3.7, 1, 0, 4.1, -0.085, 0, 4.467, 0.459, 0, 5.067, -0.518, 0, 5.5, 3, 0, 5.667, 2.55, 2, 6.067, 2.55, 0, 6.5, 1, 0, 6.967, 2.738, 0, 7.567, 0.784, 0, 8.267, 2.34, 0, 8.8, 0.844, 0, 9.433, 2.143, 0, 10, 1.115, 0, 10.767, 1.822, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRAngle", "Segments": [0, 7.4, 0, 0.133, 7.628, 0, 0.4, 5.481, 2, 0.6, 5.481, 0, 0.8, 9.326, 0, 1.067, -1.495, 0, 1.267, 1.225, 0, 1.467, -1.495, 0, 1.6, 0.651, 0, 1.833, -1.495, 0, 2, -0.171, 0, 2.2, -1, 0, 2.367, -0.609, 0, 2.667, -5, 0, 3.067, -4.395, 0, 3.3, -5, 0, 3.667, 8.225, 0, 3.933, 6.815, 0, 4.167, 7.628, 0, 4.4, 7.4, 2, 5, 7.4, 0, 5.167, 7.592, 0, 5.5, 6, 2, 6.3, 6, 0, 7.067, -2.832, 0, 7.633, -0.571, 0, 8.367, -3.568, 0, 8.9, -1.829, 0, 9.5, -4.619, 0, 10.167, -1.136, 0, 10.8, -3.361, 0, 11.3, 7.95, 0, 11.833, 7.4]}, {"Target": "Parameter", "Id": "ParamArmHandRAngle", "Segments": [0, 0, 0, 0.333, -2, 1, 0.411, -2, 0.489, -1.862, 0.567, -1, 1, 0.656, -0.015, 0.744, 1, 0.833, 1, 0, 1.133, -10, 0, 1.333, -3.44, 0, 1.5, -9, 0, 1.667, -4.596, 0, 1.9, -8, 0, 2.067, -5.623, 0, 2.267, -7.596, 0, 2.433, -7.06, 0, 2.733, -10, 2, 2.767, -10, 0, 3.067, -5.65, 1, 3.122, -5.65, 3.178, -6.648, 3.233, -7.596, 1, 3.3, -8.734, 3.366, -9, 3.433, -9, 0, 3.8, -4, 0, 4, -6.284, 0, 4.233, -4.642, 0, 4.467, -5.261, 1, 4.589, -5.261, 4.711, -5.201, 4.833, -4.642, 1, 4.922, -4.235, 5.011, -3.44, 5.1, -3.44, 0, 5.433, -4.898, 0, 5.633, -3.439, 2, 6.333, -3.439, 0, 6.967, -10, 2, 8.267, -10, 2, 10.833, -10, 0, 11.567, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRH", "Segments": [0, 0, 0, 0.333, -16, 2, 0.6, -16, 0, 0.833, -0.886, 0, 1.067, -11, 0, 1.3, -5.812, 0, 1.6, -11, 2, 2.633, -11, 0, 3.167, 0, 1, 3.6, 0, 4.034, -1.75, 4.467, -1.814, 1, 5.145, -1.914, 5.822, -1.923, 6.5, -2, 1, 6.667, -2.019, 6.833, -14, 7, -14, 0, 7.267, 1.614, 0, 7.6, -12, 0, 7.967, 0.508, 0, 8.267, -14, 0, 8.567, -2.332, 0, 8.833, -14, 0, 9.133, -1.235, 0, 9.467, -12, 0, 9.733, -3.317, 0, 9.967, -10, 0, 10.367, -4, 0, 10.733, -13, 0, 10.967, 2.683, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRAngle", "Segments": [0, 0, 2, 0.333, 0, 0, 0.733, -3, 0, 1.233, 7, 0, 1.667, 4, 0, 2.167, 5, 0, 2.433, 0, 0, 2.767, 10, 1, 2.845, 10, 2.922, 7.665, 3, 6, 1, 3.211, 1.48, 3.422, 0, 3.633, 0, 2, 4.9, 0, 0, 5.5, -6, 0, 6.233, -3, 0, 8.867, -6, 0, 9.467, 0, 2, 10.133, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamHandT2R", "Segments": [0, 0, 0, 0.4, 0.7, 0, 0.733, 0, 2, 0.967, 0, 0, 1.1, 0.2, 0, 1.267, 0, 0, 1.5, 0.016, 0, 1.633, 0, 2, 1.667, 0, 0, 1.9, 0.1, 0, 2.333, 0, 2, 2.533, 0, 1, 2.6, 0, 2.666, 0.277, 2.733, 0.5, 1, 2.789, 0.686, 2.844, 0.7, 2.9, 0.7, 1, 2.956, 0.7, 3.011, 0.511, 3.067, 0.394, 1, 3.211, 0.088, 3.356, 0, 3.5, 0, 2, 3.633, 0, 1, 4.055, 0, 4.478, 0.002, 4.9, 0.034, 1, 5.078, 0.047, 5.255, 0.8, 5.433, 0.8, 2, 5.8, 0.8, 0, 6.2, 0, 0, 6.867, 0.5, 0, 7.4, 0, 2, 7.667, 0, 0, 8.367, 0.4, 0, 8.867, 0, 0, 9.567, 0.181, 0, 10.133, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRMail", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.8, 1, 2, 2.467, 1, 0, 2.867, 0, 2, 4.467, 0, 2, 8.867, 0, 2, 9.467, 0, 2, 10.133, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "Segments": [0, 0, 2, 0.2, 0, 0, 0.633, 1, 0, 0.967, -1, 0, 1.167, 1, 0, 1.367, -1, 0, 1.533, 0.719, 0, 1.767, -0.585, 0, 1.933, 0.36, 0, 2.167, -0.338, 0, 2.367, 0.143, 0, 2.6, -0.232, 0, 2.8, 0.097, 0, 3.033, -0.112, 0, 3.267, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 0.7, 1, 1, 0.756, 1, 0.811, -2.134, 0.867, -3, 1, 1.067, -6.119, 1.267, -7, 1.467, -7, 2, 1.667, -7, 0, 1.967, -7.32, 1, 2.089, -7.32, 2.211, -7.308, 2.333, -7, 1, 2.522, -6.524, 2.711, -6, 2.9, -6, 0, 3.267, -7, 2, 4.467, -7, 2, 4.9, -7, 0, 5.567, 0, 2, 6.233, 0, 0, 6.967, -6, 2, 7.467, -6, 0, 7.933, -7, 0, 8.867, -5, 0, 9.533, -6.227, 0, 10.033, -5.351, 0, 10.667, -5.789, 0, 11.4, 0.637, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.1, 0, 0, 0.4, 6, 0, 0.7, -2, 0, 1.033, 5, 0, 1.233, 0.771, 0, 1.333, 1.45, 0, 1.467, 0.128, 0, 1.6, 1.45, 0, 1.733, 0.185, 0, 1.867, 1.871, 0, 2.067, 1, 0, 2.333, 5, 0, 2.833, -3, 0, 3.033, 1, 1, 3.2, 1, 3.366, 0.456, 3.533, -2.49, 1, 3.666, -4.847, 3.8, -7.942, 3.933, -7.942, 0, 4.233, 0, 0, 4.467, -5.498, 0, 4.733, 0.771, 0, 5, -0.844, 0, 5.233, 1.441, 0, 5.533, -10, 0, 5.967, 0, 1, 6.145, 0, 6.322, -0.006, 6.5, -1.49, 1, 6.633, -2.603, 6.767, -6.942, 6.9, -6.942, 0, 7.1, 1.318, 0, 7.433, -4.498, 0, 7.833, 0.113, 0, 8.167, -6.914, 0, 8.433, 1.441, 0, 8.733, -4.456, 0, 9.033, 1.441, 0, 9.333, -4.413, 0, 9.533, 1.318, 0, 9.867, -2.717, 0, 10.267, 0.352, 0, 10.6, -3.474, 0, 10.867, 1.441, 0, 11.167, -4.456, 0, 11.633, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 0.2, -0.414, 0, 0.533, 0.856, 0, 0.933, 0, 0, 1.233, 0.353, 0, 1.567, 0, 2, 2.667, 0, 0, 2.9, 1, 1, 3.133, 1, 3.367, 0.615, 3.6, -2, 1, 3.722, -3.37, 3.845, -7.483, 3.967, -7.483, 0, 4.433, 1, 0, 5.067, 0, 0, 5.433, 5, 0, 6.267, 0, 0, 6.433, 0.736, 0, 6.933, -6.483, 0, 7.5, 3.064, 0, 8.2, -5, 0, 8.767, 3, 0, 9.367, -6.483, 0, 9.933, 3.064, 0, 10.633, -5, 0, 11.2, 3, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX2", "Segments": [0, 0, 2, 0.233, 0, 0, 0.567, -0.436, 0, 0.8, 0, 0, 1.233, -6.36, 0, 1.267, -4.782, 0, 1.4, -6.36, 0, 1.533, -5.436, 0, 1.633, -6.36, 1, 1.678, -6.36, 1.722, -5.483, 1.767, -5.342, 1, 2.045, -4.464, 2.322, -4.024, 2.6, -3, 1, 2.711, -2.59, 2.822, 0, 2.933, 0, 2, 4.467, 0, 2, 8.867, 0, 2, 9.467, 0, 2, 10.133, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY2", "Segments": [0, 0, 0, 0.433, 7.979, 1, 0.555, 7.979, 0.678, 0.014, 0.8, 0, 1, 0.944, -0.016, 1.089, -0.013, 1.233, -0.013, 0, 1.367, 1.53, 0, 1.533, -0.013, 0, 1.633, 1.72, 0, 1.8, -0.013, 0, 1.9, 2.289, 0, 2.1, -0.501, 0, 2.367, 0, 0, 2.6, -1, 0, 3.333, 1.038, 0, 3.833, -8, 0, 4.167, -1, 0, 4.333, -5.529, 1, 4.411, -5.529, 4.489, -3.462, 4.567, -2.034, 1, 4.722, 0.822, 4.878, 1.588, 5.033, 1.588, 0, 5.4, -10, 0, 6.333, 2.617, 0, 6.8, -7, 0, 7.033, 0.318, 0, 7.3, -4.529, 0, 7.733, 0.637, 0, 8.1, -7.529, 0, 8.367, 2.168, 0, 8.667, -5.292, 0, 8.967, 2.168, 0, 9.233, -4.454, 0, 9.467, 0.624, 0, 9.733, -2.739, 0, 10.167, 0.637, 0, 10.533, -3.809, 0, 10.8, 2.168, 0, 11.1, -5.292, 0, 11.633, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechX", "Segments": [0, 0, 2, 0.333, 0, 2, 0.6, 0, 0, 1.1, -3, 0, 2.567, 0, 2, 3.067, 0, 0, 3.967, 4, 2, 4.467, 4, 2, 5.233, 4, 0, 6.033, 6.162, 0, 6.967, -1, 0, 7.567, 1, 1, 8, 1, 8.434, 0.599, 8.867, 0, 1, 9.067, -0.276, 9.267, -0.34, 9.467, -0.34, 0, 9.967, 3, 0, 10.6, -0.531, 0, 11.133, 0.256, 0, 11.733, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechW", "Segments": [0, 0, 2, 0.333, 0, 2, 1.033, 0, 0, 1.233, 1.08, 0, 1.267, 0, 0, 1.4, 1.034, 0, 1.533, 0, 0, 1.633, 0.914, 0, 1.767, 0, 2, 4.467, 0, 2, 8.867, 0, 2, 9.467, 0, 2, 10.133, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, 0, 2, 0.033, 0, 0, 0.667, 3, 0, 1.067, -1, 0, 1.3, 1, 0, 1.833, -0.011, 0, 4.467, 0, 2, 4.567, 0, 0, 5.133, -0.34, 0, 5.433, 1, 0, 6.133, 0, 2, 8.867, 0, 2, 9.467, 0, 2, 10.133, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamShoulderR", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, -12, 0, 1.1, 8, 2, 2.067, 8, 0, 2.267, 16, 0, 2.633, -4.143, 0, 2.933, 3, 0, 3.2, -14, 0, 3.767, 0, 2, 4.467, 0, 2, 5.933, 0, 0, 6.4, 11, 0, 6.767, 0, 0, 7.033, 5.123, 0, 7.3, 0, 0, 7.5, 0.223, 0, 7.7, 0, 2, 8.867, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamShoulderL", "Segments": [0, 0, 2, 0.333, 0, 2, 4.467, 0, 2, 5.933, 0, 0, 6.4, 9.243, 0, 6.767, 0, 0, 7.033, 2.243, 0, 7.3, 0, 2, 7.5, 0, 2, 7.7, 0, 2, 8.867, 0, 2, 9.467, 0, 2, 10.133, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Z", "Segments": [0, 0, 2, 0.367, 0, 0, 0.867, -1.596, 0, 1.7, 0.595, 0, 2.7, -0.415, 0, 3.3, 0, 1, 3.7, 0, 4.1, -0.931, 4.5, -1.339, 1, 4.656, -1.498, 4.811, -1.464, 4.967, -1.538, 1, 5.022, -1.564, 5.078, -1.596, 5.133, -1.596, 2, 5.767, -1.596, 0, 7.033, 0.595, 0, 7.9, -0.415, 0, 8.567, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLegR1X", "Segments": [0, 0, 2, 0.567, 0, 0, 1, -3, 0, 1.833, 1.509, 0, 2.767, 0, 2, 3.133, 0, 0, 4.267, -3, 2, 4.967, -3, 2, 5.567, -3, 0, 6.633, 13, 0, 7.667, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Z", "Segments": [0, 0, 2, 0.367, 0, 2, 0.467, 0, 2, 0.633, 0, 0, 1.167, -2, 0, 1.967, 0.138, 0, 2.867, -1, 0, 3.5, 0, 1, 3.922, 0, 4.345, -0.766, 4.767, -1.211, 1, 4.834, -1.281, 4.9, -1.221, 4.967, -1.266, 1, 5.134, -1.379, 5.3, -1.675, 5.467, -1.675, 2, 6.1, -1.675, 0, 7.2, 0.701, 0, 8.167, -0.51, 0, 9.5, 0.138, 0, 10.167, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Z", "Segments": [0, 0, 0, 0.367, -0.632, 1, 1.9, -0.632, 3.434, -0.548, 4.967, -0.379, 1, 7.256, -0.127, 9.544, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyX", "Segments": [0, 0, 1, 0.144, 0, 0.289, -0.023, 0.433, 1, 1, 0.533, 1.708, 0.633, 7, 0.733, 7, 0, 1.133, -3, 0, 1.4, 4, 1, 1.5, 4, 1.6, 1.376, 1.7, 0, 1, 2.089, -5.352, 2.478, -7.178, 2.867, -7.178, 1, 3.567, -7.178, 4.267, -7.299, 4.967, -6.293, 1, 7.256, -3.003, 9.544, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyY", "Segments": [0, 0, 0, 0.467, -1, 0, 0.767, 7, 0, 1.167, -3, 0, 1.467, 4, 1, 1.556, 4, 1.644, 2.554, 1.733, 0, 1, 2.011, -7.98, 2.289, -11.811, 2.567, -11.811, 1, 3.367, -11.811, 4.167, -11.782, 4.967, -9.867, 1, 7.256, -4.388, 9.544, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyZ", "Segments": [0, 0, 0, 0.3, -0.611, 0, 0.733, 2, 0, 1.133, -11.745, 0, 1.7, -5.725, 0, 2.067, -7, 0, 4.533, 1.845, 1, 4.678, 1.845, 4.822, 1.851, 4.967, 1.627, 1, 5.111, 1.403, 5.256, 0.993, 5.4, 0.15, 1, 5.556, -0.758, 5.711, -4.109, 5.867, -4.109, 2, 6, -4.109, 0, 6.767, 7.669, 2, 6.9, 7.669, 1, 7.189, 7.669, 7.478, -5.302, 7.767, -5.82, 1, 7.811, -5.9, 7.856, -5.837, 7.9, -5.837, 0, 8.767, 7.979, 1, 8.845, 7.979, 8.922, 8.074, 9, 7.928, 1, 9.278, 7.407, 9.555, -4.524, 9.833, -4.524, 2, 9.967, -4.524, 0, 11.133, 2, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRPositionZ", "Segments": [0, 0, 2, 0.3, 0, 2, 0.433, 0, 0, 0.833, 2, 0, 1.233, -9.745, 0, 1.833, -5.725, 0, 2.133, -7, 0, 4.333, 0.584, 1, 4.544, 0.584, 4.756, 0.523, 4.967, 0.249, 1, 5.045, 0.148, 5.122, -0.042, 5.2, -0.539, 1, 5.378, -1.675, 5.555, -3.362, 5.733, -3.362, 2, 5.867, -3.362, 0, 6.6, 4.443, 2, 6.767, 4.443, 1, 7.067, 4.443, 7.367, -3.853, 7.667, -4.495, 1, 7.7, -4.566, 7.734, -4.507, 7.767, -4.507, 0, 8.633, 4.649, 1, 8.7, 4.649, 8.766, 4.726, 8.833, 4.615, 1, 9.122, 4.133, 9.411, -3.637, 9.7, -3.637, 2, 9.833, -3.637, 0, 11.033, 0.687, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyX", "Segments": [0, 0, 1, 0.067, 0, 0.133, -0.17, 0.2, -0.732, 1, 0.278, -1.387, 0.355, -1.863, 0.433, -1.863, 0, 0.733, 7, 0, 1.133, -3, 0, 1.4, 4, 1, 1.5, 4, 1.6, 1.376, 1.7, 0, 1, 2.089, -5.352, 2.478, -7.178, 2.867, -7.178, 0, 3.967, 4.747, 1, 4.234, 4.747, 4.5, 3.341, 4.767, 0, 1, 4.834, -0.835, 4.9, -1.178, 4.967, -3.237, 1, 5.211, -10.785, 5.456, -19, 5.7, -19, 0, 6.3, 15.636, 0, 6.833, -15.592, 0, 7.3, 15.636, 0, 7.867, -12.685, 0, 8.4, 13.376, 0, 8.867, -11.343, 0, 9.467, 11.829, 0, 9.967, -5, 0, 10.367, 3.787, 0, 10.933, -2.598, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyY", "Segments": [0, 0, 0, 0.2, 0.865, 0, 0.5, -0.931, 0, 0.767, 7, 0, 1.167, -3, 0, 1.467, 4, 1, 1.556, 4, 1.644, 2.554, 1.733, 0, 1, 2.011, -7.98, 2.289, -11.811, 2.567, -11.811, 0, 4.067, 10.277, 1, 4.311, 10.277, 4.556, 6.567, 4.8, 0, 1, 4.856, -1.493, 4.911, -1.343, 4.967, -3.801, 1, 5.222, -15.106, 5.478, -26.049, 5.733, -26.049, 0, 6.367, 24.049, 0, 6.867, -15.592, 0, 7.333, 22.998, 0, 7.9, -6.282, 0, 8.433, 20.205, 0, 8.9, -10.341, 0, 9.5, 18.294, 0, 9.967, -7.766, 0, 10.4, 3.787, 0, 11.033, -2.598, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyZ", "Segments": [0, 0, 0, 0.3, 1.397, 0, 0.567, -6, 0, 0.967, 3, 0, 1.3, -1.059, 0, 1.633, 0, 0, 2.333, -5.624, 2, 2.433, -5.624, 0, 4.633, 7.585, 1, 4.744, 7.585, 4.856, 6.724, 4.967, 4.235, 1, 4.989, 3.737, 5.011, 1.7, 5.033, 0, 1, 5.255, -17, 5.478, -26.049, 5.7, -26.049, 2, 5.833, -26.049, 0, 6.867, 19.953, 2, 7, 19.953, 1, 7.289, 19.953, 7.578, -19.588, 7.867, -22.035, 1, 7.9, -22.317, 7.934, -22.088, 7.967, -22.088, 0, 8.867, 20.919, 1, 8.945, 20.919, 9.022, 21.197, 9.1, 20.758, 1, 9.378, 19.191, 9.655, -18, 9.933, -18, 2, 10.033, -18, 0, 11.233, 2, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUEyesForm", "Segments": [0, 0, 2, 0.133, 0, 2, 0.367, 0, 2, 0.933, 0, 0, 1.533, -1, 2, 1.9, -1, 0, 2.433, 0, 2, 4.967, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyY", "Segments": [0, 0, 2, 0.133, 0, 2, 0.533, 0, 0, 0.9, 6.26, 0, 1.2, -1.62, 0, 1.333, 2.925, 0, 1.467, -2.598, 0, 1.567, 3.787, 0, 1.667, -3.047, 0, 1.833, 2.925, 0, 1.933, -2.703, 0, 2.067, 2, 1, 2.278, 2, 2.489, 1.31, 2.7, 0, 1, 2.878, -1.103, 3.055, -1.62, 3.233, -1.62, 0, 4.8, 2, 1, 4.856, 2, 4.911, 2.88, 4.967, 1.13, 1, 5.222, -6.919, 5.478, -26.049, 5.733, -26.049, 0, 6.367, 24.049, 0, 6.867, -15.592, 0, 7.333, 22.998, 0, 7.9, -6.282, 0, 8.433, 20.205, 0, 8.9, -10.341, 0, 9.5, 18.294, 0, 9.967, -7.766, 0, 10.4, 3.787, 0, 11.033, -2.598, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyZ", "Segments": [0, 0, 0, 0.433, -2.382, 0, 0.867, 20.858, 2, 0.933, 20.858, 0, 1.167, -27.181, 0, 1.3, 27.181, 0, 1.4, -27.181, 0, 1.533, 27.181, 0, 1.633, -27.181, 0, 1.767, 27.181, 0, 1.967, -10, 0, 2.067, 4, 0, 2.367, -2.382, 0, 2.7, 2.816, 0, 3.133, -1.3, 0, 3.533, 1.227, 0, 4.667, -2.382, 1, 4.767, -2.382, 4.867, -2.083, 4.967, -0.82, 1, 5.045, 0.162, 5.122, 1.227, 5.2, 1.227, 0, 5.833, -26.049, 0, 6.867, 19.953, 0, 7.867, -22.035, 0, 8.867, 20.919, 0, 9.933, -18, 0, 11.233, 2, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUArmZ", "Segments": [0, 0, 2, 0.133, 0, 0, 0.4, 1, 1, 0.467, 1, 0.533, 0.589, 0.6, -2, 1, 0.8, -9.766, 1, -15, 1.2, -15, 0, 1.333, 15.881, 0, 1.467, -17.598, 0, 1.567, 16.598, 0, 1.667, -18.598, 0, 1.833, 16.598, 0, 2.033, -12, 0, 2.2, 11, 0, 2.567, -5.118, 0, 2.8, 0, 2, 4.967, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineU", "Segments": [0, 0, 2, 0.133, 0, 2, 1.233, 0, 2, 1.367, 0, 2, 1.5, 0, 1, 1.533, 0.333, 1.567, 0.667, 1.6, 1, 2, 1.633, 0, 2, 1.7, 0, 2, 1.833, 0, 2, 4.967, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineD", "Segments": [0, 0, 2, 0.133, 0, 2, 1.233, 0, 2, 1.367, 0, 1, 1.411, 0.333, 1.456, 0.667, 1.5, 1, 2, 1.533, 0, 2, 1.6, 0, 1, 1.633, 0.333, 1.667, 0.667, 1.7, 1, 2, 1.733, 0, 2, 1.833, 0, 1, 1.878, 0.333, 1.922, 0.667, 1.967, 1, 2, 2, 0, 2, 2.033, 0, 2, 2.333, 0, 2, 4.967, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyW", "Segments": [0, 0, 2, 0.133, 0, 2, 0.767, 0, 0, 1.167, -0.6, 0, 1.3, 0.4, 0, 1.4, -0.5, 0, 1.567, 0.6, 0, 1.633, -0.5, 0, 1.733, 0.2, 1, 1.789, 0.2, 1.844, 0.136, 1.9, -0.029, 1, 1.956, -0.194, 2.011, -0.3, 2.067, -0.3, 0, 2.367, 0.2, 0, 2.633, -0.2, 0, 2.867, 0.2, 0, 3.233, -0.1, 0, 3.533, 0.1, 0, 4.233, 0, 2, 4.967, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPositionXPanda", "Segments": [0, 0, 2, 0.3, 0, 1, 0.489, 0, 0.678, 0.148, 0.867, 1, 1, 1.678, 4.66, 2.489, 9.019, 3.3, 12.745, 1, 3.856, 15.297, 4.411, 17.658, 4.967, 19.899, 1, 5, 20.033, 5.034, 20, 5.067, 20, 2, 5.667, 20, 1, 5.856, 20, 6.044, 18.285, 6.233, 17.079, 1, 6.822, 13.318, 7.411, 10.989, 8, 7.012, 1, 8.556, 3.26, 9.111, 0, 9.667, 0, 2, 10.2, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.3, 0, 2, 4.967, 0, 2, 5.167, 0, 0, 5.667, 1, 2, 9.667, 1, 0, 10.2, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyX", "Segments": [0, 0, 2, 0.533, 0, 0, 0.633, 2.538, 0, 0.8, -2.538, 0, 0.933, 2.538, 0, 1.033, -2.538, 0, 1.267, 2.538, 0, 1.467, -2.538, 0, 1.667, 2.538, 0, 1.767, -2.538, 0, 2, 2.538, 0, 2.067, -2.538, 0, 2.3, 2.538, 0, 2.467, -2.538, 0, 2.633, 2.538, 0, 2.8, -2.538, 0, 2.967, 2.538, 0, 3.067, -2.538, 0, 3.367, 2.538, 0, 3.467, -2.538, 0, 3.6, 2.538, 0, 4.067, -2.538, 0, 4.267, 2.538, 0, 4.533, -2.538, 0, 4.8, 2.538, 1, 4.856, 2.538, 4.911, 2.304, 4.967, 0.763, 1, 5, -0.162, 5.034, -2.538, 5.067, -2.538, 0, 5.267, 2.538, 0, 5.5, 0, 2, 6, 0, 0, 6.333, -2.538, 0, 6.6, 2.538, 0, 6.867, -2.538, 0, 7.167, 2.538, 0, 7.3, -2.538, 0, 7.533, 2.538, 0, 7.733, -2.538, 0, 7.933, 2.538, 0, 8.2, -2.538, 0, 8.4, 2.538, 0, 8.6, -2.538, 0, 8.767, 2.538, 0, 9, -2.538, 0, 9.333, 2.538, 0, 9.667, -2.538, 0, 9.933, 2.538, 0, 10.1, 0, 2, 10.2, 0, 2, 10.367, 0, 2, 10.567, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyY", "Segments": [0, 0, 2, 0.467, 0, 0, 0.567, 3.183, 0, 0.733, -3.183, 0, 0.867, 3.183, 0, 0.967, -3.183, 0, 1.2, 3.183, 0, 1.367, -3.183, 0, 1.6, 3.183, 0, 1.7, -3.183, 0, 1.933, 3.183, 0, 2.033, -3.183, 0, 2.233, 3.183, 0, 2.4, -3.183, 0, 2.567, 3.183, 0, 2.767, -3.183, 0, 2.867, 3.183, 0, 3.033, -3.183, 0, 3.3, 3.183, 0, 3.467, -3.183, 0, 3.533, 3.183, 0, 3.967, -3.183, 0, 4.2, 3.183, 0, 4.5, -3.183, 0, 4.7, 3.183, 1, 4.789, 3.183, 4.878, 1.243, 4.967, -2.526, 1, 4.978, -2.997, 4.989, -3.183, 5, -3.183, 0, 5.133, 3.183, 0, 5.433, 0, 2, 5.9, 0, 0, 6.233, -3.183, 0, 6.533, 3.183, 0, 6.767, -3.183, 0, 7.067, 3.183, 0, 7.2, -3.183, 0, 7.467, 3.183, 0, 7.667, -3.183, 0, 7.867, 3.183, 0, 8.133, -3.183, 0, 8.333, 3.183, 0, 8.533, -3.183, 0, 8.733, 3.183, 0, 8.867, -3.183, 0, 9.233, 3.183, 0, 9.5, -3.183, 0, 9.767, 3.183, 0, 9.967, 0, 2, 10.2, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2Panda", "Segments": [0, 0, 2, 0.3, 0, 0, 0.4, -6.229, 0, 0.467, 6.229, 0, 0.533, -6.229, 0, 0.633, 6.229, 0, 0.667, -6.229, 0, 0.767, 6.229, 0, 0.867, -6.229, 0, 0.933, 6.229, 0, 1.033, -6.229, 0, 1.1, 6.229, 0, 1.167, -6.229, 0, 1.267, 6.229, 0, 1.367, -6.229, 0, 1.4, 6.229, 0, 1.5, -6.229, 0, 1.567, 6.229, 0, 1.667, -6.229, 0, 1.733, 6.229, 0, 1.833, -6.229, 0, 1.933, 6.229, 0, 2, -6.229, 0, 2.067, 6.229, 0, 2.1, -6.229, 0, 2.233, 6.229, 0, 2.3, -6.229, 0, 2.4, 6.229, 0, 2.433, -6.229, 0, 2.567, 6.229, 0, 2.633, -6.229, 0, 2.733, 6.229, 0, 2.767, -6.229, 0, 2.833, 6.229, 0, 2.967, -6.229, 0, 3.033, 6.229, 0, 3.067, -6.229, 0, 3.167, 6.229, 0, 3.3, -6.229, 0, 3.367, 6.229, 0, 3.433, -6.229, 0, 3.467, 6.229, 0, 3.567, -6.229, 0, 3.9, 6.229, 0, 4.033, -6.229, 0, 4.133, 6.229, 0, 4.2, -6.229, 0, 4.267, 6.229, 0, 4.4, -6.229, 0, 4.567, 6.229, 0, 4.667, -6.229, 0, 4.8, 6.229, 0, 4.967, -6.229, 0, 5.033, 6.229, 0, 5.133, 0, 2, 5.733, 0, 0, 5.833, -6.229, 0, 5.9, 6.229, 0, 6, -6.229, 0, 6.133, 6.229, 0, 6.233, -6.229, 0, 6.333, 6.229, 0, 6.433, -6.229, 0, 6.567, 6.229, 0, 6.733, -6.229, 0, 6.767, 6.229, 0, 6.967, -6.229, 0, 7, 6.229, 0, 7.067, -6.229, 0, 7.167, 6.229, 0, 7.267, -6.229, 0, 7.433, 6.229, 0, 7.5, -6.229, 0, 7.6, 6.229, 0, 7.7, -6.229, 0, 7.8, 6.229, 0, 7.9, -6.229, 0, 7.967, 6.229, 0, 8.067, -6.229, 0, 8.2, 6.229, 0, 8.333, -6.229, 0, 8.4, 6.229, 0, 8.533, -6.229, 0, 8.6, 6.229, 0, 8.733, -6.229, 0, 8.767, 6.229, 0, 8.867, -6.229, 0, 9.033, 6.229, 0, 9.167, -6.229, 0, 9.3, 6.229, 0, 9.433, -6.229, 0, 9.5, 6.229, 0, 9.6, -6.229, 0, 9.767, 0, 2, 10.2, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyZ", "Segments": [0, 0, 0, 0.533, 1.431, 0, 0.667, -1.431, 0, 0.833, 1.431, 0, 0.933, -1.431, 0, 1.167, 1.431, 0, 1.367, -1.431, 0, 1.567, 1.431, 0, 1.667, -1.431, 0, 1.9, 1.431, 0, 2, -1.431, 0, 2.2, 1.431, 0, 2.367, -1.431, 0, 2.5, 1.431, 0, 2.733, -1.431, 0, 2.833, 1.431, 0, 2.967, -1.431, 0, 3.233, 1.431, 0, 3.4, -1.431, 0, 3.5, 1.431, 0, 3.933, -1.431, 0, 4.167, 1.431, 0, 4.333, -1.431, 0, 4.667, 1.431, 0, 4.967, -1.431, 0, 5.067, 1.431, 1, 5.178, 1.431, 5.289, 1.48, 5.4, 0, 1, 5.478, -1.036, 5.555, -30, 5.633, -30, 0, 5.933, 1.431, 0, 6.2, -1.431, 0, 6.5, 1.431, 0, 6.733, -1.431, 0, 7.033, 1.431, 0, 7.167, -1.431, 0, 7.433, 1.431, 0, 7.6, -1.431, 0, 7.8, 1.431, 0, 8.067, -1.431, 0, 8.233, 1.431, 0, 8.467, -1.431, 0, 8.633, 1.431, 0, 8.833, -1.431, 0, 9.167, 1.431, 0, 9.467, -1.431, 0, 9.733, 1.431, 1, 9.8, 1.431, 9.866, 1.52, 9.933, 0, 1, 9.989, -1.267, 10.044, -21, 10.1, -21, 0, 10.367, 9, 0, 10.733, -9.429, 0, 11.167, 5.312, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegFZ", "Segments": [0, 0, 2, 0.233, 0, 0, 0.4, 0.6, 0, 0.533, -1, 0, 0.667, 1, 0, 0.867, -1, 0, 1.1, 1, 0, 1.2, -1, 0, 1.367, 1, 0, 1.5, -1, 0, 1.667, 1, 0, 1.833, -1, 0, 2, 1, 0, 2.1, -1, 0, 2.3, 1, 0, 2.467, -1, 0, 2.667, 1, 0, 2.767, -1, 0, 2.967, 1, 0, 3.067, -1, 0, 3.3, 1, 0, 3.467, -1, 0, 3.567, 1, 0, 4.033, -1, 0, 4.233, 1, 0, 4.533, -1, 0, 4.8, 1, 1, 4.856, 1, 4.911, 0.582, 4.967, -0.475, 1, 4.978, -0.686, 4.989, -1, 5, -1, 0, 5.133, 0, 2, 5.633, 0, 0, 5.767, 1, 0, 5.933, -1, 0, 6.2, 1, 0, 6.4, -1, 0, 6.6, 1, 0, 6.833, -1, 0, 7.033, 1, 0, 7.167, -1, 0, 7.467, 1, 0, 7.667, -1, 0, 7.867, 1, 0, 8, -1, 0, 8.233, 1, 0, 8.467, -1, 0, 8.633, 1, 0, 8.833, -1, 0, 9.1, 1, 0, 9.3, -1, 0, 9.667, 1, 0, 9.767, 0, 2, 10.2, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegBZ", "Segments": [0, 0, 2, 0.233, 0, 0, 0.4, 0.6, 0, 0.533, -1, 0, 0.667, 1, 0, 0.867, -1, 0, 1.1, 1, 0, 1.2, -1, 0, 1.367, 1, 0, 1.5, -1, 0, 1.667, 1, 0, 1.833, -1, 0, 2, 1, 0, 2.1, -1, 0, 2.3, 1, 0, 2.467, -1, 0, 2.667, 1, 0, 2.767, -1, 0, 2.967, 1, 0, 3.067, -1, 0, 3.3, 1, 0, 3.467, -1, 0, 3.567, 1, 0, 4.033, -1, 0, 4.233, 1, 0, 4.533, -1, 0, 4.8, 1, 1, 4.856, 1, 4.911, 0.582, 4.967, -0.475, 1, 4.978, -0.686, 4.989, -1, 5, -1, 0, 5.133, 0, 2, 5.633, 0, 0, 5.767, 1, 0, 5.933, -1, 0, 6.2, 1, 0, 6.4, -1, 0, 6.6, 1, 0, 6.833, -1, 0, 7.033, 1, 0, 7.167, -1, 0, 7.467, 1, 0, 7.667, -1, 0, 7.867, 1, 0, 8, -1, 0, 8.233, 1, 0, 8.467, -1, 0, 8.633, 1, 0, 8.833, -1, 0, 9.1, 1, 0, 9.3, -1, 0, 9.667, 1, 0, 9.767, 0, 2, 10.2, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyY", "Segments": [0, 0, 2, 0.3, 0, 2, 0.5, 0, 0, 0.733, -7, 0, 1.133, 3, 0, 1.4, -4, 0, 1.7, 0, 2, 4.967, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamCannonZ", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, 6, 0, 0.933, -3, 0, 1.267, 1.059, 0, 1.6, 0, 2, 2.7, 0, 0, 2.933, 2.231, 0, 3.4, -13, 0, 3.6, -11.678, 0, 4.233, -12.606, 2, 4.967, -12.606, 2, 5.133, -12.606, 0, 6.3, 11.702, 0, 7.067, 0, 0, 7.7, 2, 0, 8.133, -2, 0, 8.567, 0.493, 0, 8.9, 0, 1, 9.1, 0, 9.3, 0.035, 9.5, 0.285, 1, 9.567, 0.368, 9.633, 2.76, 9.7, 2.76, 0, 10.333, 0, 2, 10.833, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamCannonGaY", "Segments": [0, 0, 2, 0.433, 0, 0, 0.6, -8.384, 0, 1, 3.845, 0, 1.333, -3.618, 0, 1.633, 3.37, 0, 1.967, -1.16, 0, 2.3, 1.234, 1, 2.456, 1.234, 2.611, 1.062, 2.767, -0.231, 1, 2.845, -0.877, 2.922, -3.248, 3, -3.248, 0, 3.467, 11.418, 0, 3.933, 6, 0, 4.333, 10.888, 0, 4.767, 6.475, 1, 4.834, 6.475, 4.9, 6.517, 4.967, 7.623, 1, 5.045, 8.913, 5.122, 10.888, 5.2, 10.888, 0, 6.367, -11, 0, 7.167, 3.37, 0, 7.9, -2.389, 0, 8.233, 1.41, 0, 8.733, -1.075, 0, 9.133, 0.472, 1, 9.2, 0.472, 9.266, 0.552, 9.333, -0.231, 1, 9.478, -1.928, 9.622, -3.981, 9.767, -3.981, 0, 10.4, 0, 0, 11.033, -1.075, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupY", "Segments": [0, 0, 2, 0.467, 0, 0, 0.633, 0.2, 0, 1.033, -0.1, 0, 1.367, 0.035, 0, 1.7, 0, 2, 2.767, 0, 0, 3.033, 0.074, 0, 3.467, -0.433, 0, 3.967, -0.389, 0, 4.4, -0.42, 2, 4.967, -0.42, 2, 5.267, -0.42, 0, 6.4, 0.39, 0, 7.2, 0, 2, 9.1, 0, 1, 9.289, 0, 9.478, 0.001, 9.667, 0.01, 1, 9.722, 0.013, 9.778, 0.092, 9.833, 0.092, 0, 10.433, 0, 2, 10.933, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupZ", "Segments": [0, 0, 2, 0.3, 0, 0, 0.5, 0.2, 0, 0.9, -0.1, 0, 1.233, 0.035, 0, 1.567, 0, 2, 2.667, 0, 0, 2.867, 0.074, 0, 3.367, -0.433, 0, 3.567, -0.389, 0, 4.2, -0.42, 2, 4.967, -0.42, 2, 5.067, -0.42, 0, 6.233, 0.39, 0, 7.033, 0, 2, 8.867, 0, 1, 9.067, 0, 9.267, 0.001, 9.467, 0.01, 1, 9.534, 0.013, 9.6, 0.092, 9.667, 0.092, 0, 10.267, 0, 2, 10.8, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamCannonHandZ", "Segments": [0, 0, 2, 0.3, 0, 2, 4.767, 0, 2, 4.967, 0, 2, 6.733, 0, 0, 7.5, 8, 2, 9.967, 8, 0, 10.333, 10, 0, 11.367, 0, 2, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamParamStrongCatZ", "Segments": [0, 0, 2, 0.3, 0, 0, 0.733, 2, 0, 1.133, -11.745, 0, 1.7, -5.725, 0, 2.067, -7, 0, 4.333, 1.845, 1, 4.544, 1.845, 4.756, 1.751, 4.967, 1.335, 1, 5.045, 1.182, 5.122, 0.895, 5.2, 0.15, 1, 5.378, -1.552, 5.555, -4.109, 5.733, -4.109, 2, 5.867, -4.109, 0, 6.6, 7.669, 2, 6.767, 7.669, 1, 7.067, 7.669, 7.367, -4.914, 7.667, -5.82, 1, 7.7, -5.921, 7.734, -5.837, 7.767, -5.837, 0, 8.633, 7.979, 1, 8.7, 7.979, 8.766, 8.094, 8.833, 7.928, 1, 9.122, 7.21, 9.411, -4.524, 9.7, -4.524, 2, 9.833, -4.524, 0, 11.033, 2, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamParamSCBodyZ", "Segments": [0, 0, 2, 0.3, 0, 2, 0.433, 0, 0, 0.833, 2, 0, 1.233, -9.745, 0, 1.833, -5.725, 0, 2.133, -7, 0, 4.533, 1.845, 1, 4.678, 1.845, 4.822, 1.851, 4.967, 1.627, 1, 5.111, 1.403, 5.256, 0.993, 5.4, 0.15, 1, 5.556, -0.758, 5.711, -4.109, 5.867, -4.109, 2, 6, -4.109, 0, 6.767, 7.669, 2, 6.9, 7.669, 1, 7.189, 7.669, 7.478, -5.302, 7.767, -5.82, 1, 7.811, -5.9, 7.856, -5.837, 7.9, -5.837, 0, 8.767, 7.979, 1, 8.845, 7.979, 8.922, 8.074, 9, 7.928, 1, 9.278, 7.407, 9.555, -4.524, 9.833, -4.524, 2, 9.967, -4.524, 0, 11.133, 2, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamSCDishY", "Segments": [0, 0, 2, 0.3, 0, 2, 0.533, 0, 0, 0.933, 2, 0, 1.333, -2.745, 0, 1.9, 1.275, 0, 2.233, 0, 0, 4.533, 1.845, 1, 4.678, 1.845, 4.822, 1.851, 4.967, 1.627, 1, 5.111, 1.403, 5.256, 0.993, 5.4, 0.15, 1, 5.556, -0.758, 5.711, -4.109, 5.867, -4.109, 2, 6, -4.109, 0, 6.767, 7.669, 2, 6.9, 7.669, 1, 7.189, 7.669, 7.478, -5.302, 7.767, -5.82, 1, 7.811, -5.9, 7.856, -5.837, 7.9, -5.837, 0, 8.767, 7.979, 1, 8.845, 7.979, 8.922, 8.074, 9, 7.928, 1, 9.278, 7.407, 9.555, -4.524, 9.833, -4.524, 2, 9.967, -4.524, 0, 11.133, 2, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamSCDishZ", "Segments": [0, 0, 2, 0.3, 0, 2, 0.567, 0, 0, 0.967, 2, 0, 1.367, -2.745, 0, 1.933, 1.275, 0, 2.267, 0, 0, 4.7, 1.845, 1, 4.789, 1.845, 4.878, 1.873, 4.967, 1.808, 1, 5.167, 1.661, 5.367, 1.049, 5.567, 0.15, 1, 5.745, -0.649, 5.922, -4.109, 6.1, -4.109, 2, 6.2, -4.109, 0, 6.967, 7.669, 2, 7.067, 7.669, 1, 7.356, 7.669, 7.644, -5.302, 7.933, -5.82, 1, 7.978, -5.9, 8.022, -5.837, 8.067, -5.837, 0, 9, 7.979, 1, 9.056, 7.979, 9.111, 8.109, 9.167, 7.928, 1, 9.445, 7.022, 9.722, -4.524, 10, -4.524, 2, 10.133, -4.524, 0, 11.333, 2, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamSCCupZ", "Segments": [0, 0, 2, 0.3, 0, 2, 0.633, 0, 0, 1.1, 5.028, 0, 1.467, -5.773, 0, 2.067, 3.378, 0, 2.333, -1.036, 0, 4.8, 1.845, 1, 4.856, 1.845, 4.911, 1.859, 4.967, 1.838, 1, 5.2, 1.749, 5.434, 1.021, 5.667, 0.15, 1, 5.834, -0.472, 6, -4.109, 6.167, -4.109, 2, 6.3, -4.109, 0, 7.033, 7.669, 2, 7.167, 7.669, 1, 7.445, 7.669, 7.722, -5.493, 8, -5.82, 1, 8.056, -5.885, 8.111, -5.837, 8.167, -5.837, 0, 9.1, 7.979, 1, 9.156, 7.979, 9.211, 8.109, 9.267, 7.928, 1, 9.545, 7.022, 9.822, -4.524, 10.1, -4.524, 2, 10.2, -4.524, 0, 11.4, 2, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "MB_DRFWXZKTMD", "Segments": [0, 1, 0, 11.833, 1]}, {"Target": "Parameter", "Id": "ParamAllSizeFix", "Segments": [0, 1, 0, 11.833, 1]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBGHide", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBG2Hide", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBGX", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBGY", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN2", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN3", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBlackY", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBlackCollar", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBlackOrder", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamWhiteIN", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamCHHide", "Segments": [0, 1, 0, 11.833, 1]}, {"Target": "Parameter", "Id": "ParamDeskHide", "Segments": [0, 1, 0, 11.833, 1]}, {"Target": "Parameter", "Id": "ParamStoolHide", "Segments": [0, 1, 0, 11.833, 1]}, {"Target": "Parameter", "Id": "ParamCupDesk", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamCHX", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamCHY", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamCHZ", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamChaSize", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamCcharacterZ", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionX", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionY", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionX", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionY", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamAllZ", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamAllSize", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamALLSize2", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamDeskShow", "Segments": [0, 10, 0, 11.833, 10]}, {"Target": "Parameter", "Id": "ParamStrongCatShow", "Segments": [0, 10, 0, 11.833, 10]}, {"Target": "Parameter", "Id": "ParamCannonShow", "Segments": [0, 10, 0, 11.833, 10]}, {"Target": "Parameter", "Id": "ParamLightPositionX", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamFixT", "Segments": [0, 1, 0, 11.833, 1]}, {"Target": "Parameter", "Id": "ParamFlap", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamScare", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPupilExp", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpen2", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthType", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBlackFace", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamTeethLight", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamHeart2", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamMark", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamShameLine", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamMarkShake", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLX", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLY", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeL", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRX", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRY", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeR", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeRLightOpen", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLightLine1", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLightLine2", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLightLine3", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo1", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo2", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo3", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1Y", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle2", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow1", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow2", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamTearLight", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamTears", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperLAngle", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamArmHandLAngle", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerLAngle", "Segments": [0, 10, 0, 11.833, 10]}, {"Target": "Parameter", "Id": "ParamArmLowerLH", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerLAngle", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamHandT2L", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamFanOpenR", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamChili", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamChiliX", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperRH", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamHandR", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRY", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamHand_Cl", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamHandT1R", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRCup", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "Segments": [0, 1, 0, 11.833, 1]}, {"Target": "Parameter", "Id": "ParamHandLIQY1", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY2", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY3", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamHandCupZ", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamHandCupY", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Y", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Y", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Y", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamFootRX", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Z", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Z", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Z", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Y", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Y", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Y", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamFootLX", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLegLF", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRHide", "Segments": [0, 1, 0, 11.833, 1]}, {"Target": "Parameter", "Id": "ParamMJRFlap", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuR", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuR", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuR", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuR", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRInput", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamMRCupSet", "Segments": [0, 1, 0, 11.833, 1]}, {"Target": "Parameter", "Id": "ParamMalpositionManjuuR", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuREyeOpen", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRArmB", "Segments": [0, 30, 0, 11.833, 30]}, {"Target": "Parameter", "Id": "ParamManjuuRMouth", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRSigh", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow1", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow2", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow3", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow4", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowB", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW2", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamMRCupFZ", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX1", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX2", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLiqH", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX1", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX2", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX3", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLHide", "Segments": [0, 1, 0, 11.833, 1]}, {"Target": "Parameter", "Id": "ParamMJLSigh", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuL", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuL", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuL", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamMjLFlip", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPositionZManjuuL", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLEyeOpen", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyW", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuL", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamClawFX", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamClawFY", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamClawBX", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamClawBY", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUHide", "Segments": [0, 1, 0, 11.833, 1]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuU", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuU", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuU", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyX", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamPandaHide", "Segments": [0, 1, 0, 11.833, 1]}, {"Target": "Parameter", "Id": "ParamPositionYPanda", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamSizePanda", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyX", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY1", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY2", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY3", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupIce", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupZ", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupInput", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamSCDishRO", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamSCCupRO", "Segments": [0, 0, 0, 11.833, 0]}, {"Target": "Parameter", "Id": "ParamSCCupY", "Segments": [0, 0, 0, 11.833, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.0, "Value": ""}, {"Time": 11.333, "Value": ""}]}