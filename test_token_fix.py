#!/usr/bin/env python3
"""
测试token限制修复
验证max_tokens参数是否正确应用
"""

import json
import sys
import os

def test_token_config():
    """测试token配置"""
    print("🔍 测试token配置修复")
    print("=" * 60)
    
    try:
        from openai import OpenAI
        
        # 读取配置
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        # 使用llm配置
        llm_config = config.get("llm", {})
        api_config = llm_config.get("api_config", {})
        default_params = llm_config.get("default_params", {})
        
        print(f"📋 当前配置:")
        print(f"   - API地址: {api_config.get('base_url')}")
        print(f"   - 模型: {api_config.get('model')}")
        print(f"   - max_tokens: {default_params.get('max_tokens')}")
        print(f"   - temperature: {default_params.get('temperature')}")
        print(f"   - top_p: {default_params.get('top_p')}")
        
        # 创建客户端
        client = OpenAI(
            base_url=api_config["base_url"],
            api_key=api_config["api_key"],
            timeout=api_config.get("timeout", 30.0)
        )
        
        # 准备请求参数
        request_params = {
            "messages": [
                {"role": "system", "content": "你是我的ai女儿"},
                {"role": "user", "content": "你在干嘛？"}
            ],
            "model": api_config["model"],
            **{k: v for k, v in default_params.items() if v is not None}
        }
        
        print("\n📤 发送的请求格式:")
        print(json.dumps(request_params, indent=2, ensure_ascii=False))
        
        # 发送请求
        response = client.chat.completions.create(**request_params)
        
        print("\n📥 收到回复:")
        print(f"🔍 响应ID: {response.id}")
        print(f"🔍 模型: {response.model}")
        
        if response.choices:
            choice = response.choices[0]
            print(f"🔍 finish_reason: {choice.finish_reason}")
            print(f"🔍 message存在: {choice.message is not None}")
            
            if choice.message and choice.message.content:
                content = choice.message.content
                print(f"✅ 成功收到回复: {content}")
                return True
            else:
                print(f"❌ message为空或content为空")
                print(f"🔍 choice详情: {choice}")
                return False
        else:
            print("❌ 没有choices")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_max_tokens():
    """测试不同的max_tokens值"""
    print("\n🔍 测试不同的max_tokens值")
    print("=" * 60)
    
    try:
        from openai import OpenAI
        
        # 读取配置
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        llm_config = config.get("llm", {})
        api_config = llm_config.get("api_config", {})
        default_params = llm_config.get("default_params", {})
        
        # 创建客户端
        client = OpenAI(
            base_url=api_config["base_url"],
            api_key=api_config["api_key"],
            timeout=api_config.get("timeout", 30.0)
        )
        
        # 测试不同的max_tokens值
        test_values = [2000, 4000, 8000]
        
        for max_tokens in test_values:
            print(f"\n🧪 测试 max_tokens = {max_tokens}")
            
            # 准备请求参数
            test_params = default_params.copy()
            test_params["max_tokens"] = max_tokens
            
            request_params = {
                "messages": [
                    {"role": "system", "content": "你是我的ai女儿"},
                    {"role": "user", "content": "请详细介绍一下你自己"}
                ],
                "model": api_config["model"],
                **{k: v for k, v in test_params.items() if v is not None}
            }
            
            try:
                response = client.chat.completions.create(**request_params)
                
                if response.choices:
                    choice = response.choices[0]
                    print(f"   finish_reason: {choice.finish_reason}")
                    
                    if choice.message and choice.message.content:
                        content = choice.message.content
                        print(f"   ✅ 成功 - 回复长度: {len(content)} 字符")
                        print(f"   📝 回复预览: {content[:100]}...")
                    else:
                        print(f"   ❌ 失败 - message为空")
                else:
                    print(f"   ❌ 失败 - 没有choices")
                    
            except Exception as e:
                print(f"   ❌ 请求失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    # 测试当前配置
    success1 = test_token_config()
    
    # 测试不同token值
    success2 = test_different_max_tokens()
    
    if success1 and success2:
        print("\n🎉 token配置修复成功！")
        print("💡 现在快速输入应该能正常收到完整回复了")
    else:
        print("\n❌ 仍有问题，需要进一步调试")
