#!/usr/bin/env python3
"""
Live2D语音对话系统 - 语音转文本(STT)模块

这个模块提供了完整的语音识别功能，包括：
- faster-whisper本地语音识别
- STT管理器和配置
- 音频预处理和后处理
- 多语言支持

使用示例：
    from dialogue_system.stt import STTManager, FasterWhisperClient

    # 创建STT管理器
    stt_manager = STTManager(config_manager)

    # 识别音频
    text = stt_manager.transcribe_audio(audio_data)

高级使用示例：
    from dialogue_system.stt import FasterWhisperClient

    # 直接使用faster-whisper客户端
    whisper_client = FasterWhisperClient(model_path, device="auto")
    result = whisper_client.transcribe(audio_file_path)
"""

# 导入核心组件
from .faster_whisper_client import FasterWhisperClient
from .stt_manager import STTManager

__version__ = "1.0.0"
__author__ = "Live2D Voice Dialogue Team"

# 导出主要类
__all__ = [
    'FasterWhisperClient',
    'STTManager'
]
