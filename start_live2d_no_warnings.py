#!/usr/bin/env python3
"""
启动Live2D应用（无字体警告版本）
"""

import os
import sys
import subprocess

def start_live2d():
    """启动Live2D应用并抑制字体警告"""
    print("🚀 启动Live2D应用（无字体警告版本）")
    print("=" * 50)
    
    # 设置环境变量抑制Qt字体警告
    env = os.environ.copy()
    env["QT_LOGGING_RULES"] = "qt.qpa.fonts.warning=false"
    
    print("✅ 设置环境变量抑制字体警告")
    print("📂 切换到dev目录")
    
    # 切换到dev目录
    dev_path = os.path.join(os.path.dirname(__file__), "dev")
    
    try:
        # 启动main_window.py
        print("🎯 启动main_window.py...")
        
        result = subprocess.run(
            [sys.executable, "main_window.py"],
            cwd=dev_path,
            env=env,
            capture_output=False  # 让输出直接显示
        )
        
        print(f"\n📊 应用程序退出，返回码: {result.returncode}")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    start_live2d()
