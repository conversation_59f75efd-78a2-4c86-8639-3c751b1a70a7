#!/usr/bin/env python3
"""
Live2D语音对话系统测试程序

这个程序用于测试语音对话系统的各个组件，包括：
- 麦克风管理器测试
- 语音输入模式测试
- STT功能测试
- TTS功能测试
- 完整对话流程测试

使用方法：
    python test_voice_dialogue.py
"""

import sys
import os
import time
import json
import asyncio
from pathlib import Path
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget
from PySide6.QtWidgets import QPushButton, QLabel, QTextEdit, QComboBox, QSlider, QProgressBar
from PySide6.QtCore import Qt, QTimer, Signal, QThread, pyqtSignal
from PySide6.QtGui import QFont

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入语音对话系统模块
try:
    from dialogue_system.voice import VoiceDialogueManager
    from dialogue_system.stt import STTManager
    from dialogue_system.tts import TTSManager
    from dev.settings_dialog import ConfigManager
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    import traceback
    traceback.print_exc()
    MODULES_AVAILABLE = False


class VoiceDialogueTestWindow(QMainWindow):
    """语音对话测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Live2D语音对话系统测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 初始化组件
        self.config_manager = None
        self.voice_manager = None
        self.stt_manager = None
        self.tts_manager = None
        
        # 状态
        self.is_testing = False
        self.test_results = {}
        
        self.init_ui()
        self.init_components()
        
    def init_ui(self):
        """初始化UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("Live2D语音对话系统测试")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 状态显示
        self.status_label = QLabel("状态: 未初始化")
        layout.addWidget(self.status_label)
        
        # 测试按钮区域
        button_layout = QHBoxLayout()
        
        self.init_btn = QPushButton("初始化系统")
        self.init_btn.clicked.connect(self.init_components)
        button_layout.addWidget(self.init_btn)
        
        self.test_mic_btn = QPushButton("测试麦克风")
        self.test_mic_btn.clicked.connect(self.test_microphone)
        button_layout.addWidget(self.test_mic_btn)
        
        self.test_stt_btn = QPushButton("测试STT")
        self.test_stt_btn.clicked.connect(self.test_stt)
        button_layout.addWidget(self.test_stt_btn)
        
        self.test_tts_btn = QPushButton("测试TTS")
        self.test_tts_btn.clicked.connect(self.test_tts)
        button_layout.addWidget(self.test_tts_btn)
        
        layout.addLayout(button_layout)
        
        # 语音输入测试区域
        voice_group = QWidget()
        voice_layout = QVBoxLayout(voice_group)
        
        voice_label = QLabel("语音输入测试")
        voice_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        voice_layout.addWidget(voice_label)
        
        voice_button_layout = QHBoxLayout()
        
        self.key_trigger_btn = QPushButton("按键触发模式")
        self.key_trigger_btn.clicked.connect(self.test_key_triggered_input)
        voice_button_layout.addWidget(self.key_trigger_btn)
        
        self.realtime_btn = QPushButton("实时语音模式")
        self.realtime_btn.clicked.connect(self.test_realtime_input)
        voice_button_layout.addWidget(self.realtime_btn)
        
        self.stop_voice_btn = QPushButton("停止语音输入")
        self.stop_voice_btn.clicked.connect(self.stop_voice_input)
        voice_button_layout.addWidget(self.stop_voice_btn)
        
        voice_layout.addLayout(voice_button_layout)
        
        # 音量指示器
        self.volume_label = QLabel("音量: 0%")
        voice_layout.addWidget(self.volume_label)
        
        self.volume_bar = QProgressBar()
        self.volume_bar.setRange(0, 100)
        voice_layout.addWidget(self.volume_bar)
        
        layout.addWidget(voice_group)
        
        # TTS测试区域
        tts_group = QWidget()
        tts_layout = QVBoxLayout(tts_group)
        
        tts_label = QLabel("TTS测试")
        tts_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        tts_layout.addWidget(tts_label)
        
        self.tts_text_input = QTextEdit()
        self.tts_text_input.setPlainText("你好，这是语音合成测试。")
        self.tts_text_input.setMaximumHeight(80)
        tts_layout.addWidget(self.tts_text_input)
        
        tts_button_layout = QHBoxLayout()
        
        self.speak_btn = QPushButton("合成并播放")
        self.speak_btn.clicked.connect(self.test_speak)
        tts_button_layout.addWidget(self.speak_btn)
        
        self.stop_speak_btn = QPushButton("停止播放")
        self.stop_speak_btn.clicked.connect(self.stop_speaking)
        tts_button_layout.addWidget(self.stop_speak_btn)
        
        tts_layout.addLayout(tts_button_layout)
        
        layout.addWidget(tts_group)
        
        # 日志显示
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setPlainText("=== 测试日志 ===\n")
        layout.addWidget(self.log_text)
        
        # 定时器用于更新状态
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(100)  # 100ms更新一次
        
    def init_components(self):
        """初始化组件"""
        if not MODULES_AVAILABLE:
            self.log("❌ 模块不可用，无法初始化")
            return
            
        try:
            self.log("🔄 正在初始化语音对话系统...")
            
            # 加载配置
            self.config_manager = ConfigManager()
            self.log("✅ 配置管理器加载完成")
            
            # 初始化语音管理器
            self.voice_manager = VoiceDialogueManager(self.config_manager)
            self.log("✅ 语音管理器初始化完成")
            
            # 初始化STT管理器
            self.stt_manager = STTManager(self.config_manager)
            self.log("✅ STT管理器初始化完成")
            
            # 初始化TTS管理器
            self.tts_manager = TTSManager(self.config_manager)
            self.log("✅ TTS管理器初始化完成")
            
            # 设置回调
            self.setup_callbacks()
            
            self.log("🎉 语音对话系统初始化成功！")
            self.status_label.setText("状态: 已初始化")
            
        except Exception as e:
            self.log(f"❌ 初始化失败: {e}")
            import traceback
            traceback.print_exc()
            
    def setup_callbacks(self):
        """设置回调函数"""
        if self.voice_manager:
            # 设置音量变化回调
            if hasattr(self.voice_manager, 'microphone_manager'):
                # 这里可以设置音量监听回调
                pass
                
    def test_microphone(self):
        """测试麦克风"""
        if not self.voice_manager:
            self.log("❌ 语音管理器未初始化")
            return
            
        try:
            self.log("🎤 测试麦克风...")
            
            # 启动麦克风
            if self.voice_manager.microphone_manager.start():
                self.log("✅ 麦克风启动成功")
                
                # 测试音频级别
                time.sleep(1)
                audio_level = self.voice_manager.microphone_manager.get_audio_level()
                self.log(f"📊 当前音频级别: {audio_level:.3f}")
                
                # 获取设备信息
                device_info = self.voice_manager.microphone_manager.get_device_info()
                self.log(f"🔧 音频设备: {device_info}")
                
            else:
                self.log("❌ 麦克风启动失败")
                
        except Exception as e:
            self.log(f"❌ 麦克风测试失败: {e}")
            
    def test_stt(self):
        """测试STT"""
        if not self.stt_manager:
            self.log("❌ STT管理器未初始化")
            return
            
        try:
            self.log("🎤 测试STT功能...")
            
            # 初始化STT
            if self.stt_manager.initialize():
                self.log("✅ STT引擎初始化成功")
                
                # 运行测试
                if self.stt_manager.test_stt():
                    self.log("✅ STT功能测试通过")
                else:
                    self.log("❌ STT功能测试失败")
            else:
                self.log("❌ STT引擎初始化失败")
                
        except Exception as e:
            self.log(f"❌ STT测试失败: {e}")
            
    def test_tts(self):
        """测试TTS"""
        if not self.tts_manager:
            self.log("❌ TTS管理器未初始化")
            return
            
        try:
            self.log("🎵 测试TTS功能...")
            
            # 初始化TTS
            if self.tts_manager.initialize():
                self.log("✅ TTS引擎初始化成功")
                
                # 运行测试
                if self.tts_manager.test_tts():
                    self.log("✅ TTS功能测试通过")
                else:
                    self.log("❌ TTS功能测试失败")
            else:
                self.log("❌ TTS引擎初始化失败")
                
        except Exception as e:
            self.log(f"❌ TTS测试失败: {e}")
            
    def test_key_triggered_input(self):
        """测试按键触发输入"""
        if not self.voice_manager:
            self.log("❌ 语音管理器未初始化")
            return
            
        try:
            self.log("⌨️ 启动按键触发语音输入模式...")
            self.log("💡 按住空格键开始录音，松开结束录音")
            
            # 切换到按键触发模式
            self.voice_manager.set_mode("key_triggered")
            
        except Exception as e:
            self.log(f"❌ 按键触发输入测试失败: {e}")
            
    def test_realtime_input(self):
        """测试实时语音输入"""
        if not self.voice_manager:
            self.log("❌ 语音管理器未初始化")
            return
            
        try:
            self.log("🔄 启动实时语音输入模式...")
            self.log("💡 开始说话，系统会自动检测语音")
            
            # 切换到实时模式
            self.voice_manager.set_mode("realtime")
            
        except Exception as e:
            self.log(f"❌ 实时语音输入测试失败: {e}")
            
    def stop_voice_input(self):
        """停止语音输入"""
        if self.voice_manager:
            self.voice_manager.stop()
            self.log("⏹️ 语音输入已停止")
            
    def test_speak(self):
        """测试语音合成"""
        if not self.tts_manager:
            self.log("❌ TTS管理器未初始化")
            return
            
        text = self.tts_text_input.toPlainText().strip()
        if not text:
            self.log("⚠️ 请输入要合成的文本")
            return
            
        try:
            self.log(f"🎵 合成语音: {text}")
            
            # 合成并播放
            if self.tts_manager.speak(text):
                self.log("✅ 语音合成和播放成功")
            else:
                self.log("❌ 语音合成或播放失败")
                
        except Exception as e:
            self.log(f"❌ 语音合成失败: {e}")
            
    def stop_speaking(self):
        """停止语音播放"""
        if self.tts_manager:
            self.tts_manager.stop_speaking()
            self.log("⏹️ 语音播放已停止")
            
    def update_status(self):
        """更新状态显示"""
        try:
            # 更新音量指示器
            if self.voice_manager and hasattr(self.voice_manager, 'microphone_manager'):
                if self.voice_manager.microphone_manager.is_running:
                    audio_level = self.voice_manager.microphone_manager.get_audio_level()
                    volume_percent = int(audio_level * 100)
                    self.volume_bar.setValue(volume_percent)
                    self.volume_label.setText(f"音量: {volume_percent}%")
                    
        except Exception as e:
            pass  # 忽略更新错误
            
    def log(self, message: str):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        
        # 添加到UI日志
        self.log_text.append(log_message)
        
        # 滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)
        
    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 清理资源
            if self.voice_manager:
                self.voice_manager.cleanup()
            if self.stt_manager:
                self.stt_manager.cleanup()
            if self.tts_manager:
                self.tts_manager.cleanup()
                
            self.log("🧹 资源清理完成")
        except Exception as e:
            print(f"清理资源时出错: {e}")
            
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("Live2D语音对话系统测试")
    app.setApplicationVersion("1.0.0")
    
    # 创建测试窗口
    window = VoiceDialogueTestWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
