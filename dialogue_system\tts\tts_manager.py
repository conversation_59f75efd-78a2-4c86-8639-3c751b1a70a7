#!/usr/bin/env python3
"""
Live2D语音对话系统 - TTS管理器

这个模块提供了TTS功能的统一管理，包括：
- 配置管理和加载
- GPT-SoVITS客户端管理
- 音频播放控制
- 预设管理和切换

使用示例：
    from dialogue_system.tts.tts_manager import TTSManager
    
    # 创建TTS管理器
    tts_manager = TTSManager(config_manager)
    
    # 合成并播放语音
    result = tts_manager.speak("你好，世界！")
    
    # 仅合成语音
    audio_data = tts_manager.synthesize_text("你好")
"""

import asyncio
import threading
import time
import os
from typing import Optional, Dict, Any, Callable, Union
from pathlib import Path
from .gptsovits_client import GPTSoVITSClient
from .audio_player import AudioPlayer


class TTSManager:
    """TTS管理器 - 统一管理语音合成功能"""
    
    def __init__(self, config_manager=None):
        """初始化TTS管理器"""
        self.config_manager = config_manager
        
        # 加载配置
        self.tts_config = self._load_tts_config()
        
        # TTS客户端
        self.tts_client: Optional[GPTSoVITSClient] = None
        
        # 音频播放器
        self.audio_player = AudioPlayer()
        
        # 状态控制
        self.is_initialized = False
        self.is_busy = False
        self.is_speaking = False
        
        # 当前预设
        self.current_preset = self.tts_config.get("current_preset", "default")
        
        # 回调函数
        self.on_synthesis_start: Optional[Callable] = None
        self.on_synthesis_complete: Optional[Callable] = None
        self.on_synthesis_error: Optional[Callable] = None
        self.on_speech_start: Optional[Callable] = None
        self.on_speech_finish: Optional[Callable] = None
        
        # 性能统计
        self.synthesis_count = 0
        self.total_synthesis_time = 0.0
        self.last_synthesis_time = 0.0
        
        # 音频设置
        self.audio_settings = self.tts_config.get("audio_settings", {})
        
        print("🎵 TTS管理器初始化完成")
        
    def _load_tts_config(self) -> Dict[str, Any]:
        """加载TTS配置"""
        default_config = {
            "api_url": "http://localhost:9880",
            "default_params": {
                "text_lang": "zh",
                "cut_punc": ",.;?!、，。？！；：",
                "top_k": 20,
                "top_p": 0.6,
                "temperature": 0.6,
                "speed_factor": 1.0
            },
            "audio_settings": {
                "output_dir": "output/audio",
                "auto_play": True,
                "save_audio": False,
                "format": "wav"
            },
            "current_preset": "default"
        }
        
        if self.config_manager:
            voice_config = self.config_manager.config.get("voice_dialogue", {})
            tts_config = voice_config.get("tts_config", {})
            
            config = default_config.copy()
            config.update(tts_config)
            return config
        else:
            return default_config
            
    def initialize(self) -> bool:
        """初始化TTS引擎"""
        if self.is_initialized:
            print("✅ TTS引擎已初始化")
            return True
            
        try:
            print("🔄 正在初始化TTS引擎...")
            
            # 创建GPT-SoVITS客户端
            self.tts_client = GPTSoVITSClient(
                api_url=self.tts_config["api_url"]
            )
            
            # 设置默认参数
            self.tts_client.set_default_params(**self.tts_config["default_params"])
            
            # 设置音频播放器回调
            self.audio_player.set_callbacks(
                on_playback_start=self._on_playback_start,
                on_playback_finish=self._on_playback_finish,
                on_playback_error=self._on_playback_error
            )
            
            # 检查API服务
            if self.tts_client.check_health():
                self.is_initialized = True
                print("✅ TTS引擎初始化成功")
                return True
            else:
                print("❌ TTS API服务不可用")
                return False
                
        except Exception as e:
            print(f"❌ TTS引擎初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    def load_presets(self, preset_data: Dict[str, Any]):
        """加载语音预设"""
        if not self.tts_client:
            if not self.initialize():
                return False
                
        for preset_name, preset_config in preset_data.items():
            self.tts_client.load_preset(preset_name, preset_config)
            
        print(f"📝 已加载 {len(preset_data)} 个语音预设")
        return True
        
    def set_preset(self, preset_name: str) -> bool:
        """设置当前预设"""
        if not self.tts_client:
            if not self.initialize():
                return False
                
        available_presets = self.tts_client.get_available_presets()
        if preset_name in available_presets:
            self.current_preset = preset_name
            print(f"🎭 已切换到预设: {preset_name}")
            return True
        else:
            print(f"⚠️ 预设不存在: {preset_name}")
            return False
            
    def set_callbacks(self,
                     on_synthesis_start: Optional[Callable] = None,
                     on_synthesis_complete: Optional[Callable] = None,
                     on_synthesis_error: Optional[Callable] = None,
                     on_speech_start: Optional[Callable] = None,
                     on_speech_finish: Optional[Callable] = None):
        """设置回调函数"""
        self.on_synthesis_start = on_synthesis_start
        self.on_synthesis_complete = on_synthesis_complete
        self.on_synthesis_error = on_synthesis_error
        self.on_speech_start = on_speech_start
        self.on_speech_finish = on_speech_finish
        
    def synthesize_text(self, 
                       text: str,
                       preset: Optional[str] = None,
                       **kwargs) -> Optional[bytes]:
        """合成语音文本"""
        if not self.is_initialized:
            if not self.initialize():
                return None
                
        if self.is_busy:
            print("⚠️ TTS引擎忙碌中")
            return None
            
        try:
            self.is_busy = True
            start_time = time.time()
            
            # 触发开始回调
            if self.on_synthesis_start:
                self.on_synthesis_start(text)
                
            print(f"🎵 开始合成语音: {text[:50]}...")
            
            # 使用指定预设或当前预设
            use_preset = preset or self.current_preset
            
            # 合成语音
            result = self.tts_client.synthesize(text, preset=use_preset, **kwargs)
            
            # 更新统计信息
            synthesis_time = time.time() - start_time
            self.last_synthesis_time = synthesis_time
            self.total_synthesis_time += synthesis_time
            self.synthesis_count += 1
            
            if result.get("success", False):
                audio_data = result.get("audio_data")
                
                if audio_data:
                    print(f"✅ 语音合成完成 (时长: {synthesis_time:.2f}秒)")
                    
                    # 保存音频文件（如果需要）
                    if self.audio_settings.get("save_audio", False):
                        self._save_audio_file(audio_data, text)
                        
                    # 触发完成回调
                    if self.on_synthesis_complete:
                        self.on_synthesis_complete(result)
                        
                    return audio_data
                else:
                    error_msg = "合成结果中没有音频数据"
                    print(f"❌ {error_msg}")
                    
                    if self.on_synthesis_error:
                        self.on_synthesis_error(error_msg)
                    return None
            else:
                error_msg = result.get("error", "语音合成失败")
                print(f"❌ {error_msg}")
                
                if self.on_synthesis_error:
                    self.on_synthesis_error(error_msg)
                return None
                
        except Exception as e:
            error_msg = f"语音合成异常: {e}"
            print(f"❌ {error_msg}")
            
            if self.on_synthesis_error:
                self.on_synthesis_error(error_msg)
            return None
        finally:
            self.is_busy = False
            
    def speak(self, 
             text: str,
             preset: Optional[str] = None,
             **kwargs) -> bool:
        """合成并播放语音"""
        # 合成语音
        audio_data = self.synthesize_text(text, preset=preset, **kwargs)
        
        if audio_data:
            # 播放音频
            if self.audio_settings.get("auto_play", True):
                return self.play_audio_data(audio_data)
            else:
                return True
        else:
            return False
            
    def play_audio_data(self, audio_data: bytes) -> bool:
        """播放音频数据"""
        try:
            self.is_speaking = True
            return self.audio_player.play_audio_data(audio_data)
        except Exception as e:
            print(f"❌ 播放音频失败: {e}")
            self.is_speaking = False
            return False
            
    def stop_speaking(self):
        """停止语音播放"""
        self.audio_player.stop()
        self.is_speaking = False
        print("⏹️ 语音播放已停止")
        
    def pause_speaking(self):
        """暂停语音播放"""
        self.audio_player.pause()
        print("⏸️ 语音播放已暂停")
        
    def resume_speaking(self):
        """恢复语音播放"""
        self.audio_player.resume()
        print("▶️ 语音播放已恢复")
        
    def set_volume(self, volume: float):
        """设置播放音量"""
        self.audio_player.set_volume(volume)
        
    def set_playback_rate(self, rate: float):
        """设置播放速度"""
        self.audio_player.set_playback_rate(rate)
        
    def _save_audio_file(self, audio_data: bytes, text: str):
        """保存音频文件"""
        try:
            output_dir = self.audio_settings.get("output_dir", "output/audio")
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = int(time.time())
            safe_text = "".join(c for c in text[:20] if c.isalnum() or c in (' ', '-', '_')).strip()
            filename = f"{timestamp}_{safe_text}.{self.audio_settings.get('format', 'wav')}"
            filepath = os.path.join(output_dir, filename)
            
            # 保存文件
            with open(filepath, 'wb') as f:
                f.write(audio_data)
                
            print(f"💾 音频已保存: {filepath}")
            
        except Exception as e:
            print(f"❌ 保存音频文件失败: {e}")
            
    def _on_playback_start(self):
        """播放开始回调"""
        self.is_speaking = True
        if self.on_speech_start:
            self.on_speech_start()
            
    def _on_playback_finish(self):
        """播放完成回调"""
        self.is_speaking = False
        if self.on_speech_finish:
            self.on_speech_finish()
            
    def _on_playback_error(self, error_msg: str):
        """播放错误回调"""
        self.is_speaking = False
        print(f"❌ 播放错误: {error_msg}")
        
    def get_available_presets(self) -> list:
        """获取可用预设列表"""
        if self.tts_client:
            return self.tts_client.get_available_presets()
        else:
            return []
            
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        avg_time = (self.total_synthesis_time / self.synthesis_count 
                   if self.synthesis_count > 0 else 0.0)
                   
        return {
            "synthesis_count": self.synthesis_count,
            "total_synthesis_time": self.total_synthesis_time,
            "average_synthesis_time": avg_time,
            "last_synthesis_time": self.last_synthesis_time,
            "is_busy": self.is_busy,
            "is_speaking": self.is_speaking
        }
        
    def test_tts(self) -> bool:
        """测试TTS功能"""
        if not self.is_initialized:
            if not self.initialize():
                return False
                
        if self.tts_client:
            return self.tts_client.test_synthesis()
        else:
            return False
            
    def get_status(self) -> Dict[str, Any]:
        """获取TTS状态"""
        return {
            "initialized": self.is_initialized,
            "busy": self.is_busy,
            "speaking": self.is_speaking,
            "current_preset": self.current_preset,
            "available_presets": self.get_available_presets(),
            "config": self.tts_config,
            "performance": self.get_performance_stats(),
            "audio_player": self.audio_player.get_status()
        }
        
    def cleanup(self):
        """清理资源"""
        # 停止播放
        self.stop_speaking()
        
        # 清理音频播放器
        self.audio_player.cleanup()
        
        # 清理TTS客户端
        self.tts_client = None
        
        self.is_initialized = False
        self.is_busy = False
        self.is_speaking = False
        print("🧹 TTS管理器资源已清理")
        
    def __del__(self):
        """析构函数"""
        self.cleanup()
