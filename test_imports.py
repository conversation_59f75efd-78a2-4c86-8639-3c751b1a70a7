#!/usr/bin/env python3
"""
测试导入模块
"""

import sys
import os

print("Python版本:", sys.version)
print("当前工作目录:", os.getcwd())

# 测试基础导入
try:
    print("测试PySide6导入...")
    from PySide6.QtWidgets import QApplication
    print("✅ PySide6导入成功")
except ImportError as e:
    print(f"❌ PySide6导入失败: {e}")

# 测试配置管理器
try:
    print("测试ConfigManager导入...")
    from dev.settings_dialog import ConfigManager
    print("✅ ConfigManager导入成功")
    
    config_manager = ConfigManager()
    print("✅ ConfigManager创建成功")
except ImportError as e:
    print(f"❌ ConfigManager导入失败: {e}")
except Exception as e:
    print(f"❌ ConfigManager创建失败: {e}")

# 测试语音模块
try:
    print("测试语音模块导入...")
    from dialogue_system.voice import VoiceDialogueManager
    print("✅ VoiceDialogueManager导入成功")
except ImportError as e:
    print(f"❌ VoiceDialogueManager导入失败: {e}")

# 测试STT模块
try:
    print("测试STT模块导入...")
    from dialogue_system.stt import STTManager
    print("✅ STTManager导入成功")
except ImportError as e:
    print(f"❌ STTManager导入失败: {e}")

# 测试TTS模块
try:
    print("测试TTS模块导入...")
    from dialogue_system.tts import TTSManager
    print("✅ TTSManager导入成功")
except ImportError as e:
    print(f"❌ TTSManager导入失败: {e}")

print("导入测试完成")
