target_sources(${LIB_NAME}
  PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/ACubismMotion.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/ACubismMotion.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismExpressionMotion.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismExpressionMotion.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismMotion.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismMotion.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismMotionInternal.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismMotionJson.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismMotionJson.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismMotionManager.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismMotionManager.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismMotionQueueEntry.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismMotionQueueEntry.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismMotionQueueManager.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismMotionQueueManager.hpp
)
