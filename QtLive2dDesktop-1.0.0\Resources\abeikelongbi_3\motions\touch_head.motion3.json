{"Version": 3, "Meta": {"Duration": 5.533, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 244, "TotalSegmentCount": 7980, "TotalPointCount": 9580, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 0, 0.233, 0.943, 0, 0.467, 1, 0, 0.767, 0, 1, 1.634, 0, 2.5, 0.001, 3.367, 0.004, 1, 3.545, 0.005, 3.722, 1, 3.9, 1, 2, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 0.233, 0.943, 0, 0.467, 1, 0, 0.767, 0, 1, 1.634, 0, 2.5, 0.001, 3.367, 0.004, 1, 3.545, 0.005, 3.722, 1, 3.9, 1, 2, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 0, 0.333, -1, 2, 3.367, -1, 0, 3.9, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeEmotion", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0.914, 0.333, 0.973, 1, 0.411, 1, 0.489, 1, 0.567, 1, 0, 0.867, -0.4, 0, 1.167, 0, 2, 3.367, 0, 2, 3.9, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 0.667, 1, 2, 3.367, 1, 0, 3.9, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamShameLine", "Segments": [0, 0, 2, 3.367, 0, 2, 3.9, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPHYInputX", "Segments": [0, 0, 2, 0.3, 0, 0, 0.467, -6.792, 0, 0.733, 5.247, 0, 1.033, -6.792, 0, 1.4, 2.379, 0, 1.767, -5.538, 0, 2.067, 0.93, 0, 2.4, -1.374, 1, 2.711, -1.374, 3.022, -1.374, 3.333, -1.365, 1, 3.511, -1.36, 3.689, 5.247, 3.867, 5.247, 1, 3.989, 5.247, 4.111, 0.561, 4.233, -4.302, 1, 4.355, -9.165, 4.478, -10.284, 4.6, -10.284, 0, 5.2, 8.232, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.2, 0, 0, 0.7, -5.302, 0, 1, 6.819, 0, 1.367, -4.614, 0, 1.733, 5.86, 0, 2.033, -4.83, 0, 2.367, 3.32, 0, 2.7, -4.83, 0, 2.933, 3, 0, 3.367, -4.83, 0, 3.667, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.233, 0, 0, 0.567, 18.092, 0, 0.933, 11, 0, 1.1, 13.772, 0, 1.467, 10.329, 0, 1.8, 13.034, 0, 2.233, 10.329, 0, 2.5, 13.034, 0, 2.867, 10.329, 0, 3.1, 13.034, 0, 3.433, 10.329, 1, 3.478, 10.329, 3.522, 10.282, 3.567, 10.373, 1, 3.667, 10.577, 3.767, 16.416, 3.867, 16.416, 0, 4.533, 0, 0, 5.133, 3, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 0, 0.3, 1.482, 1, 0.478, 1.482, 0.655, -10.526, 0.833, -15, 1, 0.933, -17.516, 1.033, -17, 1.133, -17, 2, 3.267, -17, 0, 3.6, -18, 0, 4.367, 0, 0, 4.933, -2.51, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamAngleH", "Segments": [0, 0, 0, 0.333, 4.058, 1, 1.333, 4.058, 2.333, 3.873, 3.333, 3.221, 1, 3.511, 3.105, 3.689, 0, 3.867, 0, 2, 5.267, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamAngleS", "Segments": [0, 0, 2, 0.333, 0, 2, 3.333, 0, 2, 3.867, 0, 0, 4.8, 1, 0, 5.167, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperLAngle", "Segments": [0, 0, 0, 0.4, 1.815, 0, 0.667, -2, 1, 1.556, -2, 2.444, -1.99, 3.333, -1.949, 1, 3.511, -1.941, 3.689, 0, 3.867, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerLAngle", "Segments": [0, 10, 0, 0.533, 8.59, 0, 0.9, 10.2, 1, 1.711, 10.2, 2.522, 10.199, 3.333, 10.196, 1, 3.511, 10.195, 3.689, 10, 3.867, 10, 2, 5.533, 10]}, {"Target": "Parameter", "Id": "ParamArmLowerLH", "Segments": [0, 0, 0, 0.333, -9.72, 1, 1.333, -9.72, 2.333, -9.275, 3.333, -7.721, 1, 3.511, -7.445, 3.689, 0, 3.867, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerLAngle", "Segments": [0, 0, 0, 0.333, 7.92, 1, 1.333, 7.92, 2.333, 7.632, 3.333, 6.614, 1, 3.511, 6.433, 3.689, 0, 3.867, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamArm<PERSON>pperRAngle", "Segments": [0, 0, 2, 0.333, 0, 0, 0.633, -0.474, 0, 0.967, 0.089, 0, 1.267, -0.474, 0, 1.633, 0.126, 0, 1.933, -0.223, 0, 2.3, 0, 2, 3.333, 0, 0, 3.7, -0.576, 0, 3.9, 0.1, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRAngle", "Segments": [0, 7.4, 0, 0.533, 16.795, 0, 0.767, 16.013, 2, 3.333, 16.013, 0, 3.867, 6.68, 0, 4.1, 7.4, 2, 5.533, 7.4]}, {"Target": "Parameter", "Id": "ParamArmHandRAngle", "Segments": [0, 0, 0, 0.333, -10, 0, 0.633, -6, 0, 0.867, -7, 1, 1.689, -7, 2.511, -6.964, 3.333, -6.851, 1, 3.444, -6.836, 3.556, 1.566, 3.667, 1.566, 0, 4, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRH", "Segments": [0, 0, 0, 0.333, -15.841, 1, 1.333, -15.841, 2.333, -15.261, 3.333, -13.232, 1, 3.511, -12.871, 3.689, 0, 3.867, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperRH", "Segments": [0, 0, 0, 0.767, 2.349, 0, 0.933, 0, 2, 3.333, 0, 2, 3.867, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRAngle", "Segments": [0, 0, 0, 0.333, -5, 1, 1.333, -5, 2.333, -4.965, 3.333, -4.836, 1, 3.511, -4.813, 3.689, 4.607, 3.867, 4.607, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamHandT2R", "Segments": [0, 0, 0, 0.333, 0.441, 0, 1.667, 0, 2, 3.733, 0, 0, 3.9, 0.164, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 0.467, -5.264, 0, 0.733, -1.251, 0, 1.033, -5.264, 0, 1.4, -2.207, 0, 1.767, -4.846, 0, 2.067, -2.69, 0, 2.4, -4.434, 0, 2.733, -1.678, 0, 3.1, -4.434, 0, 3.333, -1.678, 0, 3.633, -2.69, 1, 3.711, -2.69, 3.789, -2.55, 3.867, -1.251, 1, 4, 0.975, 4.134, 3, 4.267, 3, 0, 4.9, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 0, 0.833, -5, 0, 1.133, -3.832, 0, 1.5, -4.049, 0, 1.833, -3.933, 0, 2.267, -4.049, 1, 2.622, -4.049, 2.978, -4.049, 3.333, -4.042, 1, 3.489, -4.039, 3.644, 1.076, 3.8, 1.076, 0, 4.1, -2, 0, 4.567, 0.717, 0, 4.833, -1, 0, 5.233, 0.218, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.167, 0, 0, 0.433, 5.232, 0, 0.933, 4.322, 0, 1.3, 5.232, 0, 1.667, 4.259, 0, 2.133, 4.767, 1, 2.244, 4.767, 2.356, 4.441, 2.467, 4.268, 1, 2.734, 3.852, 3, 3.748, 3.267, 3.748, 0, 3.467, 5, 0, 3.933, -0.266, 0, 4.667, 1, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX2", "Segments": [0, 0, 1, 0.111, 0, 0.222, -9.89, 0.333, -9.901, 1, 1.1, -9.977, 1.866, -10, 2.633, -10, 0, 3.333, -8.272, 0, 3.6, -9.084, 0, 4.3, 1.906, 0, 5.067, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY2", "Segments": [0, 0, 2, 0.1, 0, 0, 0.333, 7, 0, 0.733, -1.577, 0, 1, 1.966, 0, 1.433, 1.034, 0, 1.7, 1.805, 0, 2.167, 1, 0, 2.467, 2.136, 0, 3.333, 1.363, 0, 3.567, 2.136, 0, 4.033, -2, 0, 4.233, 0, 0, 4.7, -1.577, 0, 5.3, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechX", "Segments": [0, 0, 0, 0.6, 5.892, 0, 0.933, -0.326, 0, 1.233, 6.326, 0, 1.6, 1.228, 0, 1.9, 5.885, 0, 2.367, 3.325, 0, 2.667, 8, 0, 3.767, -3.936, 0, 4.1, 0.626, 0, 4.533, -2.072, 0, 5.133, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, 0, 2, 0.1, 0, 0, 0.533, -6, 1, 0.611, -6, 0.689, -5.023, 0.767, -5, 1, 1.622, -4.744, 2.478, -4.686, 3.333, -4.295, 1, 3.966, -4.005, 4.6, 0, 5.233, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamShoulderR", "Segments": [0, 0, 0, 0.333, 29.702, 0, 3.333, 24.812, 0, 3.467, 30, 0, 3.867, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamShoulderL", "Segments": [0, 0, 0, 0.333, 29.156, 1, 1.333, 29.156, 2.333, 28.059, 3.333, 24.293, 1, 3.511, 23.623, 3.689, 0, 3.867, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Z", "Segments": [0, 0, 2, 0.5, 0, 0, 0.967, -1.596, 0, 1.7, 0.595, 0, 2.533, -0.415, 0, 3.067, 0, 0, 3.633, -1.596, 0, 3.967, -1.339, 0, 4.367, -1.596, 0, 4.9, 0.234, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLegR1X", "Segments": [0, 0, 2, 0.7, 0, 0, 1.1, -3, 0, 1.8, 1.509, 0, 2.467, -0.132, 0, 2.933, 0, 0, 3.567, -3, 2, 4.667, -3, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Z", "Segments": [0, 0, 2, 0.5, 0, 2, 0.6, 0, 2, 0.767, 0, 0, 1.2, -2, 0, 1.9, 0.138, 0, 2.733, -1, 0, 3.267, 0, 0, 3.7, -1.675, 0, 4.167, -1.211, 0, 4.6, -1.675, 0, 5.033, 0.276, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Z", "Segments": [0, 0, 0, 0.5, -0.632, 0, 1.3, -0.263, 0, 1.867, -0.485, 0, 2.167, 1.03, 0, 2.8, -0.689, 0, 3.367, 0.216, 0, 3.667, -0.671, 0, 4.967, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyX", "Segments": [0, 0, 2, 0.533, 0, 0, 0.8, 7, 0, 1.133, -3, 0, 1.4, 4, 0, 2.667, -7.178, 0, 4.467, 1.049, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyY", "Segments": [0, 0, 2, 0.567, 0, 0, 0.833, 7, 0, 1.167, -3, 0, 1.433, 4, 0, 2.367, -11.811, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyZ", "Segments": [0, 0, 2, 0.433, 0, 0, 0.8, 2, 0, 1.133, -11.745, 0, 1.633, -5.725, 0, 1.933, -7, 0, 3.7, 1.845, 1, 3.867, 1.845, 4.033, 1.73, 4.2, 0.15, 1, 4.311, -0.904, 4.422, -4.109, 4.533, -4.109, 2, 4.633, -4.109, 0, 5.133, 3, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRPositionZ", "Segments": [0, 0, 2, 0.433, 0, 2, 0.533, 0, 0, 0.9, 2, 0, 1.2, -9.745, 0, 1.733, -5.725, 0, 2.033, -7, 0, 3.6, 0.584, 1, 3.778, 0.584, 3.955, 0.508, 4.133, -0.539, 1, 4.244, -1.193, 4.356, -3.362, 4.467, -3.362, 2, 4.533, -3.362, 0, 5.067, 3, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyX", "Segments": [0, 0, 2, 0.433, 0, 2, 0.6, 0, 0, 0.8, 7, 0, 1.133, -3, 0, 1.4, 4, 1, 1.478, 4, 1.555, 1.205, 1.633, 0, 1, 1.978, -5.338, 2.322, -7.178, 2.667, -7.178, 0, 3.367, 4.747, 1, 3.534, 4.747, 3.7, 3.474, 3.867, 0, 1, 4.056, -3.937, 4.244, -6.474, 4.433, -6.474, 0, 4.8, 4.059, 0, 5.167, -5.438, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyY", "Segments": [0, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 0.6, 0, 0, 0.833, 7, 0, 1.167, -3, 0, 1.433, 4, 1, 1.511, 4, 1.589, 2.635, 1.667, 0, 1, 1.9, -7.905, 2.134, -11.811, 2.367, -11.811, 0, 3.433, 10.277, 1, 3.589, 10.277, 3.744, 5.473, 3.9, 0, 1, 4.089, -6.646, 4.278, -8.618, 4.467, -8.618, 0, 4.867, 6.618, 0, 5.2, -5.438, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyZ", "Segments": [0, 0, 2, 0.433, 0, 2, 0.5, 0, 0, 0.633, -6, 0, 1, 3, 0, 1.267, -1.059, 0, 1.567, 0, 0, 2.167, -5.624, 2, 2.267, -5.624, 0, 3.767, 7.585, 1, 3.845, 7.585, 3.922, 3.553, 4, 0, 1, 4.144, -6.598, 4.289, -8.618, 4.433, -8.618, 2, 4.5, -8.618, 0, 5.067, 3.857, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUEyesForm", "Segments": [0, 0, 2, 0.3, 0, 2, 0.467, 0, 2, 0.967, 0, 0, 1.5, -1, 2, 1.8, -1, 0, 2.267, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyY", "Segments": [0, 0, 2, 0.3, 0, 2, 0.6, 0, 0, 0.933, 6.26, 0, 1.2, -1.62, 0, 1.3, 2.925, 0, 1.433, -2.598, 0, 1.5, 3.787, 0, 1.6, -3.047, 0, 1.733, 2.925, 0, 1.8, -2.703, 0, 1.933, 2, 1, 2.111, 2, 2.289, 1.239, 2.467, 0, 1, 2.634, -1.161, 2.8, -1.62, 2.967, -1.62, 0, 3.9, 2, 0, 4.467, -12.621, 0, 4.867, 9.177, 0, 5.2, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyZ", "Segments": [0, 0, 2, 0.3, 0, 2, 0.567, 0, 0, 0.9, 20.858, 2, 0.967, 20.858, 0, 1.167, -27.181, 0, 1.267, 27.181, 0, 1.4, -27.181, 0, 1.5, 27.181, 0, 1.567, -27.181, 0, 1.7, 27.181, 0, 1.833, -10, 0, 1.967, 4, 0, 2.2, -2.382, 0, 2.467, 2.816, 0, 2.867, -1.3, 0, 3.233, 1.227, 0, 3.8, -2.382, 0, 4.133, 1.227, 0, 4.5, -12.621, 0, 5.1, 7.576, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUArmZ", "Segments": [0, 0, 2, 0.3, 0, 2, 0.5, 0, 1, 0.556, 0, 0.611, 0.138, 0.667, -2, 1, 0.845, -8.842, 1.022, -15, 1.2, -15, 0, 1.3, 15.881, 0, 1.433, -17.598, 0, 1.5, 16.598, 0, 1.6, -18.598, 0, 1.733, 16.598, 0, 1.9, -12, 0, 2.067, 11, 0, 2.367, -5.118, 0, 2.6, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineU", "Segments": [0, 0, 2, 0.3, 0, 2, 1.2, 0, 1, 1.244, 0.333, 1.289, 0.667, 1.333, 1, 2, 1.367, 0, 2, 1.467, 0, 1, 1.489, 0.333, 1.511, 0.667, 1.533, 1, 2, 1.567, 0, 2, 1.633, 0, 2, 1.733, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineD", "Segments": [0, 0, 2, 0.3, 0, 2, 1.2, 0, 2, 1.333, 0, 1, 1.378, 0.333, 1.422, 0.667, 1.467, 1, 2, 1.5, 0, 2, 1.533, 0, 1, 1.566, 0.333, 1.6, 0.667, 1.633, 1, 2, 1.667, 0, 2, 1.733, 0, 1, 1.766, 0.333, 1.8, 0.667, 1.833, 1, 2, 1.867, 0, 2, 1.9, 0, 2, 2.167, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyW", "Segments": [0, 0, 2, 0.3, 0, 2, 0.833, 0, 0, 1.167, -0.6, 0, 1.267, 0.4, 0, 1.4, -0.5, 0, 1.5, 0.6, 0, 1.567, -0.5, 0, 1.667, 0.2, 1, 1.711, 0.2, 1.756, 0.119, 1.8, -0.029, 1, 1.856, -0.214, 1.911, -0.3, 1.967, -0.3, 0, 2.2, 0.2, 0, 2.4, -0.2, 0, 2.667, 0.2, 0, 2.967, -0.1, 0, 3.233, 0.1, 0, 3.567, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyX", "Segments": [0, 0, 0, 0.867, -2.419, 0, 1.633, 3.119, 0, 2.7, -3.163, 0, 3.933, 4.41, 0, 5, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyY", "Segments": [0, 0, 0, 0.533, -2.43, 0, 1.033, 2.838, 0, 1.733, -3.468, 0, 2.5, 3, 0, 3.167, -4.216, 0, 3.733, 2.569, 0, 4.367, -3, 0, 4.9, 1.442, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyZ", "Segments": [0, 0, 0, 0.8, 3.698, 0, 1.333, -3.586, 0, 2.133, 3, 0, 2.767, -3.586, 0, 3.367, 3.355, 0, 4.033, -3.586, 0, 4.6, 3.225, 0, 5.367, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyX", "Segments": [0, 0, 0, 0.567, -8, 0, 1.167, 7, 0, 1.9, -9, 0, 2.567, 5, 0, 3.3, -6, 0, 3.933, 6, 0, 4.533, -8, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyY", "Segments": [0, 0, 2, 0.433, 0, 2, 0.6, 0, 0, 0.8, -7, 0, 1.133, 3, 0, 1.4, -4, 0, 1.633, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamCannonZ", "Segments": [0, 0, 2, 0.467, 0, 0, 0.6, 6, 0, 0.967, -3, 0, 1.233, 1.059, 0, 1.533, 0, 2, 2.467, 0, 0, 2.7, 2.231, 0, 3.067, -13, 0, 3.3, -11.678, 0, 3.567, -12.606, 2, 4.067, -12.606, 0, 4.8, 3.698, 0, 5.367, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamCannonGaY", "Segments": [0, 0, 2, 0.533, 0, 0, 0.667, -8.384, 0, 1.033, 3.845, 0, 1.3, -3.618, 0, 1.567, 3.37, 0, 1.833, -1.16, 0, 2.133, 1.234, 1, 2.266, 1.234, 2.4, 1.058, 2.533, -0.231, 1, 2.6, -0.876, 2.666, -3.248, 2.733, -3.248, 0, 3.133, 11.418, 0, 3.333, 6, 0, 3.6, 10.888, 0, 3.867, 6.475, 0, 4.133, 10.888, 0, 4.867, -3.248, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupY", "Segments": [0, 0, 2, 0.567, 0, 0, 0.7, 0.2, 0, 1.067, -0.1, 0, 1.333, 0.035, 0, 1.633, 0, 2, 2.567, 0, 0, 2.767, 0.074, 0, 3.167, -0.433, 0, 3.367, -0.389, 0, 3.633, -0.42, 2, 4.167, -0.42, 0, 4.9, 0.39, 0, 5.433, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupZ", "Segments": [0, 0, 2, 0.433, 0, 0, 0.6, 0.2, 0, 0.933, -0.1, 0, 1.2, 0.035, 0, 1.5, 0, 2, 2.433, 0, 0, 2.667, 0.074, 0, 3.033, -0.433, 0, 3.267, -0.389, 0, 3.533, -0.42, 2, 4.033, -0.42, 0, 4.8, 0.39, 0, 5.333, 0, 2, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamParamStrongCatZ", "Segments": [0, 0, 2, 0.5, 0, 0, 0.9, 2, 0, 1.267, -11.745, 0, 1.833, -5.725, 0, 2.167, -7, 0, 3.4, 1.845, 1, 3.589, 1.845, 3.778, 1.435, 3.967, 0.15, 1, 4.089, -0.682, 4.211, -1.436, 4.333, -1.436, 2, 4.433, -1.436, 0, 5, 1.223, 2, 5.1, 1.223, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamParamSCBodyZ", "Segments": [0, 0, 2, 0.5, 0, 2, 0.6, 0, 0, 1, 2, 0, 1.367, -9.745, 0, 1.933, -5.725, 0, 2.267, -7, 0, 3.5, 1.845, 1, 3.689, 1.845, 3.878, 1.435, 4.067, 0.15, 1, 4.189, -0.682, 4.311, -1.436, 4.433, -1.436, 2, 4.533, -1.436, 0, 5.1, 1.223, 2, 5.2, 1.223, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamSCDishY", "Segments": [0, 0, 2, 0.5, 0, 2, 0.7, 0, 0, 1.1, 2, 0, 1.467, -2.745, 0, 2, 1.275, 0, 2.333, 0, 0, 3.5, 1.845, 1, 3.689, 1.845, 3.878, 1.435, 4.067, 0.15, 1, 4.189, -0.682, 4.311, -1.436, 4.433, -1.436, 2, 4.533, -1.436, 0, 5.1, 1.223, 2, 5.2, 1.223, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamSCDishZ", "Segments": [0, 0, 2, 0.5, 0, 2, 0.733, 0, 0, 1.133, 2, 0, 1.5, -2.745, 0, 2.033, 1.275, 0, 2.367, 0, 0, 3.633, 1.845, 1, 3.822, 1.845, 4.011, 1.435, 4.2, 0.15, 1, 4.322, -0.682, 4.445, -1.436, 4.567, -1.436, 2, 4.667, -1.436, 0, 5.233, 1.223, 2, 5.333, 1.223, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamSCCupZ", "Segments": [0, 0, 2, 0.5, 0, 2, 0.8, 0, 0, 1.233, 5.028, 0, 1.6, -5.773, 0, 2.167, 3.378, 0, 2.433, -1.036, 0, 3.7, 1.845, 1, 3.889, 1.845, 4.078, 1.435, 4.267, 0.15, 1, 4.389, -0.682, 4.511, -1.436, 4.633, -1.436, 2, 4.733, -1.436, 0, 5.3, 1.223, 2, 5.4, 1.223, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "MB_yanwubaozha", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "MB_DRFWXZKTMD", "Segments": [0, 1, 0, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamAllSizeFix", "Segments": [0, 1, 0, 5.533, 1]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBGHide", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBG2Hide", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBGX", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBGY", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN2", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN3", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBlackY", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBlackCollar", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBlackOrder", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamWhiteIN", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamCHHide", "Segments": [0, 1, 0, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamDeskHide", "Segments": [0, 1, 0, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamStoolHide", "Segments": [0, 1, 0, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamCupDesk", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamCHX", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamCHY", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamCHZ", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamChaSize", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamCcharacterZ", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionX", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionY", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionX", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionY", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamAllZ", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamAllSize", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamALLSize2", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamDeskShow", "Segments": [0, 10, 0, 5.533, 10]}, {"Target": "Parameter", "Id": "ParamStrongCatShow", "Segments": [0, 10, 0, 5.533, 10]}, {"Target": "Parameter", "Id": "ParamCannonShow", "Segments": [0, 10, 0, 5.533, 10]}, {"Target": "Parameter", "Id": "ParamLightPositionX", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamFixT", "Segments": [0, 1, 0, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamFlap", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamScare", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPupilExp", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeSmileL", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeSmileR", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpen2", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamMouthType", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamMouthSize", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamMouthY", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBlackFace", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamTeethLight", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamHeart2", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamMark", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamMarkShake", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLX", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLY", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeL", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRX", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRY", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeR", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeRLightOpen", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLightLine1", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLightLine2", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLightLine3", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLightShine", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo1", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo2", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo3", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1Y", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle2", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow1", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow2", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamTearLight", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamTears", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamArmHandLAngle", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamHandT2L", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamFanOpenR", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamChili", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamChiliX", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamHandR", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRY", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamHand_Cl", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamHandT1R", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamHandRCup", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamHandRMail", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "Segments": [0, 1, 0, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamHandLIQY1", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY2", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY3", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamHandCupZ", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamHandCupY", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechW", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Y", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Y", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Y", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamFootRX", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Z", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Z", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Z", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Y", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Y", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Y", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamFootLX", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLegLF", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRHide", "Segments": [0, 1, 0, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamMJRFlap", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuR", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuR", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuR", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuR", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRInput", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamMRCupSet", "Segments": [0, 1, 0, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamMalpositionManjuuR", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuREyeOpen", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRArmB", "Segments": [0, 30, 0, 5.533, 30]}, {"Target": "Parameter", "Id": "ParamManjuuRMouth", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRSigh", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow1", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow2", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow3", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow4", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowB", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW2", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamMRCupFZ", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX1", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX2", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLiqH", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX1", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX2", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX3", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLHide", "Segments": [0, 1, 0, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamMJLSigh", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuL", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuL", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuL", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamMjLFlip", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPositionZManjuuL", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLEyeOpen", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyW", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuL", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamClawFX", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamClawFY", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamClawBX", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamClawBY", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUHide", "Segments": [0, 1, 0, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuU", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuU", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuU", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyX", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPandaHide", "Segments": [0, 1, 0, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamPositionXPanda", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPositionYPanda", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamSizePanda", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2Panda", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegFZ", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegBZ", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamCannonHandZ", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY1", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY2", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY3", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupIce", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupZ", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupInput", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamSCDishRO", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamSCCupRO", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamSCCupY", "Segments": [0, 0, 0, 5.533, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 5.033, "Value": ""}]}