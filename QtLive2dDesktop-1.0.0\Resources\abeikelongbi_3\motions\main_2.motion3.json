{"Version": 3, "Meta": {"Duration": 8.97, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 252, "TotalSegmentCount": 21240, "TotalPointCount": 25740, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamCcharacterZ", "Segments": [0, 0, 2, 0.167, 0, 2, 3.167, 0, 0, 3.6, 2.628, 0, 3.8, 2.474, 0, 4.167, 2.546, 1, 4.711, 2.546, 5.256, 2.536, 5.8, 2.474, 1, 5.956, 2.456, 6.111, 0, 6.267, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamStrongCatShow", "Segments": [0, 10, 2, 0.367, 10, 1, 0.489, 10, 0.611, 10, 0.733, 8.234, 1, 0.855, 6.362, 0.978, 0, 1.1, 0, 2, 6.9, 0, 0, 7.6, 10, 2, 8.967, 10]}, {"Target": "Parameter", "Id": "ParamCannonShow", "Segments": [0, 10, 2, 0.667, 10, 1, 0.7, 10, 0.734, 8.672, 0.767, 7.53, 1, 0.922, 2.2, 1.078, 0, 1.233, 0, 2, 6.467, 0, 0, 7.167, 10, 2, 8.967, 10]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.167, 1, 2, 0.267, 1, 0, 0.4, 0, 2, 0.433, 0, 0, 0.533, 0.8, 0, 0.6, 0.2, 0, 0.8, 1, 0, 0.867, 0.938, 0, 0.933, 1, 0, 0.967, 0.932, 0, 1.033, 0.988, 0, 1.1, 0.933, 0, 1.167, 0.982, 0, 1.233, 0.931, 0, 1.267, 0.984, 0, 1.333, 0.929, 0, 1.4, 0.988, 0, 1.467, 0.931, 0, 1.6, 0.935, 1, 1.733, 0.935, 1.867, 0.935, 2, 0.936, 1, 2.367, 0.938, 2.733, 0.94, 3.1, 0.94, 0, 3.233, 0, 1, 3.289, 0, 3.344, 0.898, 3.4, 0.943, 1, 3.478, 1, 3.555, 1, 3.633, 1, 0, 3.933, 0.8, 1, 4.5, 0.8, 5.066, 0.8, 5.633, 0.801, 0, 5.8, 1, 0, 6.033, 0, 2, 6.5, 0, 0, 6.9, 0.7, 2, 7.967, 0.7, 0, 8.267, 1, 2, 8.967, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.167, 1, 2, 0.267, 1, 0, 0.4, 0, 2, 0.433, 0, 0, 0.533, 0.8, 0, 0.6, 0.2, 0, 0.8, 1, 0, 0.867, 0.938, 0, 0.933, 1, 0, 0.967, 0.931, 0, 1.033, 0.988, 0, 1.1, 0.933, 0, 1.167, 0.982, 0, 1.233, 0.93, 0, 1.267, 0.983, 0, 1.333, 0.929, 0, 1.4, 0.988, 0, 1.467, 0.93, 0, 1.6, 0.935, 1, 1.733, 0.935, 1.867, 0.935, 2, 0.936, 1, 2.367, 0.938, 2.733, 0.94, 3.1, 0.94, 0, 3.233, 0, 1, 3.289, 0, 3.344, 0.898, 3.4, 0.943, 1, 3.478, 1, 3.555, 1, 3.633, 1, 0, 3.933, 0.8, 1, 4.5, 0.8, 5.066, 0.8, 5.633, 0.801, 0, 5.8, 1, 0, 6.033, 0, 2, 6.5, 0, 0, 6.9, 0.7, 2, 7.967, 0.7, 0, 8.267, 1, 2, 8.967, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 2, 0.167, 0, 1, 0.256, 0, 0.344, -0.416, 0.433, -0.774, 1, 0.489, -0.998, 0.544, -1, 0.6, -1, 2, 0.867, -1, 0, 0.933, -0.823, 0, 1, -1, 0, 1.067, -0.916, 0, 1.167, -1, 0, 1.233, -0.91, 0, 1.3, -1, 0, 1.367, -0.603, 0, 1.433, -0.693, 0, 1.533, -0.603, 0, 1.633, -0.7, 0, 1.7, -0.593, 1, 1.733, -0.593, 1.767, -0.609, 1.8, -0.7, 1, 1.811, -0.73, 1.822, -1, 1.833, -1, 2, 2.233, -1, 0, 2.3, -0.8, 2, 2.333, -0.8, 0, 2.767, -1, 0, 2.933, -0.5, 1, 3.089, -0.5, 3.244, -0.98, 3.4, -0.988, 1, 3.656, -1, 3.911, -1, 4.167, -1, 0, 4.433, -0.8, 2, 4.567, -0.8, 0, 4.7, -1, 1, 5.078, -1, 5.455, -0.983, 5.833, -0.826, 1, 5.989, -0.761, 6.144, -0.13, 6.3, -0.13, 1, 6.356, -0.13, 6.411, -0.297, 6.467, -0.5, 1, 6.567, -0.866, 6.667, -1, 6.767, -1, 2, 7.567, -1, 0, 8.133, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.167, 0, 2, 0.3, 0, 0, 0.467, 0.5, 2, 0.6, 0.5, 1, 0.667, 0.5, 0.733, 0.517, 0.8, 0.661, 1, 0.833, 0.733, 0.867, 1, 0.9, 1, 0, 1.1, 0.5, 0, 1.267, 1, 0, 1.333, 0.618, 0, 1.433, 1, 0, 1.533, 0.618, 0, 1.633, 0.999, 0, 1.667, 0.626, 0, 1.833, 1, 0, 1.9, 0.618, 0, 2, 1, 0, 2.1, 0.618, 0, 2.2, 0.9, 0, 2.4, 0, 2, 2.667, 0, 1, 2.7, 0, 2.734, 0.388, 2.767, 0.5, 1, 2.822, 0.686, 2.878, 0.7, 2.933, 0.7, 0, 3, 0.571, 0, 3.2, 0.9, 1, 3.267, 0.9, 3.333, 0.738, 3.4, 0.5, 1, 3.5, 0.143, 3.6, 0, 3.7, 0, 0, 3.933, 0.3, 2, 4.167, 0.3, 0, 4.433, 1, 0, 4.567, 0.8, 0, 4.7, 0.866, 0, 4.833, 0.571, 0, 4.967, 1, 0, 5.133, 0.727, 0, 5.267, 0.866, 0, 5.4, 0.737, 0, 5.5, 0.8, 0, 5.633, 0.671, 0, 5.767, 1, 2, 6.1, 1, 1, 6.167, 1, 6.233, 0.974, 6.3, 0.87, 1, 6.344, 0.801, 6.389, 0.696, 6.433, 0.571, 1, 6.555, 0.226, 6.678, 0, 6.8, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamMouthSize", "Segments": [0, 0, 2, 0.167, 0, 2, 0.3, 0, 2, 0.533, 0, 0, 0.8, 0.5, 0, 0.867, 0.158, 0, 0.933, 0.736, 0, 1, 0.158, 0, 1.067, 0.666, 1, 1.1, 0.666, 1.134, 0.219, 1.167, 0.158, 1, 1.189, 0.117, 1.211, 0.138, 1.233, 0.103, 1, 1.255, 0.068, 1.278, -0.442, 1.3, -0.442, 0, 1.367, 0.145, 0, 1.433, -0.4, 0, 1.533, 0.145, 0, 1.633, -0.442, 0, 1.7, 0.103, 0, 1.8, -0.442, 0, 1.9, 0.039, 0, 2.6, 0, 2, 2.967, 0, 2, 3.4, 0, 2, 5.833, 0, 2, 6.3, 0, 2, 6.533, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamMouthY", "Segments": [0, 0, 2, 0.167, 0, 2, 0.3, 0, 0, 0.533, 0.3, 0, 0.8, 0, 1, 0.878, 0.002, 0.955, 0.6, 1.033, 0.6, 2, 2.367, 0.6, 0, 2.733, 0, 2, 6.533, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamEyeEmotion", "Segments": [0, 0, 2, 0.167, 0, 0, 0.467, -0.6, 0, 0.967, 1, 0, 3.4, 0.973, 0, 3.633, 1, 0, 3.933, -0.4, 0, 4.233, 0, 0, 5, -1, 2, 5.633, -1, 0, 5.867, 0, 0, 6.133, -1, 2, 7.833, -1, 0, 8.333, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 2, 0.167, 0, 2, 3.133, 0, 0, 3.733, 1, 2, 5.833, 1, 0, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamBlackFace", "Segments": [0, 0, 2, 0.167, 0, 0, 0.8, 1, 2, 3.033, 1, 0, 3.267, 0, 2, 5.833, 0, 2, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamMark", "Segments": [0, 0, 2, 0.167, 0, 0, 0.9, 1.2, 2, 2.767, 1.2, 0, 3.033, 1.5, 2, 3.067, 0, 2, 5.833, 0, 2, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamShameLine", "Segments": [0, 0, 2, 0.167, 0, 2, 0.5, 0, 1, 0.567, 0, 0.633, 0.194, 0.7, 0.5, 2, 0.933, 1, 2, 1.167, 0.5, 2, 1.433, 1, 2, 1.667, 0.5, 2, 1.933, 1, 2, 2.167, 0.5, 2, 2.5, 0.5, 0, 2.633, 0, 2, 5.833, 0, 2, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamMarkShake", "Segments": [0, 0, 2, 0.167, 0, 2, 0.967, 0, 0, 1.033, 1, 0, 1.3, -1, 0, 1.5, 1, 0, 1.633, 0, 2, 3.4, 0, 2, 5.833, 0, 2, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLY", "Segments": [0, 0, 2, 0.167, 0, 2, 3.4, 0, 0, 3.6, -0.3, 2, 5.833, -0.3, 0, 6, 0, 2, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeL", "Segments": [0, 0, 2, 0.167, 0, 2, 0.533, 0, 0, 0.9, -0.7, 0, 1.033, -0.621, 0, 1.1, -0.769, 0, 1.233, -0.621, 0, 1.3, -0.769, 0, 1.367, -0.621, 0, 1.4, -0.769, 0, 1.5, -0.621, 0, 1.533, -0.769, 0, 1.6, -0.621, 0, 1.667, -0.769, 0, 1.767, -0.621, 0, 1.833, -0.769, 0, 1.9, -0.621, 0, 1.967, -0.769, 0, 2.067, -0.621, 0, 2.1, -0.769, 0, 2.133, -0.621, 0, 2.2, -0.769, 0, 2.333, -0.621, 0, 2.4, -0.769, 0, 2.467, -0.621, 0, 2.533, -0.7, 1, 2.822, -0.7, 3.111, -0.696, 3.4, -0.692, 1, 4.211, -0.681, 5.022, -0.674, 5.833, -0.661, 1, 6.011, -0.658, 6.189, 0, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRY", "Segments": [0, 0, 2, 0.167, 0, 0, 0.9, -0.396, 2, 2, -0.396, 1, 2.467, -0.396, 2.933, -0.397, 3.4, -0.392, 1, 4.211, -0.383, 5.022, -0.353, 5.833, -0.327, 1, 6.011, -0.321, 6.189, 0, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeR", "Segments": [0, 0, 2, 0.167, 0, 2, 0.533, 0, 0, 0.9, -0.7, 0, 1.033, -0.621, 0, 1.1, -0.769, 0, 1.233, -0.621, 0, 1.3, -0.769, 0, 1.367, -0.621, 0, 1.4, -0.769, 0, 1.5, -0.621, 0, 1.533, -0.769, 0, 1.6, -0.621, 0, 1.667, -0.769, 0, 1.767, -0.621, 0, 1.833, -0.769, 0, 1.9, -0.621, 0, 1.967, -0.769, 0, 2.067, -0.621, 0, 2.1, -0.769, 0, 2.133, -0.621, 0, 2.2, -0.769, 0, 2.333, -0.621, 0, 2.4, -0.769, 0, 2.467, -0.621, 0, 2.533, -0.7, 0, 3.4, -0.693, 0, 5.833, -0.7, 0, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow1", "Segments": [0, 0, 2, 0.167, 0, 2, 0.967, 0, 1, 1.189, 0.333, 1.411, 0.667, 1.633, 1, 2, 1.667, 0, 2, 1.767, 0, 2, 3.4, 0, 2, 5.833, 0, 2, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow2", "Segments": [0, 0, 2, 0.167, 0, 2, 1.1, 0, 1, 1.311, 0.333, 1.522, 0.667, 1.733, 1, 2, 1.767, 0, 2, 3.4, 0, 2, 5.833, 0, 2, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamTearLight", "Segments": [0, 0, 2, 0.167, 0, 2, 0.733, 0, 0, 1.267, 1, 0, 1.333, 0, 0, 1.467, 1, 0, 1.567, 0, 0, 1.7, 1, 0, 1.767, 0, 0, 1.833, 1, 0, 1.867, 0, 0, 2, 1, 0, 2.033, 0, 0, 2.067, 1, 0, 2.133, 0, 0, 2.267, 1, 0, 2.333, 0, 0, 2.433, 1, 0, 2.467, 0, 0, 2.567, 1, 0, 2.6, 0, 0, 2.633, 1, 0, 2.7, 0, 0, 2.833, 1, 0, 2.9, 0, 0, 3, 1, 0, 3.033, 0, 0, 3.167, 1, 0, 3.2, 0, 0, 3.233, 1, 1, 3.244, 1, 3.256, 0.406, 3.267, 0.2, 1, 3.278, 0, 3.289, 0, 3.3, 0, 2, 3.4, 0, 2, 5.833, 0, 2, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamTears", "Segments": [0, 0, 2, 0.167, 0, 2, 0.533, 0, 0, 0.9, 1, 2, 2, 1, 1, 2.467, 1, 2.933, 1, 3.4, 0.99, 1, 4.211, 0.967, 5.022, 0.893, 5.833, 0.827, 1, 6.011, 0.813, 6.189, 0, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamPHYInputX", "Segments": [0, 0, 2, 0.167, 0, 2, 0.233, 0, 0, 0.533, 9, 0, 0.9, -9, 0, 1.033, 2.901, 1, 1.044, 2.901, 1.056, 2.276, 1.067, 0.584, 1, 1.089, -2.798, 1.111, -4.747, 1.133, -4.747, 0, 1.2, 3.555, 0, 1.267, -5.37, 0, 1.333, 3.555, 0, 1.4, -2.981, 0, 1.433, 5.667, 0, 1.5, -4.747, 0, 1.533, 3.9, 0, 1.567, -5.37, 0, 1.633, 3.555, 0, 1.7, -5.37, 0, 1.767, 3.555, 0, 1.833, -2.981, 0, 1.867, 5.667, 0, 1.933, -4.747, 0, 1.967, 3.9, 1, 1.978, 3.9, 1.989, -5.37, 2, -5.37, 0, 2.067, 3.555, 0, 2.133, -5.37, 0, 2.2, 3.555, 0, 2.267, -2.981, 0, 2.3, 5.667, 0, 2.367, -4.747, 0, 2.4, 3.9, 1, 2.411, 3.9, 2.422, -5.37, 2.433, -5.37, 0, 2.6, 1.783, 0, 2.867, -0.006, 0, 3.367, 14.308, 0, 3.533, -10, 0, 3.8, 15, 0, 4.1, -14, 0, 4.467, 13, 0, 4.833, -13, 0, 5.133, 9, 0, 5.467, -1.374, 0, 5.833, -1.365, 0, 6.033, -22, 0, 6.367, 23, 0, 6.733, -4.302, 0, 7.667, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.167, 0, 2, 0.2, 0, 2, 0.3, 0, 0, 0.6, 2.717, 0, 0.9, -20.209, 1, 0.911, -20.209, 0.922, -19.276, 0.933, -18.486, 1, 0.955, -16.906, 0.978, -16.416, 1, -16.416, 0, 1.067, -19.378, 0, 1.133, -16.193, 0, 1.2, -19.378, 0, 1.267, -17.045, 0, 1.3, -20.129, 0, 1.367, -16.416, 0, 1.4, -19.5, 0, 1.433, -16.193, 0, 1.5, -19.378, 0, 1.567, -16.193, 0, 1.633, -19.378, 0, 1.7, -17.045, 0, 1.733, -20.129, 0, 1.8, -16.416, 0, 1.833, -19.5, 0, 1.867, -16.193, 0, 1.933, -19.378, 0, 2, -16.193, 0, 2.067, -19.378, 0, 2.133, -17.045, 0, 2.167, -20.129, 0, 2.233, -16.416, 0, 2.267, -19.5, 0, 2.3, -16.193, 0, 2.367, -19.378, 0, 2.433, -17.993, 0, 3.133, -18.05, 0, 3.733, -17, 0, 4.1, -19.378, 0, 4.567, 4.814, 0, 4.833, -13.921, 0, 5.033, 7.092, 0, 5.267, -10.703, 0, 5.567, 7.092, 0, 5.733, -6.351, 0, 6.1, 0, 2, 6.3, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.167, 0, 2, 0.2, 0, 2, 0.267, 0, 0, 0.567, -4, 0, 0.9, 30, 0, 1, 27, 0, 1.133, 29.916, 0, 1.367, 27.919, 0, 2, 29, 1, 2.222, 29, 2.445, 28.728, 2.667, 27, 1, 2.745, 26.395, 2.822, 10.219, 2.9, 10.219, 0, 3.233, 27, 0, 3.733, -18.773, 0, 4, 4, 0, 4.3, -5, 0, 4.6, 1.034, 0, 4.833, -1.671, 0, 5.067, 0, 0, 5.3, -1.671, 0, 5.767, 13.803, 0, 6.2, -18.773, 0, 6.633, 2.668, 0, 6.967, -1.671, 0, 7.433, 0, 2, 7.567, 0, 0, 7.9, -11, 0, 8.567, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.167, 0, 2, 0.2, 0, 0, 0.633, -3.24, 0, 0.933, 0.688, 0, 1.067, -0.668, 0, 1.233, 0, 2, 2.8, 0, 0, 3, -2.498, 0, 3.333, 9.765, 0, 3.933, 0, 2, 5.533, 0, 0, 5.833, 8, 0, 6.267, -12, 0, 6.733, -8, 0, 7.067, -9.278, 0, 7.633, -8.902, 0, 7.833, -9.307, 0, 8.367, 0.688, 0, 8.567, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamAngleH", "Segments": [0, 0, 2, 0.167, 0, 2, 0.2, 0, 0, 0.367, 12, 1, 0.445, 12, 0.522, 8.887, 0.6, 8, 1, 0.722, 6.606, 0.845, 5.054, 0.967, 5, 1, 1.778, 4.641, 2.589, 4.356, 3.4, 4.058, 1, 4.2, 3.764, 5, 3.556, 5.8, 3.221, 1, 5.967, 3.151, 6.133, 0, 6.3, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamAngleS", "Segments": [0, 0, 2, 0.167, 0, 0, 0.533, 1, 0, 0.8, 0, 2, 3.4, 0, 2, 5.8, 0, 2, 6.3, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperLAngle", "Segments": [0, 0, 2, 0.167, 0, 0, 0.5, -2, 0, 0.633, 1, 0, 0.9, -5, 2, 2.833, -5, 0, 3.133, -6.22, 0, 3.4, 1.815, 0, 3.6, -2, 1, 4.333, -2, 5.067, -1.991, 5.8, -1.949, 1, 5.967, -1.94, 6.133, 0, 6.3, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerLAngle", "Segments": [0, 10, 2, 0.167, 10, 0, 0.433, 9.874, 0, 0.567, 12, 0, 0.9, -3.362, 0, 1, -3, 2, 1.1, -3, 2, 3.2, -3, 1, 3.3, -3, 3.4, 6.815, 3.5, 8.59, 1, 3.6, 10.365, 3.7, 10.2, 3.8, 10.2, 1, 4.467, 10.2, 5.133, 10.199, 5.8, 10.196, 1, 5.967, 10.195, 6.133, 10, 6.3, 10, 2, 8.967, 10]}, {"Target": "Parameter", "Id": "ParamArmLowerLH", "Segments": [0, 0, 2, 0.167, 0, 0, 0.9, -12, 1, 1.267, -12, 1.633, -11.457, 2, -11, 1, 2.433, -10.46, 2.867, -10.262, 3.3, -9.72, 1, 3.422, -9.567, 3.545, 0, 3.667, 0, 2, 6.3, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerLAngle", "Segments": [0, 0, 2, 0.167, 0, 0, 0.9, 8, 2, 2, 8, 1, 2.467, 8, 2.933, 8.026, 3.4, 7.92, 1, 4.2, 7.738, 5, 7.137, 5.8, 6.614, 1, 5.967, 6.505, 6.133, 0, 6.3, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamArm<PERSON>pperRAngle", "Segments": [0, 0, 2, 0.167, 0, 0, 0.433, -2, 0, 0.8, 1, 0, 1.033, 0, 2, 2, 0, 2, 3.4, 0, 0, 3.7, -0.474, 0, 4.033, 0.089, 0, 4.333, -0.474, 0, 4.7, 0.126, 0, 5, -0.223, 0, 5.333, 0, 2, 5.5, 0, 0, 5.8, -1.013, 1, 5.856, -1.013, 5.911, -1.093, 5.967, -0.576, 1, 6.022, -0.059, 6.078, 5, 6.133, 5, 0, 6.267, 4.551, 0, 6.533, 5, 2, 7.733, 5, 1, 7.922, 5, 8.111, 0.255, 8.3, 0.126, 1, 8.522, -0.026, 8.745, 0, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRAngle", "Segments": [0, 7.4, 2, 0.167, 7.4, 2, 0.233, 7.4, 0, 0.533, 7, 0, 0.9, 11, 2, 1, 11, 2, 2, 11, 2, 2.9, 11, 0, 3.1, 10.46, 0, 3.6, 16.795, 0, 3.833, 16.013, 2, 5.633, 16.013, 0, 6.133, -4, 0, 6.433, -2.463, 0, 6.8, -3.238, 0, 7.1, -2.837, 0, 7.767, -3.238, 0, 8.167, 7.58, 0, 8.367, 7.4, 2, 8.967, 7.4]}, {"Target": "Parameter", "Id": "ParamArmHandRAngle", "Segments": [0, 0, 2, 0.167, 0, 0, 0.567, 3, 0, 0.9, -10, 0, 1.167, -9.621, 0, 1.567, -10, 2, 3.2, -10, 2, 3.4, -10, 0, 3.7, -6, 0, 3.933, -7, 1, 4.5, -7, 5.066, -6.968, 5.633, -6.851, 1, 5.733, -6.83, 5.833, 1.566, 5.933, 1.566, 0, 6.133, -10, 0, 6.467, -7, 0, 6.867, -10, 0, 7.1, -8.319, 0, 7.367, -10, 2, 7.933, -10, 0, 8.3, 0.463, 0, 8.533, -0.427, 0, 8.767, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRH", "Segments": [0, 0, 2, 0.167, 0, 0, 0.467, -23, 0, 0.9, -16, 2, 2, -16, 1, 2.467, -16, 2.933, -16.053, 3.4, -15.841, 1, 4.2, -15.478, 5, -14.276, 5.8, -13.232, 1, 5.967, -13.015, 6.133, 0, 6.3, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperRH", "Segments": [0, 0, 2, 0.167, 0, 0, 0.8, -5, 0, 1.367, -3, 2, 2, -3, 2, 2.933, -3, 0, 3.233, -7, 0, 3.833, 2.349, 0, 4, 0, 2, 5.8, 0, 2, 6.3, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRAngle", "Segments": [0, 0, 2, 0.167, 0, 0, 0.9, -5, 2, 3.4, -5, 1, 4.2, -5, 5, -4.968, 5.8, -4.836, 1, 5.967, -4.809, 6.133, 4.607, 6.3, 4.607, 0, 6.667, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamHandT2R", "Segments": [0, 0, 2, 0.167, 0, 2, 0.667, 0, 0, 0.9, 0.799, 0, 1.1, 0.415, 0, 1.3, 0.5, 2, 2, 0.5, 1, 2.467, 0.5, 2.933, 0.464, 3.4, 0.441, 1, 4.2, 0.402, 5, 0.384, 5.8, 0.348, 1, 5.922, 0.343, 6.045, 0, 6.167, 0, 0, 6.333, 0.164, 0, 6.733, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.167, 0, 2, 0.233, 0, 0, 0.533, 3, 0, 0.9, -7.13, 0, 1.033, -5.264, 1, 1.044, -5.264, 1.056, -5.574, 1.067, -5.838, 1, 1.089, -6.365, 1.111, -6.528, 1.133, -6.528, 0, 1.2, -5.541, 0, 1.267, -6.602, 0, 1.333, -5.541, 0, 1.4, -6.318, 0, 1.433, -5.29, 0, 1.5, -6.528, 0, 1.533, -5.5, 0, 1.567, -6.602, 0, 1.633, -5.541, 0, 1.7, -6.602, 0, 1.767, -5.541, 0, 1.833, -6.318, 0, 1.867, -5.29, 0, 1.933, -6.528, 0, 1.967, -5.5, 0, 2, -6.602, 0, 2.067, -5.541, 0, 2.133, -6.602, 0, 2.2, -5.541, 0, 2.267, -6.318, 0, 2.3, -5.29, 0, 2.367, -6.528, 0, 2.4, -5.5, 0, 2.433, -6.602, 0, 2.6, -5.541, 0, 2.867, -6.002, 0, 3.533, -5.264, 0, 3.733, -10, 0, 4.3, -0.207, 0, 4.6, -4.846, 0, 4.833, -0.69, 0, 5.067, -4.478, 0, 5.267, -0.69, 0, 5.567, -3.458, 0, 5.8, -3.455, 0, 6.3, -5.264, 0, 8.267, 0.39, 0, 8.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.167, 0, 0, 0.3, 4, 0, 0.6, -1, 0, 0.933, 8.668, 0, 1.033, 7.45, 2, 3.3, 7.45, 0, 3.9, -8, 0, 4.2, -3.832, 0, 4.567, -4.049, 1, 4.767, -4.049, 4.967, -4.069, 5.167, -3.933, 1, 5.322, -3.827, 5.478, 4, 5.633, 4, 0, 6.167, -7.933, 1, 6.211, -7.933, 6.256, -7.189, 6.3, -6.721, 1, 6.789, -1.577, 7.278, 0.717, 7.767, 0.717, 0, 8.233, -0.584, 0, 8.633, 0.345, 0, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.167, 0, 2, 0.233, 0, 0, 0.467, -0.988, 0, 0.967, 3, 2, 2, 3, 0, 2.833, 2.065, 0, 3.433, 5.232, 0, 4, 4.322, 0, 4.367, 5.232, 0, 4.733, 4.259, 0, 5.2, 4.767, 1, 5.311, 4.767, 5.422, 4.639, 5.533, 4.268, 1, 5.622, 3.971, 5.711, 3.748, 5.8, 3.748, 0, 6.3, 4.762, 0, 7.033, 0.925, 0, 7.667, 2, 0, 8.233, -0.413, 0, 8.7, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX2", "Segments": [0, 0, 2, 0.167, 0, 0, 0.533, 5, 0, 0.9, -10, 2, 2, -10, 0, 3.4, -9.901, 0, 5.667, -10, 0, 5.8, -8.272, 0, 5.9, -9.084, 0, 6.3, -3.186, 0, 6.9, -5.976, 0, 7.767, 3.466, 0, 8.367, -1.992, 0, 8.733, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY2", "Segments": [0, 0, 2, 0.167, 0, 0, 0.4, -1, 0, 0.8, 9.064, 1, 1.667, 9.064, 2.533, 9.043, 3.4, 8.974, 1, 3.533, 8.963, 3.667, -1.577, 3.8, -1.577, 0, 4.067, 1.966, 0, 4.5, 1.034, 0, 4.767, 1.805, 0, 5.4, 1, 0, 5.633, 2.136, 1, 5.711, 2.136, 5.789, 2.12, 5.867, 1.363, 1, 5.911, 0.93, 5.956, -10, 6, -10, 1, 6.1, -10, 6.2, -7.84, 6.3, -6.836, 1, 6.8, -1.817, 7.3, 0, 7.8, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechX", "Segments": [0, 0, 2, 0.167, 0, 0, 0.533, 10, 1, 0.655, 10, 0.778, 7.863, 0.9, 5, 1, 0.911, 4.74, 0.922, 4.838, 0.933, 4.838, 0, 1, 5.528, 0, 1.067, 4.541, 0, 1.133, 5.602, 0, 1.2, 4.541, 0, 1.267, 5.318, 0, 1.3, 4.29, 0, 1.367, 5.528, 0, 1.4, 4.5, 0, 1.433, 5.602, 0, 1.5, 4.541, 0, 1.533, 5, 0, 1.567, 4.838, 0, 1.633, 5.528, 0, 1.7, 4.541, 0, 1.767, 5.602, 0, 1.833, 4.541, 0, 1.9, 5.318, 0, 1.933, 4.29, 0, 2, 5.528, 0, 2.033, 4.5, 0, 2.067, 5.602, 0, 2.133, 4.541, 0, 2.2, 5.528, 0, 2.267, 4.541, 0, 2.333, 5.602, 0, 2.4, 4.541, 0, 2.467, 5.318, 0, 2.5, 4.29, 0, 2.567, 5.528, 0, 2.633, 5, 0, 2.867, 5.002, 0, 3.067, 3, 0, 3.667, 5.892, 0, 4, -0.326, 0, 4.3, 6.326, 0, 4.667, 1.228, 0, 5.467, 7.357, 0, 5.9, 0.956, 0, 6.133, 7.357, 1, 6.189, 7.357, 6.244, 6.452, 6.3, 5.892, 1, 6.789, 0.962, 7.278, -1.169, 7.767, -1.169, 0, 8.567, 0.956, 0, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechW", "Segments": [0, 0, 2, 0.167, 0, 0, 0.533, 4, 0, 0.8, -6, 0, 0.967, -3, 0, 1.133, -5, 0, 1.3, -1.665, 0, 1.5, -2.256, 0, 1.933, 0, 2, 3.4, 0, 2, 5.8, 0, 2, 6.3, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, 0, 2, 0.167, 0, 0, 0.533, -1, 0, 0.9, 2, 0, 0.933, 1.848, 0, 0.967, 2.031, 0, 1, 1.885, 0, 1.067, 2.042, 0, 1.1, 1.885, 0, 1.133, 2, 0, 1.167, 1.848, 0, 1.233, 2.031, 0, 1.267, 1.879, 0, 1.3, 2.042, 0, 1.367, 1.885, 0, 1.4, 2, 0, 1.433, 1.848, 0, 1.467, 2.031, 0, 1.5, 1.885, 0, 1.567, 2.042, 0, 1.6, 1.885, 0, 1.633, 2, 0, 1.667, 1.848, 0, 1.733, 2.042, 0, 1.767, 1.885, 0, 1.8, 2, 0, 1.833, 1.848, 0, 1.9, 2.031, 0, 1.933, 1.879, 0, 1.967, 2.042, 0, 2.033, 1.885, 0, 2.067, 2, 0, 2.1, 1.848, 0, 2.133, 2.031, 0, 2.167, 1.885, 0, 2.233, 2.042, 0, 2.3, 1.885, 0, 2.367, 2.031, 0, 2.433, 1.879, 0, 2.467, 2.042, 0, 2.533, 1.885, 0, 2.6, 1.995, 1, 2.811, 1.995, 3.022, 2.001, 3.233, 1.848, 1, 3.355, 1.759, 3.478, -7.087, 3.6, -7.087, 0, 3.833, -5, 2, 5.9, -5, 0, 6.133, -5.443, 1, 6.189, -5.443, 6.244, -5.534, 6.3, -5.285, 1, 7, -2.145, 7.7, 0, 8.4, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamShoulderR", "Segments": [0, 0, 2, 0.167, 0, 0, 0.533, -30, 0, 0.9, 30, 2, 2, 30, 1, 2.467, 30, 2.933, 30, 3.4, 29.702, 1, 4.2, 28.918, 5, 24.812, 5.8, 24.812, 0, 5.933, 30, 0, 6.3, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamShoulderL", "Segments": [0, 0, 2, 0.167, 0, 0, 0.533, -30, 0, 0.9, 30, 1, 1.733, 30, 2.567, 29.991, 3.4, 29.156, 1, 4.2, 28.354, 5, 26.232, 5.8, 24.293, 1, 5.967, 23.889, 6.133, 0, 6.3, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Z", "Segments": [0, 0, 2, 0.333, 0, 0, 0.7, 7.446, 0, 0.833, 7, 2, 1.833, 7, 0, 1.9, 6.734, 2, 2.967, 6.734, 1, 3.211, 6.734, 3.456, 2.196, 3.7, 0, 1, 3.811, -0.998, 3.922, -0.72, 4.033, -0.72, 0, 4.333, 0, 0, 4.7, -0.72, 0, 5, 0, 0, 5.433, -0.54, 1, 5.566, -0.54, 5.7, -0.541, 5.833, -0.539, 1, 6.011, -0.537, 6.189, 0, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Z", "Segments": [0, 0, 2, 0.433, 0, 0, 0.867, -17, 0, 1.067, -16.202, 0, 1.333, -17, 0, 1.467, -16.403, 1, 1.478, -16.403, 1.489, -16.445, 1.5, -16.454, 1, 1.578, -16.518, 1.655, -16.544, 1.733, -16.629, 1, 1.778, -16.677, 1.822, -17.211, 1.867, -17.334, 1, 1.878, -17.365, 1.889, -17.365, 1.9, -17.366, 1, 2.256, -17.404, 2.611, -17.422, 2.967, -17.422, 1, 3, -17.422, 3.034, -17.532, 3.067, -17, 1, 3.156, -15.58, 3.244, -13.628, 3.333, -10.36, 1, 3.444, -6.275, 3.556, 0.564, 3.667, 0.564, 0, 3.8, -0.17, 0, 4, 0, 2, 4.067, 0, 2, 5.833, 0, 2, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Z", "Segments": [0, 0, 1, 0.5, 0, 1, -0.471, 1.5, -0.615, 1, 1.578, -0.637, 1.655, -0.624, 1.733, -0.628, 1, 1.789, -0.631, 1.844, -0.632, 1.9, -0.632, 2, 2.267, -0.632, 2, 2.5, -0.632, 2, 2.633, -0.632, 2, 2.967, -0.632, 0, 3.433, 0, 2, 5.833, 0, 2, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Y", "Segments": [0, 0, 2, 3.167, 0, 1, 3.334, 0, 3.5, -5.302, 3.667, -12.15, 1, 3.778, -16.715, 3.889, -17.769, 4, -17.769, 0, 4.3, -12.15, 0, 4.667, -16.645, 0, 4.967, -12.712, 0, 5.4, -16.083, 1, 5.544, -16.083, 5.689, -16.093, 5.833, -16.062, 1, 6.011, -16.024, 6.189, 0, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Y", "Segments": [0, 0, 2, 3.167, 0, 0, 3.633, 20, 1, 4.366, 20, 5.1, 19.904, 5.833, 19.45, 1, 6.011, 19.34, 6.189, 0, 6.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuL", "Segments": [0, 0, 2, 0.667, 0, 0, 0.7, -0.325, 0, 0.733, 0.107, 0, 0.767, -0.181, 0, 0.8, 0.088, 0, 0.833, -0.125, 0, 0.867, 0.139, 0, 0.933, 0.005, 0, 0.967, 0.163, 0, 1, 0, 2, 1.667, 0, 2, 1.833, 0, 0, 3.367, 20, 2, 5.067, 20, 1, 5.4, 20, 5.734, 10.163, 6.067, 6.613, 1, 6.622, 0.696, 7.178, 0, 7.733, 0, 2, 8.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuL", "Segments": [0, 0, 2, 0.233, 0, 0, 0.267, -0.18, 2, 0.3, -0.18, 2, 0.533, -0.18, 0, 0.667, 4.426, 0, 0.7, 4.102, 0, 0.733, 4.762, 0, 0.767, 4.246, 0, 0.8, 4.743, 0, 0.833, 4.53, 0, 0.867, 4.794, 0, 0.933, 4.432, 1, 0.944, 4.432, 0.956, 4.469, 0.967, 4.59, 1, 1.034, 5.318, 1.1, 5.71, 1.167, 5.71, 1, 1.222, 5.71, 1.278, 5.714, 1.333, 5.653, 1, 1.378, 5.604, 1.422, 5.365, 1.467, 5.016, 1, 1.511, 4.667, 1.556, 3.086, 1.6, 1.781, 1, 1.622, 1.129, 1.645, -0.06, 1.667, -0.06, 0, 1.733, 0, 2, 7.133, 0, 2, 8.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuL", "Segments": [0, 0, 2, 0.533, 0, 0, 0.667, 4.426, 0, 0.7, 4.102, 0, 0.733, 4.762, 0, 0.767, 4.246, 0, 0.8, 4.743, 0, 0.833, 4.53, 0, 0.867, 4.794, 0, 0.933, 4.432, 1, 0.944, 4.432, 0.956, 4.548, 0.967, 4.59, 1, 0.978, 4.632, 0.989, 4.63, 1, 4.63, 0, 1.067, 0, 2, 7.133, 0, 2, 8.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamMjLFlip", "Segments": [0, 0, 2, 1, 0, 0, 1.1, 1, 2, 1.567, 1, 2, 4.2, 1, 0, 4.567, 0, 2, 7.733, 0, 2, 8.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamPositionZManjuuL", "Segments": [0, 0, 2, 0.167, 0, 0, 0.267, -13, 1, 0.4, -13, 0.534, -9.224, 0.667, 7, 1, 0.734, 15.112, 0.8, 30, 0.867, 30, 1, 0.911, 30, 0.956, 13.747, 1, 6.012, 1, 1.056, -3.657, 1.111, -13.515, 1.167, -15, 1, 1.211, -16.188, 1.256, -16, 1.3, -16, 0, 1.6, -8, 0, 1.733, -30, 2, 2.167, -30, 0, 2.333, 0, 2, 7.133, 0, 2, 8.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLEyeOpen", "Segments": [0, 0, 2, 0.267, 0, 0, 0.667, 1, 2, 1, 1, 2, 1.667, 1, 2, 2.333, 1, 2, 2.6, 1, 2, 3, 1, 0, 7.133, 0, 2, 8.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyX", "Segments": [0, 0, 2, 0.667, 0, 0, 1, 30, 0, 1.167, -30, 0, 1.567, 30, 1, 1.6, 30, 1.634, 10.661, 1.667, 0, 1, 1.722, -17.768, 1.778, -21, 1.833, -21, 0, 2.1, 10.631, 0, 2.333, -17, 0, 2.6, 0, 2, 7.133, 0, 2, 8.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyY", "Segments": [0, 0, 2, 0.967, 0, 0, 1.7, -22.948, 0, 2.2, 30, 0, 2.4, -12, 0, 2.8, 0, 2, 5.133, 0, 0, 5.2, 21.076, 0, 5.267, -21.076, 1, 5.289, -21.076, 5.311, 21.076, 5.333, 21.076, 0, 5.4, -21.076, 0, 5.467, 21.076, 1, 5.489, 21.076, 5.511, -21.076, 5.533, -21.076, 0, 5.6, 21.076, 0, 5.667, -21.076, 1, 5.689, -21.076, 5.711, 21.076, 5.733, 21.076, 0, 5.8, -21.076, 0, 5.867, 21.076, 1, 5.889, 21.076, 5.911, -21.076, 5.933, -21.076, 0, 6, 21.076, 0, 6.067, -21.076, 1, 6.089, -21.076, 6.111, 21.076, 6.133, 21.076, 0, 6.2, -21.076, 0, 6.267, 21.076, 1, 6.289, 21.076, 6.311, -21.076, 6.333, -21.076, 0, 6.4, 21.076, 0, 6.467, -21.076, 1, 6.489, -21.076, 6.511, 21.076, 6.533, 21.076, 0, 6.6, -21.076, 0, 6.667, 21.076, 0, 6.767, -21.076, 1, 6.789, -21.076, 6.811, 21.076, 6.833, 21.076, 0, 6.933, -21.076, 0, 7, 21.076, 0, 7.067, -21.076, 1, 7.089, -21.076, 7.111, 21.076, 7.133, 21.076, 0, 7.2, -21.076, 0, 7.267, 21.076, 1, 7.289, 21.076, 7.311, -21.076, 7.333, -21.076, 0, 7.4, 21.076, 0, 7.467, -21.076, 1, 7.489, -21.076, 7.511, 21.076, 7.533, 21.076, 0, 7.6, -21.076, 0, 7.667, 21.076, 1, 7.689, 21.076, 7.711, -21.076, 7.733, -21.076, 0, 7.8, 21.076, 0, 7.867, -21.076, 1, 7.889, -21.076, 7.911, 21.076, 7.933, 21.076, 0, 8.167, -3.527, 0, 8.4, 3, 0, 8.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyZ", "Segments": [0, 0, 2, 0.3, 0, 0, 0.4, -8, 1, 0.533, -8, 0.667, -2.41, 0.8, 10, 1, 0.867, 16.205, 0.933, 20, 1, 20, 2, 1.133, 20, 0, 1.333, 30, 0, 1.733, -18, 0, 1.933, 30, 0, 2.1, 0, 0, 2.233, 30, 2, 2.467, 30, 0, 2.733, 0, 2, 4.9, 0, 0, 5.367, 30, 2, 7.3, 30, 0, 7.667, 0, 0, 7.867, 30, 0, 8.1, -7.258, 0, 8.333, 6.105, 0, 8.6, 0, 2, 8.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyW", "Segments": [0, 0, 2, 0.167, 0, 0, 0.267, -22.148, 0, 0.4, 30, 2, 0.467, 30, 0, 0.7, -30, 2, 1.133, -30, 0, 1.4, 27.255, 0, 1.5, -19.148, 1, 1.544, -19.148, 1.589, -14.371, 1.633, 0, 1, 1.689, 17.963, 1.744, 30, 1.8, 30, 0, 1.967, -30, 0, 2.167, 19.49, 0, 2.367, -22.148, 0, 2.533, 8, 0, 2.667, -11.034, 0, 2.767, 0, 2, 7.133, 0, 2, 7.633, 0, 0, 7.8, 11, 0, 8, -8, 0, 8.2, 0, 2, 8.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuL", "Segments": [0, 0, 2, 5.1, 0, 0, 5.167, 1, 0, 5.233, -1, 0, 5.3, 1, 0, 5.367, -1, 0, 5.433, 1, 0, 5.5, -1, 0, 5.567, 1, 0, 5.633, -1, 0, 5.7, 1, 0, 5.767, -1, 0, 5.833, 1, 0, 5.9, -1, 0, 5.967, 1, 0, 6.033, -1, 0, 6.1, 1, 0, 6.167, -1, 0, 6.233, 1, 0, 6.3, -1, 0, 6.367, 1, 0, 6.433, -1, 0, 6.5, 1, 0, 6.567, -1, 0, 6.633, 1, 0, 6.733, -1, 0, 6.8, 1, 0, 6.9, -1, 0, 6.967, 1, 0, 7.033, -1, 0, 7.1, 1, 0, 7.167, -1, 0, 7.233, 1, 0, 7.3, -1, 0, 7.367, 1, 0, 7.433, -1, 0, 7.5, 1, 0, 7.567, -1, 0, 7.633, 1, 0, 7.7, -1, 0, 7.767, 1, 0, 7.833, -1, 0, 7.9, 1, 0, 7.933, 0, 2, 8.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamClawFX", "Segments": [0, 0, 2, 1.667, 0, 0, 1.8, 1, 0, 1.9, -1, 2, 1.933, -1, 0, 2.033, 1, 0, 2.133, -1, 2, 2.167, -1, 0, 2.267, 1, 0, 2.367, -1, 2, 2.4, -1, 0, 2.5, 1, 0, 2.6, -1, 2, 2.633, -1, 0, 2.733, 1, 0, 2.833, -1, 2, 2.867, -1, 0, 2.967, 1, 0, 3.067, -1, 2, 3.1, -1, 0, 3.2, 1, 0, 3.3, 0, 2, 5.1, 0, 0, 5.233, 1, 0, 5.333, -1, 2, 5.367, -1, 0, 5.467, 1, 0, 5.567, -1, 2, 5.6, -1, 0, 5.7, 1, 0, 5.8, -1, 2, 5.833, -1, 0, 5.933, 1, 0, 6.033, -1, 2, 6.067, -1, 0, 6.167, 1, 0, 6.267, -1, 2, 6.3, -1, 0, 6.4, 1, 0, 6.5, -1, 2, 6.533, -1, 0, 6.633, 1, 0, 6.733, -1, 2, 6.767, -1, 0, 6.867, 1, 0, 6.967, -1, 2, 7, -1, 0, 7.1, 1, 0, 7.2, -1, 2, 7.233, -1, 0, 7.333, 1, 0, 7.433, -1, 2, 7.467, -1, 0, 7.567, 1, 0, 7.667, -1, 2, 7.7, -1, 0, 7.8, 1, 0, 7.9, -1, 0, 7.967, 0, 2, 8.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamClawFY", "Segments": [0, 0, 2, 1.667, 0, 0, 1.733, -1, 0, 1.8, 1, 0, 1.933, -1, 0, 2.133, 1, 0, 2.167, -1, 0, 2.367, 1, 0, 2.4, -1, 2, 2.633, -1, 0, 2.833, 1, 0, 2.867, -1, 0, 3.067, 1, 0, 3.1, -1, 0, 3.3, 0, 2, 5.1, 0, 0, 5.167, -1, 0, 5.267, 1, 0, 5.367, -1, 2, 5.433, -1, 0, 5.5, 1, 0, 5.6, -1, 1, 5.622, -1, 5.645, -1, 5.667, -0.999, 1, 5.689, -0.998, 5.711, 1, 5.733, 1, 0, 5.833, -1, 2, 5.9, -1, 0, 5.967, 1, 0, 6.067, -1, 2, 6.133, -1, 0, 6.2, 1, 0, 6.3, -1, 2, 6.367, -1, 0, 6.433, 1, 0, 6.533, -1, 2, 6.6, -1, 0, 6.667, 1, 0, 6.767, -1, 2, 6.833, -1, 0, 6.9, 1, 0, 7, -1, 2, 7.067, -1, 0, 7.133, 1, 0, 7.233, -1, 2, 7.3, -1, 0, 7.367, 1, 0, 7.467, -1, 2, 7.533, -1, 0, 7.6, 1, 0, 7.7, -1, 2, 7.767, -1, 0, 7.833, 1, 0, 7.933, 0, 2, 8.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamClawBX", "Segments": [0, 0, 2, 1.667, 0, 0, 1.8, -1, 0, 1.9, 1, 2, 1.933, 1, 0, 2.033, -1, 0, 2.133, 1, 2, 2.167, 1, 0, 2.267, -1, 0, 2.367, 1, 2, 2.4, 1, 0, 2.5, -1, 0, 2.6, 1, 2, 2.633, 1, 0, 2.733, -1, 0, 2.833, 1, 2, 2.867, 1, 0, 2.967, -1, 0, 3.067, 1, 2, 3.1, 1, 0, 3.2, -1, 0, 3.3, 0, 2, 5.1, 0, 0, 5.233, -1, 0, 5.333, 1, 2, 5.367, 1, 0, 5.467, -1, 0, 5.567, 1, 2, 5.6, 1, 0, 5.7, -1, 0, 5.8, 1, 2, 5.833, 1, 0, 5.933, -1, 0, 6.033, 1, 2, 6.067, 1, 0, 6.167, -1, 0, 6.267, 1, 2, 6.3, 1, 0, 6.4, -1, 0, 6.5, 1, 2, 6.533, 1, 0, 6.633, -1, 0, 6.733, 1, 2, 6.767, 1, 0, 6.867, -1, 0, 6.967, 1, 2, 7, 1, 0, 7.1, -1, 0, 7.2, 1, 2, 7.233, 1, 0, 7.333, -1, 0, 7.433, 1, 2, 7.467, 1, 0, 7.567, -1, 0, 7.667, 1, 2, 7.7, 1, 0, 7.8, -1, 0, 7.9, 1, 0, 7.967, 0, 2, 8.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamClawBY", "Segments": [0, 0, 2, 1.667, 0, 0, 1.733, 1, 0, 1.8, -1, 0, 1.933, 1, 0, 2.133, -1, 0, 2.167, 1, 0, 2.367, -1, 0, 2.4, 1, 2, 2.633, 1, 0, 2.833, -1, 0, 2.867, 1, 0, 3.067, -1, 0, 3.1, 1, 0, 3.3, 0, 2, 5.1, 0, 0, 5.167, 1, 0, 5.267, -1, 0, 5.367, 1, 2, 5.433, 1, 0, 5.5, -1, 0, 5.6, 1, 1, 5.622, 1, 5.645, 1, 5.667, 0.999, 1, 5.689, 0.998, 5.711, -1, 5.733, -1, 0, 5.833, 1, 2, 5.9, 1, 0, 5.967, -1, 0, 6.067, 1, 2, 6.133, 1, 0, 6.2, -1, 0, 6.3, 1, 2, 6.367, 1, 0, 6.433, -1, 0, 6.533, 1, 2, 6.6, 1, 0, 6.667, -1, 0, 6.767, 1, 2, 6.833, 1, 0, 6.9, -1, 0, 7, 1, 2, 7.067, 1, 0, 7.133, -1, 0, 7.233, 1, 2, 7.3, 1, 0, 7.367, -1, 0, 7.467, 1, 2, 7.533, 1, 0, 7.6, -1, 0, 7.7, 1, 2, 7.767, 1, 0, 7.833, -1, 0, 7.933, 0, 2, 8.033, 0, 2, 8.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuU", "Segments": [0, 0, 2, 0.167, 0, 2, 0.433, 0, 0, 0.7, 1.32, 1, 0.778, 1.32, 0.855, 0.826, 0.933, -1.857, 1, 0.966, -3.007, 1, -9, 1.033, -9, 2, 1.233, -9, 0, 6.933, -9.063, 1, 7.044, -9.063, 7.156, -9.051, 7.267, -8.473, 1, 7.334, -8.126, 7.4, -5.124, 7.467, -2.978, 1, 7.534, -0.832, 7.6, 0.951, 7.667, 0.951, 0, 7.767, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyY", "Segments": [0, 0, 2, 0.167, 0, 2, 0.2, 0, 0, 0.4, -30, 0, 0.667, 30, 0, 1.167, 0, 2, 1.233, 0, 2, 6.933, 0, 2, 6.967, 0, 0, 7.167, 30, 0, 7.433, -30, 0, 7.933, 0, 2, 8, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyZ", "Segments": [0, 0, 2, 0.167, 0, 0, 0.367, -25, 0, 0.633, 19, 0, 1.133, 0, 2, 1.233, 0, 2, 6.933, 0, 0, 7.133, -16, 0, 7.4, 23, 0, 7.8, -21, 0, 8, 8, 0, 8.233, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUArmZ", "Segments": [0, 0, 2, 0.167, 0, 2, 0.267, 0, 0, 0.4, -12, 0, 0.767, 30, 0, 1.233, 0, 2, 6.933, 0, 2, 7.033, 0, 0, 7.167, -12, 0, 7.533, 30, 0, 8, -7, 0, 8.233, 3.141, 0, 8.467, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyW", "Segments": [0, 0, 2, 0.167, 0, 0, 0.367, -0.5, 0, 0.6, 1, 0, 0.933, -1, 0, 1.233, 0, 2, 6.933, 0, 0, 7.133, 0.5, 0, 7.367, -1, 0, 7.7, 1, 0, 7.933, -0.5, 0, 8.167, 0.5, 0, 8.433, -0.3, 0, 8.733, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamPositionXPanda", "Segments": [0, 0, 2, 1.467, 0, 1, 1.834, 0, 2.2, 14.25, 2.567, 16.892, 1, 3.022, 20.175, 3.478, 20, 3.933, 20, 2, 4.667, 20, 1, 5.134, 20, 5.6, 17.642, 6.067, 12, 1, 6.7, 4.343, 7.334, 0, 7.967, 0, 2, 8.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamPositionYPanda", "Segments": [0, 0, 2, 0.433, 0, 0, 0.633, -0.12, 1, 0.678, -0.12, 0.722, -0.139, 0.767, 0.216, 1, 0.8, 0.482, 0.834, 2.73, 0.867, 2.73, 0, 0.9, 2.18, 0, 0.933, 3.107, 0, 0.967, 2.373, 0, 1, 2.932, 0, 1.033, 2.385, 0, 1.067, 3.131, 0, 1.1, 2.403, 0, 1.133, 2.972, 0, 1.167, 2.614, 0, 1.2, 2.993, 0, 1.233, 2.287, 0, 1.267, 2.77, 0, 1.3, 2.437, 0, 1.367, 2.73, 1, 1.4, 2.73, 1.434, 2.525, 1.467, 1.664, 1, 1.5, 0.803, 1.534, 0, 1.567, 0, 0, 1.633, 0.018, 0, 2.067, 0, 2, 8.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamSizePanda", "Segments": [0, 0, 2, 0.433, 0, 2, 0.533, 0, 2, 0.8, 0, 1, 0.844, 0, 0.889, 0.316, 0.933, 0.972, 1, 0.944, 1.136, 0.956, 1.231, 0.967, 1.231, 0, 1, 0.913, 0, 1.033, 1.161, 0, 1.067, 0.923, 0, 1.1, 1.025, 0, 1.133, 0.898, 0, 1.2, 1.072, 0, 1.233, 0.834, 0, 1.267, 1.075, 0, 1.3, 0, 2, 1.933, 0, 2, 2.6, 0, 2, 2.867, 0, 2, 3.267, 0, 2, 8.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 4.033, 0, 0, 4.8, 1, 2, 7.833, 1, 0, 8.1, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyX", "Segments": [0, 0, 2, 0.633, 0, 0, 0.967, 30, 2, 1.267, 30, 0, 1.567, -5.045, 0, 1.767, 7.48, 0, 1.967, -5.045, 0, 2.133, 2.228, 0, 2.4, -2.785, 0, 2.633, 0, 2, 8.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyY", "Segments": [0, 0, 0, 0.7, -0.06, 0, 0.9, 30, 2, 1.367, 30, 0, 1.667, -30, 0, 1.867, 7.878, 0, 2.067, -5.889, 0, 2.3, 1.989, 0, 2.567, -2.865, 0, 2.767, 0, 2, 7.767, 0, 0, 8, -23, 0, 8.333, 5, 0, 8.567, -2.865, 0, 8.867, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2Panda", "Segments": [0, 0, 2, 2.1, 0, 0, 2.167, -6.213, 0, 2.2, 6.229, 0, 2.267, -6.213, 0, 2.3, 6.229, 0, 2.4, -6.229, 0, 2.433, 6.229, 0, 2.467, -6.229, 0, 2.567, 6.229, 0, 2.6, -6.229, 0, 2.7, 6.229, 0, 2.733, -6.229, 0, 2.767, 6.229, 0, 2.867, -6.229, 0, 2.9, 6.229, 0, 3, -6.213, 0, 3.033, 6.229, 0, 3.067, -6.229, 0, 3.133, 6.229, 0, 3.167, -6.229, 0, 3.267, 6.229, 0, 3.3, -6.229, 0, 3.4, 6.229, 0, 3.433, -6.229, 0, 3.467, 6.229, 0, 3.567, -6.229, 0, 3.6, 6.229, 0, 3.633, -6.229, 0, 3.7, 6.229, 0, 3.767, -6.229, 0, 3.833, 6.229, 0, 3.9, -6.229, 0, 3.933, 6.229, 0, 3.967, 0, 2, 4.767, 0, 0, 4.8, -6.213, 0, 4.9, 6.229, 0, 4.933, -6.213, 0, 5.033, 6.229, 0, 5.133, -6.229, 0, 5.233, 6.229, 0, 5.3, -6.229, 0, 5.4, 6.229, 0, 5.5, -6.229, 0, 5.6, 6.229, 0, 5.7, -6.229, 0, 5.767, 6.229, 0, 5.867, -6.229, 0, 5.967, 6.229, 0, 6.067, -6.213, 0, 6.167, 6.229, 0, 6.233, -6.229, 0, 6.3, 6.229, 0, 6.4, -6.229, 0, 6.467, 6.229, 1, 6.489, 6.229, 6.511, 4.954, 6.533, 0, 1, 6.544, -2.477, 6.556, -6.229, 6.567, -6.229, 0, 6.667, 6.229, 0, 6.767, -6.229, 0, 6.867, 6.229, 0, 6.933, -6.229, 0, 7.033, 6.229, 0, 7.1, -6.229, 0, 7.167, 6.229, 0, 7.267, -6.229, 0, 7.367, 6.229, 0, 7.467, -6.229, 0, 7.567, 6.229, 0, 7.633, -6.229, 0, 7.733, 6.229, 0, 7.767, 0, 2, 8.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyZ", "Segments": [0, 0, 0, 0.167, -6.314, 1, 0.256, -6.314, 0.344, -6.185, 0.433, 0, 1, 0.5, 4.639, 0.566, 30, 0.633, 30, 1, 0.7, 30, 0.766, -1.045, 0.833, -12, 1, 0.933, -28.433, 1.033, -30, 1.133, -30, 0, 1.367, -13, 1, 1.478, -13, 1.589, -25.978, 1.7, -28.747, 1, 1.767, -30, 1.833, -30, 1.9, -30, 0, 2.233, 26.569, 2, 2.333, 26.569, 0, 2.433, 29.431, 0, 2.533, 26.569, 0, 2.7, 29.431, 0, 2.833, 26.569, 1, 2.855, 26.569, 2.878, 27.27, 2.9, 28, 1, 2.933, 29.095, 2.967, 29.431, 3, 29.431, 0, 3.067, 26.569, 0, 3.167, 29.431, 0, 3.3, 26.569, 0, 3.433, 29.431, 0, 3.567, 26.569, 0, 3.7, 29.431, 0, 3.767, 26.569, 0, 3.9, 29.431, 1, 3.922, 29.431, 3.945, 29.478, 3.967, 28, 1, 4.234, 10.263, 4.5, 0, 4.767, 0, 0, 4.9, 1.431, 0, 5.067, -1.431, 0, 5.233, 1.431, 0, 5.367, -1.431, 0, 5.6, 1.431, 0, 5.833, -1.431, 1, 5.878, -1.431, 5.922, -1.066, 5.967, 0, 1, 6, 0.8, 6.034, 1.431, 6.067, 1.431, 0, 6.2, -1.431, 0, 6.4, 1.431, 0, 6.533, -1.431, 0, 6.767, 1.431, 0, 6.933, -1.431, 0, 7.133, 1.431, 0, 7.333, -1.431, 0, 7.5, 1.431, 1, 7.544, 1.431, 7.589, 2.337, 7.633, -1.431, 1, 7.722, -8.967, 7.811, -30, 7.9, -30, 0, 8.167, 11.918, 0, 8.4, -5, 0, 8.633, 3.581, 0, 8.833, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegFZ", "Segments": [0, 0, 2, 0.633, 0, 0, 0.767, 1, 0, 1.267, -1, 0, 1.533, 1, 0, 1.7, -1, 0, 1.733, 1, 0, 1.833, -1, 0, 1.933, 1, 0, 2.067, -1, 0, 2.167, 1, 0, 2.267, -1, 0, 2.4, 1, 0, 2.467, -1, 0, 2.6, 1, 0, 2.733, -1, 0, 2.867, 1, 0, 3, -1, 0, 3.067, 1, 0, 3.167, -1, 0, 3.3, 1, 0, 3.433, -1, 0, 3.567, 1, 0, 3.633, -1, 0, 3.767, 1, 0, 3.9, -1, 0, 3.967, 0, 2, 4.8, 0, 0, 4.933, -1, 0, 5.133, 1, 0, 5.3, -1, 0, 5.5, 1, 0, 5.7, -1, 0, 5.867, 1, 0, 6.067, -1, 0, 6.233, 1, 0, 6.4, -1, 0, 6.567, 1, 0, 6.767, -1, 0, 6.933, 1, 0, 7.1, -1, 0, 7.267, 1, 0, 7.467, -1, 0, 7.633, 1, 0, 7.767, 0, 2, 8.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegBZ", "Segments": [0, 0, 2, 0.633, 0, 0, 0.767, 1, 0, 1.267, -1, 0, 1.533, 1, 0, 1.7, -1, 0, 1.733, 1, 0, 1.833, -1, 0, 1.933, 1, 0, 2.067, -1, 0, 2.167, 1, 0, 2.267, -1, 0, 2.4, 1, 0, 2.467, -1, 0, 2.6, 1, 0, 2.733, -1, 0, 2.867, 1, 0, 3, -1, 0, 3.067, 1, 0, 3.167, -1, 0, 3.3, 1, 0, 3.433, -1, 0, 3.567, 1, 0, 3.633, -1, 0, 3.767, 1, 0, 3.9, -1, 0, 3.967, 0, 2, 4.8, 0, 0, 4.933, -1, 0, 5.133, 1, 0, 5.3, -1, 0, 5.5, 1, 0, 5.7, -1, 0, 5.867, 1, 0, 6.067, -1, 0, 6.233, 1, 0, 6.4, -1, 0, 6.567, 1, 0, 6.767, -1, 0, 6.933, 1, 0, 7.1, -1, 0, 7.267, 1, 0, 7.467, -1, 0, 7.633, 1, 0, 7.767, 0, 2, 8.367, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyX", "Segments": [0, 0, 2, 0.167, 0, 2, 3.4, 0, 2, 5.833, 0, 2, 6.5, 0, 0, 6.867, 30, 0, 7.167, -21, 0, 7.6, 2, 0, 7.867, -1.558, 0, 8.2, 1.896, 0, 8.567, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyY", "Segments": [0, 0, 2, 0.367, 0, 0, 0.633, -24, 0, 0.8, 0, 2, 6.5, 0, 2, 6.6, 0, 0, 7.1, -29.915, 0, 7.5, -3, 0, 7.667, -6, 0, 8.033, 0.875, 0, 8.333, -1.432, 0, 8.567, 0.875, 0, 8.767, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamCannonZ", "Segments": [0, 0, 2, 0.167, 0, 2, 3.4, 0, 2, 5.833, 0, 2, 6.5, 0, 2, 6.667, 0, 0, 6.8, 7, 0, 7.133, -12, 0, 7.533, 1, 0, 7.667, 0, 2, 8, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamCannonGaY", "Segments": [0, 0, 2, 6.5, 0, 2, 6.667, 0, 0, 6.967, 10, 0, 7.367, -16, 0, 7.7, 1, 0, 7.967, -4.138, 0, 8.167, 1, 0, 8.5, -1.06, 0, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupY", "Segments": [0, 0, 2, 5.833, 0, 2, 6.5, 0, 2, 6.8, 0, 0, 7.3, -1, 0, 7.8, 1, 0, 8.067, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupZ", "Segments": [0, 0, 2, 5.833, 0, 2, 6.5, 0, 2, 6.9, 0, 0, 7.433, -1, 0, 7.867, 0.601, 0, 8.2, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamCannonHandZ", "Segments": [0, 0, 2, 5.833, 0, 2, 6.5, 0, 2, 6.667, 0, 0, 7.167, -10, 0, 7.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY1", "Segments": [0, 0, 2, 5.833, 0, 2, 6.5, 0, 2, 6.967, 0, 1, 7.034, 0, 7.1, 2.029, 7.167, 4.128, 1, 7.411, 11.824, 7.656, 15, 7.9, 15, 2, 7.967, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY2", "Segments": [0, 0, 2, 5.833, 0, 2, 6.5, 0, 2, 7.033, 0, 1, 7.111, 0, 7.189, 2.762, 7.267, 4.128, 1, 7.445, 7.249, 7.622, 9.16, 7.8, 11.904, 1, 7.922, 13.79, 8.045, 15, 8.167, 15, 2, 8.2, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY3", "Segments": [0, 0, 2, 5.833, 0, 2, 6.5, 0, 2, 7.1, 0, 1, 7.189, 0, 7.278, 2.377, 7.367, 4.128, 1, 7.534, 7.411, 7.7, 10.456, 7.867, 12.454, 1, 8.045, 14.586, 8.222, 15, 8.4, 15, 2, 8.433, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamParamStrongCatZ", "Segments": [0, 0, 2, 0.067, 0, 0, 0.467, 1.919, 1, 1.022, 1.919, 1.578, -3.507, 2.133, -14.656, 1, 2.289, -17.778, 2.444, -19.495, 2.6, -19.495, 1, 2.867, -19.495, 3.133, -19.429, 3.4, -16.399, 1, 4.211, -7.183, 5.022, 0, 5.833, 0, 2, 6.9, 0, 0, 7.333, -17, 0, 7.7, 8, 0, 8, -6, 1, 8.1, -6, 8.2, -1.608, 8.3, 0, 1, 8.378, 1.251, 8.455, 1.109, 8.533, 1.109, 0, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamParamSCBodyZ", "Segments": [0, 0, 2, 0.167, 0, 0, 0.6, -17, 0, 1.333, 9.636, 0, 2.7, -5.163, 1, 2.933, -5.163, 3.167, -3.67, 3.4, 0.563, 1, 3.644, 4.997, 3.889, 8, 4.133, 8, 0, 6.9, 0, 2, 7.033, 0, 0, 7.4, -15, 0, 7.767, 5, 0, 8.067, -2, 0, 8.367, 0, 2, 8.767, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamSCDishY", "Segments": [0, 0, 2, 0.167, 0, 0, 2.233, 8, 0, 2.633, -5.976, 0, 2.967, 3.586, 0, 3.233, 0, 2, 3.4, 0, 2, 5.833, 0, 2, 6.967, 0, 0, 7.467, -30, 0, 7.9, 10, 0, 8.233, -6, 0, 8.567, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamSCDishZ", "Segments": [0, 0, 2, 0.167, 0, 0, 2.267, 4.542, 0, 2.667, -5, 0, 3.033, 2.14, 0, 3.3, 0, 2, 3.4, 0, 2, 5.833, 0, 2, 6.967, 0, 2, 7.133, 0, 0, 7.567, -15, 0, 7.967, 5, 0, 8.3, -2, 0, 8.633, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamSCCupRO", "Segments": [0, 0, 2, 0.167, 0, 2, 3.4, 0, 2, 5.833, 0, 2, 7, 0, 0, 7.667, 1, 0, 7.7, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamSCCupY", "Segments": [0, 0, 2, 0.167, 0, 2, 3.4, 0, 2, 5.833, 0, 2, 7, 0, 0, 7.333, 30, 0, 7.667, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "ParamSCCupZ", "Segments": [0, 0, 2, 0.167, 0, 0, 2.1, -10.916, 0, 2.4, 2.789, 0, 2.633, -2.63, 0, 2.833, 0, 2, 3.4, 0, 2, 5.833, 0, 2, 7, 0, 2, 7.467, 0, 0, 7.933, 30, 0, 8.267, -16, 0, 8.567, 9, 0, 8.7, 0, 2, 8.967, 0]}, {"Target": "Parameter", "Id": "MB_yanwubaozha", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "MB_DRFWXZKTMD", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "Parameter", "Id": "ParamAllSizeFix", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamBGHide", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamBG2Hide", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamBGX", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamBGY", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN2", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN3", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamBlackY", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamBlackCollar", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamBlackOrder", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamWhiteIN", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamCHHide", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "Parameter", "Id": "ParamDeskHide", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "Parameter", "Id": "ParamStoolHide", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "Parameter", "Id": "ParamCupDesk", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamCHX", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamCHY", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamCHZ", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamChaSize", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionX", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionY", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionX", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionY", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamAllZ", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamAllSize", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamALLSize2", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamDeskShow", "Segments": [0, 10, 0, 8.97, 10]}, {"Target": "Parameter", "Id": "ParamLightPositionX", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamFixT", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "Parameter", "Id": "ParamFlap", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamScare", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamPupilExp", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamEyeSmileL", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamEyeSmileR", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpen2", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamMouthType", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamTeethLight", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamHeart2", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLX", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRX", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamEyeRLightOpen", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLightLine1", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLightLine2", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLightLine3", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLightShine", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo1", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo2", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo3", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1Y", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle2", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamArmHandLAngle", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamHandT2L", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamFanOpenR", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamChili", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamChiliX", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamHandR", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRY", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamHand_Cl", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamHandT1R", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamHandRCup", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamHandRMail", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "Parameter", "Id": "ParamHandLIQY1", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY2", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY3", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamHandCupZ", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamHandCupY", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLegR1X", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Y", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamFootRX", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Z", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Z", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Z", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Y", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Y", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Y", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamFootLX", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLegLF", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRHide", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "Parameter", "Id": "ParamMJRFlap", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuR", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuR", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuR", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuR", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRInput", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamMRCupSet", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "Parameter", "Id": "ParamMalpositionManjuuR", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamManjuuREyeOpen", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyX", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyY", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyZ", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRPositionZ", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRArmB", "Segments": [0, 30, 0, 8.97, 30]}, {"Target": "Parameter", "Id": "ParamManjuuRMouth", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRSigh", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow1", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow2", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow3", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow4", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowB", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW2", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamMRCupFZ", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX1", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX2", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLiqH", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX1", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX2", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX3", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLHide", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "Parameter", "Id": "ParamMJLSigh", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUHide", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuU", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuU", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUEyesForm", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyX", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineU", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineD", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamPandaHide", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupIce", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupZ", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupInput", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "Parameter", "Id": "ParamSCDishRO", "Segments": [0, 0, 0, 8.97, 0]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 8.97, 1]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 0, 8.97, 1]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.4, "Value": ""}, {"Time": 8.47, "Value": ""}]}