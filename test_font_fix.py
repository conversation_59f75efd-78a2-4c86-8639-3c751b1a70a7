#!/usr/bin/env python3
"""
测试字体警告修复
验证Qt字体警告是否被正确抑制
"""

import os
import sys

def test_font_warnings():
    """测试字体警告抑制"""
    print("🔍 测试字体警告修复")
    print("=" * 60)
    
    try:
        # 设置环境变量抑制Qt字体警告
        os.environ["QT_LOGGING_RULES"] = "qt.qpa.fonts.warning=false"
        
        # 导入Qt模块
        from PySide6.QtWidgets import QApplication
        from PySide6.QtGui import QFont
        from PySide6.QtCore import Qt
        import logging
        
        print("✅ Qt模块导入成功")
        
        # 抑制Python logging中的Qt字体警告
        logging.getLogger("qt.qpa.fonts").setLevel(logging.ERROR)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        print("✅ QApplication创建成功")
        
        # 设置默认字体
        default_font = QFont("Microsoft YaHei", 9)
        if not default_font.exactMatch():
            default_font = QFont()
            default_font.setPointSize(9)
        
        app.setFont(default_font)
        print(f"✅ 默认字体设置成功: {default_font.family()}")
        
        # 设置应用程序属性
        app.setAttribute(Qt.ApplicationAttribute.AA_DisableWindowContextHelpButton, True)
        app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
        print("✅ 应用程序属性设置成功")
        
        # 测试创建一些字体对象，看是否还有警告
        test_fonts = [
            "Fixedsys",
            "Modern", 
            "MS Sans Serif",
            "MS Serif",
            "Microsoft YaHei",
            "Arial"
        ]
        
        print("\n🧪 测试字体创建:")
        for font_name in test_fonts:
            try:
                test_font = QFont(font_name, 9)
                test_font.setBold(True)
                # 强制字体解析
                test_font.exactMatch()
                print(f"   ✅ {font_name}: 无警告")
            except Exception as e:
                print(f"   ❌ {font_name}: {e}")
        
        print("\n🎉 字体警告修复测试完成")
        print("💡 如果没有看到字体相关的警告信息，说明修复成功")
        
        # 清理
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_environment_settings():
    """显示环境设置"""
    print("\n🔧 环境设置:")
    print("=" * 60)
    
    qt_logging_rules = os.environ.get("QT_LOGGING_RULES", "未设置")
    print(f"QT_LOGGING_RULES: {qt_logging_rules}")
    
    if "qt.qpa.fonts.warning=false" in qt_logging_rules:
        print("✅ Qt字体警告已被抑制")
    else:
        print("⚠️ Qt字体警告抑制可能未生效")

if __name__ == "__main__":
    # 显示环境设置
    show_environment_settings()
    
    # 测试字体警告修复
    success = test_font_warnings()
    
    if success:
        print("\n🎉 字体警告修复验证成功！")
        print("💡 Live2D应用启动时应该不再显示字体相关警告")
    else:
        print("\n❌ 字体警告修复验证失败")
        print("💡 可能需要进一步调试")
