{"Version": 3, "Meta": {"Duration": 9.4, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 244, "TotalSegmentCount": 16440, "TotalPointCount": 20100, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamPupilExp", "Segments": [0, 0, 2, 0.4, 0, 2, 4.7, 0, 0, 4.867, 1, 2, 7.567, 1, 0, 7.667, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.4, 1, 2, 0.433, 1, 0, 0.5, 0, 0, 0.667, 0.9, 0, 0.767, 0.7, 2, 1.833, 0.7, 0, 2.033, 1, 2, 2.7, 1, 0, 2.833, 0, 0, 2.967, 1, 2, 3.133, 1, 2, 4.567, 1, 0, 4.7, 0, 0, 4.833, 1, 0, 5, 0.7, 2, 7.433, 0.7, 0, 7.533, 1, 0, 7.633, 0, 0, 7.733, 1, 2, 9.4, 1]}, {"Target": "Parameter", "Id": "ParamEyeSmileL", "Segments": [0, 0, 2, 0.4, 0, 0, 0.633, 1, 2, 2.533, 1, 0, 2.7, 0, 2, 2.8, 0, 0, 2.833, 1, 2, 7.433, 1, 0, 7.633, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.4, 1, 2, 0.433, 1, 0, 0.5, 0, 0, 0.667, 0.9, 0, 0.767, 0.7, 2, 1.833, 0.7, 0, 2.033, 1, 2, 2.7, 1, 0, 2.833, 0, 0, 2.967, 1, 2, 3.133, 1, 2, 4.567, 1, 0, 4.7, 0, 0, 4.833, 1, 0, 5, 0.7, 2, 7.433, 0.7, 0, 7.533, 1, 0, 7.633, 0, 0, 7.733, 1, 2, 9.4, 1]}, {"Target": "Parameter", "Id": "ParamEyeSmileR", "Segments": [0, 0, 2, 0.4, 0, 0, 0.633, 1, 2, 2.533, 1, 0, 2.7, 0, 2, 2.8, 0, 0, 2.833, 1, 2, 7.433, 1, 0, 7.633, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 2, 0.167, 0, 0, 0.4, 1, 1, 0.489, 1, 0.578, 0.873, 0.667, 0.5, 1, 0.734, 0.22, 0.8, -0.363, 0.867, -0.5, 1, 0.922, -0.614, 0.978, -0.6, 1.033, -0.6, 2, 1.267, -0.6, 0, 1.5, 0, 0, 1.733, -0.5, 2, 1.967, -0.5, 2, 2.067, -0.5, 0, 2.433, -0.9, 0, 2.6, -0.221, 1, 2.656, -0.221, 2.711, -0.57, 2.767, -0.7, 1, 2.878, -0.959, 2.989, -1, 3.1, -1, 0, 3.2, -0.8, 2, 3.233, -0.8, 0, 3.333, -0.5, 1, 3.355, -0.5, 3.378, -0.686, 3.4, -0.8, 1, 3.433, -0.971, 3.467, -1, 3.5, -1, 0, 3.867, -0.5, 2, 3.9, -0.5, 2, 4.067, -0.5, 2, 4.333, -0.5, 2, 4.5, -0.5, 0, 4.667, 1, 0, 5.067, 0.9, 0, 5.233, 0.983, 1, 5.289, 0.983, 5.344, 0.954, 5.4, 0.799, 1, 5.478, 0.583, 5.555, 0.312, 5.633, 0, 1, 5.7, -0.267, 5.766, -0.5, 5.833, -0.5, 0, 6.033, 0, 2, 6.133, 0, 0, 6.3, 0.5, 1, 6.367, 0.5, 6.433, 0.387, 6.5, 0, 1, 6.556, -0.322, 6.611, -0.6, 6.667, -0.6, 1, 6.756, -0.6, 6.844, -0.595, 6.933, -0.5, 1, 6.978, -0.452, 7.022, 0, 7.067, 0, 0, 7.3, -1, 2, 7.4, -1, 0, 7.8, 0, 2, 7.933, 0, 2, 8.067, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.1, 0, 2, 0.2, 0, 1, 0.3, 0, 0.4, 0.104, 0.5, 0.5, 1, 0.556, 0.72, 0.611, 1, 0.667, 1, 0, 0.867, 0.5, 0, 0.967, 0.7, 0, 1.033, 0.3, 0, 1.2, 0.5, 0, 1.267, 0.2, 0, 1.433, 0.55, 0, 1.5, 0, 1, 1.578, 0, 1.655, 0.212, 1.733, 0.5, 1, 1.778, 0.665, 1.822, 0.7, 1.867, 0.7, 0, 1.967, 0.5, 0, 2.067, 0.9, 0, 2.167, 0.775, 0, 2.267, 0.881, 0, 2.333, 0.775, 0, 2.433, 1, 0, 2.567, 0.5, 0, 2.833, 0.8, 2, 2.9, 0.8, 1, 2.956, 0.8, 3.011, 0.82, 3.067, 0.9, 1, 3.089, 0.932, 3.111, 1, 3.133, 1, 0, 3.2, 0.55, 1, 3.244, 0.55, 3.289, 0.541, 3.333, 0.6, 1, 3.378, 0.659, 3.422, 1, 3.467, 1, 1, 3.522, 1, 3.578, 0.906, 3.633, 0.841, 1, 3.689, 0.776, 3.744, 0.801, 3.8, 0.7, 1, 3.944, 0.438, 4.089, 0, 4.233, 0, 0, 4.5, 0.7, 0, 4.667, 0.3, 0, 4.767, 0.5, 0, 4.9, 0.3, 2, 5.067, 0.3, 2, 5.5, 0.3, 0, 5.633, 0.2, 0, 5.833, 1, 0, 5.9, 0.669, 0, 6, 0.853, 0, 6.1, 0.3, 1, 6.133, 0.3, 6.167, 0.401, 6.2, 0.5, 1, 6.233, 0.599, 6.267, 0.618, 6.3, 0.618, 0, 6.5, 0.5, 0, 6.667, 1, 0, 6.767, 0.5, 0, 6.967, 0.841, 0, 7.133, 0.449, 0, 7.3, 1, 2, 7.5, 1, 0, 8, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamMouthSize", "Segments": [0, 0, 2, 0.167, 0, 0, 0.267, -0.4, 0, 0.733, 0, 2, 1.5, 0, 0, 1.733, -0.8, 2, 1.967, -0.8, 2, 2.067, -0.8, 0, 2.267, -0.518, 0, 2.433, -0.8, 0, 2.6, -0.056, 0, 2.8, -0.401, 2, 4.833, -0.401, 0, 5.233, 0, 2, 6.033, 0, 2, 6.533, 0, 0, 6.667, -0.5, 1, 6.8, -0.5, 6.934, -0.301, 7.067, 0, 1, 7.167, 0.226, 7.267, 0.3, 7.367, 0.3, 2, 7.5, 0.3, 0, 8, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamMouthY", "Segments": [0, 0, 2, 0.4, 0, 2, 2.633, 0, 0, 2.9, 0.3, 0, 3.2, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeEmotion", "Segments": [0, 0, 2, 0.4, 0, 0, 1.1, 1, 0, 1.733, 0, 0, 2.1, 1, 2, 2.9, 1, 0, 3, -1, 2, 7.533, -1, 0, 7.633, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamMark", "Segments": [0, 0, 2, 4.4, 0, 0, 5.033, 1.2, 2, 6.3, 1.2, 0, 6.5, 1.5, 2, 6.533, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamMarkShake", "Segments": [0, 0, 2, 5.167, 0, 0, 5.233, -0.8, 0, 5.533, 0.9, 0, 5.633, -0.638, 0, 5.867, 0.638, 0, 6, -0.2, 0, 6.133, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamPHYInputX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 11, 0, 1.1, -10.203, 0, 1.6, 10, 0, 1.9, -5, 0, 2.2, 2.745, 0, 2.467, -3, 1, 2.522, -3, 2.578, -3.14, 2.633, 0, 1, 2.744, 6.281, 2.856, 14, 2.967, 14, 0, 3.633, -15.431, 0, 4.133, 0, 2, 4.633, 0, 0, 5, -8.546, 0, 5.433, 18.335, 0, 5.767, -10.203, 0, 6.267, 11, 0, 7.2, -7.786, 0, 7.667, 7.161, 0, 8.067, -12.844, 0, 8.3, 12, 0, 8.633, -1.86, 0, 8.833, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 0, 0.4, 0.075, 0, 0.933, 0, 0, 1.3, 7, 0, 1.9, 0, 2, 2.867, 0, 0, 3.1, 0.847, 0, 3.6, -5, 0, 4.4, 1.243, 0, 5.233, 0, 2, 6.967, 0, 1, 7.045, 0, 7.122, -0.812, 7.2, -0.873, 1, 7.356, -0.995, 7.511, -1, 7.667, -1, 0, 7.9, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.4, 0, 0, 0.567, 3.141, 0, 0.7, -8.803, 0, 1.133, 11.07, 0, 1.3, 8.09, 0, 1.367, 11.484, 0, 1.467, 7.936, 0, 1.6, 11.864, 0, 1.7, 8.433, 0, 1.8, 12.067, 1, 1.833, 12.067, 1.867, 11.289, 1.9, 10.248, 1, 1.933, 9.207, 1.967, 8.874, 2, 8.874, 0, 2.133, 10.248, 0, 2.2, 8.874, 0, 2.633, 10.248, 0, 2.833, 5, 1, 2.9, 5, 2.966, 5.451, 3.033, 6.513, 1, 3.189, 8.992, 3.344, 10.319, 3.5, 10.319, 0, 3.867, 7.263, 0, 4.233, 9, 0, 4.4, 7.263, 1, 4.489, 7.263, 4.578, 7.184, 4.667, 7.396, 1, 4.8, 7.714, 4.934, 18, 5.067, 18, 0, 5.4, -13, 0, 5.767, 10.898, 0, 6.067, -5, 0, 6.233, 7.079, 0, 6.433, -3, 0, 6.6, 3.193, 0, 6.733, 0.47, 0, 7.033, 16.956, 0, 7.367, -11.347, 1, 7.489, -11.347, 7.611, -10.361, 7.733, -6, 1, 7.866, -1.242, 8, 3.141, 8.133, 3.141, 0, 8.5, -0.665, 0, 8.9, 0.47, 0, 9.267, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.367, 0, 0, 0.8, 4.691, 0, 1.167, -4.606, 0, 1.567, 5.904, 0, 1.933, -1.868, 0, 2.2, 0, 0, 2.667, -0.497, 0, 3, 7.556, 0, 3.433, -10, 0, 3.833, -5.822, 0, 4.233, -9.325, 0, 4.467, -8.244, 0, 4.7, -8.679, 0, 5.633, 22, 0, 6.233, -4, 0, 6.567, 1, 0, 6.733, 0, 0, 7.067, 8.367, 0, 7.533, -4, 0, 7.967, 3.904, 0, 8.367, -1.154, 0, 8.767, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "Segments": [0, 0, 0, 0.4, 0.075, 2, 2.5, 0.075, 0, 2.7, -4.285, 0, 2.9, 0, 2, 7.2, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamAngleH", "Segments": [0, 0, 0, 0.4, 0.075, 2, 2.6, 0.075, 0, 2.9, 2, 0, 3.167, -3, 2, 3.433, -3, 2, 4.267, -3, 0, 5.033, 0, 2, 7.2, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamAngleS", "Segments": [0, 0, 0, 0.4, 0.075, 0, 2.9, 0, 0, 3.333, 1, 2, 4.5, 1, 0, 4.933, 0, 2, 7.2, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamArm<PERSON>pperRAngle", "Segments": [0, 0, 2, 0.4, 0, 0, 0.533, 1, 0, 1.067, -4.276, 0, 1.2, -1.447, 0, 1.3, -2.116, 0, 1.367, -1, 0, 1.5, -1.814, 0, 1.633, -1, 0, 1.733, -1.814, 0, 1.967, -1.447, 0, 2.133, -1.814, 0, 2.6, -1.81, 0, 2.9, -1.96, 1, 3.022, -1.96, 3.145, -1.062, 3.267, -1.053, 1, 3.6, -1.029, 3.934, -1.023, 4.267, -1, 1, 4.367, -0.993, 4.467, 1, 4.567, 1, 0, 5.033, 0, 2, 5.7, 0, 0, 5.967, 0.21, 0, 6.133, 0, 0, 6.333, 0.36, 0, 7.2, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRAngle", "Segments": [0, 7.4, 2, 0.4, 7.4, 0, 0.833, 9.26, 0, 1.1, -1, 0, 1.267, 3.949, 0, 1.333, 0.789, 0, 1.433, 3.626, 0, 1.567, 0.789, 0, 1.667, 3.121, 1, 1.7, 3.121, 1.734, 2.928, 1.767, 1.222, 1, 1.8, -0.484, 1.834, -3, 1.867, -3, 0, 2.067, -2.76, 0, 2.267, -2.88, 0, 2.633, -2.876, 0, 2.9, -4, 0, 3.1, -2.522, 1, 3.167, -2.522, 3.233, -2.741, 3.3, -2.76, 1, 3.633, -2.854, 3.967, -2.88, 4.3, -2.88, 0, 4.633, -2.64, 0, 5.167, -3, 2, 5.7, -3, 0, 6, -2.852, 0, 6.167, -3, 0, 6.367, -2.819, 0, 6.533, -3, 2, 6.7, -3, 2, 7.067, -3, 1, 7.111, -3, 7.156, -2.919, 7.2, -2.88, 1, 7.344, -2.753, 7.489, -2.78, 7.633, -2.551, 1, 7.8, -2.287, 7.966, 7.52, 8.133, 7.52, 0, 8.4, 7.22, 0, 8.6, 7.4, 2, 9.4, 7.4]}, {"Target": "Parameter", "Id": "ParamArmHandRAngle", "Segments": [0, 0, 2, 0.4, 0, 0, 0.633, -1, 0, 0.9, 1, 0, 1.1, -10, 0, 1.267, -6, 0, 1.367, -10, 0, 1.5, -7, 0, 1.667, -10, 0, 1.8, -7.65, 0, 1.9, -8.059, 0, 2.6, -8.06, 0, 2.9, -10, 0, 3.133, -5.65, 0, 3.233, -6.059, 1, 3.344, -6.06, 3.456, -6.06, 3.567, -6.06, 2, 4.367, -6.06, 0, 4.667, -7.187, 0, 4.9, -7, 0, 5.533, -10, 2, 5.733, -10, 0, 6.033, -8, 0, 6.2, -10, 0, 6.4, -9, 0, 6.567, -9.761, 0, 6.733, -9.284, 2, 6.9, -9.284, 1, 6.978, -9.284, 7.055, -8.428, 7.133, -8.059, 1, 7.3, -7.269, 7.466, -7.294, 7.633, -6.168, 1, 7.822, -4.892, 8.011, 0.767, 8.2, 0.767, 0, 8.467, -0.486, 0, 8.633, 0.407, 0, 8.933, -0.266, 0, 9.2, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRH", "Segments": [0, 0, 2, 0.4, 0, 2, 0.833, 0, 0, 1.1, -11, 2, 7.2, -11, 1, 7.344, -11, 7.489, -11.05, 7.633, -10.876, 1, 7.8, -10.675, 7.966, 0, 8.133, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRAngle", "Segments": [0, 0, 2, 0.4, 0, 2, 1.833, 0, 0, 2.333, -1.069, 1, 2.422, -1.069, 2.511, -1.195, 2.6, 0, 1, 2.7, 1.345, 2.8, 6, 2.9, 6, 0, 3.133, 0, 2, 4.7, 0, 1, 4.867, 0, 5.033, 5.098, 5.2, 7, 1, 5.378, 9.029, 5.555, 9, 5.733, 9, 0, 6.033, 6.523, 0, 6.2, 7.477, 0, 6.4, 6.523, 0, 6.567, 7, 2, 6.733, 7, 1, 6.789, 7, 6.844, 7.109, 6.9, 6.841, 1, 7, 6.359, 7.1, 0, 7.2, 0, 2, 7.633, 0, 2, 8.133, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamHandT2R", "Segments": [0, 0, 2, 0.4, 0, 0, 0.833, 0.4, 0, 1.1, 0, 2, 1.833, 0, 0, 2.067, 0.1, 0, 2.467, 0, 2, 2.7, 0, 0, 2.9, 0.5, 0, 3.1, 0, 2, 4.833, 0, 0, 5.233, 0.4, 2, 5.733, 0.4, 0, 6.033, 0, 0, 6.2, 0.181, 0, 6.4, 0, 0, 6.733, 0.238, 1, 7.033, 0.238, 7.333, 0.226, 7.633, 0.199, 1, 7.655, 0.197, 7.678, 0.196, 7.7, 0.181, 1, 7.844, 0.087, 7.989, 0, 8.133, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 0.4, 0.075, 0, 7.2, 0, 2, 7.633, 0, 2, 8.133, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 0.4, 0.075, 2, 0.467, 0.075, 0, 0.767, 1.529, 1, 0.822, 1.529, 0.878, -2.974, 0.933, -4.285, 1, 1.122, -8.741, 1.311, -10, 1.5, -10, 2, 1.7, -10, 2, 2, -10, 2, 2.333, -10, 0, 6.967, -7, 0, 7.2, -7.74, 1, 7.344, -7.74, 7.489, -7.779, 7.633, -7.664, 1, 7.822, -7.514, 8.011, 0.696, 8.2, 0.696, 0, 8.533, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 0, 0.4, 0.075, 0, 0.667, -2.831, 0, 1.067, 7.342, 0, 1.267, 1.195, 0, 1.333, 4.563, 0, 1.433, 0.85, 0, 1.567, 4.501, 0, 1.667, 1.007, 0, 1.767, 2.795, 0, 2.067, 1.529, 2, 2.4, 1.529, 0, 2.6, 0.075, 0, 2.8, 2.441, 0, 3.2, -10, 0, 3.767, -1.428, 0, 4.167, -3.428, 2, 4.4, -3.428, 0, 4.967, 3.234, 0, 5.267, -10, 0, 5.733, 3.914, 0, 6.033, -10, 0, 6.2, -2.445, 0, 6.4, -9.281, 0, 6.567, -1.477, 0, 6.733, -9.094, 1, 6.789, -9.094, 6.844, -5.995, 6.9, -2.445, 1, 6.933, -0.315, 6.967, 0, 7, 0, 0, 7.233, -10, 0, 7.633, -0.844, 0, 7.833, -10, 0, 8.2, 1.441, 0, 8.633, -0.62, 0, 8.833, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 0.333, -0.749, 0, 0.567, 1.382, 0, 0.967, -0.749, 0, 1.367, 0.625, 0, 1.7, -0.002, 1, 1.956, -0.002, 2.211, -0.002, 2.467, 0, 1, 2.578, 0.001, 2.689, 1.254, 2.8, 1.254, 1, 2.933, 1.254, 3.067, -7.819, 3.2, -8.154, 1, 3.389, -8.628, 3.578, -8.585, 3.767, -8.585, 1, 3.967, -8.585, 4.167, -8.811, 4.367, -8.154, 1, 4.656, -7.205, 4.944, 10, 5.233, 10, 0, 5.767, -6.639, 1, 6.078, -6.639, 6.389, -5.875, 6.7, -4.052, 1, 6.911, -2.815, 7.122, -1.12, 7.333, 0, 1, 7.478, 0.766, 7.622, 0.92, 7.767, 0.92, 0, 8.233, 0, 2, 8.6, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX2", "Segments": [0, 0, 0, 0.4, 0.075, 0, 0.633, -0.558, 0, 0.867, 0.075, 0, 1.267, -9.168, 0, 1.333, -6.874, 0, 1.433, -9.168, 0, 1.567, -7.824, 0, 1.667, -9.168, 1, 1.7, -9.168, 1.734, -7.838, 1.767, -7.688, 1, 2.045, -6.442, 2.322, -5.741, 2.6, -4.285, 1, 2.7, -3.761, 2.8, 0, 2.9, 0, 2, 7.2, 0, 2, 7.633, 0, 2, 8.133, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY2", "Segments": [0, 0, 0, 0.4, 0.075, 2, 2.367, 0.075, 0, 2.567, -1.378, 0, 2.867, 1.935, 0, 3.4, -8.637, 0, 3.9, -0.034, 0, 4.2, -2.034, 2, 4.3, -2.034, 0, 4.8, 3.234, 0, 5.167, -10, 0, 5.6, 1.935, 0, 6, -10, 0, 6.133, -4.261, 0, 6.367, -7.666, 0, 6.533, -3.449, 0, 6.7, -6.476, 1, 6.756, -6.476, 6.811, -5.854, 6.867, -3.415, 1, 6.9, -1.952, 6.934, 0, 6.967, 0, 2, 7.2, 0, 2, 7.633, 0, 2, 8.133, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechX", "Segments": [0, 0, 0, 0.4, 0.075, 2, 0.667, 0.075, 0, 1.133, -4.285, 0, 2.567, 0.075, 0, 3.033, 0, 0, 3.6, 4, 2, 4.867, 4, 0, 5.467, 8.885, 0, 6.133, -6.834, 1, 6.289, -6.834, 6.444, -4.12, 6.6, -2.445, 1, 6.8, -0.291, 7, 0, 7.2, 0, 0, 7.633, -0.34, 0, 8, 3, 0, 8.467, -0.531, 0, 8.867, 0.256, 0, 9.3, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechW", "Segments": [0, 0, 0, 0.4, 0.075, 2, 1.067, 0.075, 0, 1.267, 1.644, 0, 1.333, 0.075, 0, 1.433, 1.579, 0, 1.567, 0.075, 0, 1.667, 1.403, 1, 1.7, 1.403, 1.734, 0.076, 1.767, 0.075, 1, 3.578, 0.024, 5.389, 0, 7.2, 0, 2, 7.633, 0, 2, 8.133, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, 0, 2, 0.467, 0, 0, 0.733, 2, 0, 1.1, -1, 0, 1.467, 1, 0, 1.933, -0.041, 1, 2.155, -0.041, 2.378, -0.041, 2.6, 0, 1, 2.711, 0.02, 2.822, 0.901, 2.933, 0.901, 0, 3.3, -1, 0, 3.8, 0.208, 0, 4.1, 0, 2, 5.4, 0, 0, 5.967, -2.649, 0, 6.467, 0, 2, 7.333, 0, 2, 8.133, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamShoulderR", "Segments": [0, 0, 0, 0.4, 0.075, 2, 2.6, 0.075, 0, 2.9, 9, 0, 3.167, -14, 0, 3.433, 0, 0, 5.4, -0.639, 0, 5.733, 19.503, 0, 6, -4.639, 0, 6.167, 15.114, 0, 6.367, -4.639, 0, 6.567, 8.529, 0, 6.7, -4.639, 0, 7.2, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamShoulderL", "Segments": [0, 0, 0, 0.4, 0.075, 0, 5.4, -0.639, 0, 5.733, 15.647, 0, 6, -4.639, 0, 6.167, 6.868, 0, 6.367, -4.639, 0, 6.567, 4.673, 0, 6.7, -4.639, 0, 7.2, 0, 2, 7.633, 0, 2, 8.133, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Z", "Segments": [0, 0, 2, 0.4, 0, 0, 0.867, -1.596, 0, 1.6, 0.595, 0, 2.433, -0.415, 0, 2.967, 0, 0, 3.633, -1.596, 0, 4, -1.339, 0, 4.433, -1.596, 2, 4.9, -1.596, 0, 5.833, 0.595, 0, 6.467, -0.415, 0, 6.967, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLegR1X", "Segments": [0, 0, 2, 0.6, 0, 0, 1, -3, 0, 1.7, 1.509, 0, 2.5, 0, 2, 2.833, 0, 0, 3.567, -3, 2, 4.733, -3, 0, 5.567, 13, 0, 6.267, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Z", "Segments": [0, 0, 2, 0.4, 0, 2, 0.5, 0, 2, 0.667, 0, 0, 1.1, -2, 0, 1.8, 0.138, 0, 2.633, -1, 0, 3.167, 0, 0, 3.7, -1.675, 0, 4.2, -1.211, 0, 4.667, -1.675, 2, 5.1, -1.675, 0, 5.967, 0.701, 0, 6.667, -0.51, 0, 7.267, 0.138, 0, 7.767, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Z", "Segments": [0, 0, 0, 0.4, -0.632, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyX", "Segments": [0, 0, 2, 0.5, 0, 0, 0.8, 7, 0, 1.167, -3, 0, 1.467, 4, 1, 1.556, 4, 1.644, 1.258, 1.733, 0, 1, 2.111, -5.346, 2.489, -7.178, 2.867, -7.178, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyY", "Segments": [0, 0, 2, 0.533, 0, 0, 0.833, 7, 0, 1.2, -3, 0, 1.5, 4, 1, 1.589, 4, 1.678, 2.72, 1.767, 0, 1, 2.022, -7.819, 2.278, -11.811, 2.533, -11.811, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyZ", "Segments": [0, 0, 2, 0.4, 0, 0, 0.8, 2, 0, 1.167, -11.745, 0, 1.733, -5.725, 0, 2.067, -7, 0, 4.033, 1.845, 1, 4.222, 1.845, 4.411, 1.729, 4.6, 0.15, 1, 4.722, -0.871, 4.845, -4.109, 4.967, -4.109, 2, 5.067, -4.109, 0, 5.633, 7.669, 2, 5.733, 7.669, 1, 5.944, 7.669, 6.156, -5.329, 6.367, -5.82, 1, 6.4, -5.897, 6.434, -5.837, 6.467, -5.837, 0, 7.133, 7.979, 1, 7.178, 7.979, 7.222, 8.102, 7.267, 7.928, 1, 7.478, 7.103, 7.689, -4.524, 7.9, -4.524, 2, 8, -4.524, 0, 8.867, 2, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRPositionZ", "Segments": [0, 0, 2, 0.4, 0, 2, 0.5, 0, 0, 0.9, 2, 0, 1.267, -9.745, 0, 1.833, -5.725, 0, 2.167, -7, 0, 3.933, 0.584, 1, 4.122, 0.584, 4.311, 0.512, 4.5, -0.539, 1, 4.622, -1.219, 4.745, -3.362, 4.867, -3.362, 2, 4.967, -3.362, 0, 5.533, 4.443, 2, 5.633, 4.443, 1, 5.844, 4.443, 6.056, -4.146, 6.267, -4.495, 1, 6.3, -4.55, 6.334, -4.507, 6.367, -4.507, 0, 7.033, 4.649, 1, 7.078, 4.649, 7.122, 4.732, 7.167, 4.615, 1, 7.378, 4.057, 7.589, -3.637, 7.8, -3.637, 2, 7.9, -3.637, 0, 8.767, 0.687, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyX", "Segments": [0, 0, 2, 0.4, 0, 2, 0.567, 0, 0, 0.8, 7, 0, 1.167, -3, 0, 1.467, 4, 1, 1.556, 4, 1.644, 1.258, 1.733, 0, 1, 2.111, -5.346, 2.489, -7.178, 2.867, -7.178, 0, 3.667, 4.747, 1, 3.845, 4.747, 4.022, 4.91, 4.2, 0, 1, 4.411, -5.83, 4.622, -19, 4.833, -19, 0, 5.267, 15.636, 0, 5.667, -15.592, 0, 6.033, 15.636, 0, 6.433, -12.685, 0, 6.833, 13.376, 0, 7.2, -11.343, 0, 7.633, 11.829, 0, 8, -5, 0, 8.3, 3.787, 0, 8.733, -2.598, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyY", "Segments": [0, 0, 2, 0.4, 0, 2, 0.433, 0, 2, 0.6, 0, 0, 0.833, 7, 0, 1.2, -3, 0, 1.5, 4, 1, 1.589, 4, 1.678, 2.72, 1.767, 0, 1, 2.022, -7.819, 2.278, -11.811, 2.533, -11.811, 0, 3.733, 10.277, 1, 3.9, 10.277, 4.066, 9.333, 4.233, 0, 1, 4.444, -11.822, 4.656, -26.049, 4.867, -26.049, 0, 5.333, 24.049, 0, 5.7, -15.592, 0, 6.067, 22.998, 0, 6.467, -6.282, 0, 6.867, 20.205, 0, 7.233, -10.341, 0, 7.667, 18.294, 0, 8, -7.766, 0, 8.333, 3.787, 0, 8.767, -2.598, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyZ", "Segments": [0, 0, 2, 0.4, 0, 2, 0.467, 0, 0, 0.633, -6, 0, 1.033, 3, 0, 1.333, -1.059, 0, 1.667, 0, 0, 2.333, -5.624, 2, 2.433, -5.624, 0, 4.1, 7.585, 1, 4.189, 7.585, 4.278, 7.23, 4.367, 0, 1, 4.522, -12.652, 4.678, -26.049, 4.833, -26.049, 2, 4.933, -26.049, 0, 5.7, 19.953, 2, 5.8, 19.953, 1, 6.011, 19.953, 6.222, -20.604, 6.433, -22.035, 1, 6.466, -22.261, 6.5, -22.088, 6.533, -22.088, 0, 7.2, 20.919, 1, 7.244, 20.919, 7.289, 21.258, 7.333, 20.758, 1, 7.544, 18.382, 7.756, -18, 7.967, -18, 2, 8.067, -18, 0, 8.933, 2, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUEyesForm", "Segments": [0, 0, 2, 0.233, 0, 2, 0.433, 0, 2, 1, 0, 0, 1.567, -1, 2, 1.9, -1, 0, 2.433, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyY", "Segments": [0, 0, 2, 0.233, 0, 2, 0.6, 0, 0, 0.967, 6.26, 0, 1.233, -1.62, 0, 1.367, 2.925, 0, 1.5, -2.598, 0, 1.6, 3.787, 0, 1.7, -3.047, 0, 1.833, 2.925, 0, 1.933, -2.703, 0, 2.067, 2, 1, 2.267, 2, 2.467, 1.275, 2.667, 0, 1, 2.845, -1.133, 3.022, -1.62, 3.2, -1.62, 0, 4.233, 2, 0, 4.867, -26.049, 0, 5.333, 24.049, 0, 5.7, -15.592, 0, 6.067, 22.998, 0, 6.467, -6.282, 0, 6.867, 20.205, 0, 7.233, -10.341, 0, 7.667, 18.294, 0, 8, -7.766, 0, 8.333, 3.787, 0, 8.767, -2.598, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyZ", "Segments": [0, 0, 2, 0.233, 0, 2, 0.533, 0, 0, 0.933, 20.858, 2, 1, 20.858, 0, 1.2, -27.181, 0, 1.333, 27.181, 0, 1.467, -27.181, 0, 1.567, 27.181, 0, 1.667, -27.181, 0, 1.8, 27.181, 0, 1.967, -10, 0, 2.1, 4, 0, 2.367, -2.382, 0, 2.667, 2.816, 0, 3.1, -1.3, 0, 3.5, 1.227, 0, 4.133, -2.382, 0, 4.5, 1.227, 0, 4.933, -26.049, 0, 5.7, 19.953, 0, 6.433, -22.035, 0, 7.2, 20.919, 0, 7.967, -18, 0, 8.933, 2, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUArmZ", "Segments": [0, 0, 2, 0.233, 0, 2, 0.467, 0, 1, 0.534, 0, 0.6, 0.236, 0.667, -2, 1, 0.856, -8.336, 1.044, -15, 1.233, -15, 0, 1.367, 15.881, 0, 1.5, -17.598, 0, 1.6, 16.598, 0, 1.7, -18.598, 0, 1.833, 16.598, 0, 2.033, -12, 0, 2.2, 11, 0, 2.533, -5.118, 0, 2.8, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineU", "Segments": [0, 0, 2, 0.233, 0, 2, 1.267, 0, 1, 1.311, 0.333, 1.356, 0.667, 1.4, 1, 2, 1.433, 0, 2, 1.533, 0, 1, 1.566, 0.333, 1.6, 0.667, 1.633, 1, 2, 1.667, 0, 2, 1.733, 0, 2, 1.833, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineD", "Segments": [0, 0, 2, 0.233, 0, 2, 1.267, 0, 2, 1.4, 0, 1, 1.444, 0.333, 1.489, 0.667, 1.533, 1, 2, 1.567, 0, 2, 1.633, 0, 1, 1.666, 0.333, 1.7, 0.667, 1.733, 1, 2, 1.767, 0, 2, 1.833, 0, 1, 1.878, 0.333, 1.922, 0.667, 1.967, 1, 2, 2, 0, 2, 2.033, 0, 2, 2.333, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyW", "Segments": [0, 0, 2, 0.233, 0, 2, 0.833, 0, 0, 1.2, -0.6, 0, 1.333, 0.4, 0, 1.467, -0.5, 0, 1.6, 0.6, 0, 1.667, -0.5, 0, 1.767, 0.2, 1, 1.811, 0.2, 1.856, 0.103, 1.9, -0.029, 1, 1.967, -0.226, 2.033, -0.3, 2.1, -0.3, 0, 2.367, 0.2, 0, 2.6, -0.2, 0, 2.867, 0.2, 0, 3.2, -0.1, 0, 3.5, 0.1, 0, 3.867, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamPositionXPanda", "Segments": [0, 0, 2, 0.4, 0, 1, 0.578, 0, 0.755, 0.15, 0.933, 1, 1, 1.7, 4.665, 2.466, 7.874, 3.233, 12.745, 1, 3.622, 15.216, 4.011, 20, 4.4, 20, 2, 4.8, 20, 1, 4.944, 20, 5.089, 18.3, 5.233, 17.079, 1, 5.678, 13.323, 6.122, 11.038, 6.567, 7.012, 1, 6.967, 3.388, 7.367, 0, 7.767, 0, 2, 8.2, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.4, 0, 2, 4.467, 0, 0, 4.8, 1, 2, 7.767, 1, 0, 8.2, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyX", "Segments": [0, 0, 2, 0.6, 0, 0, 0.7, 2.538, 0, 0.867, -2.538, 0, 1, 2.538, 0, 1.1, -2.538, 0, 1.3, 2.538, 0, 1.5, -2.538, 0, 1.7, 2.538, 0, 1.8, -2.538, 0, 2, 2.538, 0, 2.1, -2.538, 0, 2.3, 2.538, 0, 2.467, -2.538, 0, 2.6, 2.538, 0, 2.8, -2.538, 0, 2.933, 2.538, 0, 3.033, -2.538, 0, 3.3, 2.538, 0, 3.433, -2.538, 0, 3.567, 2.538, 0, 3.733, -2.538, 0, 3.9, 2.538, 0, 4.033, -2.538, 0, 4.233, 2.538, 0, 4.4, -2.538, 0, 4.533, 2.538, 0, 4.7, 0, 2, 5.067, 0, 0, 5.3, -2.538, 0, 5.533, 2.538, 0, 5.7, -2.538, 0, 5.933, 2.538, 0, 6.033, -2.538, 0, 6.2, 2.538, 0, 6.333, -2.538, 0, 6.5, 2.538, 0, 6.7, -2.538, 0, 6.833, 2.538, 0, 7, -2.538, 0, 7.133, 2.538, 0, 7.267, -2.538, 0, 7.533, 2.538, 0, 7.767, -2.538, 0, 7.967, 2.538, 0, 8.1, 0, 2, 8.2, 0, 2, 8.3, 0, 2, 8.433, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyY", "Segments": [0, 0, 2, 0.533, 0, 0, 0.633, 3.183, 0, 0.8, -3.183, 0, 0.933, 3.183, 0, 1.033, -3.183, 0, 1.233, 3.183, 0, 1.433, -3.183, 0, 1.633, 3.183, 0, 1.733, -3.183, 0, 1.933, 3.183, 0, 2.033, -3.183, 0, 2.233, 3.183, 0, 2.4, -3.183, 0, 2.533, 3.183, 0, 2.733, -3.183, 0, 2.867, 3.183, 0, 3, -3.183, 0, 3.233, 3.183, 0, 3.4, -3.183, 0, 3.5, 3.183, 0, 3.667, -3.183, 0, 3.833, 3.183, 0, 4, -3.183, 0, 4.167, 3.183, 0, 4.333, -3.183, 0, 4.433, 3.183, 0, 4.633, 0, 2, 5, 0, 0, 5.233, -3.183, 0, 5.467, 3.183, 0, 5.633, -3.183, 0, 5.867, 3.183, 0, 5.967, -3.183, 0, 6.133, 3.183, 0, 6.267, -3.183, 0, 6.433, 3.183, 0, 6.633, -3.183, 0, 6.767, 3.183, 0, 6.933, -3.183, 0, 7.1, 3.183, 0, 7.2, -3.183, 0, 7.433, 3.183, 0, 7.667, -3.183, 0, 7.867, 3.183, 0, 8, 0, 2, 8.2, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2Panda", "Segments": [0, 0, 2, 0.4, 0, 0, 0.467, -6.229, 0, 0.533, 6.229, 0, 0.6, -6.229, 0, 0.7, 6.229, 0, 0.767, -6.229, 0, 0.833, 6.229, 0, 0.933, -6.229, 0, 1, 6.229, 0, 1.1, -6.229, 0, 1.133, 6.229, 0, 1.2, -6.229, 0, 1.3, 6.229, 0, 1.4, -6.229, 0, 1.467, 6.229, 0, 1.533, -6.229, 0, 1.6, 6.229, 0, 1.7, -6.229, 0, 1.767, 6.229, 0, 1.833, -6.229, 0, 1.933, 6.229, 0, 2, -6.229, 0, 2.067, 6.229, 0, 2.133, -6.229, 0, 2.233, 6.229, 0, 2.3, -6.229, 0, 2.4, 6.229, 0, 2.433, -6.229, 0, 2.533, 6.229, 0, 2.6, -6.229, 0, 2.7, 6.229, 0, 2.767, -6.229, 0, 2.833, 6.229, 0, 2.933, -6.229, 0, 3, 6.229, 0, 3.033, -6.229, 0, 3.133, 6.229, 0, 3.233, -6.229, 0, 3.3, 6.229, 0, 3.367, -6.229, 0, 3.433, 6.229, 0, 3.533, -6.229, 0, 3.6, 6.229, 0, 3.7, -6.229, 0, 3.767, 6.229, 0, 3.833, -6.229, 0, 3.9, 6.229, 0, 3.967, -6.229, 0, 4.067, 6.229, 0, 4.133, -6.229, 0, 4.233, 6.229, 0, 4.3, -6.229, 0, 4.367, 6.229, 0, 4.433, 0, 2, 4.867, 0, 0, 4.933, -6.229, 0, 5, 6.229, 0, 5.067, -6.229, 0, 5.133, 6.229, 0, 5.233, -6.229, 0, 5.3, 6.229, 0, 5.4, -6.229, 0, 5.5, 6.229, 0, 5.6, -6.229, 0, 5.633, 6.229, 0, 5.767, -6.229, 0, 5.8, 6.229, 0, 5.867, -6.229, 0, 5.933, 6.229, 0, 6, -6.229, 0, 6.1, 6.229, 0, 6.167, -6.229, 0, 6.233, 6.229, 0, 6.3, -6.229, 0, 6.4, 6.229, 0, 6.467, -6.229, 0, 6.533, 6.229, 0, 6.6, -6.229, 0, 6.7, 6.229, 0, 6.767, -6.229, 0, 6.833, 6.229, 0, 6.933, -6.229, 0, 7, 6.229, 0, 7.1, -6.229, 0, 7.133, 6.229, 0, 7.2, -6.229, 0, 7.3, 6.229, 0, 7.4, -6.229, 0, 7.5, 6.229, 0, 7.6, -6.229, 0, 7.667, 6.229, 0, 7.733, -6.229, 0, 7.867, 0, 2, 8.2, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyZ", "Segments": [0, 0, 0, 0.6, 1.431, 0, 0.767, -1.431, 0, 0.9, 1.431, 0, 1, -1.431, 0, 1.2, 1.431, 0, 1.4, -1.431, 0, 1.6, 1.431, 0, 1.7, -1.431, 0, 1.9, 1.431, 0, 2, -1.431, 0, 2.2, 1.431, 0, 2.367, -1.431, 0, 2.5, 1.431, 0, 2.7, -1.431, 0, 2.833, 1.431, 0, 2.933, -1.431, 0, 3.2, 1.431, 0, 3.333, -1.431, 0, 3.467, 1.431, 0, 3.633, -1.431, 0, 3.8, 1.431, 0, 3.933, -1.431, 0, 4.133, 1.431, 0, 4.3, -1.431, 0, 4.4, 1.431, 1, 4.467, 1.431, 4.533, 1.53, 4.6, 0, 1, 4.656, -1.275, 4.711, -30, 4.767, -30, 0, 5.033, 1.431, 0, 5.2, -1.431, 0, 5.433, 1.431, 0, 5.6, -1.431, 0, 5.833, 1.431, 0, 5.933, -1.431, 0, 6.1, 1.431, 0, 6.233, -1.431, 0, 6.4, 1.431, 0, 6.6, -1.431, 0, 6.733, 1.431, 0, 6.9, -1.431, 0, 7.033, 1.431, 0, 7.167, -1.431, 0, 7.4, 1.431, 0, 7.633, -1.431, 0, 7.833, 1.431, 1, 7.878, 1.431, 7.922, 1.594, 7.967, 0, 1, 8.011, -1.594, 8.056, -21, 8.1, -21, 0, 8.3, 9, 0, 8.567, -9.429, 0, 8.9, 5.312, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegFZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, 0.6, 0, 0.6, -1, 0, 0.767, 1, 0, 0.933, -1, 0, 1.133, 1, 0, 1.233, -1, 0, 1.4, 1, 0, 1.533, -1, 0, 1.7, 1, 0, 1.833, -1, 0, 2, 1, 0, 2.133, -1, 0, 2.3, 1, 0, 2.467, -1, 0, 2.633, 1, 0, 2.733, -1, 0, 2.933, 1, 0, 3.033, -1, 0, 3.233, 1, 0, 3.4, -1, 0, 3.533, 1, 0, 3.7, -1, 0, 3.867, 1, 0, 4.033, -1, 0, 4.233, 1, 0, 4.333, -1, 0, 4.433, 0, 2, 4.767, 0, 0, 4.9, 1, 0, 5.033, -1, 0, 5.2, 1, 0, 5.367, -1, 0, 5.533, 1, 0, 5.667, -1, 0, 5.833, 1, 0, 5.933, -1, 0, 6.133, 1, 0, 6.267, -1, 0, 6.433, 1, 0, 6.567, -1, 0, 6.733, 1, 0, 6.9, -1, 0, 7.033, 1, 0, 7.167, -1, 0, 7.333, 1, 0, 7.5, -1, 0, 7.767, 1, 0, 7.867, 0, 2, 8.2, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegBZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, 0.6, 0, 0.6, -1, 0, 0.767, 1, 0, 0.933, -1, 0, 1.133, 1, 0, 1.233, -1, 0, 1.4, 1, 0, 1.533, -1, 0, 1.7, 1, 0, 1.833, -1, 0, 2, 1, 0, 2.133, -1, 0, 2.3, 1, 0, 2.467, -1, 0, 2.633, 1, 0, 2.733, -1, 0, 2.933, 1, 0, 3.033, -1, 0, 3.233, 1, 0, 3.4, -1, 0, 3.533, 1, 0, 3.7, -1, 0, 3.867, 1, 0, 4.033, -1, 0, 4.233, 1, 0, 4.333, -1, 0, 4.433, 0, 2, 4.767, 0, 0, 4.9, 1, 0, 5.033, -1, 0, 5.2, 1, 0, 5.367, -1, 0, 5.533, 1, 0, 5.667, -1, 0, 5.833, 1, 0, 5.933, -1, 0, 6.133, 1, 0, 6.267, -1, 0, 6.433, 1, 0, 6.567, -1, 0, 6.733, 1, 0, 6.9, -1, 0, 7.033, 1, 0, 7.167, -1, 0, 7.333, 1, 0, 7.5, -1, 0, 7.767, 1, 0, 7.867, 0, 2, 8.2, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyY", "Segments": [0, 0, 2, 0.4, 0, 2, 0.567, 0, 0, 0.8, -7, 0, 1.167, 3, 0, 1.467, -4, 0, 1.733, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamCannonZ", "Segments": [0, 0, 2, 0.433, 0, 0, 0.6, 6, 0, 1, -3, 0, 1.3, 1.059, 0, 1.633, 0, 2, 2.667, 0, 0, 2.9, 2.231, 0, 3.333, -13, 0, 3.567, -11.678, 0, 3.867, -12.606, 2, 4.433, -12.606, 0, 5.267, 11.702, 0, 5.867, 0, 0, 6.3, 2, 0, 6.633, -2, 0, 6.967, 0.493, 0, 7.233, 0, 1, 7.378, 0, 7.522, 0.04, 7.667, 0.285, 1, 7.711, 0.36, 7.756, 2.76, 7.8, 2.76, 0, 8.267, 0, 2, 8.633, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamCannonGaY", "Segments": [0, 0, 2, 0.5, 0, 0, 0.667, -8.384, 0, 1.067, 3.845, 0, 1.367, -3.618, 0, 1.667, 3.37, 0, 1.967, -1.16, 0, 2.3, 1.234, 1, 2.444, 1.234, 2.589, 1.072, 2.733, -0.231, 1, 2.811, -0.932, 2.889, -3.248, 2.967, -3.248, 0, 3.4, 11.418, 0, 3.633, 6, 0, 3.933, 10.888, 0, 4.2, 6.475, 0, 4.5, 10.888, 0, 5.333, -11, 0, 5.933, 3.37, 0, 6.467, -2.389, 0, 6.733, 1.41, 0, 7.1, -1.075, 0, 7.367, 0.472, 1, 7.422, 0.472, 7.478, 0.562, 7.533, -0.231, 1, 7.644, -1.817, 7.756, -3.981, 7.867, -3.981, 0, 8.333, 0, 0, 8.767, -1.075, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupY", "Segments": [0, 0, 2, 0.533, 0, 0, 0.7, 0.2, 0, 1.1, -0.1, 0, 1.4, 0.035, 0, 1.733, 0, 2, 2.767, 0, 0, 3, 0.074, 0, 3.433, -0.433, 0, 3.667, -0.389, 0, 3.967, -0.42, 2, 4.533, -0.42, 0, 5.367, 0.39, 0, 5.967, 0, 2, 7.333, 0, 1, 7.478, 0, 7.622, 0.001, 7.767, 0.01, 1, 7.811, 0.013, 7.856, 0.092, 7.9, 0.092, 0, 8.367, 0, 2, 8.733, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupZ", "Segments": [0, 0, 2, 0.4, 0, 0, 0.567, 0.2, 0, 0.967, -0.1, 0, 1.267, 0.035, 0, 1.6, 0, 2, 2.633, 0, 0, 2.867, 0.074, 0, 3.3, -0.433, 0, 3.533, -0.389, 0, 3.833, -0.42, 2, 4.4, -0.42, 0, 5.233, 0.39, 0, 5.833, 0, 2, 7.2, 0, 1, 7.344, 0, 7.489, 0.001, 7.633, 0.01, 1, 7.678, 0.013, 7.722, 0.092, 7.767, 0.092, 0, 8.233, 0, 2, 8.6, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamCannonHandZ", "Segments": [0, 0, 2, 0.4, 0, 2, 4.2, 0, 2, 5.6, 0, 0, 6.167, 8, 2, 8, 8, 0, 8.267, 10, 0, 9.033, 0, 2, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamParamStrongCatZ", "Segments": [0, 0, 2, 0.4, 0, 0, 0.8, 2, 0, 1.167, -11.745, 0, 1.733, -5.725, 0, 2.067, -7, 0, 3.933, 1.845, 1, 4.122, 1.845, 4.311, 1.729, 4.5, 0.15, 1, 4.622, -0.871, 4.745, -4.109, 4.867, -4.109, 2, 4.967, -4.109, 0, 5.533, 7.669, 2, 5.633, 7.669, 1, 5.844, 7.669, 6.056, -5.329, 6.267, -5.82, 1, 6.3, -5.897, 6.334, -5.837, 6.367, -5.837, 0, 7.033, 7.979, 1, 7.078, 7.979, 7.122, 8.102, 7.167, 7.928, 1, 7.378, 7.103, 7.589, -4.524, 7.8, -4.524, 2, 7.9, -4.524, 0, 8.767, 2, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamParamSCBodyZ", "Segments": [0, 0, 2, 0.4, 0, 2, 0.5, 0, 0, 0.9, 2, 0, 1.267, -9.745, 0, 1.833, -5.725, 0, 2.167, -7, 0, 4.033, 1.845, 1, 4.222, 1.845, 4.411, 1.729, 4.6, 0.15, 1, 4.722, -0.871, 4.845, -4.109, 4.967, -4.109, 2, 5.067, -4.109, 0, 5.633, 7.669, 2, 5.733, 7.669, 1, 5.944, 7.669, 6.156, -5.329, 6.367, -5.82, 1, 6.4, -5.897, 6.434, -5.837, 6.467, -5.837, 0, 7.133, 7.979, 1, 7.178, 7.979, 7.222, 8.102, 7.267, 7.928, 1, 7.478, 7.103, 7.689, -4.524, 7.9, -4.524, 2, 8, -4.524, 0, 8.867, 2, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamSCDishY", "Segments": [0, 0, 2, 0.4, 0, 2, 0.6, 0, 0, 1, 2, 0, 1.367, -2.745, 0, 1.9, 1.275, 0, 2.233, 0, 0, 4.033, 1.845, 1, 4.222, 1.845, 4.411, 1.729, 4.6, 0.15, 1, 4.722, -0.871, 4.845, -4.109, 4.967, -4.109, 2, 5.067, -4.109, 0, 5.633, 7.669, 2, 5.733, 7.669, 1, 5.944, 7.669, 6.156, -5.329, 6.367, -5.82, 1, 6.4, -5.897, 6.434, -5.837, 6.467, -5.837, 0, 7.133, 7.979, 1, 7.178, 7.979, 7.222, 8.102, 7.267, 7.928, 1, 7.478, 7.103, 7.689, -4.524, 7.9, -4.524, 2, 8, -4.524, 0, 8.867, 2, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamSCDishZ", "Segments": [0, 0, 2, 0.4, 0, 2, 0.633, 0, 0, 1.033, 2, 0, 1.4, -2.745, 0, 1.933, 1.275, 0, 2.267, 0, 0, 4.167, 1.845, 1, 4.356, 1.845, 4.544, 1.729, 4.733, 0.15, 1, 4.855, -0.871, 4.978, -4.109, 5.1, -4.109, 2, 5.2, -4.109, 0, 5.767, 7.669, 2, 5.867, 7.669, 1, 6.078, 7.669, 6.289, -5.329, 6.5, -5.82, 1, 6.533, -5.897, 6.567, -5.837, 6.6, -5.837, 0, 7.267, 7.979, 1, 7.311, 7.979, 7.356, 8.102, 7.4, 7.928, 1, 7.611, 7.103, 7.822, -4.524, 8.033, -4.524, 2, 8.133, -4.524, 0, 9, 2, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamSCCupZ", "Segments": [0, 0, 2, 0.4, 0, 2, 0.7, 0, 0, 1.133, 5.028, 0, 1.5, -5.773, 0, 2.067, 3.378, 0, 2.333, -1.036, 0, 4.233, 1.845, 1, 4.422, 1.845, 4.611, 1.729, 4.8, 0.15, 1, 4.922, -0.871, 5.045, -4.109, 5.167, -4.109, 2, 5.267, -4.109, 0, 5.833, 7.669, 2, 5.933, 7.669, 1, 6.144, 7.669, 6.356, -5.329, 6.567, -5.82, 1, 6.6, -5.897, 6.634, -5.837, 6.667, -5.837, 0, 7.333, 7.979, 1, 7.378, 7.979, 7.422, 8.102, 7.467, 7.928, 1, 7.678, 7.103, 7.889, -4.524, 8.1, -4.524, 2, 8.2, -4.524, 0, 9.067, 2, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "MB_yanwubaozha", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "MB_DRFWXZKTMD", "Segments": [0, 1, 0, 9.4, 1]}, {"Target": "Parameter", "Id": "ParamAllSizeFix", "Segments": [0, 1, 0, 9.4, 1]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBGHide", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBG2Hide", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBGX", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBGY", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN2", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN3", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBlackY", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBlackCollar", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBlackOrder", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamWhiteIN", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamCHHide", "Segments": [0, 1, 0, 9.4, 1]}, {"Target": "Parameter", "Id": "ParamDeskHide", "Segments": [0, 1, 0, 9.4, 1]}, {"Target": "Parameter", "Id": "ParamStoolHide", "Segments": [0, 1, 0, 9.4, 1]}, {"Target": "Parameter", "Id": "ParamCupDesk", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamCHX", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamCHY", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamCHZ", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamChaSize", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamCcharacterZ", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionX", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionY", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionX", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionY", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamAllZ", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamAllSize", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamALLSize2", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamDeskShow", "Segments": [0, 10, 0, 9.4, 10]}, {"Target": "Parameter", "Id": "ParamStrongCatShow", "Segments": [0, 10, 0, 9.4, 10]}, {"Target": "Parameter", "Id": "ParamCannonShow", "Segments": [0, 10, 0, 9.4, 10]}, {"Target": "Parameter", "Id": "ParamLightPositionX", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamFixT", "Segments": [0, 1, 0, 9.4, 1]}, {"Target": "Parameter", "Id": "ParamFlap", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamScare", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpen2", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamMouthType", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamBlackFace", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamTeethLight", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamHeart2", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamShameLine", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLX", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLY", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeL", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRX", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRY", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeR", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeRLightOpen", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLightLine1", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLightLine2", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLightLine3", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLightShine", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo1", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo2", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo3", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1Y", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle2", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow1", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow2", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamTearLight", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamTears", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperLAngle", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamArmHandLAngle", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerLAngle", "Segments": [0, 10, 0, 9.4, 10]}, {"Target": "Parameter", "Id": "ParamArmLowerLH", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerLAngle", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamHandT2L", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamFanOpenR", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamChili", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamChiliX", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperRH", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamHandR", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRY", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamHand_Cl", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamHandT1R", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamHandRCup", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamHandRMail", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "Segments": [0, 1, 0, 9.4, 1]}, {"Target": "Parameter", "Id": "ParamHandLIQY1", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY2", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY3", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamHandCupZ", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamHandCupY", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Y", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Y", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Y", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamFootRX", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Z", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Z", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Z", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Y", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Y", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Y", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamFootLX", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLegLF", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRHide", "Segments": [0, 1, 0, 9.4, 1]}, {"Target": "Parameter", "Id": "ParamMJRFlap", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuR", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuR", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuR", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuR", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRInput", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamMRCupSet", "Segments": [0, 1, 0, 9.4, 1]}, {"Target": "Parameter", "Id": "ParamMalpositionManjuuR", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuREyeOpen", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRArmB", "Segments": [0, 30, 0, 9.4, 30]}, {"Target": "Parameter", "Id": "ParamManjuuRMouth", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRSigh", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow1", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow2", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow3", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow4", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowB", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW2", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamMRCupFZ", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX1", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX2", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLiqH", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX1", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX2", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX3", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLHide", "Segments": [0, 1, 0, 9.4, 1]}, {"Target": "Parameter", "Id": "ParamMJLSigh", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuL", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuL", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuL", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamMjLFlip", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamPositionZManjuuL", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLEyeOpen", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyW", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuL", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamClawFX", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamClawFY", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamClawBX", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamClawBY", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUHide", "Segments": [0, 1, 0, 9.4, 1]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuU", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuU", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuU", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyX", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamPandaHide", "Segments": [0, 1, 0, 9.4, 1]}, {"Target": "Parameter", "Id": "ParamPositionYPanda", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamSizePanda", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyX", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY1", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY2", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY3", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupIce", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupZ", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupInput", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamSCDishRO", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamSCCupRO", "Segments": [0, 0, 0, 9.4, 0]}, {"Target": "Parameter", "Id": "ParamSCCupY", "Segments": [0, 0, 0, 9.4, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.267, "Value": ""}, {"Time": 8.9, "Value": ""}]}