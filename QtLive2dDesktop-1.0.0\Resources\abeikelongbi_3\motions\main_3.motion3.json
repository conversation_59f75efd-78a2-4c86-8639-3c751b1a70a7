{"Version": 3, "Meta": {"Duration": 11.233, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 252, "TotalSegmentCount": 22300, "TotalPointCount": 25720, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamBGHide", "Segments": [0, 0, 0, 0.8, 1, 2, 5.9, 1, 0, 6.6, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN3", "Segments": [0, 0, 0, 0.8, 0.1, 2, 5.9, 0.1, 0, 6.6, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPupilExp", "Segments": [0, 0, 2, 1.533, 0, 2, 7.667, 0, 0, 7.9, 1, 2, 9.967, 1, 0, 10.067, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.133, 1, 0, 0.4, 0, 2, 0.767, 0, 0, 1.033, 1, 2, 5.7, 1, 2, 6.767, 1, 2, 7.567, 1, 0, 7.667, 0, 2, 7.767, 0, 0, 7.9, 1, 2, 9.6, 1, 2, 9.9, 1, 0, 10.033, 0, 0, 10.167, 0.8, 0, 10.3, 0.5, 2, 10.567, 0.5, 0, 11.233, 1]}, {"Target": "Parameter", "Id": "ParamEyeSmileL", "Segments": [0, 0, 2, 9.933, 0, 0, 10.133, 1, 2, 10.6, 1, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.133, 1, 0, 0.4, 0, 2, 0.767, 0, 0, 1.033, 1, 2, 5.7, 1, 2, 6.767, 1, 2, 7.567, 1, 0, 7.667, 0, 2, 7.767, 0, 0, 7.9, 1, 2, 9.6, 1, 2, 9.9, 1, 0, 10.033, 0, 0, 10.167, 0.8, 0, 10.3, 0.5, 2, 10.567, 0.5, 0, 11.233, 1]}, {"Target": "Parameter", "Id": "ParamEyeSmileR", "Segments": [0, 0, 2, 9.933, 0, 0, 10.133, 1, 2, 10.6, 1, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 0, 0.467, -1, 2, 0.867, -1, 0, 1.733, -0.5, 0, 2.267, -1, 0, 2.8, -0.5, 0, 3.167, -0.8, 0, 3.6, 0.197, 0, 4, -0.4, 0, 4.1, 0, 0, 4.6, -0.5, 0, 5.033, 0.5, 1, 5.244, 0.5, 5.456, 0.505, 5.667, 0.3, 1, 5.8, 0.171, 5.934, -1, 6.067, -1, 1, 6.111, -1, 6.156, -0.613, 6.2, -0.5, 1, 6.356, -0.104, 6.511, 0, 6.667, 0, 0, 6.9, -0.5, 1, 7.022, -0.5, 7.145, -0.344, 7.267, 0, 1, 7.334, 0.188, 7.4, 0.3, 7.467, 0.3, 0, 7.6, -0.3, 0, 7.7, 0.2, 0, 7.9, -0.4, 0, 8.1, 0, 2, 8.367, 0, 0, 8.867, 1, 2, 9.033, 1, 0, 9.167, -0.5, 2, 9.867, -0.5, 0, 10.033, -1, 0, 10.133, -0.5, 2, 10.333, -0.5, 0, 10.533, 0.5, 2, 10.733, 0.5, 1, 10.789, 0.5, 10.844, 0.409, 10.9, 0.3, 1, 11.011, 0.082, 11.122, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 0.467, 1, 0, 0.6, 0.856, 0, 0.8, 1, 0, 1.467, 0.8, 0, 1.733, 1, 0, 1.9, 0.6, 0, 2.1, 1, 0, 2.4, 0.6, 0, 2.5, 0.8, 0, 2.6, 0.7, 0, 2.7, 0.991, 0, 2.967, 0.6, 0, 3.167, 0.993, 0, 3.3, 0.5, 0, 3.4, 0.773, 0, 3.533, 0.733, 0, 3.633, 1, 0, 3.8, 0.554, 0, 4, 1, 1, 4.1, 1, 4.2, 0.745, 4.3, 0.6, 1, 4.4, 0.455, 4.5, 0.435, 4.6, 0.3, 1, 4.689, 0.18, 4.778, 0, 4.867, 0, 2, 5.267, 0, 2, 5.7, 0, 1, 5.756, 0, 5.811, 0.284, 5.867, 0.5, 1, 5.934, 0.759, 6, 0.8, 6.067, 0.8, 0, 6.5, 0, 2, 6.667, 0, 0, 6.9, 0.856, 0, 7, 0.781, 0, 7.133, 1, 1, 7.244, 1, 7.356, 0.812, 7.467, 0.5, 1, 7.545, 0.282, 7.622, 0.2, 7.7, 0.2, 0, 7.9, 0.5, 2, 8.2, 0.5, 0, 8.367, 0.1, 1, 8.422, 0.1, 8.478, 0.309, 8.533, 0.5, 1, 8.578, 0.653, 8.622, 0.669, 8.667, 0.669, 0, 8.767, 0.554, 0, 8.933, 0.7, 0, 9.033, 0.5, 0, 9.167, 0.8, 0, 9.3, 0.6, 0, 9.467, 0.802, 0, 9.6, 0.5, 0, 9.733, 0.7, 0, 9.867, 0.509, 0, 10.033, 1, 0, 10.233, 0.493, 0, 10.467, 1, 0, 10.733, 0.5, 2, 10.833, 0.5, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthSize", "Segments": [0, 0, 2, 1.067, 0, 1, 1.2, 0, 1.334, -0.328, 1.467, -0.5, 1, 1.556, -0.615, 1.644, -0.601, 1.733, -0.7, 1, 1.844, -0.823, 1.956, -1, 2.067, -1, 0, 5.267, 0, 2, 5.7, 0, 2, 5.733, 0, 0, 6.067, -0.9, 0, 6.767, 0, 0, 6.9, -1, 1, 7.089, -1, 7.278, -0.991, 7.467, -0.89, 1, 7.545, -0.848, 7.622, 0, 7.7, 0, 2, 8.667, 0, 0, 8.767, -0.401, 2, 8.967, -0.401, 0, 9.133, 0, 2, 9.867, 0, 0, 10.033, -0.5, 0, 10.167, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeEmotion", "Segments": [0, 0, 0, 0.233, 1, 0, 0.667, -1, 2, 1.767, -1, 2, 4.167, -1, 0, 4.8, 0, 2, 5.6, 0, 0, 5.767, 1, 0, 6, -1, 2, 7.1, -1, 0, 7.667, 1, 2, 9.6, 1, 0, 10.133, -1, 0, 10.367, 1, 2, 10.633, 1, 0, 11.033, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBlackFace", "Segments": [0, 0, 2, 0.367, 0, 2, 5.733, 0, 0, 6.1, 1, 2, 6.767, 1, 2, 7.6, 1, 0, 8.267, 0, 2, 9.6, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLX", "Segments": [0, 0, 2, 0.7, 0, 0, 1.067, -0.625, 2, 1.7, -0.625, 0, 2.033, 0.2, 2, 2.767, 0.2, 0, 3.033, -0.625, 2, 5.7, -0.625, 2, 6.767, -0.625, 2, 7.633, -0.625, 0, 7.833, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLY", "Segments": [0, 0, 2, 0.2, 0, 2, 0.7, 0, 0, 1.167, -0.6, 2, 1.633, -0.6, 0, 1.933, 0.106, 2, 2.533, 0.106, 0, 3.067, -0.6, 1, 3.878, -0.601, 4.689, -0.602, 5.5, -0.602, 0, 5.733, -0.495, 0, 6, -1, 2, 6.933, -1, 1, 7.144, -1, 7.356, -0.904, 7.567, -0.6, 1, 7.6, -0.552, 7.634, 0, 7.667, 0, 2, 7.933, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeL", "Segments": [0, 0, 2, 5.7, 0, 0, 6.1, -0.6, 2, 6.767, -0.6, 2, 7.8, -0.6, 0, 7.9, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRX", "Segments": [0, 0, 2, 0.7, 0, 0, 1.067, -0.625, 2, 1.7, -0.625, 0, 2.033, 0.2, 2, 2.767, 0.2, 0, 3.033, -0.625, 2, 5.7, -0.625, 2, 6.767, -0.625, 2, 7.633, -0.625, 0, 7.833, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRY", "Segments": [0, 0, 2, 0.2, 0, 2, 0.7, 0, 0, 1.167, -0.6, 2, 1.633, -0.6, 0, 1.933, 0.106, 2, 2.533, 0.106, 0, 3.067, -0.6, 1, 3.878, -0.601, 4.689, -0.602, 5.5, -0.602, 0, 5.733, -0.495, 0, 6, -1, 2, 6.933, -1, 1, 7.144, -1, 7.356, -0.904, 7.567, -0.6, 1, 7.6, -0.552, 7.634, 0, 7.667, 0, 2, 7.933, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeR", "Segments": [0, 0, 2, 5.7, 0, 0, 6.1, -0.6, 2, 6.767, -0.6, 2, 7.8, -0.6, 0, 7.9, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPHYInputX", "Segments": [0, 0, 2, 0.1, 0, 0, 0.433, -5.457, 0, 1.233, 10.523, 0, 1.533, -5.745, 0, 1.833, 10.142, 0, 2.067, -5.263, 0, 2.433, 11.704, 0, 2.733, -4.082, 0, 3, 10.453, 0, 3.267, -5, 0, 3.567, 10.453, 0, 3.833, -4.082, 0, 4.167, 11.704, 0, 4.5, -4.082, 0, 4.867, 6.453, 0, 5.167, -4.082, 0, 5.867, 7.817, 0, 6.767, 0, 2, 7.6, 0, 0, 8.067, -6.868, 0, 8.467, 10.523, 0, 9.2, -9.059, 0, 9.6, 8.39, 0, 10.033, -9.677, 0, 10.4, 9, 0, 10.833, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.2, 0, 0, 1.167, -30, 0, 1.433, -27.666, 0, 1.767, -30, 0, 2.067, -27.717, 0, 2.333, -30, 0, 2.8, -27.563, 0, 3.167, -30, 0, 3.567, -27.769, 0, 4.033, -30, 0, 4.6, -28.284, 0, 5.133, -30, 0, 5.567, -27.666, 0, 6.033, -30, 0, 6.667, -28.387, 0, 7.167, -28.851, 1, 7.245, -28.851, 7.322, -29.327, 7.4, -27.666, 1, 7.589, -23.633, 7.778, -15, 7.967, -15, 0, 8.233, -17, 2, 9.1, -17, 0, 9.467, -15.594, 1, 9.556, -15.594, 9.644, -15.874, 9.733, -16, 1, 9.866, -16.189, 10, -16.209, 10.133, -16.209, 0, 10.933, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 0, 0.333, -10, 0, 0.767, 0.383, 0, 1.233, -3.753, 0, 1.5, 3.031, 0, 1.8, -2.72, 0, 2.133, 3.225, 0, 2.467, -2.72, 0, 2.7, 2.575, 0, 2.967, -2.72, 0, 3.267, 2.575, 0, 3.567, -2.72, 0, 3.9, 3.225, 0, 4.267, -2.72, 0, 4.6, 2.575, 0, 4.933, -2.72, 0, 5.267, 1.608, 0, 5.6, -0.824, 0, 5.867, -0.419, 0, 6.567, -3.81, 0, 6.867, -1.73, 0, 7.233, -2.254, 2, 7.533, -2.254, 0, 7.833, 9, 0, 8.133, -9, 0, 8.433, 4.075, 0, 8.633, -8.058, 0, 8.833, 2.615, 0, 9.033, -7.548, 0, 9.233, 1.282, 0, 9.433, -6.801, 0, 9.733, -2.72, 1, 9.778, -2.72, 9.822, -2.56, 9.867, -3, 1, 9.956, -3.879, 10.044, -8, 10.133, -8, 0, 10.433, 2.615, 0, 10.933, -3, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 0, 0.7, 9, 0, 1.333, -3.311, 0, 1.7, 0.199, 0, 1.967, -2.312, 0, 2.333, 0.473, 0, 2.633, -2.312, 0, 2.9, -0.454, 0, 3.167, -2.312, 0, 3.467, -0.454, 0, 3.767, -2.312, 0, 4.1, 0.473, 0, 4.433, -2.312, 0, 4.8, -0.454, 0, 5.1, -2.312, 1, 5.2, -2.312, 5.3, -0.075, 5.4, 0, 1, 5.489, 0.067, 5.578, 0.048, 5.667, 0.048, 0, 6.1, -4.07, 0, 6.567, -2.726, 0, 6.9, -3.311, 0, 8.8, -3, 2, 9.5, -3, 0, 9.8, -3.417, 0, 10.7, 2, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamChili", "Segments": [0, 0, 2, 0.567, 0, 0, 1.133, 1, 2, 5.733, 1, 2, 7.233, 1, 0, 7.367, 2, 1, 8.078, 2, 8.789, 0.874, 9.5, 0.462, 1, 9.611, 0.398, 9.722, 0.458, 9.833, 0.422, 1, 10.1, 0.337, 10.366, 0, 10.633, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamChiliX", "Segments": [0, 0, 2, 1.1, 0, 0, 1.233, -0.306, 0, 1.567, 0.307, 0, 1.867, -0.208, 0, 2.2, 0.244, 0, 2.5, -0.306, 0, 2.767, 0.26, 0, 3.067, -0.295, 0, 3.4, 0.355, 0, 3.733, -0.36, 0, 4.133, 0.239, 0, 4.5, -0.5, 0, 5, 0.127, 0, 5.2, -0.306, 0, 5.467, 0.127, 0, 5.733, -0.206, 0, 6.033, 0.087, 0, 6.3, 0, 2, 6.733, 0, 0, 6.933, 0.2, 0, 7.1, -0.2, 0, 7.333, 0, 2, 9.5, 0, 2, 9.833, 0, 2, 10.633, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamArm<PERSON>pperRAngle", "Segments": [0, 0, 2, 0.067, 0, 0, 0.3, -1, 0, 0.833, 2, 2, 0.867, 2, 0, 1.067, 2.09, 0, 1.3, 1.85, 0, 1.6, 2.15, 0, 1.9, 1.85, 0, 2.267, 2.12, 0, 2.6, 2, 0, 2.867, 2.09, 0, 3.167, 1.85, 0, 3.467, 2.15, 0, 3.8, 1.85, 0, 4.233, 2.09, 0, 4.5, 1.85, 0, 4.767, 2.15, 0, 5.1, 1.85, 0, 5.4, 2.12, 0, 5.733, 2, 1, 5.755, 2, 5.778, 2.025, 5.8, 2.034, 1, 5.856, 2.057, 5.911, 2.081, 5.967, 2.09, 1, 6.234, 2.134, 6.5, 2.15, 6.767, 2.15, 0, 6.867, 2.12, 2, 7.3, 2.12, 1, 7.378, 2.12, 7.455, 1.767, 7.533, 1.43, 1, 7.611, 1.093, 7.689, 1.031, 7.767, 1.031, 0, 8.233, 3.497, 0, 8.4, 3.335, 0, 8.6, 3.595, 0, 8.833, 3.335, 0, 9.033, 3.497, 0, 9.267, 3.335, 0, 9.5, 3.425, 1, 9.656, 3.425, 9.811, 3.537, 9.967, 2.99, 1, 10.211, 2.13, 10.456, 0, 10.7, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRAngle", "Segments": [0, 7.4, 2, 0.133, 7.4, 0, 0.333, 8.043, 0, 0.833, -12.8, 1, 0.978, -12.8, 1.122, -12.778, 1.267, -12.513, 1, 1.356, -12.35, 1.444, -11.865, 1.533, -11.865, 0, 1.867, -12.573, 0, 2.167, -11.865, 0, 2.467, -12.68, 0, 2.8, -11.925, 0, 3.1, -12.513, 0, 3.333, -11.865, 0, 3.667, -12.573, 0, 4, -11.925, 0, 4.4, -12.513, 0, 4.633, -11.865, 0, 4.967, -12.573, 0, 5.3, -11.925, 0, 5.667, -12.661, 1, 5.756, -12.661, 5.844, -11.944, 5.933, -11.925, 1, 6.189, -11.872, 6.444, -11.865, 6.7, -11.865, 0, 6.867, -12.393, 0, 7, -12.173, 0, 7.433, -12.5, 0, 7.833, 5.515, 1, 7.933, 5.515, 8.033, 4.383, 8.133, 4, 1, 8.222, 3.66, 8.311, 3.698, 8.4, 3.698, 0, 8.6, 3.858, 0, 8.833, 3.698, 1, 8.911, 3.698, 8.989, 3.857, 9.067, 3.858, 1, 9.289, 3.862, 9.511, 3.862, 9.733, 3.862, 0, 9.933, 3.678, 0, 10.467, 7.62, 0, 10.633, 7.4, 2, 11.233, 7.4]}, {"Target": "Parameter", "Id": "ParamArmHandRAngle", "Segments": [0, 0, 0, 0.2, -0.408, 0, 0.467, 3.055, 0, 0.633, -10, 2, 0.667, 5, 1, 0.767, 5, 0.867, -4.065, 0.967, -5, 1, 1.056, -5.831, 1.144, -5.667, 1.233, -5.667, 0, 1.567, -4.667, 0, 1.867, -5.667, 0, 2.133, -5, 0, 2.433, -5.667, 0, 2.733, -4.84, 0, 3.067, -5.667, 0, 3.4, -4.667, 0, 3.733, -5.667, 0, 4.1, -4.667, 0, 4.5, -5.667, 0, 4.833, -4.667, 0, 5.167, -5.667, 0, 5.433, -5, 0, 5.733, -5.667, 1, 5.822, -5.667, 5.911, -5.06, 6, -5, 1, 6.244, -4.835, 6.489, -4.811, 6.733, -4.811, 0, 6.867, -5, 2, 7.367, -5, 0, 7.6, -7, 0, 8, 0, 0, 8.267, -0.805, 0, 8.633, 0, 2, 9.833, 0, 0, 10.1, -1.349, 0, 10.6, 1.805, 0, 10.933, -0.298, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRH", "Segments": [0, 0, 0, 0.167, -1.23, 0, 0.467, 0.732, 0, 0.833, -15, 2, 0.9, -15, 0, 1.2, -15.754, 0, 1.5, -14.434, 0, 1.833, -15.754, 0, 2.067, -14.874, 0, 2.367, -15.554, 0, 2.667, -14.874, 0, 2.967, -15.754, 0, 3.267, -14.434, 0, 3.6, -15.754, 0, 3.833, -14.874, 2, 3.967, -14.874, 0, 4.3, -15.754, 0, 4.6, -14.434, 0, 4.933, -15.754, 0, 5.167, -14.874, 0, 5.433, -15.554, 1, 5.533, -15.554, 5.633, -14.991, 5.733, -14.874, 1, 6.044, -14.511, 6.356, -14.434, 6.667, -14.434, 0, 6.767, -15.754, 1, 6.8, -15.754, 6.834, -14.885, 6.867, -14.874, 1, 7.745, -14.584, 8.622, -14.455, 9.5, -14.455, 0, 9.833, -14.461, 1, 9.855, -14.461, 9.878, -14.601, 9.9, -14.434, 1, 10.144, -12.602, 10.389, 0, 10.633, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamHandR", "Segments": [0, 0, 2, 0.3, 0, 2, 0.6, 0, 2, 0.667, 0, 2, 5.733, 0, 2, 6.867, 0, 2, 9.5, 0, 2, 9.833, 0, 2, 10.633, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRAngle", "Segments": [0, 0, 2, 0.3, 0, 0, 0.833, -6.213, 0, 1.133, 7.899, 0, 1.433, -4.343, 0, 1.767, 6.879, 0, 2.067, -6.213, 0, 2.367, 7, 0, 2.667, -6, 0, 3, 6.361, 0, 3.333, -6.199, 0, 3.667, 6.879, 0, 4.033, -6.213, 0, 4.433, 7, 0, 4.767, -6.213, 0, 5.1, 6.879, 0, 5.367, -6.213, 0, 5.667, 6, 0, 5.933, -1.43, 0, 6.233, 0, 2, 6.7, 0, 2, 6.867, 0, 2, 9.5, 0, 2, 9.833, 0, 2, 10.633, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRY", "Segments": [0, 0, 2, 0.6, 0, 0, 0.9, -8.203, 0, 1.2, 7.711, 0, 1.533, -7.711, 0, 1.833, 8.203, 0, 2.233, -5.783, 0, 2.5, 7.711, 0, 2.733, -7.711, 0, 3.067, 7.711, 0, 3.433, -7.711, 0, 3.767, 8.203, 0, 4.2, -8.203, 0, 4.533, 7.711, 0, 4.9, -7.711, 0, 5.233, 8.203, 0, 5.633, -5.783, 0, 5.867, 8.203, 0, 6.233, -4, 2, 6.8, -4, 0, 6.867, -5, 1, 7.745, -5, 8.622, -2.089, 9.5, -0.888, 1, 9.611, -0.736, 9.722, -0.877, 9.833, -0.804, 1, 10.1, -0.628, 10.366, 0, 10.633, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamHand_Cl", "Segments": [0, 0, 2, 0.633, 0, 2, 0.667, 30, 2, 5.733, 30, 2, 6.867, 30, 2, 7.5, 30, 2, 7.533, 0, 2, 9.5, 0, 2, 9.833, 0, 2, 10.633, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamHandT2R", "Segments": [0, 0, 2, 0.567, 0, 0, 0.6, 0.2, 2, 0.667, 0.2, 0, 0.767, 0, 2, 5.733, 0, 2, 6.867, 0, 2, 9.5, 0, 2, 9.833, 0, 2, 10.633, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 0.3, 5, 0, 0.933, -4.263, 0, 1.233, -2.569, 0, 1.667, -4, 0, 2.033, -2.874, 0, 2.333, -3.849, 0, 2.667, -2.765, 0, 3, -3.849, 0, 3.233, -3.126, 0, 3.5, -3.849, 0, 3.8, -3.126, 0, 4.1, -3.849, 0, 4.433, -2.765, 0, 4.8, -3.849, 0, 5.133, -3.126, 0, 5.467, -3.849, 2, 5.7, -3.849, 1, 5.922, -3.849, 6.145, -4.786, 6.367, -4.984, 1, 6.589, -5.182, 6.811, -5.155, 7.033, -5.155, 0, 7.767, -3.01, 0, 8.3, -4, 0, 8.8, -2.874, 1, 8.878, -2.874, 8.955, -2.856, 9.033, -3.03, 1, 9.2, -3.403, 9.366, -3.849, 9.533, -3.849, 0, 9.767, -3.803, 0, 10.1, -4, 0, 10.7, 1, 0, 11.033, 0, 2, 11.133, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.1, 0, 0, 0.433, -1.819, 0, 1.233, 1.144, 0, 1.533, -0.751, 0, 1.833, 0.887, 0, 2.1, -1.205, 0, 2.433, 1.45, 0, 2.733, -1.205, 0, 3, 0.344, 0, 3.233, -1.205, 0, 3.567, 0.344, 0, 3.833, -1.205, 0, 4.167, 1.059, 0, 4.5, -0.751, 0, 4.867, 0.671, 0, 5.167, -0.205, 0, 5.567, 0.177, 0, 6.633, -0.098, 1, 6.844, -0.098, 7.056, -0.104, 7.267, 0, 1, 7.4, 0.066, 7.534, 3.273, 7.667, 3.716, 1, 7.745, 3.974, 7.822, 3.901, 7.9, 3.901, 0, 8.1, -4.606, 0, 8.367, 2.151, 0, 8.567, -3.905, 0, 8.8, 1.645, 0, 9, -3.905, 1, 9.011, -3.905, 9.022, -3.99, 9.033, -3.503, 1, 9.078, -1.557, 9.122, 0.311, 9.167, 0.311, 0, 9.4, -1.819, 1, 9.489, -1.819, 9.578, 1.676, 9.667, 1.907, 1, 10.011, 2.803, 10.356, 3, 10.7, 3, 0, 11.133, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 0.467, 1.033, 0, 1.033, -4.276, 0, 1.4, -3.564, 0, 1.7, -4.167, 0, 2, -3.186, 0, 2.333, -4.167, 0, 2.567, -3.36, 0, 2.833, -4.167, 0, 3.167, -3.512, 0, 3.433, -4.167, 0, 3.767, -3.186, 0, 4.1, -4.167, 0, 4.433, -3.512, 0, 4.767, -4.167, 0, 5.067, -3.186, 0, 5.4, -4.167, 0, 5.633, -3.708, 1, 5.666, -3.708, 5.7, -3.721, 5.733, -4.183, 1, 5.8, -5.107, 5.866, -6, 5.933, -6, 0, 6.267, -4.924, 0, 6.6, -5.496, 1, 6.844, -5.496, 7.089, -5.503, 7.333, -5.47, 1, 7.555, -5.44, 7.778, 2.21, 8, 2.21, 1, 8.344, 2.21, 8.689, 1.982, 9.033, 1.604, 1, 9.189, 1.433, 9.344, 1.237, 9.5, 1.156, 1, 9.556, 1.127, 9.611, 1.141, 9.667, 1.118, 1, 9.789, 1.068, 9.911, 1, 10.033, 1, 0, 10.367, 1.13, 0, 10.833, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX2", "Segments": [0, 0, 2, 0.633, 0, 1, 0.855, 0, 1.078, -1.626, 1.3, -3, 1, 1.456, -3.962, 1.611, -4, 1.767, -4, 0, 2.167, -2.912, 0, 2.5, -3.8, 0, 2.833, -2.813, 0, 3.167, -3.8, 0, 3.4, -3.142, 0, 3.667, -3.8, 0, 3.967, -3.142, 0, 4.267, -3.8, 0, 4.6, -2.813, 0, 4.967, -3.8, 0, 5.3, -3.142, 0, 5.567, -3.448, 0, 5.833, -3.142, 0, 6.533, -9, 2, 7.067, -9, 0, 8.167, -2.534, 0, 8.5, -3.142, 2, 8.8, -3.142, 0, 9.033, -2.955, 1, 9.189, -2.955, 9.344, -3.098, 9.5, -3.149, 1, 9.556, -3.167, 9.611, -3.156, 9.667, -3.169, 1, 9.778, -3.195, 9.889, -3.246, 10, -3.246, 0, 10.533, 1, 0, 10.833, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY2", "Segments": [0, 0, 2, 0.633, 0, 2, 5.7, 0, 2, 6.767, 0, 2, 8.8, 0, 2, 9.033, 0, 2, 9.5, 0, 2, 9.667, 0, 2, 9.933, 0, 0, 10.467, 2, 0, 10.833, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechX", "Segments": [0, 0, 0, 0.6, -1.09, 0, 1.333, 10, 0, 1.8, 8.308, 0, 2.167, 9.83, 0, 2.467, 8.537, 0, 2.8, 9.975, 0, 3.133, 8.537, 0, 3.367, 9.495, 0, 3.633, 8.537, 0, 3.933, 9.495, 0, 4.233, 8.537, 0, 4.567, 9.975, 0, 4.933, 8.537, 0, 5.267, 9.495, 0, 5.6, 8.537, 0, 5.9, 9.19, 0, 6.633, 7.912, 2, 7.4, 7.912, 0, 7.9, 9.282, 1, 8.078, 9.282, 8.255, 6.947, 8.433, 6.753, 1, 8.733, 6.426, 9.033, 6.431, 9.333, 6.431, 0, 9.867, 7.369, 0, 10.533, -1.09, 0, 10.933, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, 0, 0, 0.633, 1, 0, 1.267, -0.246, 0, 1.733, 0, 2, 5.7, 0, 2, 6.767, 0, 2, 8.8, 0, 2, 9.033, 0, 2, 9.5, 0, 2, 9.667, 0, 2, 10.833, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamShoulderR", "Segments": [0, 0, 2, 0.633, 0, 0, 1.233, 19, 2, 5.7, 19, 2, 6.767, 19, 2, 8.1, 19, 0, 8.367, 23, 0, 8.6, 19, 0, 8.8, 22, 0, 9.033, 19.209, 1, 9.189, 19.209, 9.344, 19.912, 9.5, 20.175, 1, 9.556, 20.269, 9.611, 20.196, 9.667, 20.281, 1, 9.834, 20.536, 10, 21, 10.167, 21, 0, 10.6, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamShoulderL", "Segments": [0, 0, 2, 0.633, 0, 0, 1.233, 14, 2, 5.7, 14, 2, 6.767, 14, 2, 8.8, 14, 1, 8.878, 14, 8.955, 14.02, 9.033, 14.076, 1, 9.189, 14.187, 9.344, 14.354, 9.5, 14.428, 1, 9.556, 14.454, 9.611, 14.436, 9.667, 14.467, 1, 9.834, 14.56, 10, 14.729, 10.167, 14.729, 0, 10.6, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuREyeOpen", "Segments": [0, 0, 2, 0.367, 0, 2, 2.167, 0, 0, 2.267, 1, 2, 6.1, 1, 0, 6.267, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyX", "Segments": [0, 0, 0, 0.767, -5.239, 0, 3.1, 3.347, 0, 4.867, -1.812, 0, 5.867, 0, 0, 6.667, -5.239, 0, 9.067, 3.347, 0, 10.867, -1.812, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyY", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0.17, 0.4, -0.706, 1, 0.678, -2.531, 0.955, -5.43, 1.233, -5.43, 0, 3.567, 2.312, 0, 5.333, -2.34, 0, 5.867, 0, 1, 6.022, 0, 6.178, 0.177, 6.333, -0.706, 1, 6.611, -2.283, 6.889, -5.43, 7.167, -5.43, 0, 9.567, 2.312, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyZ", "Segments": [0, 0, 2, 0.3, 0, 0, 0.933, -3.095, 0, 1.667, 5.954, 0, 2.5, -5.739, 0, 3, 9, 0, 3.767, -1.682, 0, 4.367, 6.107, 0, 5.1, -3.28, 0, 5.867, 6, 0, 7.467, -2.263, 0, 8.067, -1.28, 0, 8.7, -2.031, 0, 9.233, 5.861, 0, 10.2, -1.28, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRPositionZ", "Segments": [0, 0, 0, 0.7, -2.311, 0, 1.533, 7.563, 0, 2.5, -1.238, 0, 2.8, 4.526, 0, 3.5, -1.161, 0, 4.1, 5.929, 0, 4.733, -2.854, 0, 5.6, 7, 0, 7, -1.479, 0, 9.067, 0.929, 0, 10.333, -0.854, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRMouth", "Segments": [0, 0, 0, 0.533, 10.12, 0, 1.367, -7.41, 0, 2.133, 2.681, 0, 2.933, -4.354, 1, 3.111, -4.354, 3.289, -4.504, 3.467, -4.096, 1, 3.745, -3.458, 4.022, 4.763, 4.3, 4.763, 0, 5.367, -2.684, 1, 5.534, -2.684, 5.7, -2.783, 5.867, 0, 1, 6.067, 3.339, 6.267, 10.12, 6.467, 10.12, 0, 7.3, -7.41, 0, 8, 2.681, 0, 8.933, -4.354, 1, 9.1, -4.354, 9.266, -4.516, 9.433, -4.096, 1, 9.711, -3.395, 9.989, 4.763, 10.267, 4.763, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuL", "Segments": [0, 0, 2, 1.433, 0, 2, 1.667, 0, 0, 4, 20, 2, 5.8, 20, 0, 9.867, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamMjLFlip", "Segments": [0, 0, 2, 0.767, 0, 0, 1.2, 1, 2, 3.7, 1, 0, 5.067, 0, 2, 9.867, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionZManjuuL", "Segments": [0, 0, 1, 0.222, 0, 0.445, -9.754, 0.667, -15, 1, 0.734, -16.574, 0.8, -16, 0.867, -16, 0, 1.333, -8, 1, 1.389, -8, 1.444, -20.691, 1.5, -21, 1, 1.622, -21.68, 1.745, -21.728, 1.867, -21.728, 0, 2.2, 2, 0, 2.467, 0, 2, 8.967, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLEyeOpen", "Segments": [0, 0, 0, 1.433, 1, 2, 2.467, 1, 2, 2.867, 1, 2, 3.467, 1, 0, 8.967, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyX", "Segments": [0, 0, 0, 0.3, 1.434, 0, 0.667, -30, 0, 1.3, 30, 1, 1.344, 30, 1.389, 10.133, 1.433, 0, 1, 1.511, -17.733, 1.589, -21, 1.667, -21, 0, 2.1, 10.631, 0, 2.467, -17, 0, 2.867, 0, 2, 8.967, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyY", "Segments": [0, 0, 0, 0.467, 2.709, 0, 1.5, -22.948, 0, 2.233, 13, 0, 2.567, -12, 0, 3.167, 0, 2, 5.9, 0, 0, 6, 21.076, 0, 6.1, -21.076, 0, 6.2, 21.076, 0, 6.3, -21.076, 0, 6.4, 21.076, 0, 6.5, -21.076, 0, 6.567, 21.076, 0, 6.667, -21.076, 0, 6.767, 21.076, 0, 6.9, -21.076, 0, 7, 21.076, 0, 7.1, -21.076, 0, 7.2, 21.076, 0, 7.3, -21.076, 0, 7.4, 21.076, 0, 7.5, -21.076, 0, 7.6, 21.076, 0, 7.667, -21.076, 1, 7.689, -21.076, 7.711, 21.076, 7.733, 21.076, 0, 7.867, -21.076, 0, 7.967, 21.076, 0, 8.1, -21.076, 0, 8.2, 21.076, 0, 8.4, -21.076, 0, 8.5, 21.076, 0, 8.7, -21.076, 0, 8.767, 21.076, 0, 8.867, -21.076, 0, 8.967, 21.076, 0, 9.067, -21.076, 0, 9.167, 21.076, 0, 9.3, -21.076, 0, 9.4, 21.076, 0, 9.5, -21.076, 0, 9.6, 21.076, 0, 9.7, -21.076, 0, 9.767, 21.076, 0, 9.867, -21.076, 0, 9.967, 21.076, 0, 10.067, -21.076, 0, 10.167, 21.076, 0, 10.533, -3.527, 0, 10.867, 3, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyZ", "Segments": [0, 0, 0, 0.333, -4, 0, 0.933, 17, 0, 1.5, -7.258, 1, 1.611, -7.258, 1.722, 9.953, 1.833, 10, 1, 2.866, 10.434, 3.9, 10.589, 4.933, 10.589, 0, 5.533, 0, 0, 6.233, 30, 2, 9.233, 30, 0, 9.767, 0, 0, 10.067, 30, 0, 10.433, -7.258, 0, 10.767, 6.105, 0, 11.167, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyW", "Segments": [0, 0, 2, 0.633, 0, 0, 1.033, 12, 0, 1.233, -8, 0, 1.633, 5, 0, 1.967, 0, 2, 8.967, 0, 2, 9.733, 0, 0, 9.967, 11, 0, 10.267, -8, 0, 10.633, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuL", "Segments": [0, 0, 2, 5.867, 0, 0, 5.967, 1, 0, 6.067, -1, 0, 6.167, 1, 0, 6.233, -1, 0, 6.333, 1, 0, 6.433, -1, 0, 6.533, 1, 0, 6.633, -1, 0, 6.733, 1, 0, 6.8, -1, 0, 6.967, 1, 0, 7.067, -1, 0, 7.167, 1, 0, 7.233, -1, 0, 7.333, 1, 0, 7.433, -1, 0, 7.5, 1, 0, 7.633, -1, 0, 7.733, 1, 0, 7.833, -1, 0, 7.933, 1, 0, 8.067, -1, 0, 8.167, 1, 0, 8.367, -1, 0, 8.467, 1, 0, 8.633, -1, 0, 8.733, 1, 0, 8.833, -1, 0, 8.933, 1, 0, 9.033, -1, 0, 9.133, 1, 0, 9.233, -1, 0, 9.333, 1, 0, 9.433, -1, 0, 9.533, 1, 0, 9.633, -1, 0, 9.733, 1, 0, 9.833, -1, 0, 9.933, 1, 0, 10.033, -1, 0, 10.133, 1, 0, 10.167, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamClawFX", "Segments": [0, 0, 2, 1.433, 0, 0, 1.633, 1, 0, 1.767, -1, 2, 1.833, -1, 0, 1.967, 1, 0, 2.167, -1, 2, 2.2, -1, 0, 2.367, 1, 0, 2.533, -1, 2, 2.567, -1, 0, 2.7, 1, 0, 2.867, -1, 2, 2.9, -1, 0, 3.067, 1, 0, 3.2, -1, 2, 3.233, -1, 0, 3.4, 1, 0, 3.567, -1, 2, 3.633, -1, 0, 3.767, 1, 0, 3.9, 0, 2, 5.867, 0, 0, 6.067, 1, 0, 6.2, -1, 2, 6.233, -1, 0, 6.4, 1, 0, 6.533, -1, 2, 6.567, -1, 0, 6.733, 1, 0, 6.9, -1, 2, 6.967, -1, 0, 7.1, 1, 0, 7.233, -1, 2, 7.3, -1, 0, 7.433, 1, 0, 7.6, -1, 2, 7.633, -1, 0, 7.733, 1, 0, 7.933, -1, 2, 7.967, -1, 0, 8.167, 1, 0, 8.367, -1, 2, 8.4, -1, 0, 8.6, 1, 0, 8.733, -1, 2, 8.767, -1, 0, 8.933, 1, 0, 9.067, -1, 2, 9.133, -1, 0, 9.3, 1, 0, 9.433, -1, 2, 9.5, -1, 0, 9.633, 1, 0, 9.767, -1, 2, 9.833, -1, 0, 9.967, 1, 0, 10.133, -1, 0, 10.233, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamClawFY", "Segments": [0, 0, 2, 1.433, 0, 0, 1.5, -1, 0, 1.633, 1, 0, 1.833, -1, 0, 2.167, 1, 0, 2.2, -1, 0, 2.533, 1, 0, 2.567, -1, 2, 2.9, -1, 0, 3.2, 1, 0, 3.233, -1, 0, 3.567, 1, 0, 3.633, -1, 0, 3.9, 0, 2, 5.867, 0, 0, 5.967, -1, 0, 6.1, 1, 0, 6.233, -1, 2, 6.333, -1, 0, 6.433, 1, 0, 6.567, -1, 1, 6.6, -1, 6.634, -1, 6.667, -0.999, 1, 6.7, -0.998, 6.734, 1, 6.767, 1, 0, 6.967, -1, 2, 7.067, -1, 0, 7.167, 1, 0, 7.3, -1, 2, 7.4, -1, 0, 7.5, 1, 0, 7.633, -1, 2, 7.733, -1, 0, 7.833, 1, 0, 7.967, -1, 2, 8.1, -1, 0, 8.2, 1, 0, 8.4, -1, 2, 8.5, -1, 0, 8.633, 1, 0, 8.767, -1, 2, 8.867, -1, 0, 8.967, 1, 0, 9.133, -1, 2, 9.233, -1, 0, 9.333, 1, 0, 9.5, -1, 2, 9.6, -1, 0, 9.7, 1, 0, 9.833, -1, 2, 9.933, -1, 0, 10.033, 1, 0, 10.167, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamClawBX", "Segments": [0, 0, 2, 1.433, 0, 0, 1.633, -1, 0, 1.767, 1, 2, 1.833, 1, 0, 1.967, -1, 0, 2.167, 1, 2, 2.2, 1, 0, 2.367, -1, 0, 2.533, 1, 2, 2.567, 1, 0, 2.7, -1, 0, 2.867, 1, 2, 2.9, 1, 0, 3.067, -1, 0, 3.2, 1, 2, 3.233, 1, 0, 3.4, -1, 0, 3.567, 1, 2, 3.633, 1, 0, 3.767, -1, 0, 3.9, 0, 2, 5.867, 0, 0, 6.067, -1, 0, 6.2, 1, 2, 6.233, 1, 0, 6.4, -1, 0, 6.533, 1, 2, 6.567, 1, 0, 6.733, -1, 0, 6.9, 1, 2, 6.967, 1, 0, 7.1, -1, 0, 7.233, 1, 2, 7.3, 1, 0, 7.433, -1, 0, 7.6, 1, 2, 7.633, 1, 0, 7.733, -1, 0, 7.933, 1, 2, 7.967, 1, 0, 8.167, -1, 0, 8.367, 1, 2, 8.4, 1, 0, 8.6, -1, 0, 8.733, 1, 2, 8.767, 1, 0, 8.933, -1, 0, 9.067, 1, 2, 9.133, 1, 0, 9.3, -1, 0, 9.433, 1, 2, 9.5, 1, 0, 9.633, -1, 0, 9.767, 1, 2, 9.833, 1, 0, 9.967, -1, 0, 10.133, 1, 0, 10.233, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamClawBY", "Segments": [0, 0, 2, 1.433, 0, 0, 1.5, 1, 0, 1.633, -1, 0, 1.833, 1, 0, 2.167, -1, 0, 2.2, 1, 0, 2.533, -1, 0, 2.567, 1, 2, 2.9, 1, 0, 3.2, -1, 0, 3.233, 1, 0, 3.567, -1, 0, 3.633, 1, 0, 3.9, 0, 2, 5.867, 0, 0, 5.967, 1, 0, 6.1, -1, 0, 6.233, 1, 2, 6.333, 1, 0, 6.433, -1, 0, 6.567, 1, 1, 6.6, 1, 6.634, 1, 6.667, 0.999, 1, 6.7, 0.998, 6.734, -1, 6.767, -1, 0, 6.967, 1, 2, 7.067, 1, 0, 7.167, -1, 0, 7.3, 1, 2, 7.4, 1, 0, 7.5, -1, 0, 7.633, 1, 2, 7.733, 1, 0, 7.833, -1, 0, 7.967, 1, 2, 8.1, 1, 0, 8.2, -1, 0, 8.4, 1, 2, 8.5, 1, 0, 8.633, -1, 0, 8.767, 1, 2, 8.867, 1, 0, 8.967, -1, 0, 9.133, 1, 2, 9.233, 1, 0, 9.333, -1, 0, 9.5, 1, 2, 9.6, 1, 0, 9.7, -1, 0, 9.833, 1, 2, 9.933, 1, 0, 10.033, -1, 0, 10.167, 0, 2, 10.3, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUEyesForm", "Segments": [0, 0, 2, 1.933, 0, 0, 2.4, 1, 2, 2.533, 1, 0, 3.167, 0, 2, 3.7, 0, 0, 6.333, -1, 2, 8.2, -1, 0, 8.967, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyX", "Segments": [0, 0, 0, 1.067, -5.098, 0, 2.133, 0, 2, 2.767, 0, 2, 8.7, 0, 0, 9, -1.187, 0, 10.033, 1.932, 0, 10.667, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyY", "Segments": [0, 0, 0, 1.667, -9.098, 1, 1.722, -9.098, 1.778, -2.271, 1.833, 0, 1, 2.022, 7.721, 2.211, 10, 2.4, 10, 0, 2.9, -5, 1, 3.011, -5, 3.122, -3.262, 3.233, 0, 1, 3.378, 4.241, 3.522, 6.26, 3.667, 6.26, 2, 3.733, 6.26, 0, 3.9, -6.255, 0, 4.2, 5.512, 0, 4.433, -5, 0, 4.733, 2.925, 0, 5, -3.677, 0, 5.233, 2, 0, 5.467, -2.703, 0, 5.667, 2, 0, 5.867, -1.62, 0, 6.033, 2.925, 0, 6.167, -2.598, 0, 6.3, 3.787, 0, 6.4, -3.047, 0, 6.533, 2.925, 0, 6.667, -2.703, 0, 6.767, 2.925, 0, 6.9, -3.424, 0, 7, 3.066, 0, 7.067, -3.979, 0, 7.167, 4.531, 0, 7.3, -2.703, 0, 7.467, 2.925, 0, 7.533, -2.703, 0, 7.7, 2.925, 0, 7.933, -1.62, 0, 8.1, 0, 2, 8.7, 0, 0, 9.333, 0.896, 0, 10.9, -3.979, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyZ", "Segments": [0, 0, 0, 0.867, -0.797, 1, 1.189, -0.797, 1.511, -0.756, 1.833, 0, 1, 1.978, 0.339, 2.122, 20.858, 2.267, 20.858, 0, 2.7, 0, 2, 3, 0, 2, 3.167, 0, 0, 3.633, 20.858, 2, 3.7, 20.858, 0, 3.867, -21, 0, 4.1, 24.646, 0, 4.4, -27, 0, 4.7, 29, 0, 4.967, -30, 0, 5.2, 30, 0, 5.433, -30, 0, 5.6, 30, 0, 5.833, -30, 0, 6, 30, 0, 6.133, -30, 0, 6.267, 30, 0, 6.367, -30, 0, 6.5, 30, 0, 6.633, -30, 0, 6.733, 30, 0, 6.833, -30, 0, 6.967, 30, 0, 7.067, -30, 1, 7.089, -30, 7.111, 30, 7.133, 30, 0, 7.267, -30, 0, 7.367, 29, 0, 7.6, -28, 0, 7.767, 14.016, 0, 8, -18.11, 0, 8.233, 3.701, 0, 8.633, -0.797, 0, 8.8, 1.86, 0, 9.367, -2.047, 0, 10.333, 1.86, 0, 10.767, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUArmZ", "Segments": [0, 0, 0, 1.167, 4, 0, 2.133, -6.299, 0, 2.633, 6, 0, 3.267, -5.118, 0, 3.733, 20, 0, 3.933, -22, 0, 4.2, 28, 0, 4.467, -28, 0, 4.733, 30, 0, 5.033, -30, 0, 5.233, 30, 0, 5.467, -30, 0, 5.633, 30, 0, 5.867, -30, 0, 6.033, 30, 0, 6.167, -30, 0, 6.3, 30, 0, 6.4, -30, 0, 6.533, 30, 0, 6.667, -30, 0, 6.767, 30, 0, 6.9, -30, 0, 7, 30, 0, 7.1, -30, 0, 7.167, 30, 0, 7.267, -30, 0, 7.433, 28, 0, 7.667, -29.134, 0, 7.867, 18.189, 0, 8.133, -20.787, 0, 8.467, 4.496, 0, 8.733, -2.913, 0, 9, 1.89, 0, 9.533, -1, 0, 9.967, 1.024, 0, 10.433, -0.6, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineU", "Segments": [0, 0, 2, 5.9, 0, 1, 5.956, 0.333, 6.011, 0.667, 6.067, 1, 2, 6.1, 0, 2, 6.2, 0, 1, 6.244, 0.333, 6.289, 0.667, 6.333, 1, 2, 6.367, 0, 2, 6.433, 0, 1, 6.478, 0.333, 6.522, 0.667, 6.567, 1, 2, 6.6, 0, 2, 6.7, 0, 1, 6.733, 0.333, 6.767, 0.667, 6.8, 1, 2, 6.833, 0, 2, 6.933, 0, 1, 6.966, 0.333, 7, 0.667, 7.033, 1, 2, 7.067, 0, 2, 7.1, 0, 1, 7.133, 0.333, 7.167, 0.667, 7.2, 1, 2, 7.233, 0, 2, 7.3, 0, 1, 7.333, 0.333, 7.367, 0.667, 7.4, 1, 2, 7.433, 0, 2, 7.6, 0, 1, 7.678, 0.333, 7.755, 0.667, 7.833, 1, 2, 7.867, 0, 2, 8.2, 0, 2, 8.7, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineD", "Segments": [0, 0, 2, 5.667, 0, 1, 5.745, 0.333, 5.822, 0.667, 5.9, 1, 2, 5.933, 0, 2, 6.067, 0, 1, 6.111, 0.333, 6.156, 0.667, 6.2, 1, 2, 6.233, 0, 2, 6.333, 0, 1, 6.366, 0.333, 6.4, 0.667, 6.433, 1, 2, 6.467, 0, 2, 6.567, 0, 1, 6.611, 0.333, 6.656, 0.667, 6.7, 1, 2, 6.733, 0, 2, 6.8, 0, 1, 6.844, 0.333, 6.889, 0.667, 6.933, 1, 2, 6.967, 0, 2, 7.033, 0, 1, 7.066, 0.333, 7.1, 0.667, 7.133, 1, 2, 7.167, 0, 2, 7.2, 0, 1, 7.233, 0.333, 7.267, 0.667, 7.3, 1, 2, 7.333, 0, 2, 7.4, 0, 1, 7.467, 0.333, 7.533, 0.667, 7.6, 1, 2, 7.633, 0, 2, 7.967, 0, 1, 8.045, 0.333, 8.122, 0.667, 8.2, 1, 2, 8.233, 0, 2, 8.7, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyW", "Segments": [0, 0, 2, 1.967, 0, 0, 2.9, 0.27, 0, 3.267, -0.1, 0, 3.567, 0, 2, 3.733, 0, 0, 3.867, -0.3, 0, 4.1, 0.6, 0, 4.4, -0.4, 0, 4.7, 0.7, 0, 4.967, -0.582, 0, 5.2, 0.8, 0, 5.433, -0.5, 0, 5.6, 0.4, 0, 5.833, -0.6, 0, 6, 0.4, 0, 6.133, -0.5, 0, 6.3, 0.6, 0, 6.367, -0.5, 0, 6.467, 0.4, 0, 6.6, 0, 2, 6.733, 0, 0, 6.833, -0.3, 0, 6.967, 0, 0, 7.1, -0.3, 0, 7.2, 0.27, 0, 7.3, -0.315, 0, 7.433, 0.285, 0, 7.567, -0.291, 0, 7.833, 0, 0, 8, -0.2, 0, 8.1, 0, 2, 8.7, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionXPanda", "Segments": [0, 0, 2, 0.133, 0, 1, 1.7, 4.333, 3.266, 8.667, 4.833, 13, 2, 5.2, 13, 1, 7.1, 8.667, 9, 4.333, 10.9, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionYPanda", "Segments": [0, 0, 2, 0.133, 0, 0, 4.833, -0.679, 2, 5.2, -0.679, 0, 10.9, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.333, 0, 2, 4.933, 0, 0, 5.233, 1, 2, 10.967, 1, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyX", "Segments": [0, 0, 2, 0.1, 0, 2, 0.233, 0, 0, 0.367, 2.538, 0, 0.533, -2.538, 0, 0.7, 2.538, 0, 0.833, -2.538, 0, 1.067, 2.538, 0, 1.3, -2.538, 0, 1.567, 2.538, 0, 1.667, -2.538, 0, 1.9, 2.538, 0, 2.033, -2.538, 0, 2.267, 2.538, 0, 2.5, -2.538, 0, 2.667, 2.538, 0, 2.9, -2.538, 0, 3.033, 2.538, 0, 3.2, -2.538, 0, 3.5, 2.538, 0, 3.7, -2.538, 0, 3.833, 2.538, 0, 4, -2.538, 0, 4.2, 2.538, 0, 4.4, -2.538, 0, 4.633, 2.538, 0, 4.833, -2.538, 0, 5.033, 2.538, 0, 5.133, 0, 2, 5.433, 0, 0, 5.6, 2.538, 0, 5.8, -2.538, 0, 5.933, 2.538, 0, 6.1, -2.538, 0, 6.333, 2.538, 0, 6.533, -2.538, 0, 6.8, 2.538, 0, 6.933, -2.538, 0, 7.133, 2.538, 0, 7.3, -2.538, 0, 7.5, 2.538, 0, 7.7, -2.538, 0, 7.867, 2.538, 0, 8.1, -2.538, 0, 8.333, 2.538, 0, 8.5, -2.538, 0, 8.8, 2.538, 0, 9, -2.538, 0, 9.133, 2.538, 0, 9.333, -2.538, 0, 9.533, 2.538, 0, 9.733, -2.538, 0, 9.933, 2.538, 0, 10.1, -2.538, 0, 10.267, 2.538, 0, 10.567, -2.538, 0, 10.7, 2.538, 0, 10.833, -2.538, 0, 11.067, 2.538, 0, 11.167, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyY", "Segments": [0, 0, 2, 0.133, 0, 0, 0.3, 3.183, 0, 0.467, -3.183, 0, 0.6, 3.183, 0, 0.767, -3.183, 0, 1, 3.183, 0, 1.233, -3.183, 0, 1.5, 3.183, 0, 1.6, -3.183, 0, 1.8, 3.183, 0, 1.967, -3.183, 0, 2.2, 3.183, 0, 2.433, -3.183, 0, 2.6, 3.183, 0, 2.8, -3.183, 0, 2.967, 3.183, 0, 3.133, -3.183, 0, 3.4, 3.183, 0, 3.633, -3.183, 0, 3.767, 3.183, 0, 3.933, -3.183, 0, 4.133, 3.183, 0, 4.333, -3.183, 0, 4.533, 3.183, 0, 4.733, -3.183, 0, 4.967, 3.183, 0, 5.067, 0, 2, 5.367, 0, 0, 5.533, 3.183, 0, 5.7, -3.183, 0, 5.867, 3.183, 0, 6.033, -3.183, 0, 6.267, 3.183, 0, 6.467, -3.183, 0, 6.733, 3.183, 0, 6.833, -3.183, 0, 7.067, 3.183, 0, 7.233, -3.183, 0, 7.433, 3.183, 0, 7.633, -3.183, 0, 7.8, 3.183, 0, 8, -3.183, 0, 8.2, 3.183, 0, 8.4, -3.183, 0, 8.733, 3.183, 0, 8.9, -3.183, 0, 9.067, 3.183, 0, 9.233, -3.183, 0, 9.467, 3.183, 0, 9.633, -3.183, 0, 9.867, 3.183, 0, 10.033, -3.183, 0, 10.2, 3.183, 0, 10.5, -3.183, 0, 10.633, 3.183, 0, 10.767, -3.183, 0, 11, 3.183, 0, 11.1, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2Panda", "Segments": [0, 0, 2, 0.133, 0, 0, 0.233, 6.229, 0, 0.3, -6.213, 1, 0.322, -6.213, 0.345, 6.229, 0.367, 6.229, 0, 0.467, -6.229, 0, 0.567, 6.229, 0, 0.633, -6.229, 0, 0.733, 6.229, 0, 0.833, -6.229, 0, 0.933, 6.229, 0, 1.033, -6.229, 0, 1.167, 6.229, 0, 1.267, -6.229, 0, 1.333, 6.229, 0, 1.433, -6.213, 0, 1.5, 6.229, 0, 1.6, -6.229, 0, 1.667, 6.229, 0, 1.767, -6.229, 0, 1.867, 6.229, 0, 1.967, -6.229, 0, 2.033, 6.229, 0, 2.167, -6.229, 0, 2.233, 6.229, 0, 2.367, -6.229, 0, 2.467, 6.229, 0, 2.533, -6.229, 0, 2.633, 6.229, 0, 2.733, -6.229, 0, 2.8, 6.229, 0, 2.9, -6.229, 0, 3, 6.229, 0, 3.1, -6.229, 0, 3.167, 6.229, 0, 3.267, -6.229, 0, 3.367, 6.229, 0, 3.467, -6.229, 0, 3.567, 6.229, 0, 3.633, -6.229, 0, 3.733, 6.229, 0, 3.867, -6.213, 0, 3.933, 6.229, 0, 4, -6.229, 0, 4.1, 6.229, 0, 4.2, -6.213, 0, 4.267, 6.229, 0, 4.333, -6.213, 0, 4.433, 6.229, 0, 4.567, -6.229, 0, 4.7, 6.229, 0, 4.8, -6.213, 0, 4.867, 6.229, 0, 4.9, 0, 2, 5.2, 0, 0, 5.3, -6.229, 0, 5.4, 6.229, 0, 5.467, -6.229, 0, 5.567, 6.229, 0, 5.667, -6.229, 0, 5.8, 6.229, 0, 5.867, -6.229, 0, 5.967, 6.229, 0, 6.067, -6.229, 0, 6.133, 6.229, 0, 6.2, -6.229, 0, 6.333, 6.229, 0, 6.4, -6.229, 0, 6.5, 6.229, 0, 6.567, -6.229, 0, 6.667, 6.229, 0, 6.767, -6.229, 0, 6.9, 6.229, 0, 7, -6.229, 0, 7.067, 6.229, 0, 7.167, -6.229, 0, 7.233, 6.229, 0, 7.3, -6.229, 0, 7.433, 6.229, 0, 7.533, -6.229, 0, 7.6, 6.229, 0, 7.7, -6.229, 0, 7.767, 6.229, 0, 7.867, -6.229, 0, 7.967, 6.229, 0, 8.1, -6.229, 0, 8.2, 6.229, 0, 8.333, -6.229, 0, 8.4, 6.229, 0, 8.5, -6.229, 0, 8.633, 6.229, 0, 8.733, -6.229, 0, 8.833, 6.229, 0, 8.9, -6.229, 0, 9, 6.229, 0, 9.067, -6.229, 0, 9.167, 6.229, 0, 9.3, -6.229, 0, 9.4, 6.229, 0, 9.5, -6.229, 0, 9.567, 6.229, 0, 9.633, -6.229, 0, 9.733, 6.229, 0, 9.833, -6.229, 0, 9.933, 6.229, 0, 10, -6.229, 0, 10.1, 6.229, 0, 10.2, -6.229, 0, 10.267, 6.229, 0, 10.367, -6.229, 0, 10.5, 6.229, 0, 10.6, -6.229, 0, 10.667, 6.229, 0, 10.733, -6.229, 0, 10.833, 6.229, 0, 10.933, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyZ", "Segments": [0, 0, 2, 0.1, 0, 0, 0.233, 1.431, 0, 0.433, -1.431, 0, 0.567, 1.431, 0, 0.7, -1.431, 0, 0.933, 1.431, 0, 1.2, -1.431, 0, 1.433, 1.431, 0, 1.567, -1.431, 0, 1.767, 1.431, 0, 1.9, -1.431, 0, 2.167, 1.431, 0, 2.4, -1.431, 0, 2.567, 1.431, 0, 2.767, -1.431, 0, 2.933, 1.431, 0, 3.067, -1.431, 0, 3.367, 1.431, 0, 3.567, -1.431, 0, 3.7, 1.431, 0, 3.9, -1.431, 0, 4.1, 1.431, 0, 4.267, -1.431, 0, 4.467, 1.431, 0, 4.7, -1.431, 0, 4.9, 1.431, 1, 4.944, 1.431, 4.989, 1.61, 5.033, 0, 1, 5.078, -1.61, 5.122, -30, 5.167, -30, 0, 5.467, 1.431, 0, 5.667, -1.431, 0, 5.833, 1.431, 0, 5.967, -1.431, 0, 6.2, 1.431, 0, 6.4, -1.431, 0, 6.667, 1.431, 0, 6.8, -1.431, 0, 7.033, 1.431, 0, 7.167, -1.431, 0, 7.367, 1.431, 0, 7.6, -1.431, 0, 7.767, 1.431, 0, 7.967, -1.431, 0, 8.167, 1.431, 0, 8.367, -1.431, 0, 8.667, 1.431, 0, 8.867, -1.431, 0, 9, 1.431, 0, 9.2, -1.431, 0, 9.4, 1.431, 0, 9.6, -1.431, 0, 9.8, 1.431, 0, 10, -1.431, 0, 10.133, 1.431, 0, 10.467, -1.431, 0, 10.6, 1.431, 0, 10.733, -1.431, 0, 10.967, 1.431, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegFZ", "Segments": [0, 0, 2, 0.133, 0, 0, 0.3, -1, 0, 0.5, 1, 0, 0.667, -1, 0, 0.9, 1, 0, 1.1, -1, 0, 1.367, 1, 0, 1.533, -1, 0, 1.733, 1, 0, 1.9, -1, 0, 2.167, 1, 0, 2.367, -1, 0, 2.567, 1, 0, 2.733, -1, 0, 2.967, 1, 0, 3.167, -1, 0, 3.367, 1, 0, 3.6, -1, 0, 3.767, 1, 0, 3.967, -1, 0, 4.167, 1, 0, 4.367, -1, 0, 4.567, 1, 0, 4.767, -1, 0, 4.867, 0, 2, 5.167, 0, 0, 5.333, 1, 0, 5.5, -1, 0, 5.767, 1, 0, 5.967, -1, 0, 6.2, 1, 0, 6.367, -1, 0, 6.567, 1, 0, 6.7, -1, 0, 7, 1, 0, 7.167, -1, 0, 7.367, 1, 0, 7.533, -1, 0, 7.767, 1, 0, 7.967, -1, 0, 8.2, 1, 0, 8.433, -1, 0, 8.667, 1, 0, 8.867, -1, 0, 9.1, 1, 0, 9.333, -1, 0, 9.5, 1, 0, 9.7, -1, 0, 9.9, 1, 0, 10.167, -1, 0, 10.433, 1, 0, 10.567, -1, 0, 10.833, 1, 0, 10.933, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegBZ", "Segments": [0, 0, 2, 0.133, 0, 0, 0.3, -1, 0, 0.5, 1, 0, 0.667, -1, 0, 0.9, 1, 0, 1.1, -1, 0, 1.367, 1, 0, 1.533, -1, 0, 1.733, 1, 0, 1.9, -1, 0, 2.167, 1, 0, 2.367, -1, 0, 2.567, 1, 0, 2.733, -1, 0, 2.967, 1, 0, 3.167, -1, 0, 3.367, 1, 0, 3.6, -1, 0, 3.767, 1, 0, 3.967, -1, 0, 4.167, 1, 0, 4.367, -1, 0, 4.567, 1, 0, 4.767, -1, 0, 4.867, 0, 2, 5.167, 0, 0, 5.333, 1, 0, 5.5, -1, 0, 5.767, 1, 0, 5.967, -1, 0, 6.2, 1, 0, 6.367, -1, 0, 6.567, 1, 0, 6.7, -1, 0, 7, 1, 0, 7.167, -1, 0, 7.367, 1, 0, 7.533, -1, 0, 7.767, 1, 0, 7.967, -1, 0, 8.2, 1, 0, 8.433, -1, 0, 8.667, 1, 0, 8.867, -1, 0, 9.1, 1, 0, 9.333, -1, 0, 9.5, 1, 0, 9.7, -1, 0, 9.9, 1, 0, 10.167, -1, 0, 10.433, 1, 0, 10.567, -1, 0, 10.833, 1, 0, 10.933, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyX", "Segments": [0, 0, 0, 1.167, 5.186, 0, 3.3, -4.826, 0, 5.333, 3.384, 0, 5.867, 0, 0, 7.067, 5.186, 0, 9.3, -4.826, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyY", "Segments": [0, 0, 0, 0.833, 1.561, 0, 3.2, -1.599, 0, 4.867, 1.177, 0, 5.867, 0, 0, 6.733, 1.561, 0, 9.167, -1.599, 0, 10.733, 0.668, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonZ", "Segments": [0, 0, 0, 2.367, 9.63, 0, 5.2, -2.048, 1, 5.422, -2.048, 5.645, -1.64, 5.867, 0, 1, 6.689, 6.069, 7.511, 9.63, 8.333, 9.63, 0, 10.733, -0.611, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonGaY", "Segments": [0, 0, 0, 1.5, 3.807, 0, 3.367, -1.392, 0, 5.067, 3.53, 0, 5.867, 0, 0, 7.433, 3.807, 0, 9.4, -1.392, 0, 10.9, 0.63, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupY", "Segments": [0, 0, 0, 2.033, -0.17, 0, 3.6, 0.126, 0, 5.267, -0.04, 0, 5.867, 0, 0, 7.933, -0.17, 0, 9.6, 0.126, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupZ", "Segments": [0, 0, 2, 0.3, 0, 0, 3.033, -0.149, 0, 4.433, 0.113, 0, 5.867, 0, 2, 6.233, 0, 0, 9, -0.149, 0, 10.433, 0.113, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonHandZ", "Segments": [0, 0, 0, 2.4, -1.699, 0, 3.733, 1.258, 0, 5.1, -0.396, 0, 5.867, 0, 0, 8.333, -1.699, 0, 9.7, 1.258, 0, 10.867, -0.396, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupIce", "Segments": [0, 0, 2, 0.833, 0, 1, 0.922, 0, 1.011, 0.67, 1.1, 1.079, 1, 1.567, 3.225, 2.033, 5.061, 2.5, 7.031, 1, 2.7, 7.875, 2.9, 8.625, 3.1, 9.521, 1, 3.133, 9.663, 3.167, 9.805, 3.2, 9.947, 2, 3.233, 0, 1, 3.266, 0, 3.3, 0.452, 3.333, 0.609, 1, 4, 3.752, 4.666, 6.681, 5.333, 9.761, 1, 5.344, 9.813, 5.356, 9.947, 5.367, 9.947, 2, 5.4, 0, 1, 5.456, 0, 5.511, 0.356, 5.567, 0.609, 1, 5.978, 2.481, 6.389, 4.099, 6.8, 5.953, 1, 6.933, 6.555, 7.067, 7.518, 7.2, 8.019, 1, 7.333, 8.519, 7.467, 9.005, 7.6, 9.199, 1, 7.822, 9.524, 8.045, 9.852, 8.267, 9.947, 1, 8.422, 10, 8.578, 10, 8.733, 10, 2, 8.767, 0, 2, 10.1, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupZ", "Segments": [0, 0, 2, 0.967, 0, 0, 1.267, 0.59, 0, 1.567, -0.59, 0, 1.867, 0.59, 0, 2.133, -0.59, 0, 2.433, 0.59, 0, 2.7, -0.59, 0, 3.033, 0.59, 0, 3.3, -0.59, 0, 3.567, 0.59, 0, 3.9, -0.59, 0, 4.167, 0.59, 0, 4.433, -0.59, 0, 4.767, 0.59, 0, 5.033, -0.59, 0, 5.333, 0.552, 0, 5.633, -0.532, 0, 5.867, 0.478, 0, 6.167, -0.511, 0, 6.433, 0.368, 0, 6.7, -0.339, 0, 6.967, 0.369, 0, 7.3, -0.307, 0, 7.6, 0.277, 0, 8, -0.185, 0, 8.233, 0, 2, 8.9, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupInput", "Segments": [0, 0, 2, 1.033, 0, 0, 1.267, 10.429, 0, 1.533, -10.544, 0, 1.8, 10.429, 0, 2.067, -10.544, 0, 2.333, 10.429, 0, 2.6, -10.544, 0, 2.867, 10.429, 0, 3.133, -10.544, 0, 3.4, 10.429, 0, 3.667, -10.544, 0, 3.933, 10.429, 0, 4.2, -10.544, 0, 4.467, 10.429, 0, 4.733, -10.544, 0, 5, 10.429, 0, 5.267, -10.544, 0, 5.533, 7.818, 0, 5.833, -6.964, 0, 6.033, 4.654, 0, 6.3, -6.094, 0, 6.567, 2.765, 0, 6.8, -4.077, 0, 6.967, 1.62, 0, 7.2, -6.964, 0, 7.4, 7.818, 0, 7.667, -6.094, 0, 8.067, 4.654, 0, 8.533, -1.313, 0, 8.933, 1.257, 0, 9.3, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamParamStrongCatZ", "Segments": [0, 0, 2, 0.667, 0, 0, 1.5, -7.238, 0, 2.133, 8.691, 0, 2.533, -15.57, 0, 2.833, -7.731, 0, 3.467, -11.653, 0, 4.167, -8.731, 0, 5.333, -11.511, 0, 7, -7.238, 0, 7.9, -13.101, 0, 8.7, 2.702, 0, 9.433, -4.522, 0, 10.433, 0, 2, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamParamSCBodyZ", "Segments": [0, 0, 0, 1.1, 2, 0, 1.8, -2.233, 0, 2.333, 10.56, 0, 2.7, -10.896, 0, 3.033, -7.731, 0, 3.667, -11.814, 0, 4.467, -8.023, 0, 6, -10.558, 0, 7.6, -7.731, 0, 8.2, -12.893, 0, 9.133, 2.999, 0, 10.633, -1, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamSCDishY", "Segments": [0, 0, 2, 0.733, 0, 0, 1.567, -2.555, 0, 2.2, 8.644, 0, 2.6, -6.809, 0, 3.133, 8.004, 0, 3.867, -2.967, 0, 4.4, 2.348, 0, 5.267, -1.652, 0, 7.067, 2.86, 0, 9.167, -0.982, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamSCDishZ", "Segments": [0, 0, 0, 1.167, -3.153, 1, 1.4, -3.153, 1.634, -3.036, 1.867, 0, 1, 2.045, 2.313, 2.222, 9.455, 2.4, 9.455, 0, 2.767, -4.423, 0, 3.3, 8.057, 0, 4.2, -3.543, 0, 4.833, 1.437, 0, 5.767, -1.686, 0, 7.833, 2.177, 0, 10.033, -1.686, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamSCCupZ", "Segments": [0, 0, 0, 2.433, -5.646, 0, 4.967, 3.433, 1, 5.267, 3.433, 5.567, 1.5, 5.867, 0, 1, 6.711, -4.222, 7.556, -5.646, 8.4, -5.646, 0, 10.867, 2.554, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "MB_yanwubaozha", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "MB_DRFWXZKTMD", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "Parameter", "Id": "ParamAllSizeFix", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBG2Hide", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBGX", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBGY", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN2", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBlackY", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBlackCollar", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBlackOrder", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamWhiteIN", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamCHHide", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "Parameter", "Id": "ParamDeskHide", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "Parameter", "Id": "ParamStoolHide", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "Parameter", "Id": "ParamCupDesk", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamCHX", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamCHY", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamCHZ", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamChaSize", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamCcharacterZ", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionX", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionY", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionX", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionY", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamAllZ", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamAllSize", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamALLSize2", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamDeskShow", "Segments": [0, 10, 0, 11.233, 10]}, {"Target": "Parameter", "Id": "ParamStrongCatShow", "Segments": [0, 10, 0, 11.233, 10]}, {"Target": "Parameter", "Id": "ParamCannonShow", "Segments": [0, 10, 0, 11.233, 10]}, {"Target": "Parameter", "Id": "ParamLightPositionX", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamFixT", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "Parameter", "Id": "ParamFlap", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamScare", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpen2", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthType", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthY", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamTeethLight", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamHeart2", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamMark", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamShameLine", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamMarkShake", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeRLightOpen", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLightLine1", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLightLine2", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLightLine3", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLightShine", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo1", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo2", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo3", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1Y", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle2", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow1", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow2", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamTearLight", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamTears", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamAngleH", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamAngleS", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperLAngle", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamArmHandLAngle", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerLAngle", "Segments": [0, 10, 0, 11.233, 10]}, {"Target": "Parameter", "Id": "ParamArmLowerLH", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerLAngle", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamHandT2L", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamFanOpenR", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperRH", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamHandT1R", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamHandRCup", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamHandRMail", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "Parameter", "Id": "ParamHandLIQY1", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY2", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY3", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamHandCupZ", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamHandCupY", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechW", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Z", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLegR1X", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Z", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Z", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Y", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Y", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Y", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamFootRX", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Z", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Z", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Z", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Y", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Y", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Y", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamFootLX", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLegLF", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRHide", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "Parameter", "Id": "ParamMJRFlap", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuR", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuR", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuR", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuR", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRInput", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamMRCupSet", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "Parameter", "Id": "ParamMalpositionManjuuR", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRArmB", "Segments": [0, 30, 0, 11.233, 30]}, {"Target": "Parameter", "Id": "ParamManjuuRSigh", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow1", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow2", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow3", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow4", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowB", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW2", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamMRCupFZ", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX1", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX2", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqH", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX1", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX2", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX3", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLHide", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "Parameter", "Id": "ParamMJLSigh", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuL", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuL", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUHide", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuU", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuU", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuU", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamPandaHide", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "Parameter", "Id": "ParamSizePanda", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY1", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY2", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY3", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamSCDishRO", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamSCCupRO", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "Parameter", "Id": "ParamSCCupY", "Segments": [0, 0, 0, 11.233, 0]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 11.233, 1]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 0, 11.233, 1]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.0, "Value": ""}, {"Time": 10.733, "Value": ""}]}