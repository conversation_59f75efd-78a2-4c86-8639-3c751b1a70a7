{"Version": 3, "Meta": {"Duration": 19.233, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 252, "TotalSegmentCount": 17670, "TotalPointCount": 22890, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamBGHide", "Segments": [0, 0, 0, 0.967, 1, 2, 18.267, 1, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBlackY", "Segments": [0, 0, 0, 1, 3.994, 2, 18.267, 3.994, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionX", "Segments": [0, 0, 2, 7.933, 0, 0, 8.967, 13.805, 2, 10.733, 13.805, 0, 11.6, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionY", "Segments": [0, 0, 0, 2.4, -30, 2, 18.167, -30, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionX", "Segments": [0, 0, 2, 7.933, 0, 0, 8.967, -0.928, 2, 10.733, -0.928, 0, 11.6, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionY", "Segments": [0, 0, 0, 2.433, -3.148, 2, 18.067, -3.148, 0, 19, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamAllSize", "Segments": [0, 0, 0, 2.433, 1.752, 0, 7.933, 1.75, 0, 8.967, 2.525, 2, 10.733, 2.525, 0, 11.6, 1.75, 0, 18.067, 1.752, 0, 19, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamStrongCatShow", "Segments": [0, 10, 2, 0.6, 10, 1, 0.722, 10, 0.845, 10, 0.967, 8.234, 1, 1.089, 6.362, 1.211, 0, 1.333, 0, 2, 18.1, 0, 0, 19, 10, 2, 19.233, 10]}, {"Target": "Parameter", "Id": "ParamCannonShow", "Segments": [0, 10, 2, 5.467, 10, 2, 8.3, 10, 0, 8.567, 9.1, 0, 9, 10, 2, 19.233, 10]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.2, 1, 0, 0.367, 0, 2, 0.4, 0, 0, 0.567, 1, 2, 0.833, 1, 0, 0.933, 0.8, 2, 1.067, 0.8, 0, 1.2, 0.956, 0, 1.267, 0.848, 0, 1.367, 1, 2, 2.167, 1, 0, 2.333, 0, 2, 3.033, 0, 0, 3.233, 1, 2, 3.367, 1, 1, 3.422, 1, 3.478, 0.962, 3.533, 0.9, 1, 3.566, 0.863, 3.6, 0.875, 3.633, 0.8, 1, 3.689, 0.674, 3.744, 0, 3.8, 0, 2, 4.567, 0, 1, 4.611, 0, 4.656, 0.569, 4.7, 0.703, 1, 4.789, 0.971, 4.878, 1, 4.967, 1, 2, 6, 1, 0, 6.133, 0, 0, 6.233, 1, 2, 8.033, 1, 0, 8.4, 0.7, 1, 8.656, 0.7, 8.911, 0.7, 9.167, 0.702, 0, 9.467, 1, 0, 9.6, 0, 2, 9.833, 0, 1, 9.944, 0, 10.056, 0.668, 10.167, 0.7, 1, 10.322, 0.744, 10.478, 0.74, 10.633, 0.74, 0, 11.2, 0.6, 0, 11.767, 1, 1, 12.1, 1, 12.434, 0.965, 12.767, 0.87, 1, 12.8, 0.86, 12.834, 0, 12.867, 0, 0, 13, 0.87, 2, 15.667, 0.87, 0, 15.8, 0, 0, 15.9, 1, 2, 16.133, 1, 2, 17.2, 1, 0, 17.4, 0, 2, 17.633, 0, 0, 17.833, 1, 2, 18.5, 1, 0, 18.6, 0, 0, 18.967, 1, 2, 19.233, 1]}, {"Target": "Parameter", "Id": "ParamEyeSmileL", "Segments": [0, 0, 2, 2.033, 0, 0, 2.167, 1, 2, 3.033, 1, 2, 6.8, 1, 2, 11.4, 1, 2, 17.567, 1, 1, 17.656, 1, 17.744, 0.989, 17.833, 0.884, 1, 18.3, 0.334, 18.766, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.2, 1, 0, 0.367, 0, 2, 0.4, 0, 0, 0.567, 1, 2, 0.833, 1, 0, 0.933, 0.8, 2, 1.067, 0.8, 0, 1.2, 0.956, 0, 1.267, 0.848, 0, 1.367, 1, 2, 2.167, 1, 0, 2.333, 0, 2, 3.033, 0, 0, 3.233, 1, 2, 3.367, 1, 1, 3.422, 1, 3.478, 0.962, 3.533, 0.9, 1, 3.566, 0.863, 3.6, 0.875, 3.633, 0.8, 1, 3.689, 0.674, 3.744, 0, 3.8, 0, 2, 4.567, 0, 1, 4.611, 0, 4.656, 0.569, 4.7, 0.703, 1, 4.789, 0.971, 4.878, 1, 4.967, 1, 2, 6, 1, 0, 6.133, 0, 0, 6.233, 1, 2, 8.033, 1, 0, 8.4, 0.7, 1, 8.656, 0.7, 8.911, 0.7, 9.167, 0.702, 0, 9.467, 1, 0, 9.6, 0, 2, 9.833, 0, 1, 9.944, 0, 10.056, 0.668, 10.167, 0.7, 1, 10.322, 0.744, 10.478, 0.74, 10.633, 0.74, 0, 11.2, 0.6, 0, 11.767, 1, 1, 12.1, 1, 12.434, 0.965, 12.767, 0.87, 1, 12.8, 0.86, 12.834, 0, 12.867, 0, 0, 13, 0.87, 2, 15.667, 0.87, 0, 15.8, 0, 0, 15.9, 1, 2, 16.333, 1, 0, 16.567, 0, 2, 17, 0, 0, 17.2, 0.3, 0, 17.4, 0, 2, 17.633, 0, 0, 17.833, 1, 2, 18.5, 1, 0, 18.6, 0, 0, 18.967, 1, 2, 19.233, 1]}, {"Target": "Parameter", "Id": "ParamEyeSmileR", "Segments": [0, 0, 2, 2.033, 0, 0, 2.167, 1, 2, 3.033, 1, 2, 6.8, 1, 2, 11.4, 1, 2, 17.567, 1, 1, 17.656, 1, 17.744, 0.989, 17.833, 0.884, 1, 18.3, 0.334, 18.766, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpen2", "Segments": [0, 0, 2, 17, 0, 0, 17.367, 1.5, 2, 17.8, 1.5, 0, 18.133, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthType", "Segments": [0, 0, 2, 16.9, 0, 2, 16.933, 1, 2, 18.2, 1, 2, 18.233, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 2, 0.167, 0, 0, 0.5, -1, 2, 1.767, -1, 0, 1.833, -0.5, 1, 1.9, -0.5, 1.966, -0.529, 2.033, -0.7, 1, 2.078, -0.814, 2.122, -1, 2.167, -1, 1, 2.222, -1, 2.278, -0.86, 2.333, -0.7, 1, 2.389, -0.54, 2.444, -0.5, 2.5, -0.5, 0, 2.833, -1, 0, 3.267, -0.5, 2, 3.4, -0.5, 1, 3.467, -0.5, 3.533, -0.546, 3.6, -0.6, 1, 3.656, -0.645, 3.711, -0.667, 3.767, -0.71, 1, 3.789, -0.727, 3.811, -0.77, 3.833, -0.8, 1, 3.933, -0.934, 4.033, -1, 4.133, -1, 2, 4.4, -1, 0, 4.667, -0.5, 0, 5.433, -0.828, 0, 5.667, -0.5, 2, 5.8, -0.5, 0, 6, -0.2, 0, 6.167, -0.5, 2, 6.533, -0.5, 0, 6.733, -0.2, 0, 6.9, -0.5, 0, 7.467, -0.38, 0, 7.967, -0.4, 0, 8.167, -0.2, 0, 8.533, -0.5, 2, 8.633, -0.5, 2, 8.9, -0.5, 0, 9.3, -0.7, 0, 9.567, -0.5, 2, 9.833, -0.5, 0, 9.967, -1, 0, 10.1, 0, 0, 10.233, -0.3, 2, 10.3, -0.3, 1, 10.344, -0.3, 10.389, -0.135, 10.433, 0, 1, 10.511, 0.237, 10.589, 0.3, 10.667, 0.3, 1, 10.745, 0.3, 10.822, 0.25, 10.9, 0, 1, 10.978, -0.25, 11.055, -0.5, 11.133, -0.5, 2, 11.4, -0.5, 0, 11.567, -0.69, 0, 12, -0.647, 1, 12.489, -0.647, 12.978, -0.663, 13.467, -0.7, 1, 13.489, -0.702, 13.511, -0.888, 13.533, -0.916, 1, 13.589, -0.986, 13.644, -1, 13.7, -1, 0, 13.9, -0.828, 0, 14.267, -0.9, 0, 14.533, -0.7, 0, 14.9, -0.9, 0, 15.333, -0.753, 0, 15.533, -0.867, 1, 15.589, -0.867, 15.644, -0.828, 15.7, -0.684, 1, 15.733, -0.598, 15.767, -0.5, 15.8, -0.5, 0, 16, -0.881, 0, 16.133, -0.655, 0, 16.333, -1, 0, 16.533, 1, 2, 16.867, 1, 2, 17.033, 1, 1, 17.111, 1, 17.189, 0.895, 17.267, 0.5, 2, 17.333, 0, 2, 17.533, 0, 0, 18, 1, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.033, 0, 0, 0.467, 0.9, 0, 0.6, 0.8, 2, 0.733, 0.8, 0, 0.9, 1, 0, 0.967, 0.93, 0, 1.033, 1, 2, 1.567, 1, 1, 1.656, 1, 1.744, 0.867, 1.833, 0.6, 1, 1.9, 0.4, 1.966, 0.3, 2.033, 0.3, 0, 2.167, 1, 0, 2.333, 0.5, 0, 2.5, 0.8, 0, 2.567, 0.682, 1, 2.589, 0.682, 2.611, 0.779, 2.633, 0.8, 1, 2.789, 0.947, 2.944, 1, 3.1, 1, 0, 3.267, 0.5, 2, 3.567, 0.5, 1, 3.634, 0.5, 3.7, 0.479, 3.767, 0.4, 1, 3.789, 0.374, 3.811, 0.31, 3.833, 0.31, 0, 4.133, 0.7, 0, 4.333, 0.6, 0, 4.433, 0.66, 0, 4.533, 0.6, 0, 4.667, 0.7, 2, 4.933, 0.7, 0, 5.133, 0.8, 1, 5.233, 0.8, 5.333, 0.803, 5.433, 0.7, 1, 5.511, 0.62, 5.589, 0.3, 5.667, 0.3, 2, 5.833, 0.3, 0, 6.167, 0.5, 0, 6.567, 0.3, 0, 6.9, 0.7, 1, 7.089, 0.7, 7.278, 0.687, 7.467, 0.54, 1, 7.634, 0.41, 7.8, 0.2, 7.967, 0.2, 0, 8.167, 0.7, 0, 8.333, 0.631, 0, 8.467, 1, 2, 8.533, 1, 0, 8.6, 0.2, 0, 8.9, 1, 0, 9.033, 0.745, 0, 9.167, 1, 0, 9.267, 0.745, 0, 9.433, 1, 0, 9.567, 0.745, 0, 9.767, 1, 1, 9.8, 1, 9.834, 0.796, 9.867, 0.745, 1, 9.9, 0.694, 9.934, 0.7, 9.967, 0.7, 0, 10.1, 1, 2, 10.2, 1, 0, 10.667, 0.1, 2, 10.9, 0.1, 1, 10.956, 0.1, 11.011, 0.271, 11.067, 0.5, 1, 11.122, 0.729, 11.178, 0.8, 11.233, 0.8, 0, 11.4, 0.5, 0, 11.567, 1, 0, 11.7, 0.745, 0, 11.833, 1, 0, 12.133, 0.745, 2, 12.367, 0.745, 0, 12.5, 1, 0, 12.6, 0.745, 0, 12.767, 1, 0, 12.9, 0.745, 0, 13.033, 1, 0, 13.133, 0.745, 0, 13.267, 0.8, 0, 13.467, 0.5, 2, 13.567, 0.5, 0, 13.667, 0.745, 0, 13.767, 0.6, 0, 13.867, 0.763, 0, 13.9, 0.748, 0, 14, 0.83, 0, 14.133, 0.745, 0, 14.267, 1, 0, 14.533, 0.5, 1, 14.555, 0.5, 14.578, 0.686, 14.6, 0.745, 1, 14.622, 0.804, 14.645, 0.799, 14.667, 0.799, 0, 14.867, 0.645, 0, 15.033, 1, 0, 15.167, 0.745, 0, 15.3, 0.8, 1, 15.333, 0.8, 15.367, 0.767, 15.4, 0.7, 1, 15.433, 0.633, 15.467, 0.6, 15.5, 0.6, 0, 15.567, 0.93, 0, 15.667, 0.545, 0, 15.8, 1, 0, 15.933, 0.545, 0, 16.067, 0.8, 0, 16.167, 0.545, 0, 16.333, 1, 1, 16.366, 1, 16.4, 0.557, 16.433, 0.5, 1, 16.533, 0.33, 16.633, 0.228, 16.733, 0.1, 1, 16.789, 0.029, 16.844, 0, 16.9, 0, 2, 17.267, 0, 2, 17.3, 0, 2, 17.333, 1, 2, 17.533, 1, 0, 17.967, 0, 2, 18.4, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthSize", "Segments": [0, 0, 1, 0.167, 0, 0.333, -0.1, 0.5, -0.4, 1, 0.556, -0.5, 0.611, -0.642, 0.667, -0.642, 0, 1.167, 0, 2, 2.033, 0, 0, 2.167, -0.6, 0, 2.333, 0, 2, 2.567, 0, 0, 2.633, -0.501, 0, 2.933, 0, 2, 3.067, 0, 0, 3.5, -0.8, 0, 4.2, 0, 0, 4.467, -0.5, 0, 4.7, 0, 2, 6, 0, 0, 6.167, -0.6, 2, 6.533, -0.6, 0, 7.233, 0, 2, 7.467, 0, 2, 8.3, 0, 0, 8.533, -0.5, 0, 8.9, 0, 1, 9, 0, 9.1, -0.499, 9.2, -0.5, 1, 9.267, -0.501, 9.333, -0.5, 9.4, -0.501, 1, 9.478, -0.502, 9.555, -1, 9.633, -1, 0, 9.767, -0.7, 2, 9.967, -0.7, 0, 10.133, 0, 0, 10.567, -0.3, 2, 10.9, -0.3, 0, 11.133, -1, 0, 11.4, -0.249, 2, 11.6, -0.249, 0, 11.833, -0.501, 0, 12.067, -0.5, 0, 12.333, -0.8, 0, 12.533, -0.5, 0, 12.833, -1, 2, 13.133, -1, 0, 13.267, -0.249, 1, 13.334, -0.249, 13.4, -0.569, 13.467, -0.8, 1, 13.522, -0.992, 13.578, -1, 13.633, -1, 1, 13.655, -1, 13.678, -1, 13.7, -0.8, 1, 13.733, -0.482, 13.767, 0, 13.8, 0, 0, 14.033, -0.4, 0, 14.367, 0, 0, 14.5, -0.5, 0, 14.733, 0, 1, 14.789, 0, 14.844, -0.134, 14.9, -0.5, 1, 14.944, -0.793, 14.989, -1, 15.033, -1, 1, 15.066, -1, 15.1, -0.606, 15.133, -0.4, 1, 15.189, -0.057, 15.244, 0, 15.3, 0, 0, 15.6, -0.7, 1, 15.844, -0.7, 16.089, -0.699, 16.333, -0.5, 1, 16.544, -0.328, 16.756, 0.1, 16.967, 0.1, 1, 17.256, 0.1, 17.544, 0.08, 17.833, 0.055, 1, 18.3, 0.015, 18.766, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeEmotion", "Segments": [0, 0, 0, 0.4, 1, 2, 0.567, 1, 0, 1, -0.9, 0, 1.5, 1, 2, 2.467, 1, 0, 2.767, -1, 0, 3.067, 1, 2, 3.7, 1, 0, 4.133, -1, 2, 4.7, -1, 0, 4.967, 1, 0, 5.2, 0.566, 2, 6.6, 0.566, 0, 6.8, 1, 0, 7.133, 0, 2, 7.8, 0, 0, 8.5, -0.5, 2, 8.8, -0.5, 0, 9.033, 0.6, 0, 9.267, 0, 2, 11, 0, 0, 11.333, -0.4, 2, 11.867, -0.4, 0, 12.2, 0.6, 2, 13.667, 0.6, 0, 14, -1, 2, 15.867, -1, 0, 16.167, 1, 0, 16.533, -0.6, 2, 16.8, -0.6, 0, 16.967, 0, 2, 17.233, 0, 0, 17.4, 1, 0, 17.733, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 2, 0.733, 0, 0, 1.4, 1, 2, 8.133, 1, 0, 8.7, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamHeart2", "Segments": [0, 0, 2, 8.7, 0, 2, 16.567, 0, 1, 16.656, 0, 16.744, 0.31, 16.833, 0.52, 1, 17.055, 1.045, 17.278, 1.2, 17.5, 1.2, 2, 17.667, 1.2, 0, 17.967, 1.5, 2, 18, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLX", "Segments": [0, 0, 2, 5.467, 0, 2, 10.9, 0, 0, 11.4, 0.6, 2, 13.333, 0.6, 0, 13.833, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeL", "Segments": [0, 0, 0, 0.333, -0.23, 0, 0.733, 0.006, 1, 0.789, 0.006, 0.844, 0.007, 0.9, 0.002, 1, 0.933, 0, 0.967, -0.095, 1, -0.095, 0, 1.067, 0, 0, 1.167, -0.104, 0, 1.233, 0, 0, 1.3, -0.112, 0, 1.4, 0.002, 0, 1.5, -0.095, 0, 1.567, 0, 0, 1.667, -0.104, 0, 1.733, 0, 0, 1.8, -0.112, 0, 1.9, 0.002, 1, 3.089, 0.002, 4.278, 0.002, 5.467, 0.001, 0, 6, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRX", "Segments": [0, 0, 2, 5.467, 0, 2, 10.9, 0, 0, 11.4, 0.5, 2, 13.333, 0.5, 0, 13.833, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeR", "Segments": [0, 0, 0, 0.333, -0.23, 0, 0.733, 0.006, 1, 0.789, 0.006, 0.844, 0.007, 0.9, 0.002, 1, 0.933, 0, 0.967, -0.095, 1, -0.095, 0, 1.067, 0, 0, 1.167, -0.104, 0, 1.233, 0, 0, 1.3, -0.112, 0, 1.4, 0.002, 0, 1.5, -0.095, 0, 1.567, 0, 0, 1.667, -0.104, 0, 1.733, 0, 0, 1.8, -0.112, 0, 1.9, 0.002, 1, 3.089, 0.002, 4.278, 0.002, 5.467, 0.001, 0, 6, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamTearLight", "Segments": [0, 0, 0, 0.333, 1, 2, 0.433, 1, 0, 0.533, 0, 0, 0.667, 1, 0, 0.833, 0, 0, 0.9, 1, 0, 1.033, 0, 0, 1.2, 1, 0, 1.333, 0, 0, 1.433, 1, 0, 1.6, 0, 0, 1.7, 1, 0, 1.8, 0, 0, 1.967, 1, 0, 2.133, 0, 0, 2.233, 1, 0, 2.333, 0, 2, 5.467, 0, 2, 6, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamTears", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 1, 2, 2.3, 1, 0, 2.333, 0, 2, 6, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPHYInputX", "Segments": [0, 0, 2, 0.133, 0, 0, 0.467, -5.457, 0, 1.3, 7.523, 0, 1.633, -3.566, 0, 1.933, 2.89, 0, 2.233, -2.89, 0, 2.567, 3.525, 0, 2.9, -2.89, 0, 3.167, 1.391, 0, 3.433, -2.89, 0, 3.767, 1.391, 0, 4.067, -2.89, 0, 4.4, 3.525, 0, 4.767, -2.89, 0, 5.133, 1.391, 0, 5.467, -2.89, 0, 6.033, 1.391, 1, 9.966, 1.391, 13.9, 0.527, 17.833, 0.019, 1, 18.3, -0.041, 18.766, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 0, 0.3, -5, 1, 0.456, -5, 0.611, -1.887, 0.767, 0, 1, 0.922, 1.887, 1.078, 2, 1.233, 2, 0, 2.067, 0, 2, 3.167, 0, 0, 3.533, -5, 1, 3.755, -5, 3.978, 0.236, 4.2, 2, 1, 4.356, 3.235, 4.511, 3, 4.667, 3, 1, 4.811, 3, 4.956, 1.131, 5.1, -0.572, 1, 5.222, -2.013, 5.345, -2.2, 5.467, -2.2, 0, 7.7, -2.07, 0, 8.333, -3, 1, 8.678, -3, 9.022, -2.23, 9.367, -2, 1, 9.911, -1.637, 10.456, -1.559, 11, -1.23, 1, 11.156, -1.136, 11.311, 9.908, 11.467, 9.908, 0, 11.8, 9.017, 1, 11.978, 9.017, 12.155, 9.151, 12.333, 9.908, 1, 12.433, 10.334, 12.533, 11.162, 12.633, 11.162, 2, 12.667, 11.162, 0, 13.367, 5, 0, 13.633, 9.017, 0, 13.933, -3.003, 0, 14.167, 3, 0, 14.5, -1.517, 0, 14.733, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 0, 0.167, -6, 0, 0.4, 12.414, 1, 0.489, 12.414, 0.578, 11.674, 0.667, 9, 1, 0.734, 6.995, 0.8, 5, 0.867, 5, 2, 1.233, 5, 0, 1.533, 7, 2, 1.833, 7, 1, 1.889, 7, 1.944, 6.811, 2, 7.384, 1, 2.1, 8.416, 2.2, 13.634, 2.3, 13.634, 0, 2.7, -7.313, 0, 2.9, -1.96, 2, 3.1, -1.96, 0, 3.233, -4, 0, 3.6, 6, 1, 3.7, 6, 3.8, -10.18, 3.9, -11.785, 1, 4.022, -13.748, 4.145, -13.769, 4.267, -15.486, 1, 4.367, -16.892, 4.467, -19.805, 4.567, -19.805, 0, 4.967, 10, 0, 5.367, -8, 0, 5.667, -5, 2, 6.7, -5, 0, 7, -6.186, 0, 7.533, 2.384, 0, 7.833, 0, 2, 7.933, 0, 0, 8.333, 12.414, 0, 8.767, 0, 2, 8.9, 0, 0, 9.167, 15, 0, 9.567, -9.31, 0, 9.967, 3.118, 0, 10.233, -5.601, 0, 10.533, 0, 2, 10.833, 0, 0, 11.067, 5, 0, 11.8, -9.31, 2, 12.067, -9.31, 0, 12.467, -2.254, 1, 12.622, -2.254, 12.778, -7.012, 12.933, -9.31, 1, 13.055, -11.116, 13.178, -11, 13.3, -11, 0, 13.8, 6, 0, 14.2, -2.254, 0, 14.567, 1.194, 0, 14.933, 0, 0, 15.167, 6, 0, 15.467, 0, 2, 15.733, 0, 0, 15.9, -10, 0, 16.233, 16, 0, 16.767, -3.117, 1, 16.911, -3.117, 17.056, -2.257, 17.2, 0, 1, 17.256, 0.868, 17.311, 1.69, 17.367, 1.69, 0, 17.6, -6, 0, 17.867, -4, 0, 18.067, -6, 0, 18.567, 4.871, 0, 19.067, -1.03, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.367, 0, 2, 0.767, 0, 2, 1.367, 0, 0, 1.933, 9, 0, 2.1, 8, 0, 2.367, 13, 0, 2.967, 0, 2, 3.033, 0, 1, 3.089, 0, 3.144, 0.157, 3.2, -0.178, 1, 3.344, -1.047, 3.489, -5, 3.633, -5, 0, 4.167, 4, 1, 4.311, 4, 4.456, 4.12, 4.6, 2.02, 1, 4.8, -0.888, 5, -6, 5.2, -6, 0, 5.733, -1.838, 0, 6.467, -5, 0, 6.833, -4.03, 0, 7.233, -6, 0, 7.733, 0, 2, 8.133, 0, 0, 8.833, 4, 2, 9.167, 4, 0, 9.767, -11, 1, 10.022, -11, 10.278, -10.392, 10.533, -7, 1, 10.644, -5.525, 10.756, 0, 10.867, 0, 0, 11, -1.3, 0, 11.6, 9, 0, 11.9, 7.473, 0, 12.2, 9, 2, 12.767, 9, 1, 12.889, 9, 13.011, 14.599, 13.133, 15.897, 1, 13.322, 17.902, 13.511, 18, 13.7, 18, 0, 14.033, 11, 0, 14.333, 13, 0, 14.8, 8, 0, 15.267, 11, 2, 15.9, 11, 0, 16.1, 20, 0, 16.667, -6, 0, 16.933, 0, 2, 17.167, 0, 0, 17.3, 3.376, 1, 17.389, 3.376, 17.478, -2.14, 17.567, -4.995, 1, 17.656, -7.849, 17.744, -8, 17.833, -8, 0, 18.6, 2.02, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperLAngle", "Segments": [0, 0, 0, 0.567, -5, 0, 0.8, -3, 2, 2, -3, 0, 2.433, -5, 2, 3.3, -5, 2, 4.633, -5, 0, 5.2, -4, 0, 5.5, -5, 2, 6.033, -5, 2, 6.467, -5, 2, 6.567, -5, 0, 6.833, 0.769, 0, 7.033, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamArmHandLAngle", "Segments": [0, 0, 2, 0.167, 0, 0, 0.367, 1.389, 0, 0.633, 0.999, 0, 0.833, 1.152, 0, 1.267, 1.106, 2, 2, 1.106, 0, 2.2, 1.9, 0, 2.4, 0.8, 0, 2.6, 1.247, 0, 2.833, 0.8, 1, 3.033, 0.8, 3.233, 0.818, 3.433, 0.999, 1, 3.511, 1.069, 3.589, 2.699, 3.667, 2.699, 0, 4.6, 0.8, 0, 4.867, 1.5, 0, 5.3, 0, 0, 5.433, 0.172, 0, 5.633, 0, 2, 6.467, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerLAngle", "Segments": [0, 10, 2, 0.067, 10, 0, 0.233, 10.308, 0, 0.6, -0.907, 0, 0.9, -0.595, 2, 2, -0.595, 0, 2.433, 3, 2, 3.3, 3, 0, 3.6, 2.5, 0, 4.433, 3, 2, 4.6, 3, 0, 4.8, 2.586, 0, 5.133, 3.483, 2, 5.867, 3.483, 0, 6, 3.265, 0, 6.233, 3.642, 2, 6.467, 3.642, 0, 6.633, 3.451, 0, 7, 10, 2, 17.833, 10, 2, 19.233, 10]}, {"Target": "Parameter", "Id": "ParamArmLowerLH", "Segments": [0, 0, 0, 0.567, -16, 0, 0.933, -12, 2, 2, -12, 0, 2.433, 0, 2, 3.3, 0, 2, 3.6, 0, 0, 4.333, -8, 0, 5.4, 0, 2, 6.033, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerLAngle", "Segments": [0, 0, 0, 0.467, 10, 0, 1.1, 0, 0, 2, 10, 2, 2.133, 10, 0, 2.633, 0, 2, 3.3, 0, 2, 5, 0, 0, 5.333, 8, 0, 6.033, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamHandT2L", "Segments": [0, 0, 2, 0.567, 0, 2, 2, 0, 0, 2.133, 0.4, 0, 2.533, 0, 2, 3.3, 0, 2, 5.467, 0, 2, 6.033, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamArm<PERSON>pperRAngle", "Segments": [0, 0, 0, 0.233, -1, 0, 0.533, 3, 2, 2, 3, 0, 2.433, 5, 2, 3.3, 5, 2, 4.667, 5, 0, 5.2, 3.146, 0, 5.5, 4, 2, 6.567, 4, 0, 6.833, 4.214, 1, 6.9, 4.214, 6.966, 3.874, 7.033, 3, 1, 7.089, 2.272, 7.144, 1.79, 7.2, 1.79, 1, 7.267, 1.79, 7.333, 1.905, 7.4, 2, 1, 7.489, 2.126, 7.578, 2.152, 7.667, 2.152, 1, 7.767, 2.152, 7.867, 2.175, 7.967, 2.09, 1, 8.089, 1.987, 8.211, 1.003, 8.333, 1.003, 0, 8.567, 3, 2, 8.867, 3, 0, 9.4, -0.939, 0, 9.7, 2, 0, 10.033, 1.79, 2, 10.633, 1.79, 0, 11.1, 0.672, 0, 11.267, 2.026, 2, 13.567, 2.026, 2, 14.933, 2.026, 1, 15.289, 2.026, 15.644, 2.023, 16, 2, 1, 16.122, 1.992, 16.245, 1.315, 16.367, 1.315, 0, 16.7, 2.475, 0, 16.867, 2, 2, 17.833, 2, 0, 18.667, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRAngle", "Segments": [0, 7.4, 0, 0.133, 6.8, 1, 0.211, 6.8, 0.289, 7.284, 0.367, 8, 1, 0.411, 8.409, 0.456, 8.647, 0.5, 9.038, 1, 0.567, 9.624, 0.633, 10, 0.7, 10, 2, 2, 10, 0, 2.433, -2, 0, 2.633, -1, 2, 3.3, -1, 0, 3.6, 0.599, 0, 4.433, -1, 2, 4.6, -1, 0, 4.8, 0, 0, 5.167, -2.672, 0, 5.567, -2, 2, 5.833, -2, 0, 5.967, -1.72, 0, 6.2, -3, 0, 6.333, -2.908, 0, 6.5, -3, 0, 6.633, -2.672, 0, 7.2, -25, 2, 7.8, -25, 0, 7.967, -25.66, 0, 8.433, 6, 2, 8.967, 6, 0, 9.6, -10.056, 0, 9.9, -8.729, 0, 10.2, -9, 1, 10.4, -9, 10.6, -8.999, 10.8, -8.99, 1, 10.878, -8.986, 10.955, 0.305, 11.033, 2.156, 1, 11.111, 4.008, 11.189, 5.856, 11.267, 6, 1, 11.5, 6.433, 11.734, 6.502, 11.967, 6.502, 2, 12.267, 6.502, 1, 12.7, 6.502, 13.134, 6.442, 13.567, 6, 1, 13.711, 5.853, 13.856, -3.48, 14, -3.48, 0, 14.167, -3, 2, 14.933, -3, 1, 15.122, -3, 15.311, -2.959, 15.5, -2.77, 1, 15.7, -2.57, 15.9, -2.336, 16.1, -2, 1, 16.2, -1.832, 16.3, -1.34, 16.4, -1.34, 0, 16.733, -2.462, 0, 16.967, -2, 2, 17.133, -2, 0, 17.433, -2.78, 1, 17.5, -2.78, 17.566, -2.613, 17.633, -2, 1, 17.7, -1.387, 17.766, -1.104, 17.833, 0.241, 1, 18.033, 4.275, 18.233, 8.752, 18.433, 8.752, 0, 18.967, 7.4, 2, 19.233, 7.4]}, {"Target": "Parameter", "Id": "ParamArmHandRAngle", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0.232, 0.2, -0.408, 1, 0.322, -1.582, 0.445, -10, 0.567, -10, 0, 0.733, -8.972, 0, 1.033, -10, 2, 1.933, -10, 0, 2.2, -5, 0, 2.4, -9.818, 0, 2.6, -9.074, 0, 2.867, -10, 2, 3.4, -10, 0, 3.733, -6, 0, 4.6, -10, 2, 4.7, -10, 0, 4.933, -8, 0, 5.433, -10, 2, 5.867, -10, 0, 6.1, -8.972, 0, 6.367, -10, 2, 6.467, -10, 0, 7.2, 10, 2, 8.1, 10, 2, 8.133, -7, 1, 8.166, -7.835, 8.2, -8, 8.233, -8, 0, 8.533, 0.717, 0, 8.733, 0, 2, 8.867, 0, 0, 9.033, 2.047, 0, 9.367, -10, 0, 9.533, -8.972, 0, 9.833, -10, 0, 9.967, -8.859, 0, 10.333, -10, 0, 10.8, -9.99, 0, 10.967, -10, 0, 11.233, -5, 2, 13.6, -5, 0, 14.033, -7.486, 0, 14.3, -5.312, 0, 14.467, -5.585, 2, 15.5, -5.585, 0, 15.867, -3, 0, 16.333, -7, 0, 16.733, -3, 2, 17.2, -3, 0, 17.5, -6, 1, 17.611, -6, 17.722, -4.649, 17.833, -3, 1, 18.078, 0.628, 18.322, 2.047, 18.567, 2.047, 0, 18.967, -0.604, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRH", "Segments": [0, 0, 0, 0.467, -13.363, 0, 1.167, -8, 2, 2, -8, 0, 2.433, -9, 1, 2.722, -9, 3.011, -8.941, 3.3, -8.729, 1, 3.4, -8.656, 3.5, -8.511, 3.6, -8.511, 0, 4.333, -18, 0, 4.967, -8, 2, 6.833, -8, 0, 7.2, -15, 2, 7.933, -15, 0, 8.167, -9, 0, 8.633, -30, 2, 8.967, -30, 0, 9.867, -13.363, 0, 10.8, -13.65, 0, 11.167, -7, 2, 11.933, -7, 2, 15.467, -7, 0, 15.9, -9, 0, 16.167, 8, 0, 16.533, -15, 0, 16.867, -11.496, 0, 17.433, -13.363, 1, 17.566, -13.363, 17.7, -13.758, 17.833, -13, 1, 18.211, -10.852, 18.589, 2, 18.967, 2, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperRH", "Segments": [0, 0, 0, 1.167, 10, 2, 2, 10, 0, 2.433, 14, 1, 2.722, 14, 3.011, 13.805, 3.3, 12.916, 1, 3.856, 11.206, 4.411, 10, 4.967, 10, 2, 8.233, 10, 0, 8.467, 16.447, 1, 9.222, 16.449, 9.978, 16.45, 10.733, 16.45, 0, 11.167, 5, 2, 11.933, 5, 2, 14.967, 5, 2, 16.733, 5, 2, 17.833, 5, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRAngle", "Segments": [0, 0, 0, 0.367, 3, 0, 0.833, 2, 2, 2.167, 2, 0, 2.333, 5, 0, 2.6, 0, 2, 3.3, 0, 2, 8.233, 0, 0, 8.467, 2.939, 0, 8.767, 2, 2, 9, 2, 0, 9.167, 0, 0, 10.267, 1, 1, 10.456, 1, 10.644, 0.776, 10.833, 0.424, 1, 10.922, 0.258, 11.011, 0.271, 11.1, 0, 1, 11.189, -0.271, 11.278, -10, 11.367, -10, 2, 11.8, -10, 2, 14.2, -10, 0, 16.2, -4, 0, 16.8, -10, 0, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamHand_Cl", "Segments": [0, 0, 2, 2, 0, 2, 3.3, 0, 2, 5.467, 0, 2, 6.867, 0, 2, 6.9, 30, 2, 8.1, 30, 1, 8.111, 30, 8.122, 0, 8.133, 0, 2, 16.733, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamHandT1R", "Segments": [0, 0, 2, 2, 0, 2, 3.3, 0, 2, 5.467, 0, 2, 6.033, 0, 2, 6.9, 0, 0, 7.2, 1, 2, 7.833, 1, 0, 8.133, 0, 2, 8.3, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamHandT2R", "Segments": [0, 0, 0, 0.433, 0.41, 0, 0.833, 0.2, 2, 2.167, 0.2, 0, 2.4, 0.3, 0, 2.667, 0, 2, 3.3, 0, 2, 4.967, 0, 0, 5.3, 0.2, 0, 6.033, 0, 2, 8.233, 0, 0, 8.4, 1, 0, 8.633, 0.927, 2, 9, 0.927, 0, 9.233, 0.41, 0, 9.867, 1, 0, 10.067, 0.927, 0, 10.333, 1, 2, 10.767, 1, 0, 11.1, 0, 1, 11.189, 0, 11.278, 0.37, 11.367, 0.373, 1, 12.145, 0.4, 12.922, 0.41, 13.7, 0.41, 0, 14.1, 0, 0, 15.2, 0.41, 1, 15.556, 0.41, 15.911, 0.41, 16.267, 0.4, 1, 16.445, 0.395, 16.622, 0, 16.8, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 0.267, -2.43, 0, 0.533, 1, 0, 0.733, 0, 2, 0.833, 0, 0, 0.933, -0.789, 0, 1.5, 1, 1, 1.656, 1, 1.811, 1.225, 1.967, 0, 1, 2.178, -1.662, 2.389, -7, 2.6, -7, 0, 2.933, -5.849, 2, 4.367, -5.849, 0, 4.633, -5, 0, 5.167, -10, 2, 5.533, -10, 0, 5.8, -9, 0, 6.4, -10, 2, 6.933, -10, 0, 7.467, -7, 2, 7.967, -7, 1, 8.022, -7, 8.078, -7.46, 8.133, -8, 1, 8.211, -8.756, 8.289, -9, 8.367, -9, 1, 8.467, -9, 8.567, -7.052, 8.667, -7, 1, 9.222, -6.71, 9.778, -6.629, 10.333, -6.629, 0, 10.633, -7, 0, 11.1, -3, 2, 12.067, -3, 0, 12.767, 1, 0, 13.067, 0, 2, 13.533, 0, 0, 13.733, 0.627, 0, 14.2, -5.455, 0, 14.533, -4.629, 0, 14.767, -5.455, 0, 15.067, -5, 2, 15.733, -5, 0, 16, -2.705, 0, 16.6, -10, 2, 16.867, -10, 2, 17.433, -10, 1, 17.566, -10, 17.7, -8.734, 17.833, -6.234, 1, 18.055, -2.067, 18.278, 0, 18.5, 0, 0, 18.967, -0.789, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.133, 0, 0, 0.467, -5, 0, 0.733, 0, 2, 0.767, 0, 0, 1.3, 5, 0, 1.767, -5.515, 1, 1.845, -5.515, 1.922, -3.61, 2, 0, 1, 2.044, 2.063, 2.089, 3, 2.133, 3, 1, 2.189, 3, 2.244, 2.625, 2.3, 0.177, 1, 2.433, -5.699, 2.567, -10, 2.7, -10, 0, 3.1, -2.487, 0, 3.267, -3, 2, 3.467, -3, 0, 3.767, -0.517, 0, 4.067, -7, 0, 4.367, -5.515, 0, 4.5, -6.39, 0, 4.767, -1, 0, 5.033, -8, 0, 5.467, -6.39, 2, 5.633, -6.39, 0, 5.8, -5, 0, 6.1, -8, 0, 6.267, -7, 2, 6.6, -7, 0, 6.833, -4, 0, 7.233, -10, 0, 7.5, -9, 0, 7.833, -10, 2, 8, -10, 0, 8.533, 1.472, 0, 8.9, 0, 0, 9.267, 5, 0, 9.733, -2, 0, 10.067, 0, 2, 10.633, 0, 0, 10.867, 5.333, 0, 11.2, -1.515, 0, 11.433, -1.252, 1, 11.555, -1.252, 11.678, -1.231, 11.8, -1.515, 1, 11.9, -1.748, 12, -3, 12.1, -3, 0, 12.633, 0, 0, 12.833, -2, 0, 13.1, -0.995, 0, 13.467, -2, 0, 13.667, -1, 0, 14.2, -7, 0, 14.367, -5.61, 0, 14.533, -6.39, 0, 14.733, -5.515, 0, 15.033, -6, 0, 15.2, -4, 0, 15.5, -6.39, 0, 15.7, -6, 0, 15.833, -8, 0, 16.1, 7.533, 0, 16.467, -8, 0, 16.7, -4.721, 0, 17.033, -6.39, 2, 17.367, -6.39, 0, 17.6, -7.455, 1, 17.678, -7.455, 17.755, -4.697, 17.833, -1.675, 1, 17.9, 0.915, 17.966, 1.472, 18.033, 1.472, 0, 18.5, -0.736, 0, 18.7, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 0.433, -1, 1, 0.511, -1, 0.589, -0.131, 0.667, -0.073, 1, 0.734, -0.023, 0.8, -0.05, 0.867, 0, 1, 0.945, 0.059, 1.022, 1, 1.1, 1, 0, 1.5, 0, 2, 2.133, 0, 0, 2.3, 1, 0, 2.633, -4, 2, 3.233, -4, 0, 3.767, 5, 0, 4.167, 4.26, 0, 4.567, 5, 0, 5.167, 0, 2, 5.467, 0, 2, 5.633, 0, 0, 5.867, 0.805, 0, 6.233, -1, 0, 6.567, 0, 2, 6.767, 0, 0, 7.033, -2, 0, 7.633, 0, 2, 9.067, 0, 0, 9.633, -3.065, 2, 10.5, -3.065, 0, 10.7, -4, 0, 11.2, 1.805, 0, 11.467, 1, 2, 12.033, 1, 0, 12.4, 3.151, 1, 12.656, 3.151, 12.911, 3.172, 13.167, 3, 1, 13.378, 2.858, 13.589, 0.762, 13.8, 0, 1, 13.989, -0.682, 14.178, -0.809, 14.367, -0.809, 0, 15.167, 0.477, 0, 15.7, 0, 0, 16.1, 6, 0, 16.733, -1, 1, 16.855, -1, 16.978, -0.802, 17.1, 0, 1, 17.156, 0.365, 17.211, 1, 17.267, 1, 0, 17.533, -1.359, 1, 17.633, -1.359, 17.733, -0.35, 17.833, 0, 1, 18.033, 0.7, 18.233, 0.805, 18.433, 0.805, 0, 18.967, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX2", "Segments": [0, 0, 2, 0.7, 0, 0, 1.867, -4, 0, 2.633, -3.8, 2, 5.233, -3.8, 0, 7, 0, 2, 13.767, 0, 0, 14.233, -4, 2, 15.833, -4, 1, 15.944, -4, 16.056, -3.897, 16.167, -4.143, 1, 16.367, -4.586, 16.567, -10, 16.767, -10, 0, 17.1, -9.322, 0, 17.8, -10, 1, 17.811, -10, 17.822, -10, 17.833, -9.91, 1, 18.111, -4.136, 18.389, 0, 18.667, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY2", "Segments": [0, 0, 2, 0.7, 0, 2, 2.133, 0, 0, 2.767, -9, 0, 3.5, 0, 2, 4.367, 0, 0, 4.633, 3.143, 0, 5.167, -4, 0, 5.467, 0, 2, 8, 0, 0, 8.367, 4.993, 0, 8.8, 0, 2, 9.033, 0, 0, 9.3, 1, 0, 9.833, -5, 0, 10.067, -4, 0, 10.567, -6, 0, 10.867, 2, 0, 11.333, -3, 0, 11.667, -1, 0, 11.9, -1.531, 0, 12.2, 0, 2, 12.8, 0, 2, 13.067, 0, 0, 13.6, -3, 0, 13.8, -2, 0, 14.667, -6, 2, 15.733, -6, 0, 16.1, -4, 0, 16.733, -10, 2, 17.667, -10, 1, 17.722, -10, 17.778, -8.99, 17.833, -6.7, 1, 17.955, -1.663, 18.078, 1, 18.2, 1, 0, 18.667, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechX", "Segments": [0, 0, 0, 0.333, 2, 0, 0.6, 0, 0, 1.133, 2.528, 0, 1.6, -0.669, 0, 1.933, 0, 2, 2.067, 0, 0, 2.433, -2.529, 0, 2.833, 4, 0, 3.633, 0, 2, 3.767, 0, 0, 4.467, 6.943, 2, 4.833, 6.943, 0, 5.333, -1.169, 0, 5.633, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, 0, 0, 0.7, 1, 0, 1.333, -0.246, 0, 1.833, 0, 2, 5.467, 0, 2, 7.967, 0, 0, 8.633, 1, 1, 8.9, 1, 9.166, 0.416, 9.433, 0, 1, 9.966, -0.832, 10.5, -1.05, 11.033, -1.05, 0, 11.8, 0, 2, 12.267, 0, 2, 13.2, 0, 2, 13.567, 0, 0, 14.033, -7, 2, 16.733, -7, 2, 17.5, -7, 1, 17.611, -7, 17.722, -6.5, 17.833, -5.05, 1, 18.078, -1.861, 18.322, 0, 18.567, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamShoulderR", "Segments": [0, 0, 2, 0.7, 0, 0, 1.3, 19, 0, 2.367, 13, 2, 2.7, 13, 0, 2.967, 5.2, 0, 3.233, 23.867, 0, 3.633, 0, 0, 3.9, 24, 0, 4.2, 5.2, 0, 4.433, 23.867, 1, 4.533, 23.867, 4.633, 11.761, 4.733, 6, 1, 4.833, 0.239, 4.933, 0, 5.033, 0, 2, 15.733, 0, 0, 15.933, -16.333, 0, 16.133, 13, 0, 16.367, -8.077, 0, 16.6, 0.53, 0, 16.733, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamShoulderL", "Segments": [0, 0, 2, 0.7, 0, 0, 1.3, 14, 0, 2.367, 8, 2, 2.7, 8, 0, 2.967, 13.965, 0, 3.3, 2.382, 0, 3.633, 21.369, 0, 3.9, 2.117, 0, 4.2, 13.965, 0, 4.5, 2.382, 0, 4.733, 11, 0, 5.033, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRHide", "Segments": [0, 1, 2, 0.033, 1, 0, 0.4, 0, 2, 0.8, 0, 2, 17.833, 0, 2, 18.233, 0, 0, 19.233, 1]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyX", "Segments": [0, 0, 0, 0.867, 3.347, 0, 2.467, -1.812, 0, 4.1, 6.521, 0, 5.367, -5.239, 0, 6.4, 3.059, 0, 7.433, 0, 2, 13.867, 0, 0, 15.733, 6.521, 0, 17.167, -5.239, 0, 18.333, 3.059, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyY", "Segments": [0, 0, 0, 1.267, 2.312, 0, 2.867, -2.34, 0, 4.5, 5.174, 0, 5.8, -5.43, 0, 7.1, 0, 2, 13.867, 0, 0, 16.2, 5.174, 0, 17.667, -5.43, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyZ", "Segments": [0, 0, 0, 1.267, 5.861, 0, 2.667, -1.28, 0, 4.1, 7.41, 0, 5.633, -3.889, 0, 6.667, 8.025, 0, 7.167, 0, 2, 13.867, 0, 0, 15.733, 7.41, 0, 17.467, -3.889, 0, 18.633, 8.025, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRPositionZ", "Segments": [0, 0, 0, 0.867, 0.929, 0, 2, -0.854, 0, 3.467, 0.365, 0, 5.1, -0.853, 0, 6.3, 0.587, 0, 7.167, 0, 2, 13.867, 0, 0, 15.033, 0.365, 0, 16.867, -0.853, 0, 18.233, 0.587, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRMouth", "Segments": [0, 0, 0, 0.733, -4.354, 1, 0.878, -4.354, 1.022, -4.527, 1.167, -4.096, 1, 1.422, -3.333, 1.678, 4.763, 1.933, 4.763, 0, 2.9, -2.684, 0, 3.7, 1.124, 0, 4.367, -1.748, 0, 5.8, 2.392, 0, 7.033, -1, 0, 7.5, 0, 2, 13.867, 0, 0, 15.267, 1.124, 0, 16.033, -1.748, 0, 17.667, 2.392, 0, 19.067, -1, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLHide", "Segments": [0, 1, 2, 0.233, 1, 0, 0.633, 0, 2, 18, 0, 0, 19, 1, 2, 19.233, 1]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyX", "Segments": [0, 0, 0, 0.667, -7.021, 0, 1.7, 12.347, 0, 2.8, -3.668, 0, 3.7, 22.937, 0, 4.867, -18.34, 0, 6.3, 16.782, 0, 7.167, 0, 2, 13.567, 0, 0, 15.567, 22.937, 0, 16.8, -18.34, 0, 18.333, 16.782, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyY", "Segments": [0, 0, 0, 0.733, -9.865, 0, 2, 18.079, 0, 3.3, -9.865, 0, 4.3, 27.794, 0, 5.3, -13.378, 0, 6.667, 18.079, 0, 7.167, 0, 2, 13.567, 0, 0, 16.2, 27.794, 0, 17.267, -13.378, 0, 18.7, 18.079, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyZ", "Segments": [0, 0, 0, 0.9, 8.452, 0, 2.467, -7.987, 0, 4.233, 10.148, 0, 6.033, -5.585, 0, 7.167, 0, 2, 13.567, 0, 0, 16.133, 10.148, 0, 18.033, -5.585, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuU", "Segments": [0, 0, 2, 0.4, 0, 2, 0.667, 0, 0, 0.933, 1.32, 1, 1.011, 1.32, 1.089, 0.826, 1.167, -1.857, 1, 1.2, -3.007, 1.234, -9, 1.267, -9, 2, 1.467, -9, 0, 17.367, -9.063, 1, 17.456, -9.063, 17.544, -9.054, 17.633, -8.473, 1, 17.689, -8.11, 17.744, -4.866, 17.8, -2.978, 1, 17.811, -2.6, 17.822, -1.932, 17.833, -1.63, 1, 17.9, 0.18, 17.966, 0.951, 18.033, 0.951, 0, 18.1, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyY", "Segments": [0, 0, 2, 0.4, 0, 2, 0.433, 0, 0, 0.633, -30, 0, 0.9, 30, 0, 1.4, 0, 2, 1.467, 0, 2, 17.367, 0, 0, 17.5, 30, 1, 17.544, 30, 17.589, 28.214, 17.633, 11.016, 1, 17.678, -6.182, 17.722, -30, 17.767, -30, 1, 17.789, -30, 17.811, -29.963, 17.833, -27.794, 1, 18.011, -10.442, 18.189, 0, 18.367, 0, 2, 18.467, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyZ", "Segments": [0, 0, 0, 0.367, 3.5, 0, 0.6, -25, 0, 0.867, 19, 0, 1.367, 0, 2, 1.467, 0, 2, 17.367, 0, 0, 17.467, -16, 1, 17.522, -16, 17.578, -12.089, 17.633, 3.5, 1, 17.655, 9.735, 17.678, 23, 17.7, 23, 1, 17.744, 23, 17.789, 21.152, 17.833, 13.453, 1, 17.955, -7.719, 18.078, -21, 18.2, -21, 0, 18.467, 8, 0, 18.667, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUArmZ", "Segments": [0, 0, 2, 0.4, 0, 2, 0.5, 0, 0, 0.633, -12, 0, 1, 30, 0, 1.467, 0, 2, 17.367, 0, 0, 17.5, -12, 1, 17.544, -12, 17.589, -10.523, 17.633, -4.297, 1, 17.7, 5.042, 17.766, 18.913, 17.833, 28.552, 1, 17.844, 30, 17.856, 30, 17.867, 30, 0, 18.467, -7, 0, 18.667, 3.141, 0, 18.8, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineU", "Segments": [0, 0, 2, 0.4, 0, 2, 0.733, 0, 0, 1, 1, 2, 1.033, 0, 2, 1.467, 0, 2, 17.367, 0, 2, 17.633, 0, 2, 17.833, 0, 2, 18.467, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyW", "Segments": [0, 0, 2, 0.4, 0, 0, 0.6, -0.5, 0, 0.833, 1, 0, 1.167, -1, 0, 1.467, 0, 2, 17.367, 0, 0, 17.467, 0.5, 1, 17.522, 0.5, 17.578, 0.272, 17.633, -0.407, 1, 17.644, -0.543, 17.656, -1, 17.667, -1, 1, 17.722, -1, 17.778, -0.601, 17.833, 0, 1, 17.9, 0.721, 17.966, 1, 18.033, 1, 0, 18.367, -0.5, 0, 18.567, 0.5, 0, 18.767, -0.3, 0, 19.033, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPandaHide", "Segments": [0, 1, 2, 0.533, 1, 0, 0.933, 0, 2, 17.833, 0, 0, 18.433, 1, 2, 19.233, 1]}, {"Target": "Parameter", "Id": "ParamPandaBodyX", "Segments": [0, 0, 0, 0.633, 4.939, 0, 1.333, -7.021, 0, 2.667, 12.347, 0, 4.5, 0, 2, 14.833, 0, 0, 16.133, -3.668, 0, 17.267, 10, 0, 18.433, -11.213, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyY", "Segments": [0, 0, 0, 0.967, 4.225, 0, 1.967, -3.672, 0, 3.033, 6.904, 0, 4.5, 0, 2, 14.833, 0, 0, 16.8, -3.672, 0, 18.1, 10.581, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyZ", "Segments": [0, 0, 0, 1.067, 9.125, 0, 2.967, -7.987, 0, 4.5, 0, 2, 14.833, 0, 0, 16.5, 10.148, 0, 17.967, -5.585, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyX", "Segments": [0, 0, 0, 1.133, 5.186, 0, 3.2, -4.826, 1, 4.089, -4.826, 4.978, -3.127, 5.867, 3.384, 1, 6.1, 5.093, 6.334, 14.005, 6.567, 14.005, 0, 7.7, -30, 2, 9.1, -30, 2, 11.033, -30, 2, 13.133, -30, 2, 16.2, -30, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyY", "Segments": [0, 0, 0, 0.833, 1.561, 1, 1.589, 1.561, 2.344, 0.011, 3.1, -4, 1, 3.233, -4.708, 3.367, -6, 3.5, -6, 0, 3.7, 6.295, 0, 4.133, -13.466, 0, 4.467, 10.518, 0, 4.867, -12.908, 0, 5.2, 7.729, 0, 5.467, -3.108, 2, 5.7, -3.108, 0, 6.167, -22, 1, 6.334, -22, 6.5, -21.17, 6.667, -15.538, 1, 7.034, -3.147, 7.4, 6.295, 7.767, 6.295, 1, 8, 6.295, 8.234, 6.012, 8.467, 0, 1, 8.622, -4.008, 8.778, -30, 8.933, -30, 0, 9.333, 30, 0, 9.667, -19, 0, 10.033, 11, 1, 10.189, 11, 10.344, 1.363, 10.5, 0, 1, 10.822, -2.824, 11.145, -3.108, 11.467, -3.108, 0, 13.1, 4.257, 0, 14.9, -4, 1, 15.233, -4, 15.567, -4.047, 15.9, -3.347, 1, 16.122, -2.88, 16.345, 6.295, 16.567, 6.295, 0, 16.967, -1.599, 0, 17.733, 1.561, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonZ", "Segments": [0, 0, 0, 2.267, 14, 0, 5.033, -2.048, 1, 5.178, -2.048, 5.322, -1.78, 5.467, -1.212, 1, 5.567, -0.819, 5.667, -0.716, 5.767, 0, 1, 6.578, 5.804, 7.389, 9.63, 8.2, 9.63, 0, 10.967, -2.048, 0, 12.8, 2.151, 1, 13.222, 2.151, 13.645, 0.88, 14.067, 0, 1, 14.578, -1.065, 15.089, -1.212, 15.6, -1.212, 0, 16.167, 6.693, 0, 16.4, -14, 0, 16.767, -8.924, 0, 17.4, -10.518, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonGaY", "Segments": [0, 0, 0, 1.467, 3.807, 0, 3.267, -1.392, 0, 4.9, 3.53, 1, 5.089, 3.53, 5.278, 1.847, 5.467, 0.595, 1, 5.567, -0.068, 5.667, 0, 5.767, 0, 0, 7.4, 3.807, 1, 7.8, 3.807, 8.2, 3.36, 8.6, 0, 1, 8.756, -1.307, 8.911, -30, 9.067, -30, 0, 9.5, 30, 0, 9.833, -15, 0, 10.3, 5.093, 0, 10.733, -4.218, 0, 11.167, 2.785, 0, 11.833, -2.706, 0, 13.833, 0.595, 1, 14.455, 0.595, 15.078, -0.022, 15.7, -2.706, 1, 15.856, -3.377, 16.011, -16, 16.167, -16, 0, 16.533, 18.805, 0, 16.933, -4.218, 0, 17.3, 3.53, 0, 17.833, -1.514, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupY", "Segments": [0, 0, 0, 2, -0.17, 0, 3.467, 0.126, 0, 5.1, -0.04, 1, 5.222, -0.04, 5.345, -0.024, 5.467, -0.011, 1, 5.567, -0.001, 5.667, 0, 5.767, 0, 0, 7.9, -0.17, 0, 9.4, 0.126, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupZ", "Segments": [0, 0, 2, 0.3, 0, 0, 2.933, -0.149, 0, 4.3, 0.113, 1, 4.689, 0.113, 5.078, 0.051, 5.467, 0.007, 1, 5.567, -0.004, 5.667, 0, 5.767, 0, 2, 6.233, 0, 0, 8.833, -0.149, 0, 10.2, 0.113, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonHandZ", "Segments": [0, 0, 0, 2.3, -1.699, 0, 3.6, 1.258, 0, 4.933, -0.396, 1, 5.111, -0.396, 5.289, -0.211, 5.467, -0.072, 1, 5.567, 0.006, 5.667, 0, 5.767, 0, 0, 8.2, -1.699, 0, 9.5, 1.258, 0, 10.867, -0.396, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamParamStrongCatZ", "Segments": [0, 0, 0, 0.3, 4, 0, 0.567, -5.984, 0, 0.967, 7, 0, 1.333, 0, 2, 17.833, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamParamSCBodyZ", "Segments": [0, 0, 2, 0.167, 0, 0, 0.4, 3.635, 0, 0.833, -17, 0, 1.133, 6.921, 0, 1.467, 0, 2, 2.1, 0, 2, 17.833, 0, 0, 18.833, -11, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamSCCupY", "Segments": [0, 0, 2, 0.4, 0, 2, 0.8, 0, 1, 0.878, 0, 0.955, -0.397, 1.033, 3.346, 1, 1.122, 7.624, 1.211, 30, 1.3, 30, 0, 1.667, 0, 2, 19.233, 0]}, {"Target": "Parameter", "Id": "MB_yanwubaozha", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "MB_DRFWXZKTMD", "Segments": [0, 1, 0, 19.233, 1]}, {"Target": "Parameter", "Id": "ParamAllSizeFix", "Segments": [0, 1, 0, 19.233, 1]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBG2Hide", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBGX", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBGY", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN2", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN3", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBlackCollar", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBlackOrder", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamWhiteIN", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamCHHide", "Segments": [0, 1, 0, 19.233, 1]}, {"Target": "Parameter", "Id": "ParamDeskHide", "Segments": [0, 1, 0, 19.233, 1]}, {"Target": "Parameter", "Id": "ParamStoolHide", "Segments": [0, 1, 0, 19.233, 1]}, {"Target": "Parameter", "Id": "ParamCupDesk", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamCHX", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamCHY", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamCHZ", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamChaSize", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamCcharacterZ", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamAllZ", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamALLSize2", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamDeskShow", "Segments": [0, 10, 0, 19.233, 10]}, {"Target": "Parameter", "Id": "ParamLightPositionX", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamFixT", "Segments": [0, 1, 0, 19.233, 1]}, {"Target": "Parameter", "Id": "ParamFlap", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamScare", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPupilExp", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthY", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBlackFace", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamTeethLight", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamMark", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamShameLine", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamMarkShake", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLY", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRY", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeRLightOpen", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLightLine1", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLightLine2", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLightLine3", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLightShine", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo1", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo2", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo3", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1Y", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle2", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow1", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow2", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamAngleH", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamAngleS", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamFanOpenR", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamChili", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamChiliX", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamHandR", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRY", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamHandRCup", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamHandRMail", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "Segments": [0, 1, 0, 19.233, 1]}, {"Target": "Parameter", "Id": "ParamHandLIQY1", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY2", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY3", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamHandCupZ", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamHandCupY", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechW", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Z", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLegR1X", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Z", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Z", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Y", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Y", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Y", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamFootRX", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Z", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Z", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Z", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Y", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Y", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Y", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamFootLX", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLegLF", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamMJRFlap", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuR", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuR", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuR", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuR", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRInput", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamMRCupSet", "Segments": [0, 1, 0, 19.233, 1]}, {"Target": "Parameter", "Id": "ParamMalpositionManjuuR", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuREyeOpen", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRArmB", "Segments": [0, 30, 0, 19.233, 30]}, {"Target": "Parameter", "Id": "ParamManjuuRSigh", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow1", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow2", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow3", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow4", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowB", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW2", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamMRCupFZ", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX1", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX2", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqH", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX1", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX2", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX3", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamMJLSigh", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuL", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuL", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuL", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamMjLFlip", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionZManjuuL", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLEyeOpen", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyW", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuL", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamClawFX", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamClawFY", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamClawBX", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamClawBY", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUHide", "Segments": [0, 1, 0, 19.233, 1]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuU", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuU", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUEyesForm", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyX", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineD", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionXPanda", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionYPanda", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamSizePanda", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2Panda", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegFZ", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegBZ", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY1", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY2", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY3", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupIce", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupZ", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupInput", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamSCDishRO", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamSCDishY", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamSCDishZ", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamSCCupRO", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "Parameter", "Id": "ParamSCCupZ", "Segments": [0, 0, 0, 19.233, 0]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 1, 0, 19.233, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 1, 0, 19.233, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 19.233, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 19.233, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 0, 19.233, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 19.233, 1]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 19.233, 1]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 0, 19.233, 1]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.17, "Value": ""}, {"Time": 18.733, "Value": ""}]}