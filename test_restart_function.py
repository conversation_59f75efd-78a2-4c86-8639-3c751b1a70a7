#!/usr/bin/env python3
"""
测试重启功能
验证重启功能是否正常工作
"""

import sys
import os
import time

def test_restart_function():
    """测试重启功能"""
    print("🔍 测试重启功能")
    print("=" * 60)
    
    try:
        # 设置环境变量
        os.environ["QT_LOGGING_RULES"] = "qt.qpa.fonts.warning=false"
        
        from PySide6.QtWidgets import QApplication, QMenu
        from PySide6.QtGui import QAction
        from PySide6.QtCore import Qt
        
        print("✅ Qt模块导入成功")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        print("✅ QApplication创建成功")
        
        # 测试导入主窗口类
        sys.path.append('dev')
        try:
            from main_window import Live2DWindow
            print("✅ Live2DWindow类导入成功")
            
            # 检查重启方法是否存在
            if hasattr(Live2DWindow, 'restart_application'):
                print("✅ restart_application方法存在")
            else:
                print("❌ restart_application方法不存在")
                return False
                
        except Exception as e:
            print(f"❌ Live2DWindow导入失败: {e}")
            return False
        
        # 测试右键菜单创建
        try:
            # 创建一个简单的菜单来测试
            menu = QMenu()
            
            # 模拟添加重启选项
            restart_action = QAction("🔄 重启程序", menu)
            menu.addAction(restart_action)
            
            print("✅ 右键菜单重启选项创建成功")
            
        except Exception as e:
            print(f"❌ 菜单创建失败: {e}")
            return False
        
        print("\n🎉 重启功能测试完成")
        print("💡 重启功能已成功添加到右键菜单")
        
        # 清理
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_restart_implementation():
    """检查重启功能实现"""
    print("\n🔍 检查重启功能实现...")
    print("=" * 60)
    
    main_window_path = "dev/main_window.py"
    
    if not os.path.exists(main_window_path):
        print(f"❌ 文件不存在: {main_window_path}")
        return False
    
    try:
        with open(main_window_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能
        checks = [
            ("restart_application方法", "def restart_application(self):"),
            ("重启菜单项", "🔄 重启程序"),
            ("subprocess导入", "import subprocess"),
            ("确认对话框", "QMessageBox.question"),
            ("新进程启动", "subprocess.Popen"),
        ]
        
        all_passed = True
        for check_name, check_pattern in checks:
            if check_pattern in content:
                print(f"✅ {check_name}: 已实现")
            else:
                print(f"❌ {check_name}: 未找到")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查文件失败: {e}")
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n📖 重启功能使用说明")
    print("=" * 60)
    print("1. 启动Live2D程序")
    print("2. 在Live2D模型上右键点击")
    print("3. 在右键菜单中选择 '🔄 重启程序'")
    print("4. 确认重启对话框")
    print("5. 程序将自动保存状态并重启")
    print("")
    print("💡 重启功能的作用:")
    print("   - 应用最新的代码更新")
    print("   - 重新加载配置文件")
    print("   - 清理内存状态")
    print("   - 自动保存当前窗口位置和模型状态")

if __name__ == "__main__":
    print("🚀 开始重启功能验证")
    print("=" * 80)
    
    # 检查实现
    implementation_ok = check_restart_implementation()
    
    # 测试功能
    function_test_ok = test_restart_function()
    
    print("\n" + "=" * 80)
    if implementation_ok and function_test_ok:
        print("🎉 重启功能添加成功！")
        show_usage_instructions()
    else:
        print("❌ 重启功能可能存在问题")
        if not implementation_ok:
            print("   - 实现检查失败")
        if not function_test_ok:
            print("   - 功能测试失败")
