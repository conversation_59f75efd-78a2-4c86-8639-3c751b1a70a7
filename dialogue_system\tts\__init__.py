#!/usr/bin/env python3
"""
Live2D语音对话系统 - 文本转语音(TTS)模块

这个模块提供了完整的语音合成功能，包括：
- GPT-SoVITS API客户端
- TTS管理器和配置
- 音频播放器
- 语音预设管理

使用示例：
    from dialogue_system.tts import TTSManager, GPTSoVITSClient

    # 创建TTS管理器
    tts_manager = TTSManager(config_manager)

    # 合成语音
    audio_data = tts_manager.synthesize_text("你好，世界！")

高级使用示例：
    from dialogue_system.tts import GPTSoVITSClient, AudioPlayer

    # 直接使用GPT-SoVITS客户端
    tts_client = GPTSoVITSClient("http://localhost:9880")
    result = tts_client.synthesize("你好", preset="shizuru")
"""

# 导入核心组件
from .gptsovits_client import GPTSoVITSClient
from .audio_player import AudioPlayer
from .tts_manager import TTSManager

__version__ = "1.0.0"
__author__ = "Live2D Voice Dialogue Team"

# 导出主要类
__all__ = [
    'GPTSoVITSClient',
    'AudioPlayer',
    'TTSManager'
]
