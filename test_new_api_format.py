#!/usr/bin/env python3
"""
测试新的OpenAI API调用格式
验证请求和响应格式是否符合要求
"""

import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_api_format():
    """测试API格式"""
    print("🔍 测试新的OpenAI API调用格式")
    print("=" * 60)
    
    try:
        # 导入LLM客户端
        from dialogue_system.core.llm_client import LLMClient
        from dialogue_system.config.config_manager import ConfigManager
        
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 创建LLM客户端
        llm_client = LLMClient(config_manager)
        
        print("✅ LLM客户端初始化成功")
        print(f"📋 当前配置:")
        print(f"   - API地址: {llm_client.api_config.get('base_url')}")
        print(f"   - 模型: {llm_client.api_config.get('model')}")
        print(f"   - API密钥: {'已设置' if llm_client.api_config.get('api_key') else '未设置'}")
        
        print(f"\n📋 默认参数:")
        for key, value in llm_client.default_params.items():
            print(f"   - {key}: {value}")
        
        # 检查是否配置完整
        if not llm_client.is_configured():
            print("⚠️ API配置不完整，无法进行实际测试")
            print("💡 请在config.json中设置正确的API密钥")
            return
        
        print("\n🚀 发送测试消息...")
        
        # 发送测试消息
        test_message = "Hi"
        response = llm_client.chat(test_message)
        
        if response and not response.startswith("❌"):
            print(f"✅ 测试成功!")
            print(f"📤 发送消息: {test_message}")
            print(f"📥 收到回复: {response}")
        else:
            print(f"❌ 测试失败: {response}")
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保所有依赖已安装")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_manual_api_call():
    """手动测试API调用"""
    print("\n🔧 手动测试API调用")
    print("=" * 60)
    
    try:
        from openai import OpenAI
        
        # 读取配置
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        # 使用openai_config配置
        openai_config = config.get("openai_config", {})
        
        if not openai_config.get("api_key"):
            print("⚠️ openai_config中未设置API密钥")
            return
        
        # 创建客户端
        client = OpenAI(
            base_url=openai_config["base_url"],
            api_key=openai_config["api_key"],
            timeout=30.0
        )
        
        # 准备请求参数
        request_params = {
            "messages": [{"role": "user", "content": "Hi"}],
            "model": openai_config["model"],
            **openai_config["default_params"]
        }
        
        # 清理None值
        cleaned_params = {k: v for k, v in request_params.items() if v is not None}
        
        print("📤 发送的请求格式:")
        print(json.dumps(cleaned_params, indent=2, ensure_ascii=False))
        
        # 发送请求
        response = client.chat.completions.create(**cleaned_params)
        
        print("\n📥 收到的回复格式:")
        if hasattr(response, 'model_dump'):
            response_dict = response.model_dump()
            print(json.dumps(response_dict, indent=2, ensure_ascii=False))
            
            # 提取关键信息
            if response_dict.get('choices'):
                choice = response_dict['choices'][0]
                message = choice.get('message', {})
                content = message.get('content')
                role = message.get('role')
                
                print(f"\n提取的消息: {{content: '{content}', role: '{role}'}}")
        else:
            print(f"响应类型: {type(response)}")
            print(f"响应内容: {response}")
            
    except Exception as e:
        print(f"❌ 手动测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_api_format()
    test_manual_api_call()
