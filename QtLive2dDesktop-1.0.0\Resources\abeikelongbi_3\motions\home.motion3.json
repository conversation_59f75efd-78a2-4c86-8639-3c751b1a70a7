{"Version": 3, "Meta": {"Duration": 7.0, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 244, "TotalSegmentCount": 23790, "TotalPointCount": 31390, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamBG2Hide", "Segments": [0, 1, 2, 4.5, 1, 2, 4.533, 1, 1, 4.622, 1, 4.711, 0.4, 4.8, 0.252, 1, 4.944, 0.011, 5.089, 0, 5.233, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamBGX", "Segments": [0, 0, 2, 1, 0, 0, 1.033, 2, 2, 4.5, 2, 0, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamBGY", "Segments": [0, -1, 1, 0.333, -1, 0.667, -0.688, 1, 0, 1, 1.011, 0.023, 1.022, 30, 1.033, 30, 2, 4.5, 30, 1, 4.511, 30, 4.522, 0, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 2, 1, 0, 0, 1.033, 20, 2, 4.5, 20, 0, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamCHY", "Segments": [0, 0, 0, 1, -0.72, 0, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamChaSize", "Segments": [0, 0, 0, 1, 3, 0, 1.033, 0, 2, 4.5, 0, 0, 4.533, -2, 0, 5.167, 0.378, 0, 5.467, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamCcharacterZ", "Segments": [0, 0.912, 0, 0.3, 2, 2, 0.533, 2, 0, 0.867, 0, 2, 1, 0, 2, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionX", "Segments": [0, -9, 0, 1, 30, 1, 1.011, 30, 1.022, 0, 1.033, 0, 0, 1.633, 30, 2, 4.5, 30, 1, 4.511, 30, 4.522, 0, 4.533, 0, 0, 5.6, 30, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionX", "Segments": [0, 0, 2, 1, 0, 0, 1.033, -15, 2, 2.367, -15, 0, 4.5, -20, 0, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionY", "Segments": [0, -10.52, 0, 1, -12.842, 1, 1.011, -12.842, 1.022, 17.64, 1.033, 17.64, 0, 1.5, 11.88, 2, 4.5, 11.88, 0, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamAllSize", "Segments": [0, 12, 0, 1, 13, 1, 1.011, 13, 1.022, 12.171, 1.033, 12, 1, 1.189, 9.611, 1.344, 8.579, 1.5, 8.579, 2, 4.5, 8.579, 1, 4.511, 8.579, 4.522, 0.857, 4.533, 0.84, 1, 4.911, 0.264, 5.289, 0, 5.667, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamDeskShow", "Segments": [0, 10, 2, 1, 10, 2, 1.033, 10, 2, 4.5, 10, 1, 4.511, 10, 4.522, 2.7, 4.533, 2.7, 0, 5.6, 10, 2, 7, 10]}, {"Target": "Parameter", "Id": "ParamStrongCatShow", "Segments": [0, 10, 2, 1, 10, 2, 1.033, 10, 2, 4.5, 10, 0, 4.533, 0, 0, 5.6, 10, 2, 7, 10]}, {"Target": "Parameter", "Id": "ParamCannonShow", "Segments": [0, 10, 2, 1, 10, 2, 1.033, 10, 2, 4.5, 10, 0, 4.533, 0, 0, 5.6, 10, 2, 7, 10]}, {"Target": "Parameter", "Id": "ParamPupilExp", "Segments": [0, 0, 2, 0.4, 0, 0, 0.6, 1, 2, 1, 1, 0, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.3, 1, 0, 0.433, 0, 0, 0.567, 1, 0, 0.733, 0.7, 2, 1, 0.7, 0, 1.033, 0, 0, 1.367, 0.8, 2, 2.633, 0.8, 0, 2.733, 0, 0, 3, 1, 2, 4.5, 1, 0, 4.533, 0, 0, 5.6, 1, 0, 5.833, 0, 0, 6.033, 1, 2, 7, 1]}, {"Target": "Parameter", "Id": "ParamEyeSmileL", "Segments": [0, 1, 2, 0.133, 1, 0, 0.3, 0, 2, 0.4, 0, 0, 0.433, 1, 2, 1, 1, 0, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.3, 1, 0, 0.433, 0, 0, 0.567, 1, 0, 0.733, 0.7, 2, 1, 0.7, 0, 1.033, 0, 0, 1.367, 0.8, 2, 2.633, 0.8, 0, 2.733, 0, 0, 3, 1, 2, 4.5, 1, 0, 4.533, 0, 0, 5.6, 1, 0, 5.833, 0, 0, 6.033, 1, 2, 7, 1]}, {"Target": "Parameter", "Id": "ParamEyeSmileR", "Segments": [0, 1, 2, 0.133, 1, 0, 0.3, 0, 2, 0.4, 0, 0, 0.433, 1, 2, 1, 1, 0, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 2, 0.367, 1, 0, 0.433, 0.5, 0, 0.633, 1, 2, 1, 1, 0, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 1, 4.589, 0, 4.644, 0.035, 4.7, -0.1, 1, 4.789, -0.316, 4.878, -1, 4.967, -1, 1, 5.034, -1, 5.1, 0.52, 5.167, 0.7, 1, 5.278, 1, 5.389, 1, 5.5, 1, 0, 5.633, -0.4, 0, 5.833, 0.5, 0, 6.067, -0.8, 1, 6.178, -0.8, 6.289, 0.008, 6.4, 0.3, 1, 6.489, 0.533, 6.578, 0.5, 6.667, 0.5, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0.5, 2, 0.033, 0.5, 0, 0.433, 0, 2, 1, 0, 2, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 0, 4.7, 1, 0, 4.833, 0, 0, 4.967, 1, 0, 5.167, 0.2, 0, 5.4, 1, 0, 5.6, 0, 0, 5.967, 0.6, 0, 6.2, 0.1, 0, 6.567, 1, 1, 6.667, 1, 6.767, 0.423, 6.867, 0.1, 1, 6.911, 0, 6.956, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamMouthY", "Segments": [0, 0, 2, 0.233, 0, 0, 0.5, 0.3, 0, 0.8, 0, 2, 1, 0, 2, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamEyeEmotion", "Segments": [0, 1, 2, 0.5, 1, 0, 0.6, -1, 2, 1, -1, 1, 1.011, -1, 1.022, -0.005, 1.033, -0.002, 1, 1.044, 0.001, 1.056, 0, 1.067, 0, 2, 3.133, 0, 0, 3.567, -0.1, 0, 3.733, 1, 2, 4.467, 1, 2, 4.5, 1, 2, 4.533, 1, 2, 5.9, 1, 0, 6.233, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLX", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 2.3, 0, 0, 3, 0.7, 0, 3.233, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLY", "Segments": [0, 0, 2, 1, 0, 0, 1.033, 0.031, 1, 1.044, 0.031, 1.056, 0.038, 1.067, 0, 1, 1.211, -0.498, 1.356, -0.8, 1.5, -0.8, 2, 2.567, -0.8, 0, 2.733, 0, 0, 3, -0.4, 2, 4.5, -0.4, 2, 4.533, -0.4, 2, 5.367, -0.4, 0, 5.833, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeL", "Segments": [0, 0, 2, 1, 0, 1, 1.011, 0, 1.022, -0.357, 1.033, -0.381, 1, 1.044, -0.405, 1.056, -0.4, 1.067, -0.4, 2, 1.167, -0.4, 0, 1.3, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRX", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 2.3, 0, 0, 3, 0.4, 0, 3.233, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRY", "Segments": [0, 0, 2, 1, 0, 0, 1.033, 0.031, 1, 1.044, 0.031, 1.056, 0.038, 1.067, 0, 1, 1.211, -0.498, 1.356, -0.8, 1.5, -0.8, 2, 2.567, -0.8, 0, 2.733, 0, 0, 3, -0.4, 2, 4.5, -0.4, 2, 4.533, -0.4, 2, 5.367, -0.4, 0, 5.833, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeR", "Segments": [0, 0, 2, 1, 0, 1, 1.011, 0, 1.022, -0.357, 1.033, -0.381, 1, 1.044, -0.405, 1.056, -0.4, 1.067, -0.4, 2, 1.167, -0.4, 0, 1.3, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamPHYInputX", "Segments": [0, -0.024, 0, 0.067, -10.083, 0, 0.333, 23.778, 0, 0.533, -22.311, 0, 0.867, 30, 0, 1.2, -30, 2, 1.233, -30, 0, 1.567, 13, 0, 1.9, -8.499, 0, 2.267, 9.093, 0, 2.6, -5.805, 0, 2.867, 1.071, 0, 2.967, 0.441, 0, 3.2, 9.804, 0, 3.533, -11.775, 0, 3.9, 9.024, 0, 4.267, -5.688, 0, 4.467, -0.513, 0, 4.533, -11.508, 0, 4.733, 23.778, 0, 5.1, -19.938, 0, 5.533, 23.239, 0, 5.933, -18, 0, 6.333, 17.562, 0, 6.767, -8.499, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, -29, 0, 0.533, 12, 0, 1, -2.939, 1, 1.011, -2.939, 1.022, 21.107, 1.033, 21.31, 1, 1.144, 23.341, 1.256, 24.133, 1.367, 24.133, 0, 2, 23.511, 0, 2.7, 30, 2, 2.833, 30, 1, 2.955, 30, 3.078, 1.549, 3.2, 0, 1, 3.756, -7.041, 4.311, -9, 4.867, -9, 0, 5.467, 21.31, 1, 5.545, 21.31, 5.622, 17.621, 5.7, 11, 1, 5.789, 3.433, 5.878, 0, 5.967, 0, 0, 6.6, 3, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 15, 0, 0.433, 5, 0, 0.667, 16.078, 1, 0.778, 16.078, 0.889, 8.186, 1, -11, 1, 1.011, -12.919, 1.022, -27.979, 1.033, -28.607, 1, 1.055, -29.864, 1.078, -30, 1.1, -30, 0, 1.3, -23, 0, 1.533, -25, 2, 2.5, -25, 1, 2.667, -25, 2.833, 2.36, 3, 3, 2, 3.067, 3, 0, 3.333, -8.691, 0, 3.633, -6.691, 2, 4.067, -6.691, 0, 4.367, 8, 1, 4.467, 8, 4.567, 6.473, 4.667, -5, 1, 4.734, -12.649, 4.8, -30, 4.867, -30, 0, 5.267, 14.296, 0, 5.567, -7, 0, 5.867, 18.835, 1, 5.967, 18.835, 6.067, 15.936, 6.167, 9.142, 1, 6.3, 0.083, 6.434, -5, 6.567, -5, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0.031, 1, 0.067, 0.031, 0.133, 4.192, 0.2, 9, 1, 0.322, 17.816, 0.445, 21, 0.567, 21, 1, 0.6, 21, 0.634, 15.919, 0.667, 12, 1, 0.778, -1.064, 0.889, -5.839, 1, -5.839, 1, 1.011, -5.839, 1.022, 13.702, 1.033, 14.274, 1, 1.089, 17.136, 1.144, 18, 1.2, 18, 0, 1.433, 17.075, 2, 2.233, 17.075, 1, 2.311, 17.075, 2.389, 17.318, 2.467, 18, 1, 2.534, 18.584, 2.6, 19, 2.667, 19, 0, 3.067, 4, 2, 4.2, 4, 1, 4.3, 4, 4.4, 4.059, 4.5, 4.204, 1, 4.511, 4.22, 4.522, 4.265, 4.533, 4.265, 0, 5.233, -7.009, 0, 5.8, 13.539, 0, 6.7, -0.618, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "Segments": [0, 0.075, 2, 0.1, 0.075, 0, 0.3, -4.285, 0, 0.5, 0, 2, 1, 0, 1, 1.011, 0, 1.022, 10.111, 1.033, 10.423, 1, 1.078, 11.671, 1.122, 12, 1.167, 12, 0, 1.5, 0, 2, 2.267, 0, 0, 2.7, 18, 0, 3.333, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamAngleH", "Segments": [0, 0.075, 2, 0.2, 0.075, 0, 0.5, 2, 0, 0.767, -3, 2, 1, -3, 0, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamAngleS", "Segments": [0, 0.009, 0, 0.5, 0, 0, 0.933, 1, 2, 1, 1, 0, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperLAngle", "Segments": [0, 0, 2, 1, 0, 0, 1.033, -2.929, 0, 1.133, -2.888, 0, 1.4, -3, 2, 2.4, -3, 0, 2.633, -3.742, 1, 2.722, -3.742, 2.811, -3.759, 2.9, -3.668, 1, 2.989, -3.577, 3.078, -2.732, 3.167, -2.732, 1, 3.322, -2.732, 3.478, -2.746, 3.633, -2.767, 1, 3.922, -2.806, 4.211, -2.841, 4.5, -2.877, 1, 4.511, -2.878, 4.522, -2.879, 4.533, -2.88, 1, 4.633, -2.886, 4.733, -2.888, 4.833, -2.888, 2, 5.433, -2.888, 0, 5.7, 1, 2, 5.933, 1, 0, 6.4, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamArmHandLAngle", "Segments": [0, 10, 0, 1, -5, 0, 1.033, 0.98, 2, 2.533, 0.98, 0, 2.9, 8.4, 1, 2.989, 8.4, 3.078, 6.88, 3.167, 6, 1, 3.222, 5.45, 3.278, 5.513, 3.333, 5.513, 2, 4.5, 5.513, 2, 4.533, 5.513, 0, 5.233, 5.5, 0, 5.633, 10, 1, 5.711, 10, 5.789, 9.931, 5.867, 7.856, 1, 6.011, 4.003, 6.156, 0, 6.3, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerLAngle", "Segments": [0, -5.454, 0, 0.5, 0.791, 1, 0.667, 0.791, 0.833, 0.145, 1, -1.386, 1, 1.011, -1.488, 1.022, -6.078, 1.033, -6.078, 1, 1.511, -6.079, 1.989, -6.08, 2.467, -6.08, 0, 2.7, -5.836, 2, 2.867, -5.836, 0, 3.167, -9.348, 0, 3.5, -8.766, 2, 4.5, -8.766, 2, 4.533, -8.766, 2, 5.2, -8.766, 1, 5.367, -8.766, 5.533, -6.115, 5.7, -0.663, 1, 5.778, 1.882, 5.855, 5.311, 5.933, 7.128, 1, 6.033, 9.463, 6.133, 10, 6.233, 10, 2, 7, 10]}, {"Target": "Parameter", "Id": "ParamArmLowerLH", "Segments": [0, -18, 0, 1, -28, 0, 1.033, 0, 2, 2.467, 0, 0, 3.033, -10.106, 0, 3.4, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 5.567, 0, 0, 5.8, -19.269, 0, 6, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerLAngle", "Segments": [0, 0, 0, 0.4, 10, 0, 0.7, -10, 1, 0.8, -10, 0.9, -7.175, 1, 0, 1, 1.011, 0.797, 1.022, 6, 1.033, 6, 2, 2.633, 6, 1, 2.811, 6, 2.989, 6.04, 3.167, 5.783, 1, 3.611, 5.142, 4.056, 3.937, 4.5, 2.997, 1, 4.511, 2.973, 4.522, 2.926, 4.533, 2.926, 0, 5.333, 10, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamHandT2L", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 2.8, 0, 0, 2.967, 0.5, 0, 3.233, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamArm<PERSON>pperRAngle", "Segments": [0, -1.812, 0, 0.333, 1.648, 0, 1, -1.044, 0, 1.033, 3.85, 2, 2.5, 3.85, 0, 2.8, 3.13, 0, 3.233, 5, 0, 3.6, 4.78, 2, 4.5, 4.78, 2, 4.533, 4.78, 2, 5.4, 4.78, 0, 5.633, 5, 0, 6.2, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRAngle", "Segments": [0, 9.58, 0, 0.533, -0.585, 1, 0.6, -0.585, 0.666, 2.834, 0.733, 3.547, 1, 0.822, 4.497, 0.911, 4.737, 1, 5.434, 1, 1.011, 5.521, 1.022, 18.678, 1.033, 18.678, 0, 1.3, 18.395, 2, 2.567, 18.395, 0, 2.933, 5, 0, 3.2, 6.24, 2, 4.5, 6.24, 0, 4.533, 30, 1, 4.711, 30, 4.889, 30, 5.067, 23.524, 1, 5.256, 16.607, 5.444, -0.585, 5.633, -0.585, 0, 6.3, 7.76, 0, 6.567, 7.4, 2, 7, 7.4]}, {"Target": "Parameter", "Id": "ParamArmHandRAngle", "Segments": [0, 10, 0, 0.6, -10, 2, 1, -10, 0, 1.033, -8.629, 0, 1.433, -10, 2, 2.633, -10, 0, 2.867, -4, 0, 3.133, -9.34, 1, 3.189, -9.34, 3.244, -8.447, 3.3, -8.444, 1, 3.556, -8.43, 3.811, -8.427, 4.067, -8.427, 0, 4.367, -9.169, 1, 4.411, -9.169, 4.456, -9.025, 4.5, -8.782, 1, 4.511, -8.721, 4.522, -8.673, 4.533, -8.634, 1, 4.555, -8.557, 4.578, -8.529, 4.6, -8.529, 0, 4.833, -8.993, 0, 5.267, 10, 0, 5.8, -10, 0, 6.4, 1.331, 0, 6.7, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRH", "Segments": [0, -11, 2, 1, -11, 0, 1.033, -12, 2, 2.8, -12, 0, 3, -11.38, 2, 3.067, -11.38, 0, 3.333, -12, 0, 3.633, -11.02, 2, 3.967, -11.02, 0, 4.267, -9.98, 1, 4.345, -9.98, 4.422, -10.36, 4.5, -10.888, 1, 4.511, -10.963, 4.522, -10.995, 4.533, -11.008, 1, 4.544, -11.021, 4.556, -11.02, 4.567, -11.02, 0, 4.8, -9.98, 2, 5.467, -9.98, 0, 5.733, 0, 2, 6.033, 0, 2, 6.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperRH", "Segments": [0, 0, 2, 1, 0, 0, 1.033, 5, 2, 2.8, 5, 0, 2.9, 4.98, 0, 3.2, 8, 0, 3.467, 4.819, 2, 4.5, 4.819, 2, 4.533, 4.819, 2, 5.233, 4.819, 0, 6.233, 5.5, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRAngle", "Segments": [0, -1.072, 1, 0.067, -1.072, 0.133, -1.231, 0.2, 0, 1, 0.3, 1.846, 0.4, 6, 0.5, 6, 0, 0.733, 0, 2, 1, 0, 0, 1.033, 10, 2, 2.8, 10, 1, 2.989, 10, 3.178, 0.784, 3.367, 0.31, 1, 3.534, -0.109, 3.7, 0, 3.867, 0, 2, 4.5, 0, 2, 4.533, 0, 0, 5.433, -10, 0, 5.767, 10, 0, 6.433, -10, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamHandT1R", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 2.8, 0, 1, 2.989, 0, 3.178, 0.921, 3.367, 0.969, 1, 3.534, 1, 3.7, 1, 3.867, 1, 2, 4.433, 1, 2, 4.5, 1, 2, 4.533, 1, 2, 4.833, 1, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamHandT2R", "Segments": [0, 0.016, 0, 0.067, 0, 2, 0.3, 0, 0, 0.5, 0.5, 0, 0.7, 0, 2, 1, 0, 0, 1.033, 0.094, 0, 1.233, 0, 0, 2.8, 0.1, 1, 2.989, 0.1, 3.178, 0.1, 3.367, 0.096, 1, 3.745, 0.087, 4.122, 0.069, 4.5, 0.055, 0, 4.533, 0.053, 1, 5.355, 0.018, 6.178, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0.06, 1, 0.333, 0.06, 0.667, 0.053, 1, 0.039, 0, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, -9.999, 1, 0.333, -9.999, 0.667, -9.689, 1, -9.004, 1, 1.011, -8.981, 1.022, 0, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 5.4, 0, 0, 5.667, 1, 0, 6.267, -0.28, 0, 6.567, 0.22, 0, 6.8, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 0, 0.367, 10, 0, 0.8, -10, 0, 1, -7.544, 0, 1.033, -9.22, 0, 1.1, -9, 0, 1.267, -10, 0, 1.467, -9.295, 0, 1.7, -10, 2, 2.467, -10, 0, 2.967, 3, 1, 3.034, 3, 3.1, 0.981, 3.167, -0.399, 1, 3.234, -1.779, 3.3, -1.92, 3.367, -1.92, 2, 3.667, -1.92, 2, 3.967, -1.92, 0, 4.267, 0.213, 1, 4.345, 0.213, 4.422, -0.572, 4.5, -1.65, 1, 4.511, -1.804, 4.522, -1.878, 4.533, -1.894, 1, 4.544, -1.91, 4.556, -1.853, 4.567, -1.92, 1, 4.656, -2.452, 4.744, -10, 4.833, -10, 0, 5.167, 10, 0, 5.767, -1, 0, 6.167, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, -0.001, 1, 0.022, -0.001, 0.045, -0.004, 0.067, 0, 1, 0.178, 0.02, 0.289, 1.254, 0.4, 1.254, 1, 0.533, 1.254, 0.667, -7.037, 0.8, -8.154, 1, 0.867, -8.713, 0.933, -8.488, 1, -8.488, 1, 1.011, -8.488, 1.022, 1.184, 1.033, 1.314, 1, 1.1, 2.094, 1.166, 2.342, 1.233, 2.342, 0, 1.567, 2.083, 0, 1.867, 2.514, 0, 2.033, 2.342, 2, 2.233, 2.342, 0, 2.833, 3.158, 1, 2.911, 3.158, 2.989, 3.178, 3.067, 2.8, 1, 3.145, 2.422, 3.222, 0.189, 3.3, 0, 1, 3.356, -0.135, 3.411, -0.096, 3.467, -0.096, 0, 3.633, 0, 2, 4.5, 0, 0, 4.533, -10, 0, 5.067, 10, 0, 5.7, -10, 0, 6.3, 5, 0, 6.867, -0.899, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX2", "Segments": [0, -5.36, 1, 0.067, -5.36, 0.133, -5.41, 0.2, -4.285, 1, 0.3, -2.597, 0.4, 0, 0.5, 0, 2, 1, 0, 2, 1.033, 0, 2, 2.467, 0, 2, 3.033, 0, 2, 3.867, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 4.833, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY2", "Segments": [0, 0.075, 0, 0.167, -1.378, 0, 0.467, 1.935, 0, 1, -8.637, 0, 1.033, -5.048, 0, 1.267, -5.545, 0, 1.533, -4.907, 0, 1.967, -5.545, 1, 2.134, -5.545, 2.3, -5.613, 2.467, -5.246, 1, 2.656, -4.83, 2.844, -0.533, 3.033, -0.31, 1, 3.311, 0.018, 3.589, 0, 3.867, 0, 2, 4.5, 0, 2, 4.533, 0, 0, 4.833, -10, 0, 5.1, 10, 1, 5.256, 10, 5.411, 6.872, 5.567, 3.271, 1, 5.711, -0.073, 5.856, -0.898, 6, -0.898, 0, 6.267, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechX", "Segments": [0, -0.156, 0, 0.167, 0.075, 0, 0.633, 0, 0, 1, 2.854, 0, 1.033, 2.78, 0, 1.167, 6, 0, 1.333, 5, 2, 2.467, 5, 0, 2.667, 6, 0, 3.033, -3, 0, 3.233, 0, 2, 3.867, 0, 2, 4.5, 0, 0, 4.533, 10, 0, 4.967, -10, 1, 5.089, -10, 5.211, -2.665, 5.333, 0, 1, 5.433, 2.181, 5.533, 2, 5.633, 2, 0, 6.333, -10, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechW", "Segments": [0, 0.059, 0, 1, 0.036, 0, 1.033, 3.724, 0, 1.233, -1, 0, 1.467, 1, 0, 1.667, -0.502, 0, 2.167, 0, 2, 2.6, 0, 0, 2.9, -4.008, 0, 3.133, 3.008, 0, 3.367, -1, 0, 3.6, 0, 2, 4.5, 0, 0, 4.533, 10, 0, 5.167, -10, 0, 5.8, 10, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, -0.026, 1, 0.067, -0.026, 0.133, -0.043, 0.2, 0, 1, 0.311, 0.071, 0.422, 0.901, 0.533, 0.901, 0, 0.9, -2, 1, 0.933, -2, 0.967, -1.961, 1, -1.721, 1, 1.011, -1.641, 1.022, 3.025, 1.033, 3.025, 1, 1.044, 3.025, 1.056, 3.023, 1.067, 3, 1, 1.211, 2.695, 1.356, 2.527, 1.5, 2.527, 0, 1.7, 2.623, 0, 2.2, 2.342, 0, 2.7, 3.158, 1, 2.811, 3.158, 2.922, 3.222, 3.033, 3, 1, 3.178, 2.711, 3.322, -0.14, 3.467, -0.14, 0, 3.8, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 5.2, 0, 0, 5.533, -1, 0, 6.133, 0.58, 0, 6.6, -0.14, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamShoulderR", "Segments": [0, 0, 0, 0.433, -30, 0, 1, 30, 1, 1.011, 30, 1.022, 0, 1.033, 0, 2, 2.5, 0, 0, 3, 8, 2, 3.067, 8, 0, 3.367, -3, 2, 3.667, -3, 0, 4.033, -4, 0, 4.333, 5, 1, 4.389, 5, 4.444, 3.333, 4.5, 0.346, 1, 4.511, -0.251, 4.522, 0.241, 4.533, -1.173, 1, 4.566, -5.414, 4.6, -30, 4.633, -30, 1, 4.722, -30, 4.811, -21.232, 4.9, 0, 1, 4.978, 18.578, 5.055, 30, 5.133, 30, 2, 5.733, 30, 0, 6.1, -30, 0, 6.5, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamShoulderL", "Segments": [0, 0, 0, 0.433, -30, 0, 1, 30, 1, 1.011, 30, 1.022, 0, 1.033, 0, 2, 2.5, 0, 0, 3, 8, 2, 3.067, 8, 0, 3.367, -3, 2, 3.667, -3, 0, 4.033, -4, 0, 4.333, 5, 1, 4.389, 5, 4.444, 3.333, 4.5, 0.346, 1, 4.511, -0.251, 4.522, 0.241, 4.533, -1.173, 1, 4.566, -5.414, 4.6, -30, 4.633, -30, 1, 4.722, -30, 4.811, -21.232, 4.9, 0, 1, 4.978, 18.578, 5.055, 30, 5.133, 30, 2, 5.733, 30, 0, 6.1, -30, 0, 6.5, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Z", "Segments": [0, 0, 0, 0.867, -3, 0, 2.067, 18, 1, 2.411, 18, 2.756, 17.249, 3.1, 14.52, 1, 3.578, 10.734, 4.055, 8.007, 4.533, 8.007, 0, 5.567, 30, 0, 6.067, -11, 0, 6.833, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamLegR1X", "Segments": [0, 0, 0, 3.067, -30, 2, 3.767, -30, 1, 4.022, -30, 4.278, -29.785, 4.533, -18.333, 1, 4.744, -8.872, 4.956, 30, 5.167, 30, 0, 6.067, -3, 0, 6.667, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Z", "Segments": [0, 0, 0, 1.433, -12, 0, 2.367, 4, 0, 3.633, -6, 0, 4.533, -0.844, 0, 5.167, -30, 0, 6.067, 3, 0, 6.667, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Z", "Segments": [0, 0, 0, 1.633, -9, 0, 2.633, 28, 0, 3.8, -5.992, 0, 4.2, 0, 0, 4.533, -0.759, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Y", "Segments": [0, 0, 2, 1.633, 0, 0, 2.933, 9.074, 0, 4.333, -0.262, 0, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Y", "Segments": [0, 0, 2, 2.4, 0, 0, 2.833, 10, 0, 3.867, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Z", "Segments": [0, 0, 2, 0.833, 0, 0, 1.9, 0.9, 2, 2.6, 0.9, 0, 3.733, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamFootLX", "Segments": [0, 0, 2, 0.833, 0, 0, 1.9, -30, 2, 2.6, -30, 0, 3.733, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRInput", "Segments": [0, 0, 2, 1, 0, 0, 1.033, -12.634, 0, 2.1, 30, 0, 3.767, -30, 1, 4.011, -30, 4.256, -19.23, 4.5, 2.152, 1, 4.511, 3.124, 4.522, 4.038, 4.533, 4.92, 1, 4.744, 21.678, 4.956, 30, 5.167, 30, 0, 5.7, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamMRCupSet", "Segments": [0, 1, 2, 1, 1, 0, 1.033, 0, 2, 3.833, 0, 0, 3.9, 1, 2, 4.5, 1, 2, 4.533, 1, 2, 7, 1]}, {"Target": "Parameter", "Id": "ParamManjuuREyeOpen", "Segments": [0, 0, 2, 1, 0, 0, 1.033, 1, 2, 3.433, 1, 0, 3.7, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 4.767, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyX", "Segments": [0, -5.963, 0, 0.467, -7.178, 1, 0.645, -7.178, 0.822, -7.138, 1, -7.042, 1, 1.011, -7.036, 1.022, 0, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyY", "Segments": [0, -11.245, 0, 0.133, -11.811, 1, 0.422, -11.811, 0.711, -11.652, 1, -11.294, 1, 1.011, -11.28, 1.022, 0, 1.033, 0, 2, 3, 0, 0, 3.667, -30, 0, 4.133, 11.181, 0, 4.333, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyZ", "Segments": [0, -6.445, 1, 0.333, -6.445, 0.667, -4.306, 1, 0.341, 1, 1.011, 0.496, 1.022, 12.218, 1.033, 12.218, 0, 2.667, -30, 2, 3.2, -30, 0, 3.933, 10, 0, 4.267, -1, 0, 4.4, 1, 1, 4.433, 1, 4.467, 0.731, 4.5, 0.209, 1, 4.511, 0.035, 4.522, -0.14, 4.533, -0.299, 1, 4.566, -0.776, 4.6, -1, 4.633, -1, 0, 4.967, 20, 0, 5.133, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRPositionZ", "Segments": [0, -6.73, 1, 0.333, -6.73, 0.667, -4.718, 1, -0.509, 1, 1.011, -0.369, 1.022, -0.119, 1.033, -0.119, 0, 1.367, -5, 2, 1.5, -5, 0, 1.9, -12.409, 2, 2.067, -12.409, 0, 2.467, -18.236, 2, 2.667, -18.236, 1, 2.767, -18.236, 2.867, -21.662, 2.967, -24.236, 1, 3.011, -25.38, 3.056, -25.236, 3.1, -25.236, 0, 3.633, 3, 0, 3.9, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRArmB", "Segments": [0, 30, 2, 1, 30, 1, 1.011, 30, 1.022, -7.521, 1.033, -7.521, 0, 1.167, -6, 2, 2.833, -6, 0, 3.033, -0.44, 0, 3.267, -6, 1, 3.422, -6, 3.578, -1.438, 3.733, 14, 1, 3.789, 19.514, 3.844, 30, 3.9, 30, 2, 4.5, 30, 2, 4.533, 30, 2, 7, 30]}, {"Target": "Parameter", "Id": "ParamManjuuRMouth", "Segments": [0, 0, 2, 1, 0, 0, 1.033, -3.244, 0, 1.233, 27, 0, 1.533, -30, 0, 1.9, 30, 0, 2.2, -28.5, 0, 2.533, 30, 0, 2.9, -30, 0, 3.333, 23.103, 0, 3.567, -18, 0, 3.833, 5.118, 0, 4.067, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 4.733, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow1", "Segments": [0, 0, 2, 1, 0, 1, 1.011, 0, 1.022, 5.099, 1.033, 5.333, 1, 1.189, 6.889, 1.344, 8.444, 1.5, 10, 1, 1.511, 6.667, 1.522, 3.333, 1.533, 0, 1, 1.866, 3.333, 2.2, 6.667, 2.533, 10, 1, 2.544, 6.667, 2.556, 3.333, 2.567, 0, 1, 2.867, 3.333, 3.167, 6.667, 3.467, 10, 2, 3.5, 0, 2, 3.6, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow2", "Segments": [0, 0, 2, 1, 0, 1, 1.011, 0, 1.022, 0.773, 1.033, 1, 1, 1.333, 4, 1.633, 7, 1.933, 10, 1, 1.944, 6.667, 1.956, 3.333, 1.967, 0, 1, 2.3, 3.333, 2.634, 6.667, 2.967, 10, 1, 2.978, 6.667, 2.989, 3.333, 3, 0, 1, 3.167, 3.333, 3.333, 6.667, 3.5, 10, 2, 3.533, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow3", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 1.4, 0, 1, 1.733, 3.333, 2.067, 6.667, 2.4, 10, 1, 2.411, 6.667, 2.422, 3.333, 2.433, 0, 1, 2.766, 3.333, 3.1, 6.667, 3.433, 10, 2, 3.467, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow4", "Segments": [0, 0, 2, 1, 0, 1, 1.011, 0, 1.022, 3.232, 1.033, 3.429, 1, 1.289, 5.619, 1.544, 7.81, 1.8, 10, 1, 1.811, 6.667, 1.822, 3.333, 1.833, 0, 2, 2.1, 0, 1, 2.444, 3.333, 2.789, 6.667, 3.133, 10, 1, 3.144, 6.667, 3.156, 3.333, 3.167, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowB", "Segments": [0, 0, 2, 1, 0, 1, 1.011, 0, 1.022, 0.698, 1.033, 0.721, 1, 1.133, 0.926, 1.233, 1, 1.333, 1, 2, 3.267, 1, 0, 3.5, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW", "Segments": [0, 0, 2, 1, 0, 1, 1.011, 0, 1.022, 0.284, 1.033, 0.383, 1, 1.078, 0.778, 1.122, 0.9, 1.167, 0.9, 0, 1.467, -1, 0, 1.833, 1, 0, 2.133, -0.95, 0, 2.467, 1, 0, 2.833, -1, 0, 3.267, 0.77, 0, 3.5, -0.6, 0, 3.767, 0.171, 0, 4, 0, 2, 4.267, 0, 0, 4.4, 1, 1, 4.433, 1, 4.467, 0.24, 4.5, -0.688, 1, 4.511, -0.997, 4.522, -1, 4.533, -1, 0, 4.7, 1, 0, 4.8, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW2", "Segments": [0, 0, 2, 1, 0, 0, 1.033, -0.413, 0, 1.333, 1, 0, 1.733, -0.882, 0, 2.033, 1, 0, 2.3, -1, 0, 2.633, 0.904, 0, 3, -1, 0, 3.467, 0.733, 0, 3.7, -0.6, 0, 3.967, 0, 2, 4.1, 0, 2, 4.333, 0, 0, 4.467, 1, 1, 4.478, 1, 4.489, 0.928, 4.5, 0.688, 1, 4.511, 0.448, 4.522, 0.111, 4.533, -0.169, 1, 4.555, -0.729, 4.578, -1, 4.6, -1, 0, 4.767, 1, 0, 4.9, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX1", "Segments": [0, 0, 2, 1, 0, 0, 1.033, 0.716, 0, 1.4, -1, 0, 1.8, 1, 0, 2.167, -1, 0, 2.567, 1, 0, 3.033, -1, 0, 3.4, 1, 0, 3.533, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX2", "Segments": [0, 0, 2, 1, 0, 1, 1.011, 0, 1.022, 0.817, 1.033, 0.876, 1, 1.055, 0.994, 1.078, 1, 1.1, 1, 0, 1.5, -1, 0, 1.9, 1, 0, 2.367, -1, 0, 2.733, 1, 0, 3.133, -1, 0, 3.533, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamLiqH", "Segments": [0, 0, 2, 1, 0, 1, 1.011, 0, 1.022, 0.008, 1.033, 0.012, 1, 1.811, 0.273, 2.589, 0.4, 3.367, 0.4, 0, 3.667, 0, 2, 4.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX1", "Segments": [0, 0, 2, 1, 0, 0, 1.033, 0.143, 0, 1.167, -0.714, 0, 1.467, 0.803, 0, 1.7, -0.714, 0, 2.033, 0.803, 0, 2.333, -0.803, 0, 2.633, 0.9, 0, 2.833, -0.9, 0, 3.133, 1, 0, 3.367, -1, 1, 3.434, -1, 3.5, 0.253, 3.567, 0.8, 1, 3.6, 1, 3.634, 1, 3.667, 1, 0, 3.9, -1, 0, 4.133, 1, 0, 4.267, -1, 1, 4.345, -1, 4.422, -0.264, 4.5, 0.747, 1, 4.511, 0.891, 4.522, 0.95, 4.533, 0.976, 1, 4.544, 1, 4.556, 1, 4.567, 1, 0, 4.8, -1, 0, 5.133, 1, 0, 5.233, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX2", "Segments": [0, 0, 2, 1, 0, 0, 1.033, 1, 0, 1.233, -1, 0, 1.533, 1, 0, 1.767, -1, 0, 2.1, 1, 0, 2.4, -1, 0, 2.7, 1, 0, 2.9, -1, 0, 3.2, 1, 0, 3.433, -1, 1, 3.478, -1, 3.522, -0.336, 3.567, 0.2, 1, 3.622, 0.87, 3.678, 1, 3.733, 1, 0, 4, -1, 0, 4.167, 1, 0, 4.333, -1, 1, 4.389, -1, 4.444, -0.584, 4.5, 0.163, 1, 4.511, 0.312, 4.522, 0.436, 4.533, 0.543, 1, 4.566, 0.863, 4.6, 1, 4.633, 1, 0, 4.867, -1, 0, 5.2, 1, 0, 5.367, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX3", "Segments": [0, 0, 2, 1, 0, 1, 1.011, 0, 1.022, 0.175, 1.033, 0.227, 1, 1.066, 0.382, 1.1, 0.418, 1.133, 0.418, 0, 1.333, -0.418, 0, 1.633, 0.418, 0, 1.867, -0.418, 0, 2.2, 0.418, 0, 2.5, -0.418, 0, 2.8, 0.418, 0, 3, -0.418, 0, 3.3, 0.418, 0, 3.5, -0.418, 1, 3.522, -0.418, 3.545, -0.444, 3.567, -0.4, 1, 3.656, -0.223, 3.744, 0.418, 3.833, 0.418, 0, 4.067, -1, 0, 4.233, 1, 0, 4.433, -1, 1, 4.455, -1, 4.478, -0.939, 4.5, -0.747, 1, 4.511, -0.651, 4.522, -0.497, 4.533, -0.35, 1, 4.6, 0.533, 4.666, 1, 4.733, 1, 0, 5, -1, 0, 5.3, 0.418, 0, 5.467, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuL", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 1.333, 0, 0, 1.367, -0.325, 0, 1.4, 0.107, 0, 1.433, -0.181, 0, 1.467, 0.088, 0, 1.5, -0.125, 0, 1.533, 0.139, 0, 1.6, 0.005, 0, 1.633, 0.163, 0, 1.667, 0, 2, 2.333, 0, 2, 2.5, 0, 0, 4.033, 20, 2, 4.5, 20, 2, 4.533, 20, 0, 6.367, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuL", "Segments": [0, 0, 2, 1, 0, 0, 1.033, -0.18, 2, 1.2, -0.18, 0, 1.333, 4.426, 0, 1.367, 4.102, 0, 1.4, 4.762, 0, 1.433, 4.246, 0, 1.467, 4.743, 0, 1.5, 4.53, 0, 1.533, 4.794, 0, 1.6, 4.432, 1, 1.611, 4.432, 1.622, 4.469, 1.633, 4.59, 1, 1.7, 5.318, 1.766, 5.71, 1.833, 5.71, 1, 1.889, 5.71, 1.944, 5.714, 2, 5.653, 1, 2.044, 5.604, 2.089, 5.365, 2.133, 5.016, 1, 2.178, 4.667, 2.222, 3.086, 2.267, 1.781, 1, 2.289, 1.129, 2.311, -0.06, 2.333, -0.06, 0, 2.4, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 5.967, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuL", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 1.2, 0, 0, 1.333, 4.426, 0, 1.367, 4.102, 0, 1.4, 4.762, 0, 1.433, 4.246, 0, 1.467, 4.743, 0, 1.5, 4.53, 0, 1.533, 4.794, 0, 1.6, 4.432, 1, 1.611, 4.432, 1.622, 4.548, 1.633, 4.59, 1, 1.644, 4.632, 1.656, 4.63, 1.667, 4.63, 0, 1.733, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 5.967, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamMjLFlip", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 1.533, 0, 0, 1.9, 1, 2, 2.233, 1, 2, 3.833, 1, 1, 4.055, 1, 4.278, 0.713, 4.5, 0.167, 1, 4.511, 0.14, 4.522, 0.107, 4.533, 0.106, 1, 5.144, 0.034, 5.756, 0, 6.367, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamPositionZManjuuL", "Segments": [0, 0, 2, 1, 0, 0, 1.033, -11.505, 1, 1.133, -11.505, 1.233, -8.027, 1.333, 7, 1, 1.4, 17.018, 1.466, 30, 1.533, 30, 1, 1.578, 30, 1.622, 13.747, 1.667, 6.012, 1, 1.722, -3.657, 1.778, -13.515, 1.833, -15, 1, 1.878, -16.188, 1.922, -16, 1.967, -16, 0, 2.267, -8, 0, 2.4, -30, 2, 2.833, -30, 0, 3, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 5.967, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLEyeOpen", "Segments": [0, 0, 2, 1, 0, 1, 1.011, 0, 1.022, 0.157, 1.033, 0.219, 1, 1.133, 0.773, 1.233, 1, 1.333, 1, 2, 1.667, 1, 2, 2.333, 1, 2, 3, 1, 2, 3.267, 1, 2, 3.667, 1, 1, 3.945, 1, 4.222, 0.958, 4.5, 0.869, 1, 4.511, 0.865, 4.522, 0.865, 4.533, 0.852, 1, 5.011, 0.301, 5.489, 0, 5.967, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyX", "Segments": [0, -5.963, 0, 0.467, -7.178, 0, 1, 1.661, 0, 1.033, 0, 2, 1.333, 0, 0, 1.667, 30, 0, 1.833, -30, 0, 2.233, 30, 1, 2.266, 30, 2.3, 10.661, 2.333, 0, 1, 2.389, -17.768, 2.444, -21, 2.5, -21, 0, 2.767, 10.631, 0, 3, -17, 0, 3.267, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 5.967, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyY", "Segments": [0, -11.245, 0, 0.133, -11.811, 0, 1, 6.109, 0, 1.033, 0, 2, 1.633, 0, 0, 2.367, -22.948, 0, 2.867, 30, 0, 3.067, -12, 0, 3.467, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 4.567, 0, 0, 4.633, 21.076, 1, 4.644, 21.076, 4.656, -21.076, 4.667, -21.076, 1, 4.678, -21.076, 4.689, 21.076, 4.7, 21.076, 1, 4.711, 21.076, 4.722, -21.076, 4.733, -21.076, 0, 4.8, 21.076, 0, 4.867, -21.076, 1, 4.878, -21.076, 4.889, 21.076, 4.9, 21.076, 1, 4.911, 21.076, 4.922, -21.076, 4.933, -21.076, 1, 4.944, -21.076, 4.956, 21.076, 4.967, 21.076, 1, 4.989, 21.076, 5.011, -21.076, 5.033, -21.076, 2, 5.1, -21.076, 0, 5.167, 21.076, 1, 5.178, 21.076, 5.189, -21.076, 5.2, -21.076, 0, 5.267, 21.076, 1, 5.278, 21.076, 5.289, -21.076, 5.3, -21.076, 1, 5.311, -21.076, 5.322, 21.076, 5.333, 21.076, 0, 5.4, -21.076, 1, 5.411, -21.076, 5.422, 21.076, 5.433, 21.076, 1, 5.444, 21.076, 5.456, -21.076, 5.467, -21.076, 1, 5.489, -21.076, 5.511, 21.076, 5.533, 21.076, 1, 5.544, 21.076, 5.556, -21.076, 5.567, -21.076, 1, 5.589, -21.076, 5.611, 21.076, 5.633, 21.076, 1, 5.644, 21.076, 5.656, -21.076, 5.667, -21.076, 1, 5.689, -21.076, 5.711, 21.076, 5.733, 21.076, 0, 5.833, -21.076, 1, 5.844, -21.076, 5.856, 21.076, 5.867, 21.076, 1, 5.878, 21.076, 5.889, -21.076, 5.9, -21.076, 0, 5.967, 21.076, 1, 5.978, 21.076, 5.989, -21.076, 6, -21.076, 1, 6.011, -21.076, 6.022, 21.076, 6.033, 21.076, 0, 6.1, -21.076, 1, 6.111, -21.076, 6.122, 21.076, 6.133, 21.076, 0, 6.2, -21.076, 1, 6.211, -21.076, 6.222, 21.076, 6.233, 21.076, 0, 6.267, -21.076, 1, 6.289, -21.076, 6.311, 21.076, 6.333, 21.076, 1, 6.344, 21.076, 6.356, -21.076, 6.367, -21.076, 1, 6.378, -21.076, 6.389, 21.076, 6.4, 21.076, 1, 6.411, 21.076, 6.422, -21.076, 6.433, -21.076, 0, 6.5, 21.076, 0, 6.667, -3.527, 0, 6.8, 3, 2, 7, 3]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyZ", "Segments": [0, -5.624, 2, 0.033, -5.624, 0, 1, 2.554, 1, 1.011, 2.554, 1.022, -4.566, 1.033, -6.347, 1, 1.044, -8.128, 1.056, -8, 1.067, -8, 1, 1.2, -8, 1.334, -2.41, 1.467, 10, 1, 1.534, 16.205, 1.6, 20, 1.667, 20, 2, 1.8, 20, 0, 2, 30, 0, 2.4, -18, 0, 2.6, 30, 0, 2.767, 0, 0, 2.9, 30, 2, 3.133, 30, 0, 3.4, 0, 2, 4.5, 0, 2, 4.533, 0, 0, 4.733, 30, 2, 6.067, 30, 0, 6.333, 0, 0, 6.433, 30, 0, 6.6, -7.258, 0, 6.8, 6.105, 2, 7, 6.105]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyW", "Segments": [0, 0, 2, 1, 0, 1, 1.011, 0, 1.022, 16.744, 1.033, 23.482, 1, 1.044, 30, 1.056, 30, 1.067, 30, 2, 1.133, 30, 0, 1.367, -30, 2, 1.8, -30, 0, 2.067, 27.255, 0, 2.167, -19.148, 1, 2.211, -19.148, 2.256, -14.371, 2.3, 0, 1, 2.356, 17.963, 2.411, 30, 2.467, 30, 0, 2.633, -30, 0, 2.833, 19.49, 0, 3.033, -22.148, 0, 3.2, 8, 0, 3.333, -11.034, 0, 3.433, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 5.967, 0, 2, 6.3, 0, 0, 6.4, 11, 0, 6.567, -8, 0, 6.667, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuL", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 0, 4.6, 1, 0, 4.633, -1, 0, 4.7, 1, 0, 4.733, -1, 0, 4.767, 1, 0, 4.833, -1, 0, 4.867, 1, 0, 4.9, -1, 0, 4.967, 1, 0, 5, -1, 0, 5.067, 1, 0, 5.1, -1, 0, 5.133, 1, 0, 5.2, -1, 0, 5.233, 1, 0, 5.267, -1, 0, 5.3, 1, 0, 5.367, -1, 0, 5.433, 1, 0, 5.467, -1, 0, 5.5, 1, 0, 5.533, -1, 0, 5.6, 1, 0, 5.667, -1, 0, 5.7, 1, 0, 5.8, -1, 0, 5.833, 1, 0, 5.867, -1, 0, 5.933, 1, 0, 6, -1, 0, 6.033, 1, 0, 6.067, -1, 0, 6.1, 1, 0, 6.167, -1, 2, 6.233, -1, 0, 6.3, 1, 0, 6.333, -1, 0, 6.4, 1, 0, 6.433, -1, 0, 6.467, 1, 0, 6.5, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamClawFX", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 2.333, 0, 0, 2.467, 1, 0, 2.567, -1, 2, 2.6, -1, 0, 2.7, 1, 0, 2.8, -1, 2, 2.833, -1, 0, 2.933, 1, 0, 3.033, -1, 2, 3.067, -1, 0, 3.167, 1, 0, 3.267, -1, 2, 3.3, -1, 0, 3.4, 1, 0, 3.5, -1, 2, 3.533, -1, 0, 3.633, 1, 0, 3.733, -1, 2, 3.767, -1, 0, 3.867, 1, 0, 3.967, 0, 2, 4.5, 0, 2, 4.533, 0, 0, 4.633, 1, 0, 4.7, -1, 2, 4.733, -1, 0, 4.8, 1, 0, 4.867, -1, 2, 4.9, -1, 0, 4.967, 1, 0, 5.033, -1, 2, 5.067, -1, 0, 5.1, 1, 0, 5.2, -1, 0, 5.267, 1, 0, 5.333, -1, 2, 5.367, -1, 0, 5.433, 1, 0, 5.5, -1, 2, 5.533, -1, 0, 5.6, 1, 0, 5.667, -1, 0, 5.767, 1, 0, 5.833, -1, 2, 5.867, -1, 0, 5.933, 1, 0, 6, -1, 2, 6.033, -1, 0, 6.1, 1, 0, 6.167, -1, 2, 6.2, -1, 0, 6.233, 1, 0, 6.333, -1, 0, 6.4, 1, 0, 6.467, -1, 0, 6.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamClawFY", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 2.333, 0, 0, 2.4, -1, 0, 2.467, 1, 0, 2.6, -1, 0, 2.8, 1, 0, 2.833, -1, 0, 3.033, 1, 0, 3.067, -1, 2, 3.3, -1, 0, 3.5, 1, 0, 3.533, -1, 0, 3.733, 1, 0, 3.767, -1, 0, 3.967, 0, 2, 4.5, 0, 2, 4.533, 0, 0, 4.6, -1, 0, 4.667, 1, 0, 4.733, -1, 2, 4.767, -1, 0, 4.833, 1, 0, 4.9, -1, 1, 4.911, -1, 4.922, -1, 4.933, -0.999, 1, 4.944, -0.998, 4.956, 1, 4.967, 1, 0, 5.067, -1, 2, 5.1, -1, 0, 5.133, 1, 0, 5.2, -1, 2, 5.267, -1, 0, 5.3, 1, 0, 5.367, -1, 2, 5.433, -1, 0, 5.467, 1, 0, 5.533, -1, 2, 5.567, -1, 0, 5.633, 1, 0, 5.667, -1, 2, 5.733, -1, 0, 5.8, 1, 0, 5.867, -1, 2, 5.9, -1, 0, 5.967, 1, 0, 6.033, -1, 2, 6.067, -1, 0, 6.1, 1, 0, 6.2, -1, 2, 6.233, -1, 0, 6.267, 1, 0, 6.333, -1, 2, 6.4, -1, 0, 6.433, 1, 0, 6.5, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamClawBX", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 2.333, 0, 0, 2.467, -1, 0, 2.567, 1, 2, 2.6, 1, 0, 2.7, -1, 0, 2.8, 1, 2, 2.833, 1, 0, 2.933, -1, 0, 3.033, 1, 2, 3.067, 1, 0, 3.167, -1, 0, 3.267, 1, 2, 3.3, 1, 0, 3.4, -1, 0, 3.5, 1, 2, 3.533, 1, 0, 3.633, -1, 0, 3.733, 1, 2, 3.767, 1, 0, 3.867, -1, 0, 3.967, 0, 2, 4.5, 0, 2, 4.533, 0, 0, 4.633, -1, 0, 4.7, 1, 2, 4.733, 1, 0, 4.8, -1, 0, 4.867, 1, 2, 4.9, 1, 0, 4.967, -1, 0, 5.033, 1, 2, 5.067, 1, 0, 5.1, -1, 0, 5.2, 1, 0, 5.267, -1, 0, 5.333, 1, 2, 5.367, 1, 0, 5.433, -1, 0, 5.5, 1, 2, 5.533, 1, 0, 5.6, -1, 0, 5.667, 1, 0, 5.767, -1, 0, 5.833, 1, 2, 5.867, 1, 0, 5.933, -1, 0, 6, 1, 2, 6.033, 1, 0, 6.1, -1, 0, 6.167, 1, 2, 6.2, 1, 0, 6.233, -1, 0, 6.333, 1, 0, 6.4, -1, 0, 6.467, 1, 0, 6.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamClawBY", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 2.333, 0, 0, 2.4, 1, 0, 2.467, -1, 0, 2.6, 1, 0, 2.8, -1, 0, 2.833, 1, 0, 3.033, -1, 0, 3.067, 1, 2, 3.3, 1, 0, 3.5, -1, 0, 3.533, 1, 0, 3.733, -1, 0, 3.767, 1, 0, 3.967, 0, 2, 4.5, 0, 2, 4.533, 0, 0, 4.6, 1, 0, 4.667, -1, 0, 4.733, 1, 2, 4.767, 1, 0, 4.833, -1, 0, 4.9, 1, 1, 4.911, 1, 4.922, 1, 4.933, 0.999, 1, 4.944, 0.998, 4.956, -1, 4.967, -1, 0, 5.067, 1, 2, 5.1, 1, 0, 5.133, -1, 0, 5.2, 1, 2, 5.267, 1, 0, 5.3, -1, 0, 5.367, 1, 2, 5.433, 1, 0, 5.467, -1, 0, 5.533, 1, 2, 5.567, 1, 0, 5.633, -1, 0, 5.667, 1, 2, 5.733, 1, 0, 5.8, -1, 0, 5.867, 1, 2, 5.9, 1, 0, 5.967, -1, 0, 6.033, 1, 2, 6.067, 1, 0, 6.1, -1, 0, 6.2, 1, 2, 6.233, 1, 0, 6.267, -1, 0, 6.333, 1, 2, 6.4, 1, 0, 6.433, -1, 0, 6.5, 0, 2, 6.567, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuU", "Segments": [0, 0, 2, 1.033, 0, 2, 4.5, 0, 0, 4.533, -9.063, 2, 4.7, -9.063, 1, 4.811, -9.063, 4.922, -9.051, 5.033, -8.473, 1, 5.1, -8.126, 5.166, -5.124, 5.233, -2.978, 1, 5.3, -0.832, 5.366, 0.951, 5.433, 0.951, 0, 5.533, 0, 2, 6.733, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyX", "Segments": [0, 0, 2, 0.5, 0, 0, 1.033, -1.354, 0, 1.433, 7.414, 0, 1.9, -2, 0, 2.333, 0, 0, 3.8, -3.245, 1, 4.033, -3.245, 4.267, -2.767, 4.5, -1.679, 1, 4.511, -1.627, 4.522, 0, 4.533, 0, 2, 4.7, 0, 2, 5.767, 0, 2, 6.733, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyY", "Segments": [0, -2.703, 2, 0.033, -2.703, 0, 0.2, 2, 1, 0.233, 2, 0.267, 1.055, 0.3, -1.62, 1, 0.311, -2.512, 0.322, -3.677, 0.333, -3.677, 0, 0.5, 2, 0, 0.6, -2.703, 0, 0.767, 2, 1, 0.789, 2, 0.811, 0.737, 0.833, -1.62, 1, 0.844, -2.799, 0.856, -4.761, 0.867, -5, 1, 0.922, -6.194, 0.978, -7.255, 1.033, -8.159, 1, 1.055, -8.521, 1.078, -8.563, 1.1, -8.563, 0, 1.5, 4.012, 0, 1.9, -6.773, 0, 2.433, 5.163, 0, 3.367, -2.152, 0, 4.433, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 4.7, 0, 2, 4.733, 0, 0, 4.933, 30, 0, 5.2, -30, 0, 5.7, 0, 2, 5.767, 0, 2, 6.733, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyZ", "Segments": [0, -30, 2, 0.033, -30, 0, 0.2, 30, 2, 0.233, 30, 0, 0.3, -30, 0, 0.5, 30, 0, 0.567, -30, 0, 0.767, 30, 1, 0.789, 30, 0.811, -30, 0.833, -30, 1, 0.9, -30, 0.966, -5.226, 1.033, -1.354, 1, 1.166, 6.389, 1.3, 7.414, 1.433, 7.414, 0, 1.9, -2, 0, 2.333, 0, 2, 2.6, 0, 0, 2.8, 5.581, 0, 3.067, -8.5, 0, 3.3, 4.855, 0, 3.633, -7.03, 0, 3.833, 0, 0, 4.5, -2.98, 0, 4.533, 0, 2, 4.7, 0, 0, 4.9, -16, 0, 5.167, 23, 0, 5.567, -21, 0, 5.767, 8, 0, 6, 0, 2, 6.733, 0, 0, 6.967, -1, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUArmZ", "Segments": [0, -30, 2, 0.033, -30, 0, 0.2, 30, 2, 0.233, 30, 1, 0.266, 30, 0.3, -30, 0.333, -30, 0, 0.5, 30, 0, 0.6, -30, 0, 0.767, 30, 1, 0.8, 30, 0.834, -30, 0.867, -30, 1, 0.878, -30, 0.889, -30, 0.9, -28, 1, 0.944, -12.364, 0.989, 23.424, 1.033, 23.424, 0, 1.133, 20.552, 0, 1.6, 27, 2, 1.8, 27, 0, 2.6, 17.892, 0, 3.3, 27, 0, 4, 19.709, 0, 4.5, 22.345, 0, 4.533, 0, 2, 4.7, 0, 2, 4.8, 0, 0, 4.933, -12, 0, 5.3, 30, 0, 5.767, -7, 0, 6, 3.141, 0, 6.233, 0, 2, 6.733, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineU", "Segments": [0, 0, 2, 0.067, 0, 0, 0.2, 1, 2, 0.233, 0, 2, 0.6, 0, 0, 0.767, 1, 2, 0.8, 0, 2, 0.9, 0, 2, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 4.7, 0, 2, 5.767, 0, 2, 6.733, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineD", "Segments": [0, 1, 2, 0.067, 1, 2, 0.1, 0, 2, 0.2, 0, 1, 0.244, 0.333, 0.289, 0.667, 0.333, 1, 2, 0.367, 0, 2, 0.733, 0, 2, 0.767, 0, 1, 0.811, 0.333, 0.856, 0.667, 0.9, 1, 2, 0.933, 0, 2, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 4.7, 0, 2, 5.767, 0, 2, 6.733, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyW", "Segments": [0, -0.5, 2, 0.033, -0.5, 0, 0.2, 0.4, 0, 0.3, -0.6, 0, 0.5, 1, 0, 0.567, -0.5, 0, 0.767, 0.4, 0, 0.833, -0.6, 0, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 4.7, 0, 0, 4.9, 0.5, 0, 5.133, -1, 0, 5.467, 1, 0, 5.7, -0.5, 0, 5.933, 0.5, 0, 6.2, -0.3, 0, 6.5, 0, 2, 6.733, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamPositionXPanda", "Segments": [0, 7.832, 1, 0.278, 7.832, 0.555, 9.3, 0.833, 12.745, 1, 0.889, 13.434, 0.944, 14.15, 1, 14.15, 0, 1.033, 0, 2, 1.867, 0, 0, 4.333, 18, 2, 4.5, 18, 1, 4.511, 18, 4.522, 18.094, 4.533, 17.995, 1, 5.278, 11.374, 6.022, 0, 6.767, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamPositionYPanda", "Segments": [0, 0, 2, 1, 0, 0, 1.033, -0.12, 1, 1.078, -0.12, 1.122, -0.131, 1.167, 0.216, 1, 1.2, 0.476, 1.234, 2, 1.267, 2, 0, 1.3, 1.82, 0, 1.333, 2.123, 0, 1.367, 1.883, 0, 1.4, 2.066, 0, 1.433, 1.887, 0, 1.467, 2.131, 0, 1.5, 1.893, 0, 1.533, 2.079, 0, 1.567, 1.962, 0, 1.6, 2.086, 0, 1.633, 1.855, 0, 1.667, 2.013, 0, 1.7, 1.904, 0, 1.767, 2, 1, 1.811, 2, 1.856, 1.794, 1.9, 0.984, 1, 1.922, 0.579, 1.945, -0.06, 1.967, -0.06, 0, 2.033, 0.018, 0, 2.467, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamSizePanda", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 1.2, 0, 0, 1.333, 1.178, 0, 1.367, 0.91, 0, 1.4, 1.456, 0, 1.433, 1.03, 0, 1.467, 1.44, 0, 1.5, 1.264, 0, 1.533, 1.482, 0, 1.6, 1.183, 1, 1.611, 1.183, 1.622, 1.279, 1.633, 1.314, 1, 1.644, 1.349, 1.656, 1.347, 1.667, 1.347, 0, 1.733, 0, 2, 2.333, 0, 2, 3, 0, 2, 3.267, 0, 2, 3.667, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 4.433, 0, 1, 4.455, 0, 4.478, 0.026, 4.5, 0.258, 1, 4.511, 0.374, 4.522, 1, 4.533, 1, 2, 6.667, 1, 0, 6.833, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyX", "Segments": [0, 0.763, 0, 0.067, -2.538, 0, 0.2, 2.538, 0, 0.4, -2.538, 0, 0.533, 2.538, 0, 0.633, -2.538, 0, 0.9, 2.538, 0, 1, -1.745, 1, 1.011, -1.745, 1.022, -1.871, 1.033, 0, 1, 1.144, 18.708, 1.256, 30, 1.367, 30, 2, 1.667, 30, 0, 1.933, -23.554, 0, 2.167, 7.48, 0, 2.367, -5.045, 0, 2.533, 2.228, 0, 2.8, -2.785, 0, 3.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyY", "Segments": [0, -3.183, 0, 0.133, 3.183, 0, 0.333, -3.183, 0, 0.467, 3.183, 0, 0.6, -3.183, 0, 0.833, 3.183, 0, 1, -3.183, 0, 1.033, -0.06, 2, 1.1, -0.06, 0, 1.3, 30, 2, 1.767, 30, 0, 2.067, -30, 0, 2.267, 7.878, 0, 2.467, -5.889, 0, 2.7, 1.989, 0, 2.967, -2.865, 0, 3.167, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 6.633, 0, 0, 6.8, -23, 2, 7, -23]}, {"Target": "Parameter", "Id": "ParamPositionY2Panda", "Segments": [0, 6.229, 0, 0.033, -6.229, 0, 0.133, 6.229, 0, 0.2, -6.229, 0, 0.3, 6.229, 0, 0.367, -6.229, 0, 0.433, 6.229, 0, 0.533, -6.229, 0, 0.6, 6.229, 0, 0.633, -6.229, 0, 0.733, 6.229, 0, 0.833, -6.229, 0, 0.9, 6.229, 0, 0.967, -6.229, 0, 1, 0, 2, 1.033, 0, 2, 2.5, 0, 0, 2.567, -6.213, 0, 2.6, 6.229, 0, 2.667, -6.213, 0, 2.7, 6.229, 0, 2.8, -6.229, 0, 2.833, 6.229, 0, 2.867, -6.229, 0, 2.967, 6.229, 0, 3, -6.229, 0, 3.1, 6.229, 0, 3.133, -6.229, 0, 3.167, 6.229, 0, 3.267, -6.229, 0, 3.3, 6.229, 0, 3.4, -6.213, 0, 3.433, 6.229, 0, 3.467, -6.229, 0, 3.533, 6.229, 0, 3.567, -6.229, 0, 3.667, 6.229, 0, 3.7, -6.229, 0, 3.8, 6.229, 0, 3.833, -6.229, 0, 3.867, 6.229, 0, 3.967, -6.229, 0, 4, 6.229, 0, 4.033, -6.229, 0, 4.1, 6.229, 0, 4.167, -6.229, 0, 4.233, 6.229, 0, 4.3, -6.229, 0, 4.333, 6.229, 0, 4.367, 0, 2, 4.5, 0, 0, 4.533, -6.213, 2, 4.633, -6.213, 0, 4.7, 6.229, 0, 4.8, -6.229, 0, 4.867, 6.229, 0, 4.9, -6.229, 0, 4.967, 6.229, 0, 5.067, -6.229, 0, 5.1, 6.229, 0, 5.167, -6.229, 0, 5.2, 6.229, 0, 5.267, -6.229, 0, 5.367, 6.229, 0, 5.4, -6.213, 0, 5.5, 6.229, 0, 5.567, -6.229, 0, 5.6, 6.229, 0, 5.667, -6.229, 0, 5.733, 6.229, 0, 5.767, -6.229, 0, 5.833, 6.229, 0, 5.9, -6.229, 0, 5.967, 6.229, 0, 6, -6.229, 0, 6.1, 6.229, 0, 6.133, -6.229, 0, 6.167, 6.229, 0, 6.3, -6.229, 0, 6.333, 6.229, 0, 6.433, -6.229, 0, 6.467, 6.229, 0, 6.5, -6.229, 0, 6.6, 6.229, 0, 6.633, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyZ", "Segments": [0, -1.431, 0, 0.1, 1.431, 0, 0.3, -1.431, 0, 0.433, 1.431, 0, 0.533, -1.431, 0, 0.8, 1.431, 0, 0.933, -1.431, 1, 0.955, -1.431, 0.978, -1.313, 1, 0, 1, 1.011, 0.656, 1.022, 30, 1.033, 30, 1, 1.1, 30, 1.166, -1.045, 1.233, -12, 1, 1.333, -28.433, 1.433, -30, 1.533, -30, 2, 1.767, -30, 1, 1.878, -30, 1.989, -29.883, 2.1, -28.747, 1, 2.167, -28.066, 2.233, -26.155, 2.3, -23.157, 1, 2.389, -19.159, 2.478, -6.35, 2.567, 3.581, 1, 2.622, 9.788, 2.678, 21.689, 2.733, 26.569, 1, 2.766, 29.497, 2.8, 29.431, 2.833, 29.431, 0, 2.933, 26.569, 0, 3.1, 29.431, 0, 3.233, 26.569, 1, 3.255, 26.569, 3.278, 27.27, 3.3, 28, 1, 3.333, 29.095, 3.367, 29.431, 3.4, 29.431, 0, 3.467, 26.569, 0, 3.567, 29.431, 0, 3.7, 26.569, 0, 3.833, 29.431, 0, 3.967, 26.569, 0, 4.1, 29.431, 0, 4.167, 26.569, 0, 4.3, 29.431, 1, 4.322, 29.431, 4.345, 29.712, 4.367, 28, 1, 4.411, 24.576, 4.456, 15.941, 4.5, 8.407, 1, 4.511, 6.524, 4.522, 0, 4.533, 0, 0, 4.633, 1.431, 0, 4.733, -1.431, 0, 4.867, 1.431, 0, 4.967, -1.431, 0, 5.1, 1.431, 0, 5.233, -1.431, 1, 5.278, -1.431, 5.322, -1.1, 5.367, 0, 1, 5.378, 0.275, 5.389, 1.431, 5.4, 1.431, 0, 5.533, -1.431, 0, 5.667, 1.431, 0, 5.767, -1.431, 0, 5.9, 1.431, 0, 6, -1.431, 0, 6.167, 1.431, 0, 6.333, -1.431, 0, 6.433, 1.431, 1, 6.455, 1.431, 6.478, 2.446, 6.5, -1.431, 1, 6.567, -13.063, 6.633, -30, 6.7, -30, 0, 6.9, 11.918, 2, 7, 11.918]}, {"Target": "Parameter", "Id": "ParamPandaLegFZ", "Segments": [0, 0.3, 0, 0.067, -1, 0, 0.233, 1, 0, 0.333, -1, 0, 0.533, 1, 0, 0.633, -1, 0, 0.833, 1, 0, 1, -1, 1, 1.011, -1, 1.022, -0.193, 1.033, 0, 1, 1.078, 0.772, 1.122, 1, 1.167, 1, 0, 1.667, -1, 0, 1.933, 1, 0, 2.1, -1, 0, 2.133, 1, 0, 2.233, -1, 0, 2.333, 1, 0, 2.467, -1, 0, 2.567, 1, 0, 2.667, -1, 0, 2.8, 1, 0, 2.867, -1, 0, 3, 1, 0, 3.133, -1, 0, 3.267, 1, 0, 3.4, -1, 0, 3.467, 1, 0, 3.567, -1, 0, 3.7, 1, 0, 3.833, -1, 0, 3.967, 1, 0, 4.033, -1, 0, 4.167, 1, 0, 4.3, -1, 0, 4.367, 0, 2, 4.5, 0, 2, 4.533, 0, 0, 4.633, -1, 0, 4.8, 1, 0, 4.9, -1, 0, 5.067, 1, 0, 5.167, -1, 0, 5.267, 1, 0, 5.4, -1, 0, 5.567, 1, 0, 5.667, -1, 0, 5.767, 1, 0, 5.9, -1, 0, 6, 1, 0, 6.133, -1, 0, 6.3, 1, 0, 6.433, -1, 0, 6.5, 1, 0, 6.633, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegBZ", "Segments": [0, 0.3, 0, 0.067, -1, 0, 0.233, 1, 0, 0.333, -1, 0, 0.533, 1, 0, 0.633, -1, 0, 0.833, 1, 0, 1, -1, 1, 1.011, -1, 1.022, -0.193, 1.033, 0, 1, 1.078, 0.772, 1.122, 1, 1.167, 1, 0, 1.667, -1, 0, 1.933, 1, 0, 2.1, -1, 0, 2.133, 1, 0, 2.233, -1, 0, 2.333, 1, 0, 2.467, -1, 0, 2.567, 1, 0, 2.667, -1, 0, 2.8, 1, 0, 2.867, -1, 0, 3, 1, 0, 3.133, -1, 0, 3.267, 1, 0, 3.4, -1, 0, 3.467, 1, 0, 3.567, -1, 0, 3.7, 1, 0, 3.833, -1, 0, 3.967, 1, 0, 4.033, -1, 0, 4.167, 1, 0, 4.3, -1, 0, 4.367, 0, 2, 4.5, 0, 2, 4.533, 0, 0, 4.633, -1, 0, 4.8, 1, 0, 4.9, -1, 0, 5.067, 1, 0, 5.167, -1, 0, 5.267, 1, 0, 5.4, -1, 0, 5.567, 1, 0, 5.667, -1, 0, 5.767, 1, 0, 5.9, -1, 0, 6, 1, 0, 6.133, -1, 0, 6.3, 1, 0, 6.433, -1, 0, 6.5, 1, 0, 6.633, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyX", "Segments": [0, 0, 2, 1, 0, 0, 1.033, 1.354, 0, 1.433, -7.414, 0, 1.8, 2, 0, 2.333, -12, 0, 3.8, 30, 1, 4.033, 30, 4.267, 25.175, 4.5, 15.527, 1, 4.511, 15.068, 4.522, 14.6, 4.533, 14.15, 1, 4.766, 4.708, 5, 0, 5.233, 0, 0, 6, 0.958, 0, 6.467, -14, 0, 6.8, 2, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyY", "Segments": [0, 0, 2, 1, 0, 1, 1.011, 0, 1.022, 7.972, 1.033, 8.159, 1, 1.055, 8.533, 1.078, 8.563, 1.1, 8.563, 0, 1.5, -4.012, 0, 1.9, 6.773, 0, 2.433, -5.163, 0, 3.367, 2.152, 0, 4.433, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 6.1, 0, 0, 6.7, -7.831, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamCannonZ", "Segments": [0, 0, 2, 0.267, 0, 0, 0.5, 2.231, 0, 0.933, -13, 1, 0.955, -13, 0.978, -12.985, 1, -12.739, 1, 1.011, -12.616, 1.022, 2.647, 1.033, 2.647, 0, 1.5, -7.414, 0, 1.967, 2, 0, 2.4, 0, 2, 2.667, 0, 0, 2.867, -5.581, 0, 3.133, 8.5, 0, 3.367, -4.855, 0, 3.7, 7.03, 0, 3.9, 0, 1, 4.1, 0, 4.3, 1.044, 4.5, 2.834, 1, 4.511, 2.933, 4.522, 2.982, 4.533, 2.988, 1, 4.555, 3, 4.578, 3, 4.6, 3, 2, 5.333, 3, 0, 5.733, 5, 0, 6.367, -10, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamCannonGaY", "Segments": [0, 1.219, 1, 0.111, 1.219, 0.222, 1.083, 0.333, -0.231, 1, 0.411, -1.151, 0.489, -3.248, 0.567, -3.248, 0, 1, 11.418, 0, 1.033, 0, 2, 2.8, 0, 0, 3, 5.581, 0, 3.267, -8.5, 0, 3.5, 4.855, 0, 3.833, -5, 0, 4.067, 0.991, 0, 4.4, -1.234, 1, 4.433, -1.234, 4.467, -1.116, 4.5, -0.833, 1, 4.511, -0.739, 4.522, -0.614, 4.533, -0.492, 1, 4.6, 0.239, 4.666, 0.617, 4.733, 0.617, 0, 5.133, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupY", "Segments": [0, 0, 2, 0.367, 0, 0, 0.6, 0.074, 0, 1, -0.425, 0, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupZ", "Segments": [0, 0, 2, 0.233, 0, 0, 0.467, 0.074, 0, 0.9, -0.433, 1, 0.933, -0.433, 0.967, -0.431, 1, -0.416, 1, 1.011, -0.411, 1.022, 0, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamCannonHandZ", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 2.7, 0, 0, 2.9, 8.305, 0, 3.167, -9.278, 0, 3.4, 7.399, 0, 3.7, -7.443, 0, 4.067, 1.541, 0, 4.3, -0.887, 1, 4.367, -0.887, 4.433, -0.639, 4.5, -0.231, 1, 4.511, -0.163, 4.522, -0.118, 4.533, -0.085, 1, 4.555, -0.019, 4.578, 0, 4.6, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupIce", "Segments": [0, 0, 2, 1.467, 0, 1, 1.5, 0, 1.534, 0.452, 1.567, 0.609, 1, 2.234, 3.752, 2.9, 6.681, 3.567, 9.761, 1, 3.578, 9.812, 3.589, 9.947, 3.6, 9.947, 2, 3.633, 0, 1, 3.689, 0, 3.744, 0.289, 3.8, 0.609, 1, 4.133, 2.529, 4.467, 4.391, 4.8, 6.173, 1, 4.933, 6.885, 5.067, 7.583, 5.2, 8.119, 1, 5.533, 9.46, 5.867, 10, 6.2, 10, 2, 6.233, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupZ", "Segments": [0, 0, 2, 1.533, 0, 0, 1.8, 0.59, 0, 2.133, -0.59, 0, 2.4, 0.59, 0, 2.667, -0.59, 0, 3, 0.59, 0, 3.267, -0.59, 0, 3.567, 0.552, 0, 3.867, -0.532, 0, 4.1, 0.478, 0, 4.4, -0.511, 0, 4.667, 0.368, 0, 4.933, -0.339, 0, 5.2, 0.369, 0, 5.533, -0.307, 0, 5.833, 0.277, 0, 6.233, -0.185, 0, 6.567, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupInput", "Segments": [0, 0, 0, 1.167, -0.007, 0, 1.633, 10.429, 0, 1.9, -10.544, 0, 2.167, 10.429, 0, 2.433, -10.544, 0, 2.7, 10.429, 0, 2.967, -10.544, 0, 3.233, 10.429, 0, 3.5, -10.544, 0, 3.767, 7.818, 0, 4.067, -6.964, 0, 4.267, 4.654, 0, 4.533, -6.094, 0, 4.8, -0.007, 2, 5.2, -0.007, 0, 5.433, -6.964, 0, 5.633, 7.818, 0, 5.9, -6.094, 1, 5.989, -6.094, 6.078, -3.446, 6.167, 0, 1, 6.256, 3.446, 6.344, 4.515, 6.433, 4.515, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamParamStrongCatZ", "Segments": [0, -6.389, 1, 0.333, -6.389, 0.667, -4.144, 1, 0.634, 1, 1.011, 0.793, 1.022, 1.354, 1.033, 1.354, 0, 1.433, -7.414, 0, 1.9, 2, 0, 2.333, 0, 0, 4, 4.208, 1, 4.167, 4.208, 4.333, 3.59, 4.5, 2.124, 1, 4.511, 2.026, 4.522, 0, 4.533, 0, 2, 5.6, 0, 0, 6.033, -17, 0, 6.4, 8, 0, 6.7, -6, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamParamSCBodyZ", "Segments": [0, -6.717, 1, 0.333, -6.717, 0.667, -4.511, 1, 0.268, 1, 1.011, 0.427, 1.022, 7.973, 1.033, 8.159, 1, 1.055, 8.53, 1.078, 8.563, 1.1, 8.563, 0, 1.5, -4.012, 0, 1.9, 6.773, 0, 2.433, -5.163, 0, 3.8, 8, 1, 4.033, 8, 4.267, 7.34, 4.5, 5.289, 1, 4.867, 2.067, 5.233, 0, 5.6, 0, 2, 5.733, 0, 0, 6.1, -15, 0, 6.467, 5, 0, 6.767, -2, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamSCDishY", "Segments": [0, 0.029, 0, 1, 1.508, 1, 1.011, 1.508, 1.022, 1.212, 1.033, 0.315, 1, 1.166, -10.444, 1.3, -16, 1.433, -16, 0, 1.8, 8, 0, 2.2, -5.976, 0, 2.533, 3.586, 0, 2.8, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 5.667, 0, 0, 6.167, -30, 0, 6.6, 10, 0, 6.933, -6, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamSCDishZ", "Segments": [0, 0.015, 0, 1, 1.423, 1, 1.011, 1.423, 1.022, 0.733, 1.033, 0.213, 1, 1.111, -3.426, 1.189, -5, 1.267, -5, 0, 1.833, 4.542, 0, 2.233, -5, 0, 2.6, 2.14, 0, 2.867, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 5.667, 0, 2, 5.833, 0, 0, 6.267, -15, 0, 6.667, 5, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamSCCupRO", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 5.7, 0, 0, 6.367, 1, 0, 6.4, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamSCCupY", "Segments": [0, 0, 2, 1, 0, 2, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 5.7, 0, 0, 6.033, 30, 0, 6.367, 0, 2, 7, 0]}, {"Target": "Parameter", "Id": "ParamSCCupZ", "Segments": [0, -1.033, 0, 1, 1.116, 0, 1.033, 0, 2, 4.5, 0, 2, 4.533, 0, 2, 5.7, 0, 2, 6.167, 0, 0, 6.633, 30, 0, 6.967, -16, 0, 7, 0]}, {"Target": "Parameter", "Id": "MB_yanwubaozha", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "MB_DRFWXZKTMD", "Segments": [0, 1, 0, 7, 1]}, {"Target": "Parameter", "Id": "ParamAllSizeFix", "Segments": [0, 1, 0, 7, 1]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamBGHide", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN2", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN3", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamBlackY", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamBlackCollar", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamBlackOrder", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamWhiteIN", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamCHHide", "Segments": [0, 1, 0, 7, 1]}, {"Target": "Parameter", "Id": "ParamDeskHide", "Segments": [0, 1, 0, 7, 1]}, {"Target": "Parameter", "Id": "ParamStoolHide", "Segments": [0, 1, 0, 7, 1]}, {"Target": "Parameter", "Id": "ParamCupDesk", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamCHX", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamCHZ", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionY", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamAllZ", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamALLSize2", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLightPositionX", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamFixT", "Segments": [0, 1, 0, 7, 1]}, {"Target": "Parameter", "Id": "ParamFlap", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamScare", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpen2", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamMouthType", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamMouthSize", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamBlackFace", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamTeethLight", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamHeart2", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamMark", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamShameLine", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamMarkShake", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamEyeRLightOpen", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLightLine1", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLightLine2", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLightLine3", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLightShine", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo1", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo2", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo3", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1Y", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle2", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow1", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow2", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamTearLight", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamTears", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamFanOpenR", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamChili", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamChiliX", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamHandR", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRY", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamHand_Cl", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamHandRCup", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamHandRMail", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "Segments": [0, 1, 0, 7, 1]}, {"Target": "Parameter", "Id": "ParamHandLIQY1", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY2", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY3", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamHandCupZ", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamHandCupY", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Y", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamFootRX", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Z", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Z", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Y", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Y", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Y", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamLegLF", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRHide", "Segments": [0, 1, 0, 7, 1]}, {"Target": "Parameter", "Id": "ParamMJRFlap", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuR", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuR", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuR", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuR", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamMalpositionManjuuR", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRSigh", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamMRCupFZ", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLHide", "Segments": [0, 1, 0, 7, 1]}, {"Target": "Parameter", "Id": "ParamMJLSigh", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUHide", "Segments": [0, 1, 0, 7, 1]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuU", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuU", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUEyesForm", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamPandaHide", "Segments": [0, 1, 0, 7, 1]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY1", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY2", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY3", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamSCDishRO", "Segments": [0, 0, 0, 7, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.633, "Value": ""}, {"Time": 6.5, "Value": ""}]}