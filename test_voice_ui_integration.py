#!/usr/bin/env python3
"""
语音对话UI集成测试

测试主窗口的语音对话功能集成是否正常工作
"""

import sys
import os

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_voice_ui_integration():
    """测试语音UI集成"""
    print("🔄 开始语音UI集成测试...")
    
    # 1. 测试语音设置对话框导入
    print("\n1. 测试语音设置对话框导入...")
    try:
        from dialogue_system.ui.voice_settings_dialog import VoiceSettingsDialog
        print("✅ VoiceSettingsDialog导入成功")
    except ImportError as e:
        print(f"❌ VoiceSettingsDialog导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ VoiceSettingsDialog导入异常: {e}")
        return False
    
    # 2. 测试UI模块导入
    print("\n2. 测试UI模块导入...")
    try:
        from dialogue_system.ui import VoiceSettingsDialog as UIVoiceSettingsDialog
        print("✅ UI模块中的VoiceSettingsDialog导入成功")
    except ImportError as e:
        print(f"❌ UI模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ UI模块导入异常: {e}")
        return False
    
    # 3. 测试配置管理器
    print("\n3. 测试配置管理器...")
    try:
        # 添加dev目录到路径
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'dev'))
        from settings_dialog import ConfigManager
        config_manager = ConfigManager()
        print("✅ ConfigManager创建成功")
    except Exception as e:
        print(f"❌ ConfigManager创建失败: {e}")
        return False
    
    # 4. 测试语音配置管理器
    print("\n4. 测试语音配置管理器...")
    try:
        from dialogue_system.config import VoiceDialogueConfig
        voice_config = VoiceDialogueConfig(config_manager)
        print("✅ VoiceDialogueConfig创建成功")
    except Exception as e:
        print(f"❌ VoiceDialogueConfig创建失败: {e}")
        return False
    
    # 5. 测试Qt应用程序创建
    print("\n5. 测试Qt应用程序...")
    try:
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        print("✅ Qt应用程序准备就绪")
    except ImportError as e:
        print(f"❌ PySide6导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ Qt应用程序创建失败: {e}")
        return False
    
    # 6. 测试语音设置对话框创建
    print("\n6. 测试语音设置对话框创建...")
    try:
        dialog = VoiceSettingsDialog(config_manager)
        print("✅ 语音设置对话框创建成功")
        
        # 测试对话框基本属性
        print(f"   对话框标题: {dialog.windowTitle()}")
        print(f"   对话框大小: {dialog.size().width()}x{dialog.size().height()}")
        print(f"   标签页数量: {dialog.tab_widget.count()}")
        
        # 测试标签页
        for i in range(dialog.tab_widget.count()):
            tab_text = dialog.tab_widget.tabText(i)
            print(f"   标签页 {i+1}: {tab_text}")
        
        dialog.close()
        
    except Exception as e:
        print(f"❌ 语音设置对话框创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 7. 测试主窗口语音功能导入
    print("\n7. 测试主窗口语音功能...")
    try:
        # 检查主窗口文件中是否包含语音相关方法
        with open('dev/main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        voice_methods = [
            'init_voice_dialogue',
            'start_key_triggered_voice',
            'start_realtime_voice',
            'stop_voice_input',
            'show_voice_settings',
            'on_voice_input_received'
        ]
        
        missing_methods = []
        for method in voice_methods:
            if f"def {method}" not in content:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 主窗口缺少语音方法: {missing_methods}")
            return False
        else:
            print("✅ 主窗口包含所有语音方法")
            
        # 检查语音菜单
        if "🎤 语音对话" in content:
            print("✅ 主窗口包含语音菜单")
        else:
            print("❌ 主窗口缺少语音菜单")
            return False
            
    except Exception as e:
        print(f"❌ 主窗口语音功能检查失败: {e}")
        return False
    
    print("\n✅ 语音UI集成测试完成！所有组件都正常工作。")
    return True

def test_voice_dialogue_manager_import():
    """测试语音对话管理器导入（可选）"""
    print("\n🔄 测试语音对话管理器导入...")
    
    try:
        # 这个测试可能会因为PyAudio未安装而失败，但这是预期的
        from dialogue_system.voice import VoiceDialogueManager
        print("✅ VoiceDialogueManager导入成功")
        return True
    except ImportError as e:
        if "pyaudio" in str(e).lower():
            print("⚠️ VoiceDialogueManager导入失败（PyAudio未安装，这是预期的）")
            print("💡 安装PyAudio后即可使用语音功能: pip install pyaudio")
        else:
            print(f"❌ VoiceDialogueManager导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ VoiceDialogueManager导入异常: {e}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("Live2D语音对话UI集成测试")
    print("="*60)
    
    # 基础UI集成测试
    ui_success = test_voice_ui_integration()
    
    # 语音管理器导入测试（可选）
    voice_success = test_voice_dialogue_manager_import()
    
    print("\n" + "="*60)
    print("测试总结:")
    print(f"- UI集成测试: {'✅ 通过' if ui_success else '❌ 失败'}")
    print(f"- 语音管理器测试: {'✅ 通过' if voice_success else '⚠️ 需要安装PyAudio'}")
    
    if ui_success:
        print("\n🎉 语音对话UI集成成功！")
        print("💡 现在可以在Live2D主窗口的右键菜单中找到'🎤 语音对话'选项")
        print("💡 安装PyAudio后即可使用完整的语音功能: pip install pyaudio")
    else:
        print("\n❌ 语音对话UI集成失败，请检查错误信息")
    
    print("="*60)

if __name__ == "__main__":
    main()
