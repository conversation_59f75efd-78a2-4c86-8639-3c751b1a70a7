{"Version": 3, "Meta": {"PhysicsSettingCount": 54, "TotalInputCount": 175, "TotalOutputCount": 126, "VertexCount": 197, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "Dummy1"}, {"Id": "PhysicsSetting2", "Name": "Dummy2"}, {"Id": "PhysicsSetting3", "Name": "Dummy3"}, {"Id": "PhysicsSetting4", "Name": "Dummy4"}, {"Id": "PhysicsSetting5", "Name": "Dummy5"}, {"Id": "PhysicsSetting6", "Name": "Dummy6"}, {"Id": "PhysicsSetting7", "Name": "Dummy7"}, {"Id": "PhysicsSetting8", "Name": "Dummy8"}, {"Id": "PhysicsSetting9", "Name": "Dummy9"}, {"Id": "PhysicsSetting10", "Name": "Dummy10"}, {"Id": "PhysicsSetting11", "Name": "Dummy11"}, {"Id": "PhysicsSetting12", "Name": "Dummy12"}, {"Id": "PhysicsSetting13", "Name": "Dummy13"}, {"Id": "PhysicsSetting14", "Name": "Dummy14"}, {"Id": "PhysicsSetting15", "Name": "Dummy15"}, {"Id": "PhysicsSetting16", "Name": "Dummy16"}, {"Id": "PhysicsSetting17", "Name": "Dummy17"}, {"Id": "PhysicsSetting18", "Name": "Dummy18"}, {"Id": "PhysicsSetting19", "Name": "Dummy19"}, {"Id": "PhysicsSetting20", "Name": "Dummy20"}, {"Id": "PhysicsSetting21", "Name": "Dummy21"}, {"Id": "PhysicsSetting22", "Name": "Dummy22"}, {"Id": "PhysicsSetting23", "Name": "Dummy23"}, {"Id": "PhysicsSetting24", "Name": "Dummy24"}, {"Id": "PhysicsSetting25", "Name": "Dummy25"}, {"Id": "PhysicsSetting26", "Name": "Dummy26"}, {"Id": "PhysicsSetting27", "Name": "Dummy27"}, {"Id": "PhysicsSetting28", "Name": "Dummy28"}, {"Id": "PhysicsSetting29", "Name": "Dummy29"}, {"Id": "PhysicsSetting30", "Name": "Dummy30"}, {"Id": "PhysicsSetting31", "Name": "Dummy31"}, {"Id": "PhysicsSetting32", "Name": "Dummy32"}, {"Id": "PhysicsSetting33", "Name": "Dummy33"}, {"Id": "PhysicsSetting34", "Name": "Dummy34"}, {"Id": "PhysicsSetting35", "Name": "Dummy35"}, {"Id": "PhysicsSetting36", "Name": "Dummy36"}, {"Id": "PhysicsSetting37", "Name": "Dummy37"}, {"Id": "PhysicsSetting38", "Name": "Dummy38"}, {"Id": "PhysicsSetting39", "Name": "Dummy39"}, {"Id": "PhysicsSetting40", "Name": "Dummy40"}, {"Id": "PhysicsSetting41", "Name": "Dummy41"}, {"Id": "PhysicsSetting42", "Name": "Dummy42"}, {"Id": "PhysicsSetting43", "Name": "Dummy43"}, {"Id": "PhysicsSetting44", "Name": "Dummy44"}, {"Id": "PhysicsSetting45", "Name": "Dummy45"}, {"Id": "PhysicsSetting46", "Name": "Dummy46"}, {"Id": "PhysicsSetting47", "Name": "Dummy47"}, {"Id": "PhysicsSetting48", "Name": "Dummy48"}, {"Id": "PhysicsSetting49", "Name": "Dummy49"}, {"Id": "PhysicsSetting50", "Name": "Dummy50"}, {"Id": "PhysicsSetting51", "Name": "Dummy51"}, {"Id": "PhysicsSetting52", "Name": "Dummy52"}, {"Id": "PhysicsSetting53", "Name": "Dummy53"}, {"Id": "PhysicsSetting54", "Name": "Dummy54"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyelashL1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamEyelashL2"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamEyeFormLY2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9.6}, "Mobility": 0.67, "Delay": 0.7, "Acceleration": 1.92, "Radius": 9.6}, {"Position": {"X": 0, "Y": 26.9}, "Mobility": 0.76, "Delay": 0.96, "Acceleration": 3.06, "Radius": 17.3}], "Normalization": {"Position": {"Minimum": -10, "Default": -3, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": -9, "Maximum": 10}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyelashR1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamEyelashR2"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamEyeFormRY2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9.6}, "Mobility": 0.67, "Delay": 0.7, "Acceleration": 1.92, "Radius": 9.6}, {"Position": {"X": 0, "Y": 26.9}, "Mobility": 0.76, "Delay": 0.96, "Acceleration": 3.06, "Radius": 17.3}], "Normalization": {"Position": {"Minimum": -10, "Default": -3, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": -9, "Maximum": 10}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeLOpen_physics"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "ParamEyeBallSizeL_physics"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 21.4}, "Mobility": 0.77, "Delay": 0.9, "Acceleration": 1.68, "Radius": 21.4}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 10, "Maximum": 10}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeROpen_physics"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "ParamEyeBallSizeR_physics"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 21.4}, "Mobility": 0.77, "Delay": 0.9, "Acceleration": 1.68, "Radius": 21.4}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 10, "Maximum": 10}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 80, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallSizeL"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamTearLight"}, "Weight": 20, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeBallL_light"}, "VertexIndex": 1, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.84, "Delay": 0.66, "Acceleration": 2, "Radius": 16}, {"Position": {"X": 0, "Y": 42}, "Mobility": 0.8, "Delay": 0.63, "Acceleration": 2, "Radius": 26}], "Normalization": {"Position": {"Minimum": -10, "Default": 3.2, "Maximum": 10}, "Angle": {"Minimum": -9.2, "Default": -8.8, "Maximum": 10}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 80, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallSizeR"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamTearLight"}, "Weight": 20, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeBallR_light"}, "VertexIndex": 1, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.84, "Delay": 0.66, "Acceleration": 2, "Radius": 16}, {"Position": {"X": 0, "Y": 42}, "Mobility": 0.8, "Delay": 0.63, "Acceleration": 2, "Radius": 26}], "Normalization": {"Position": {"Minimum": -10, "Default": 3.2, "Maximum": 10}, "Angle": {"Minimum": -9.2, "Default": -8.8, "Maximum": 10}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBrowLY"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 49.3}, "Mobility": 0.48, "Delay": 1.01, "Acceleration": 0.2, "Radius": 49.3}], "Normalization": {"Position": {"Minimum": -10, "Default": 3.2, "Maximum": 10}, "Angle": {"Minimum": -9.2, "Default": -8.8, "Maximum": 10}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBrowRY"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 49.3}, "Mobility": 0.48, "Delay": 1.01, "Acceleration": 0.2, "Radius": 49.3}], "Normalization": {"Position": {"Minimum": -10, "Default": 3.2, "Maximum": 10}, "Angle": {"Minimum": -9.2, "Default": -8.8, "Maximum": 10}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamTearLight"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamTears"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamTear1L"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamTear2L"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.95, "Delay": 0.98, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.88, "Delay": 0.95, "Acceleration": 1.2, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamTearLight"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamTears"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamTear1R"}, "VertexIndex": 1, "Scale": 1, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamTear2R"}, "VertexIndex": 2, "Scale": 1, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.95, "Delay": 0.98, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.88, "Delay": 0.95, "Acceleration": 1.2, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeEmotion"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBrowLForm_Smile"}, "VertexIndex": 1, "Scale": 4, "Weight": 97, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBrowLForm_Angry"}, "VertexIndex": 1, "Scale": 5.8, "Weight": 95, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBrowLForm_Puzzled"}, "VertexIndex": 2, "Scale": 3, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBrowRForm_Smile"}, "VertexIndex": 1, "Scale": 4, "Weight": 97, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBrowRForm_Angry"}, "VertexIndex": 1, "Scale": 5.8, "Weight": 95, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBrowRForm_Puzzled"}, "VertexIndex": 2, "Scale": 3, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamAngryL"}, "VertexIndex": 1, "Scale": 5, "Weight": 87, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamAngryR"}, "VertexIndex": 1, "Scale": 5, "Weight": 87, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 24.1}, "Mobility": 0.6, "Delay": 0.98, "Acceleration": 1.68, "Radius": 24.1}, {"Position": {"X": 0, "Y": 46.4}, "Mobility": 0.45, "Delay": 1, "Acceleration": 1, "Radius": 22.3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting12", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallX"}, "Weight": 10, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamRabbitEarL"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamRabbitEarL2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamRabbitEarL3"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.94, "Delay": 0.93, "Acceleration": 1.03, "Radius": 9}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.93, "Delay": 0.88, "Acceleration": 1.03, "Radius": 7}, {"Position": {"X": 0, "Y": 22}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 1.03, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting13", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallX"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamRabbitEarR"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamRabbitEarR2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamRabbitEarR3"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.94, "Delay": 0.94, "Acceleration": 1.03, "Radius": 9}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.93, "Delay": 0.87, "Acceleration": 1.03, "Radius": 7}, {"Position": {"X": 0, "Y": 22}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 1.03, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting14", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPHYInputX"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBallU1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 4}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 1.03, "Radius": 4}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting15", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBallU2"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 3}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 1.03, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting16", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPHYInputX"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBallD1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 3}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 1.03, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting17", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBallD2"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 3}, "Mobility": 0.97, "Delay": 0.8, "Acceleration": 1.03, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting18", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 55, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPHYInputX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairFrontM0"}, "VertexIndex": 1, "Scale": 1.3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairFrontM1"}, "VertexIndex": 2, "Scale": 1.3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairFrontM2"}, "VertexIndex": 3, "Scale": 1.3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.93, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.96, "Delay": 0.85, "Acceleration": 1.03, "Radius": 9}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.93, "Delay": 0.82, "Acceleration": 1.03, "Radius": 7}, {"Position": {"X": 0, "Y": 22}, "Mobility": 0.92, "Delay": 0.79, "Acceleration": 1.03, "Radius": 6}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.76, "Acceleration": 1.03, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.75, "Acceleration": 1.03, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting19", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 54, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPHYInputX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 6, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairFrontL0"}, "VertexIndex": 1, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairFrontL1"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairFrontL2"}, "VertexIndex": 3, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.93, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.96, "Delay": 0.85, "Acceleration": 1.03, "Radius": 9}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.93, "Delay": 0.82, "Acceleration": 1.03, "Radius": 7}, {"Position": {"X": 0, "Y": 22}, "Mobility": 0.92, "Delay": 0.79, "Acceleration": 1.03, "Radius": 6}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.76, "Acceleration": 1.03, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.75, "Acceleration": 1.03, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting20", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 62, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairFrontY"}, "VertexIndex": 1, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairFrontY2"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.93, "Delay": 0.84, "Acceleration": 1.03, "Radius": 7}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 1.03, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting21", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 55, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPHYInputX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBraidAL0"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBraidAL1"}, "VertexIndex": 3, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 1.03, "Radius": 10}, {"Position": {"X": 0, "Y": 17}, "Mobility": 0.93, "Delay": 0.82, "Acceleration": 1.03, "Radius": 7}, {"Position": {"X": 0, "Y": 23}, "Mobility": 0.92, "Delay": 0.79, "Acceleration": 1.03, "Radius": 6}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.91, "Delay": 0.76, "Acceleration": 1.03, "Radius": 5}, {"Position": {"X": 0, "Y": 31}, "Mobility": 0.9, "Delay": 0.75, "Acceleration": 1.03, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting22", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 55, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPHYInputX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBraidBL0"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBraidBL1"}, "VertexIndex": 3, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.96, "Delay": 0.85, "Acceleration": 1.03, "Radius": 10}, {"Position": {"X": 0, "Y": 17}, "Mobility": 0.93, "Delay": 0.82, "Acceleration": 1.03, "Radius": 7}, {"Position": {"X": 0, "Y": 23}, "Mobility": 0.92, "Delay": 0.79, "Acceleration": 1.03, "Radius": 6}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.91, "Delay": 0.76, "Acceleration": 1.03, "Radius": 5}, {"Position": {"X": 0, "Y": 31}, "Mobility": 0.9, "Delay": 0.75, "Acceleration": 1.03, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting23", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 55, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPHYInputX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBraidCL0"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBraidCL1"}, "VertexIndex": 3, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9.5}, "Mobility": 0.94, "Delay": 0.85, "Acceleration": 1.03, "Radius": 9.5}, {"Position": {"X": 0, "Y": 16.5}, "Mobility": 0.93, "Delay": 0.82, "Acceleration": 1.03, "Radius": 7}, {"Position": {"X": 0, "Y": 22.5}, "Mobility": 0.92, "Delay": 0.79, "Acceleration": 1.03, "Radius": 6}, {"Position": {"X": 0, "Y": 27.5}, "Mobility": 0.91, "Delay": 0.76, "Acceleration": 1.03, "Radius": 5}, {"Position": {"X": 0, "Y": 30.5}, "Mobility": 0.9, "Delay": 0.75, "Acceleration": 1.03, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting24", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 55, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPHYInputX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBackAL0"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBackAL1"}, "VertexIndex": 3, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBackAL2"}, "VertexIndex": 4, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1.05, "Radius": 0}, {"Position": {"X": 0, "Y": 11}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 1.03, "Radius": 11}, {"Position": {"X": 0, "Y": 18.3}, "Mobility": 0.93, "Delay": 0.82, "Acceleration": 1.03, "Radius": 7.3}, {"Position": {"X": 0, "Y": 24.3}, "Mobility": 0.92, "Delay": 0.79, "Acceleration": 1.03, "Radius": 6}, {"Position": {"X": 0, "Y": 29.3}, "Mobility": 0.91, "Delay": 0.76, "Acceleration": 1.03, "Radius": 5}, {"Position": {"X": 0, "Y": 32.3}, "Mobility": 0.9, "Delay": 0.75, "Acceleration": 1.03, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting25", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 55, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPHYInputX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBackBL0"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBackBL1"}, "VertexIndex": 3, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBackBL2"}, "VertexIndex": 4, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1.05, "Radius": 0}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1.03, "Radius": 12}, {"Position": {"X": 0, "Y": 19.5}, "Mobility": 0.93, "Delay": 0.82, "Acceleration": 1.03, "Radius": 7.5}, {"Position": {"X": 0, "Y": 25.5}, "Mobility": 0.92, "Delay": 0.79, "Acceleration": 1.03, "Radius": 6}, {"Position": {"X": 0, "Y": 30.5}, "Mobility": 0.91, "Delay": 0.76, "Acceleration": 1.03, "Radius": 5}, {"Position": {"X": 0, "Y": 33.5}, "Mobility": 0.9, "Delay": 0.75, "Acceleration": 1.03, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting26", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 55, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPHYInputX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBackCL0"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBackCL1"}, "VertexIndex": 3, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBackCL2"}, "VertexIndex": 4, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1.05, "Radius": 0}, {"Position": {"X": 0, "Y": 10.9}, "Mobility": 0.94, "Delay": 0.85, "Acceleration": 1.03, "Radius": 10.9}, {"Position": {"X": 0, "Y": 17.9}, "Mobility": 0.93, "Delay": 0.82, "Acceleration": 1.03, "Radius": 7}, {"Position": {"X": 0, "Y": 24.4}, "Mobility": 0.92, "Delay": 0.79, "Acceleration": 1.03, "Radius": 6.5}, {"Position": {"X": 0, "Y": 29.4}, "Mobility": 0.91, "Delay": 0.76, "Acceleration": 1.03, "Radius": 5}, {"Position": {"X": 0, "Y": 32.4}, "Mobility": 0.9, "Delay": 0.75, "Acceleration": 1.03, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting27", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 55, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPHYInputX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 10, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBackAR0"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBackAR1"}, "VertexIndex": 3, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBackAR2"}, "VertexIndex": 4, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1.05, "Radius": 0}, {"Position": {"X": 0, "Y": 11}, "Mobility": 0.93, "Delay": 0.85, "Acceleration": 1.03, "Radius": 11}, {"Position": {"X": 0, "Y": 18.3}, "Mobility": 0.93, "Delay": 0.82, "Acceleration": 1.03, "Radius": 7.3}, {"Position": {"X": 0, "Y": 24.3}, "Mobility": 0.92, "Delay": 0.79, "Acceleration": 1.03, "Radius": 6}, {"Position": {"X": 0, "Y": 29.3}, "Mobility": 0.91, "Delay": 0.76, "Acceleration": 1.03, "Radius": 5}, {"Position": {"X": 0, "Y": 32.3}, "Mobility": 0.9, "Delay": 0.75, "Acceleration": 1.03, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting28", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 55, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPHYInputX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 10, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBackBR0"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBackBR1"}, "VertexIndex": 3, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBackBR2"}, "VertexIndex": 4, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1.05, "Radius": 0}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1.03, "Radius": 12}, {"Position": {"X": 0, "Y": 19.5}, "Mobility": 0.93, "Delay": 0.82, "Acceleration": 1.03, "Radius": 7.5}, {"Position": {"X": 0, "Y": 25.5}, "Mobility": 0.92, "Delay": 0.79, "Acceleration": 1.03, "Radius": 6}, {"Position": {"X": 0, "Y": 30.5}, "Mobility": 0.91, "Delay": 0.76, "Acceleration": 1.03, "Radius": 5}, {"Position": {"X": 0, "Y": 33.5}, "Mobility": 0.9, "Delay": 0.75, "Acceleration": 1.03, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting29", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 55, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPHYInputX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 10, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBackCR0"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBackCR1"}, "VertexIndex": 3, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBackCR2"}, "VertexIndex": 4, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1.05, "Radius": 0}, {"Position": {"X": 0, "Y": 10.9}, "Mobility": 0.94, "Delay": 0.85, "Acceleration": 1.03, "Radius": 10.9}, {"Position": {"X": 0, "Y": 17.9}, "Mobility": 0.93, "Delay": 0.82, "Acceleration": 1.03, "Radius": 7}, {"Position": {"X": 0, "Y": 24.4}, "Mobility": 0.92, "Delay": 0.79, "Acceleration": 1.03, "Radius": 6.5}, {"Position": {"X": 0, "Y": 29.4}, "Mobility": 0.91, "Delay": 0.76, "Acceleration": 1.03, "Radius": 5}, {"Position": {"X": 0, "Y": 32.4}, "Mobility": 0.9, "Delay": 0.75, "Acceleration": 1.03, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting30", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyBreechX"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX2"}, "Weight": 25, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamSkirtL1"}, "VertexIndex": 1, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSkirtL2"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSkirtL3"}, "VertexIndex": 3, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.05, "Radius": 20}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.89, "Delay": 0.88, "Acceleration": 1.03, "Radius": 15}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.85, "Delay": 0.84, "Acceleration": 1.03, "Radius": 12}, {"Position": {"X": 0, "Y": 37}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1.03, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting31", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyBreechX"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamCcharacterZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX2"}, "Weight": 25, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamSkirtR1"}, "VertexIndex": 1, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSkirtR2"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSkirtR3"}, "VertexIndex": 3, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.05, "Radius": 20}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.89, "Delay": 0.88, "Acceleration": 1.03, "Radius": 15}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.85, "Delay": 0.84, "Acceleration": 1.03, "Radius": 12}, {"Position": {"X": 0, "Y": 37}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1.03, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting32", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ2"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBreastW"}, "VertexIndex": 1, "Scale": 1, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBreastW2"}, "VertexIndex": 2, "Scale": 1, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1.4, "Radius": 0}, {"Position": {"X": 0, "Y": 3.5}, "Mobility": 0.97, "Delay": 1, "Acceleration": 1.4, "Radius": 3.5}, {"Position": {"X": 0, "Y": 6}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1.4, "Radius": 2.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting33", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ2"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBreastX"}, "VertexIndex": 1, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBreastX2"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1.3, "Radius": 0}, {"Position": {"X": 0, "Y": 3}, "Mobility": 0.97, "Delay": 1, "Acceleration": 1.3, "Radius": 3}, {"Position": {"X": 0, "Y": 5.5}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1.3, "Radius": 2.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting34", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 70, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY2"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBreastY"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBreastY2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.97, "Acceleration": 1.3, "Radius": 0}, {"Position": {"X": 0, "Y": 3.9}, "Mobility": 0.93, "Delay": 1.04, "Acceleration": 1.3, "Radius": 3.9}, {"Position": {"X": 0, "Y": 6.9}, "Mobility": 0.95, "Delay": 0.97, "Acceleration": 1.3, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting35", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX2"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ2"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyBreechX"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamTailX1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamTailX2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.95, "Delay": 0.93, "Acceleration": 1, "Radius": 9}, {"Position": {"X": 0, "Y": 13.5}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 4.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting36", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY2"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyBreechX"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamCcharacterZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamTailX3"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 1.2, "Radius": 7}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting37", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPHYInputX"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBreastBow1"}, "VertexIndex": 1, "Scale": 1.3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBreastBow2"}, "VertexIndex": 2, "Scale": 1.3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5.3}, "Mobility": 0.89, "Delay": 0.93, "Acceleration": 1, "Radius": 5.3}, {"Position": {"X": 0, "Y": 9.4}, "Mobility": 0.9, "Delay": 0.93, "Acceleration": 1, "Radius": 4.1}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting38", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ2"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBreastF1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBreastF2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.94, "Delay": 0.88, "Acceleration": 1.03, "Radius": 9}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.93, "Delay": 0.84, "Acceleration": 1.03, "Radius": 7}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting39", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPHYInputX"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamCollarBow1"}, "VertexIndex": 1, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamCollarBow2"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.92, "Delay": 0.93, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.9, "Delay": 0.93, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting40", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX2"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ2"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamClothesL"}, "VertexIndex": 1, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamClothesL2"}, "VertexIndex": 2, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamClothesL3"}, "VertexIndex": 3, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.94, "Delay": 0.88, "Acceleration": 1.03, "Radius": 9}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.93, "Delay": 0.84, "Acceleration": 1.03, "Radius": 7}, {"Position": {"X": 0, "Y": 22}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 1.03, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting41", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 25, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamSkirtSide2"}, "VertexIndex": 1, "Scale": 0.8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSkirtSide"}, "VertexIndex": 2, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSkirtSide3"}, "VertexIndex": 3, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 6}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 11}, "Mobility": 0.81, "Delay": 0.89, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.78, "Delay": 0.84, "Acceleration": 1, "Radius": 4}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting42", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamCannonShow"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamCannonZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamCannonCupY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamCannonCupZ"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamCannonHandZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamCannonLIQ1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamCannonLIQ2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamCannonLIQ3"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.95, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 6}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 0.95, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.92, "Delay": 0.96, "Acceleration": 1, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting43", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamArm<PERSON>pperRAngle"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamArmLowerRAngle"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamArmHandRAngle"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamArmLowerRH"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamArmFingerRAngle"}, "Weight": 20, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHandLIQ1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHandLIQ2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHandLIQ3"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.95, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 6}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 0.95, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.92, "Delay": 0.96, "Acceleration": 1, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": -5, "Maximum": 20}}}, {"Id": "PhysicsSetting44", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamDeskCupInput"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamDeskcupLIQ1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamDeskcupLIQ2"}, "VertexIndex": 2, "Scale": 1, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamDeskcupLIQ3"}, "VertexIndex": 3, "Scale": 1, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.95, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 9.3}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 1, "Radius": 9.3}, {"Position": {"X": 0, "Y": 16.2}, "Mobility": 0.93, "Delay": 0.95, "Acceleration": 1, "Radius": 6.9}, {"Position": {"X": 0, "Y": 22.2}, "Mobility": 0.92, "Delay": 0.96, "Acceleration": 1, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting45", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamPandaBodyX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPandaBodyY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPositionY2Panda"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPandaBodyZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamPandaCupZ2"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamPandaCup2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamPandaCup3"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.95, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 6.5}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 1, "Radius": 6.5}, {"Position": {"X": 0, "Y": 10.5}, "Mobility": 0.93, "Delay": 0.95, "Acceleration": 1, "Radius": 4}, {"Position": {"X": 0, "Y": 13.5}, "Mobility": 0.92, "Delay": 0.96, "Acceleration": 1, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting46", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamPandaBodyX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPandaBodyY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPositionY2Panda"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPandaBodyZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamPandaAp1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.95, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 5.5}, "Mobility": 0.94, "Delay": 0.95, "Acceleration": 1, "Radius": 5.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting47", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamPandaBodyX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPandaBodyY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPositionY2Panda"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPandaBodyZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamPandaBow1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.95, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 4}, "Mobility": 0.93, "Delay": 0.95, "Acceleration": 1, "Radius": 4}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting48", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamPandaBodyX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPandaBodyY"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPositionY2Panda"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPandaBodyZ"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamPandaCupZ"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.95, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 3}, "Mobility": 0.92, "Delay": 0.96, "Acceleration": 1, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting49", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamManjuuRBodyX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamManjuuRBodyY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamManjuuRBodyW"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamManjuuRBodyZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamMJRAhoge"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamMJRAhoge2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamMJRAhoge3"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.94, "Delay": 0.88, "Acceleration": 1.03, "Radius": 9}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.93, "Delay": 0.84, "Acceleration": 1.03, "Radius": 7}, {"Position": {"X": 0, "Y": 22}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 1.03, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting50", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamManjuuUBodyX"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamManjuuUBodyY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamManjuuUBodyZ"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamManjuuUBodyW"}, "Weight": 20, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamMJUAhoge"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamMJUAhoge2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.93, "Delay": 0.84, "Acceleration": 1.03, "Radius": 7}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 1.03, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting51", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamStrongCatShow"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamParamSCBodyZ"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamParamStrongCatZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamSCTie"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSCTie2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.93, "Delay": 0.84, "Acceleration": 1.03, "Radius": 7}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 1.03, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting52", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamManjuuRInput"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamMRCupFZ"}, "Weight": 70, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param3"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamMJRLiqFFlowX1"}, "VertexIndex": 1, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamMJRLiqFFlowX2"}, "VertexIndex": 2, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamMJRLiqFFlowX3"}, "VertexIndex": 3, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.97, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8.5}, "Mobility": 0.97, "Delay": 0.97, "Acceleration": 1, "Radius": 8.5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.97, "Acceleration": 1, "Radius": 6.5}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.94, "Delay": 0.97, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting53", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamManjuuRInput"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamMRCupFZ"}, "Weight": 70, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param3"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamMJRLiqBFlowX1"}, "VertexIndex": 1, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamMJRLiqBFlowX2"}, "VertexIndex": 2, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamMJRLiqBFlowX3"}, "VertexIndex": 3, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.97, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.97, "Delay": 0.94, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 14.3}, "Mobility": 0.95, "Delay": 0.94, "Acceleration": 1, "Radius": 6.3}, {"Position": {"X": 0, "Y": 19.8}, "Mobility": 0.94, "Delay": 0.92, "Acceleration": 0.97, "Radius": 5.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting54", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamManjuuLBodyX"}, "Weight": 15, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamManjuuLBodyY"}, "Weight": 15, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamManjuuLBodyZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamPositionZManjuuL"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamManjuuLLIQ1"}, "VertexIndex": 1, "Scale": 1, "Weight": 95, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamManjuuLLIQ2"}, "VertexIndex": 2, "Scale": 1, "Weight": 95, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamManjuuLLIQ3"}, "VertexIndex": 3, "Scale": 1, "Weight": 95, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.97, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7.5}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 1, "Radius": 7.5}, {"Position": {"X": 0, "Y": 13.8}, "Mobility": 0.96, "Delay": 0.94, "Acceleration": 1, "Radius": 6.3}, {"Position": {"X": 0, "Y": 19.3}, "Mobility": 0.94, "Delay": 0.92, "Acceleration": 0.97, "Radius": 5.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}]}