{"Version": 3, "Meta": {"Duration": 20.1, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 313, "TotalSegmentCount": 31000, "TotalPointCount": 54200, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param109", "Segments": [0, 0, 2, 0.167, 0, 0, 0.667, 30, 2, 19.267, 30, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandL1Display", "Segments": [0, 0, 2, 0.167, 0, 2, 11.933, 0, 2, 11.967, 30, 2, 14.767, 30, 2, 14.8, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandL2Display", "Segments": [0, 0, 2, 0.167, 0, 2, 1.533, 0, 2, 1.567, 30, 2, 1.833, 30, 2, 1.867, 0, 2, 7.333, 0, 2, 7.367, 30, 2, 11.933, 30, 2, 11.967, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandL5Display", "Segments": [0, 30, 2, 0.167, 30, 2, 1.533, 30, 2, 1.567, 0, 2, 1.833, 0, 2, 1.867, 30, 2, 7.333, 30, 2, 7.367, 0, 2, 14.767, 0, 2, 14.8, 30, 2, 20.1, 30]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 6.967, 0, 2, 7, -30, 2, 14.733, -30, 2, 14.767, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandR1Display", "Segments": [0, 0, 2, 0.167, 0, 2, 7.2, 0, 2, 7.233, 30, 2, 11.6, 30, 2, 11.633, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandR4Display", "Segments": [0, 0, 2, 0.167, 0, 2, 11.6, 0, 2, 11.633, 30, 2, 18.567, 30, 2, 18.6, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandR5Display", "Segments": [0, 30, 2, 0.167, 30, 2, 1.833, 30, 2, 1.867, 0, 2, 18.567, 0, 2, 18.6, 30, 2, 20.1, 30]}, {"Target": "Parameter", "Id": "ParamHandR9Display", "Segments": [0, 0, 2, 0.167, 0, 2, 1.833, 0, 2, 1.867, 30, 2, 7.2, 30, 2, 7.233, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 2, 0.167, 0, 2, 1.167, 0, 2, 4.667, 0, 0, 4.767, -19, 0, 4.867, 16, 0, 4.933, -12, 0, 5.067, 11, 0, 5.2, 0, 2, 9.833, 0, 2, 19.1, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 2, 0.167, 0, 2, 1.167, 0, 2, 4.667, 0, 0, 4.767, -1, 0, 4.867, 10, 0, 4.933, -10, 1, 4.978, -10, 5.022, -10.102, 5.067, -9, 1, 5.111, -7.898, 5.156, 0, 5.2, 0, 2, 9.833, 0, 2, 19.1, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "Segments": [0, 0, 2, 0.167, 0, 2, 1.167, 0, 2, 1.467, 0, 0, 1.933, 4.2, 2, 6.967, 4.2, 0, 7.3, 2.741, 0, 7.767, 20.4, 0, 8.333, 18.9, 2, 9.467, 18.9, 0, 9.833, 20.399, 0, 10.633, 8.869, 1, 11.122, 8.869, 11.611, 13.211, 12.1, 20.38, 1, 12.322, 23.639, 12.545, 24.657, 12.767, 24.657, 1, 12.945, 24.657, 13.122, 23.051, 13.3, 21.931, 1, 13.589, 20.109, 13.878, 20.107, 14.167, 17.5, 1, 14.456, 14.893, 14.744, -7.2, 15.033, -7.2, 2, 17.633, -7.2, 0, 18.167, 3.404, 0, 18.767, 0, 2, 19.1, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "Segments": [0, 0, 2, 0.167, 0, 2, 1.167, 0, 2, 1.933, 0, 2, 6.967, 0, 2, 7.767, 0, 1, 8.456, 0, 9.144, 0.221, 9.833, -0.672, 1, 10.6, -1.667, 11.366, -15.3, 12.133, -15.3, 2, 12.267, -15.3, 0, 12.767, -21.84, 1, 12.945, -21.84, 13.122, -20.373, 13.3, -19.349, 1, 13.589, -17.684, 13.878, -17.65, 14.167, -15.3, 1, 14.456, -12.95, 14.744, 2, 15.033, 2, 2, 17.633, 2, 0, 18.133, 0, 2, 19.1, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "Segments": [0, 0, 2, 0.167, 0, 0, 0.667, -1, 2, 1, -1, 0, 1.167, 0.145, 0, 1.467, -6.139, 0, 1.933, 4.08, 2, 6.967, 4.08, 1, 7, 4.08, 7.034, 2.571, 7.067, 2.308, 1, 7.3, 0.464, 7.534, -0.37, 7.767, -2.7, 1, 7.956, -4.586, 8.144, -8.46, 8.333, -8.46, 2, 9.467, -8.46, 1, 9.589, -8.46, 9.711, -5.529, 9.833, -2.7, 1, 10.055, 2.444, 10.278, 4.08, 10.5, 4.08, 1, 10.867, 4.08, 11.233, 4.111, 11.6, 2.432, 1, 11.822, 1.414, 12.045, -16.7, 12.267, -16.7, 0, 12.767, -7.74, 0, 13.3, -11.153, 0, 13.767, -7.96, 0, 14.167, -16.7, 0, 15.033, 6.38, 2, 17.633, 6.38, 1, 17.789, 6.38, 17.944, -1.521, 18.1, -2.861, 1, 18.311, -4.68, 18.522, -4.637, 18.733, -4.637, 0, 19.1, 0.145, 0, 19.6, -1, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "Segments": [0, 30, 2, 0.167, 30, 2, 1.167, 30, 2, 1.933, 30, 2, 6.967, 30, 2, 7.767, 30, 2, 9.467, 30, 1, 9.589, 30, 9.711, 30.153, 9.833, 29.789, 1, 10.6, 27.507, 11.366, 25.2, 12.133, 25.2, 2, 12.267, 25.2, 2, 13.3, 25.2, 2, 14.167, 25.2, 0, 15.033, 30, 2, 17.633, 30, 2, 18.133, 30, 2, 19.1, 30, 2, 20.1, 30]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "Segments": [0, -12.6, 2, 0.167, -12.6, 1, 0.334, -12.6, 0.5, -5.895, 0.667, -0.921, 1, 0.711, 0.406, 0.756, 0, 0.8, 0, 2, 1.167, 0, 2, 1.933, 0, 2, 6.967, 0, 2, 7.767, 0, 2, 9.467, 0, 2, 9.833, 0, 2, 12.133, 0, 2, 12.267, 0, 0, 12.767, -11.76, 1, 12.945, -11.76, 13.122, -10.267, 13.3, -7.28, 1, 13.589, -2.427, 13.878, 0, 14.167, 0, 2, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 2, 19.1, 0, 2, 19.467, 0, 1, 19.511, 0, 19.556, 0.406, 19.6, -0.921, 1, 19.767, -5.895, 19.933, -12.6, 20.1, -12.6]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, 7, 2, 0.167, 7, 1, 0.5, 6.205, 0.834, 5.409, 1.167, 4.614, 1, 1.267, 8.076, 1.367, 11.538, 1.467, 15, 0, 2.333, 0, 2, 3.7, 0, 2, 9.833, 0, 2, 12.867, 0, 2, 14.567, 0, 0, 15.067, 30, 2, 16.033, 30, 0, 19.1, 4.614, 1, 19.433, 5.409, 19.767, 6.205, 20.1, 7]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "Segments": [0, -30, 2, 0.167, -30, 2, 0.5, -30, 0, 0.867, 30, 0, 1.167, 0, 0, 1.833, 30, 2, 1.867, 30, 0, 2.1, -30, 2, 6.767, -30, 0, 7.3, 30, 0, 7.667, 24, 0, 8.333, 30, 2, 9.467, 30, 1, 9.589, 30, 9.711, 13.596, 9.833, 0, 1, 10.044, -23.483, 10.256, -30, 10.467, -30, 2, 11.6, -30, 0, 12.133, -10, 2, 14.167, -10, 0, 14.767, -2, 0, 14.8, -30, 0, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 0, 18.5, 30, 0, 19.1, 0, 0, 19.3, 30, 0, 20.1, -30]}, {"Target": "Parameter", "Id": "ParamHandLChange", "Segments": [0, 0, 2, 0.167, 0, 2, 1.167, 0, 0, 1.5, -30, 2, 1.567, -30, 0, 1.933, 0, 2, 6.967, 0, 0, 7.767, 15, 0, 8.333, 0, 2, 9.467, 0, 0, 9.833, 14.341, 0, 10.467, -30, 2, 11.6, -30, 0, 12.133, 0, 2, 14.167, 0, 0, 14.8, -30, 2, 17.633, -30, 2, 18.1, -30, 0, 18.433, -24, 0, 18.6, -30, 2, 18.7, -30, 0, 19.1, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "Segments": [0, -7.4, 2, 0.167, -7.4, 0, 0.667, 2.97, 0, 0.9, -0.6, 1, 0.989, -0.6, 1.078, -0.609, 1.167, -0.572, 1, 1.256, -0.536, 1.344, 7, 1.433, 7, 0, 1.633, -12.651, 0, 1.8, -11, 0, 2.133, -16, 1, 3.744, -16, 5.356, -15.974, 6.967, -15.9, 1, 7.134, -15.892, 7.3, -1.708, 7.467, -1.708, 0, 7.767, -15, 0, 8.333, 6, 2, 9.467, 6, 0, 9.833, -18.108, 0, 11.067, 2.952, 1, 11.245, 2.952, 11.422, 3.281, 11.6, 0.776, 1, 11.778, -1.729, 11.955, -24.8, 12.133, -24.8, 0, 12.767, -19, 1, 12.945, -19, 13.122, -19.172, 13.3, -21.21, 1, 13.4, -22.356, 13.5, -28, 13.6, -28, 0, 13.967, 2, 0, 14.233, -21, 1, 14.5, -21, 14.766, -4.148, 15.033, -4, 1, 15.9, -3.52, 16.766, -3.331, 17.633, -2.893, 1, 17.8, -2.809, 17.966, 2.8, 18.133, 2.8, 0, 18.467, -11.778, 0, 19.1, -0.572, 1, 19.189, -0.572, 19.278, -0.533, 19.367, -0.6, 1, 19.611, -0.783, 19.856, -7.4, 20.1, -7.4]}, {"Target": "Parameter", "Id": "ParamHandL_DMZ", "Segments": [0, 0, 2, 0.167, 0, 2, 1.167, 0, 2, 1.933, 0, 2, 6.967, 0, 2, 7.767, 0, 2, 9.467, 0, 1, 9.589, 0, 9.711, -0.915, 9.833, 1.318, 1, 10.6, 15.325, 11.366, 30, 12.133, 30, 2, 12.267, 30, 1, 12.289, 30, 12.311, 4.12, 12.333, 0, 1, 12.455, -22.658, 12.578, -30, 12.7, -30, 1, 12.722, -30, 12.745, -23.967, 12.767, 0, 1, 12.778, 11.984, 12.789, 30, 12.8, 30, 1, 12.833, 30, 12.867, 22.683, 12.9, 22, 1, 13.033, 19.268, 13.167, 18.525, 13.3, 15.053, 1, 13.589, 7.529, 13.878, 0, 14.167, 0, 2, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 2, 19.1, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandL_SZ", "Segments": [0, 0, 2, 0.167, 0, 1, 0.222, -4.333, 0.278, -8.667, 0.333, -13, 0, 0.7, 23, 0, 1.167, -30, 1, 1.389, -30, 1.611, -28.319, 1.833, -23.462, 1, 1.866, -22.734, 1.9, 0, 1.933, 0, 2, 6.967, 0, 2, 7.767, 0, 2, 9.467, 0, 1, 9.589, 0, 9.711, -0.254, 9.833, 0.351, 1, 10.6, 4.151, 11.366, 8, 12.133, 8, 1, 12.178, 8, 12.222, 7.668, 12.267, 6, 1, 12.289, 5.166, 12.311, 3.207, 12.333, 0, 1, 12.455, -17.64, 12.578, -30, 12.7, -30, 1, 12.722, -30, 12.745, -17.487, 12.767, 0, 1, 12.778, 8.744, 12.789, 18.591, 12.8, 21, 1, 12.833, 28.227, 12.867, 30, 12.9, 30, 1, 13.033, 30, 13.167, 24.235, 13.3, 20.526, 1, 13.589, 12.492, 13.878, 9.234, 14.167, 0, 1, 14.367, -6.393, 14.567, -23.462, 14.767, -23.462, 0, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 1, 18.166, 0, 18.2, -22.914, 18.233, -23.462, 1, 18.522, -28.217, 18.811, -30, 19.1, -30, 0, 19.567, 23, 0, 19.933, -13, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandL_ZZ", "Segments": [0, 0, 2, 0.167, 0, 1, 0.256, 10, 0.344, 20, 0.433, 30, 0, 0.9, 0, 2, 1.167, 0, 2, 1.933, 0, 2, 6.967, 0, 2, 7.767, 0, 2, 9.467, 0, 1, 9.589, 0, 9.711, -0.223, 9.833, 0.308, 1, 10.6, 3.634, 11.366, 7, 12.133, 7, 1, 12.178, 7, 12.222, 6.886, 12.267, 6, 1, 12.289, 5.557, 12.311, 3.207, 12.333, 0, 1, 12.455, -17.64, 12.578, -30, 12.7, -30, 0, 12.767, 0, 2, 13.3, 0, 2, 14.167, 0, 2, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 2, 19.1, 0, 2, 19.367, 0, 0, 19.833, 30, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandL_WMZ", "Segments": [0, 0, 2, 0.167, 0, 1, 0.245, 10, 0.322, 20, 0.4, 30, 2, 1.167, 30, 1, 1.422, 30, 1.678, 21.742, 1.933, 0, 1, 1.966, -2.836, 2, -18.076, 2.033, -18.076, 0, 6.967, 0, 2, 7.767, 0, 2, 9.467, 0, 1, 9.589, 0, 9.711, 0.56, 9.833, -0.924, 1, 10.533, -9.424, 11.233, -18.076, 11.933, -18.076, 0, 12.133, 7, 1, 12.178, 7, 12.222, 6.176, 12.267, 4, 1, 12.289, 2.912, 12.311, 2.8, 12.333, 0, 1, 12.455, -15.398, 12.578, -30, 12.7, -30, 0, 12.767, 0, 2, 13.3, 0, 2, 14.167, 0, 0, 14.9, -18.076, 0, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 0, 19.1, 30, 2, 19.867, 30, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandL_XMZ", "Segments": [0, 0, 2, 0.167, 0, 2, 1.167, 0, 2, 1.933, 0, 2, 6.967, 0, 2, 7.767, 0, 2, 9.467, 0, 1, 9.589, 0, 9.711, -0.286, 9.833, 0.395, 1, 10.6, 4.668, 11.366, 9, 12.133, 9, 2, 12.267, 9, 1, 12.289, 9, 12.311, 3.863, 12.333, 0, 1, 12.455, -21.246, 12.578, -30, 12.7, -30, 0, 12.767, 0, 2, 13.3, 0, 2, 14.167, 0, 2, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 2, 19.1, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "siwala", "Segments": [0, 0, 2, 0.167, 0, 2, 1.167, 0, 2, 1.933, 0, 2, 6.967, 0, 2, 7.767, 0, 2, 9.467, 0, 2, 9.833, 0, 2, 12.1, 0, 0, 12.333, 1, 0, 12.767, -1, 0, 13, 0.4, 0, 13.2, -0.093, 0, 13.3, 0, 2, 19.1, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "Param115", "Segments": [0, 0, 2, 0.167, 0, 0, 0.433, 30, 2, 1.167, 30, 2, 19.1, 30, 2, 19.467, 30, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "Segments": [0, 2.4, 2, 0.167, 2.4, 1, 0.5, 1.6, 0.834, 0.8, 1.167, 0, 0, 1.933, 3.4, 2, 6.967, 3.4, 0, 7.767, 1.24, 2, 9.467, 1.24, 1, 9.978, 1.24, 10.489, 1.182, 11, 1.732, 1, 11.367, 2.127, 11.733, 13.6, 12.1, 13.6, 0, 12.733, 12.8, 0, 13.3, 12.82, 1, 13.589, 12.82, 13.878, 13.005, 14.167, 12.16, 1, 14.456, 11.315, 14.744, -18.1, 15.033, -18.1, 2, 17.633, -18.1, 0, 18.133, 12.16, 0, 19.1, 0, 0, 20.1, 2.4]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "Segments": [0, -16, 2, 0.167, -16, 1, 0.5, -10.667, 0.834, -5.333, 1.167, 0, 2, 1.933, 0, 2, 6.967, 0, 0, 7.767, -9.2, 2, 9.467, -9.2, 1, 9.978, -9.2, 10.489, -9.098, 11, -10.138, 1, 11.367, -10.884, 11.733, -30, 12.1, -30, 2, 13.3, -30, 2, 14.167, -30, 0, 15.033, -24.8, 2, 17.633, -24.8, 0, 18.133, -30, 0, 19.1, 0, 0, 20.1, -16]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "Segments": [0, -4.4, 2, 0.167, -4.4, 1, 0.311, -4.4, 0.456, -2.821, 0.6, 0.9, 1, 0.644, 2.045, 0.689, 2.989, 0.733, 2.989, 0, 1.033, 1.8, 2, 1.167, 1.8, 0, 1.5, -1.587, 0, 1.8, 3.84, 0, 1.933, 0.54, 2, 6.967, 0.54, 1, 7, 0.54, 7.034, 0.291, 7.067, 0, 1, 7.167, -0.872, 7.267, -1.23, 7.367, -1.23, 0, 7.767, -1.2, 2, 9.467, -1.2, 1, 9.978, -1.2, 10.489, -1.18, 11, -1.373, 1, 11.367, -1.512, 11.733, -5.04, 12.1, -5.04, 2, 13.3, -5.04, 2, 14.167, -5.04, 1, 14.278, -5.04, 14.389, -6.447, 14.5, -9, 1, 14.678, -13.085, 14.855, -30, 15.033, -30, 2, 17.633, -30, 1, 17.8, -30, 17.966, -8.228, 18.133, -5.04, 1, 18.455, 1.123, 18.778, 1.8, 19.1, 1.8, 2, 19.233, 1.8, 0, 19.533, 2.989, 1, 19.578, 2.989, 19.622, 2.045, 19.667, 0.9, 1, 19.811, -2.821, 19.956, -4.4, 20.1, -4.4]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "Segments": [0, 0, 2, 0.167, 0, 2, 1.167, 0, 2, 1.933, 0, 2, 6.967, 0, 0, 7.767, -8.9, 2, 9.467, -8.9, 1, 9.978, -8.9, 10.489, -8.946, 11, -8.499, 1, 11.367, -8.178, 11.733, 0, 12.1, 0, 2, 13.3, 0, 2, 14.167, 0, 0, 15.033, -30, 2, 17.633, -30, 0, 18.133, 0, 2, 19.1, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "Segments": [0, 0, 2, 0.167, 0, 2, 1.167, 0, 2, 1.933, 0, 2, 6.967, 0, 0, 7.767, -30, 2, 9.467, -30, 1, 9.978, -30, 10.489, -30.25, 11, -29.913, 1, 13.7, -28.134, 16.4, 0, 19.1, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, 0, 2, 0.167, 0, 2, 1.167, 0, 2, 2.233, 0, 0, 3.133, 30, 1, 5.755, 30, 8.378, 28.639, 11, 19.66, 1, 13.7, 10.415, 16.4, 0, 19.1, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "Segments": [0, 17, 2, 0.167, 17, 1, 0.222, 17, 0.278, 17.144, 0.333, 15.896, 1, 0.411, 14.148, 0.489, 9.74, 0.567, 9.74, 1, 0.767, 9.74, 0.967, 9.774, 1.167, 10.127, 1, 1.278, 10.324, 1.389, 11, 1.5, 11, 0, 1.633, 4.536, 0, 1.867, 16.455, 0, 2, 11.52, 2, 6.967, 11.52, 1, 7.022, 11.52, 7.078, 11.224, 7.133, 12.461, 1, 7.222, 14.441, 7.311, 24, 7.4, 24, 0, 7.767, 22.2, 2, 9.467, 22.2, 1, 9.978, 22.2, 10.489, 22.113, 11, 21.763, 1, 11.256, 21.588, 11.511, 21.298, 11.767, 20.884, 1, 11.878, 20.704, 11.989, 12.5, 12.1, 12.5, 2, 13.3, 12.5, 2, 14.167, 12.5, 1, 14.222, 12.5, 14.278, 8.549, 14.333, 8.3, 1, 14.566, 7.254, 14.8, 7, 15.033, 7, 1, 16.066, 7, 17.1, 7.334, 18.133, 8.3, 1, 18.255, 8.414, 18.378, 16.578, 18.5, 16.578, 1, 18.567, 16.578, 18.633, 11.412, 18.7, 11, 1, 18.833, 10.177, 18.967, 10.127, 19.1, 10.127, 0, 19.6, 20, 0, 19.933, 17, 2, 20.1, 17]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 1.167, 0, 2, 1.933, 0, 2, 6.967, 0, 0, 7.767, -5, 1, 8.456, -5, 9.144, -5.073, 9.833, -4.775, 1, 10.589, -4.448, 11.344, 0, 12.1, 0, 2, 13.3, 0, 2, 19.1, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "Segments": [0, 0, 2, 0.167, 0, 0, 0.433, 30, 0, 1.167, 0, 2, 1.867, 0, 0, 2.1, -30, 2, 6.967, -30, 2, 7.233, -30, 0, 7.467, 0, 0, 7.967, -10, 2, 8.233, -10, 2, 9.467, -10, 2, 9.7, -10, 2, 9.833, -10, 2, 9.967, -10, 1, 10.589, -10, 11.211, -7.436, 11.833, 0, 1, 11.922, 1.062, 12.011, 30, 12.1, 30, 0, 12.733, 18.6, 0, 13.3, 30, 2, 14.167, 30, 2, 14.3, 30, 0, 14.7, -30, 0, 15.133, 30, 2, 17.633, 30, 0, 17.867, -30, 0, 18.133, 30, 0, 19.1, 0, 0, 19.833, 30, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandR_SZ", "Segments": [0, 0, 2, 0.167, 0, 1, 0.234, 10, 0.3, 20, 0.367, 30, 1, 0.445, 30, 0.522, 8.945, 0.6, 0, 1, 0.789, -21.723, 0.978, -27, 1.167, -27, 0, 1.933, 0, 2, 6.967, 0, 2, 7.767, 0, 2, 9.467, 0, 2, 9.833, 0, 2, 12.1, 0, 2, 13.3, 0, 2, 14.167, 0, 2, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 0, 19.1, -27, 1, 19.289, -27, 19.478, -21.723, 19.667, 0, 1, 19.745, 8.945, 19.822, 30, 19.9, 30, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandR_ZZ", "Segments": [0, 0, 2, 0.167, 0, 1, 0.278, 4.463, 0.389, 8.927, 0.5, 13.39, 0, 1.167, 0, 2, 1.933, 0, 2, 6.967, 0, 2, 7.767, 0, 2, 9.467, 0, 2, 9.833, 0, 2, 12.1, 0, 2, 13.3, 0, 2, 14.167, 0, 2, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 2, 19.1, 0, 0, 19.767, 13.39, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandR_WMZ", "Segments": [0, 0, 2, 0.167, 0, 1, 0.367, -6.333, 0.567, -12.667, 0.767, -19, 2, 1.167, -19, 0, 1.933, 0, 2, 6.967, 0, 2, 7.767, 0, 2, 9.467, 0, 2, 9.833, 0, 2, 12.1, 0, 2, 13.3, 0, 2, 14.167, 0, 2, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 0, 19.1, -19, 2, 19.5, -19, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandR_XMZ", "Segments": [0, 0, 2, 0.167, 0, 1, 0.367, -10, 0.567, -20, 0.767, -30, 2, 1.167, -30, 0, 1.933, 0, 2, 6.967, 0, 2, 7.767, 0, 2, 9.467, 0, 2, 9.833, 0, 2, 12.1, 0, 2, 13.3, 0, 2, 14.167, 0, 2, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 0, 19.1, -30, 2, 19.5, -30, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.167, 0, 1, 0.356, 0, 0.544, -8.459, 0.733, -14, 1, 0.944, -20.192, 1.156, -21, 1.367, -21, 0, 1.933, 0, 2, 3.367, 0, 1, 3.5, 0, 3.634, -6.735, 3.767, -7, 1, 4.545, -8.545, 5.322, -9, 6.1, -9, 0, 6.967, 0, 0, 7.4, -6, 0, 8.267, 6, 2, 9.833, 6, 2, 10, 6, 0, 12.367, 0, 0, 13.2, 21.774, 1, 13.789, 21.774, 14.378, 19.691, 14.967, 12, 1, 15.167, 9.388, 15.367, -1.768, 15.567, -6, 1, 15.834, -11.643, 16.1, -13, 16.367, -13, 0, 17.167, 0, 2, 18.7, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.167, 0, 0, 0.733, -30, 0, 1.933, 0, 2, 3.367, 0, 0, 3.767, -20, 2, 6.1, -20, 1, 6.389, -20, 6.678, -14.975, 6.967, 0, 1, 7.111, 7.488, 7.256, 15.24, 7.4, 15.24, 0, 8.267, -19, 2, 9.833, -19, 2, 10, -19, 0, 14.967, 0, 1, 15.167, 0, 15.367, -15.716, 15.567, -16, 1, 16.211, -16.917, 16.856, -17.06, 17.5, -17.06, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "Segments": [0, 0, 2, 0.167, 0, 2, 1.933, 0, 2, 3.4, 0, 0, 4.1, -7, 1, 5.056, -7, 6.011, -5.105, 6.967, 0, 1, 7.089, 0.653, 7.211, 4, 7.333, 4, 1, 8.166, 4, 9, 3.483, 9.833, 2.825, 1, 10.178, 2.554, 10.522, 2.359, 10.867, 2.237, 1, 13.1, 1.448, 15.334, 0.786, 17.567, 0, 1, 17.689, -0.043, 17.811, -15.6, 17.933, -15.6, 0, 18.7, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 10, 2, 0.167, 10, 0, 0.933, -10, 0, 1.733, 6, 0, 2.167, 0, 2, 6.967, 0, 0, 7.6, -10, 0, 8.5, 10, 2, 9.833, 10, 2, 10.233, 10, 0, 12.1, -4.733, 0, 12.267, 0, 1, 12.434, -3.333, 12.6, -6.667, 12.767, -10, 1, 12.945, -10, 13.122, -9.255, 13.3, -6.753, 1, 13.589, -2.688, 13.878, 0, 14.167, 0, 2, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 0, 20.1, 10]}, {"Target": "Parameter", "Id": "ParamChestZ", "Segments": [0, 0, 2, 0.167, 0, 0, 1.933, 9.9, 2, 6.967, 9.9, 1, 7.922, 8.057, 8.878, 6.214, 9.833, 4.371, 1, 10.589, 2.914, 11.344, 1.457, 12.1, 0, 2, 12.267, 0, 2, 13.3, 0, 2, 14.167, 0, 1, 14.456, -6.567, 14.744, -13.133, 15.033, -19.7, 2, 17.633, -19.7, 1, 17.8, -13.133, 17.966, -6.567, 18.133, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.167, 0, 0, 1.467, -1.373, 0, 1.933, 0, 2, 2.333, 0, 2, 6.967, 0, 1, 7.922, 0.745, 8.878, 1.489, 9.833, 2.234, 1, 10.589, 2.823, 11.344, 3.411, 12.1, 4, 0, 12.767, 0, 2, 13.3, 0, 2, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 0, 18.733, -1.373, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "Segments": [0, 0, 2, 0.167, 0, 0, 0.833, 3.84, 0, 2.167, -4.085, 1, 2.611, -4.085, 3.056, 1.209, 3.5, 6.956, 1, 3.567, 7.818, 3.633, 7.495, 3.7, 7.495, 0, 8.333, 0, 2, 9.833, 0, 2, 10.067, 0, 1, 11.078, 0, 12.089, 2.275, 13.1, 6.956, 1, 13.167, 7.264, 13.233, 7.495, 13.3, 7.495, 0, 17.933, 0, 0, 19.233, 3.84, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "Segments": [0, 0, 2, 0.167, 0, 0, 0.5, -11.28, 0, 1.167, 9.78, 1, 1.489, 9.78, 1.811, 6.013, 2.133, 0, 1, 2.255, -2.281, 2.378, -4.183, 2.5, -5.699, 1, 2.556, -6.388, 2.611, -6.42, 2.667, -6.42, 1, 2.945, -6.42, 3.222, -5.795, 3.5, -3.578, 1, 3.967, 0.146, 4.433, 2.64, 4.9, 2.64, 0, 7.967, -5.699, 0, 8.333, 0, 0, 9.7, -5.699, 1, 9.744, -5.699, 9.789, -5.476, 9.833, -4.676, 1, 9.911, -3.276, 9.989, -1.571, 10.067, 0, 1, 10.389, 6.508, 10.711, 9.78, 11.033, 9.78, 1, 11.389, 9.78, 11.744, 1.96, 12.1, -5.699, 1, 12.156, -6.895, 12.211, -6.42, 12.267, -6.42, 1, 12.545, -6.42, 12.822, -5.795, 13.1, -3.578, 1, 13.567, 0.146, 14.033, 2.64, 14.5, 2.64, 0, 17.567, -5.699, 1, 17.689, -5.699, 17.811, -2.726, 17.933, 0, 1, 18.255, 7.187, 18.578, 9.78, 18.9, 9.78, 0, 19.567, -11.28, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 2, 0.167, 0, 0, 0.4, -20, 0, 1.367, 22, 1, 1.622, 22, 1.878, 12.609, 2.133, 0, 1, 2.255, -6.03, 2.378, -10.627, 2.5, -14.19, 1, 2.633, -18.077, 2.767, -19, 2.9, -19, 1, 3.1, -19, 3.3, -18.964, 3.5, -14.312, 1, 4.1, -0.359, 4.7, 11, 5.3, 11, 0, 7.967, -14.19, 0, 8.333, 0, 0, 9.7, -14.19, 1, 9.744, -14.19, 9.789, -13.786, 9.833, -11.857, 1, 9.911, -8.481, 9.989, -4.331, 10.067, 0, 1, 10.322, 14.23, 10.578, 22, 10.833, 22, 1, 11.255, 22, 11.678, 3.473, 12.1, -14.19, 1, 12.233, -19.768, 12.367, -19, 12.5, -19, 1, 12.7, -19, 12.9, -18.964, 13.1, -14.312, 1, 13.7, -0.359, 14.3, 11, 14.9, 11, 0, 17.567, -14.19, 1, 17.689, -14.19, 17.811, -7.633, 17.933, 0, 1, 18.189, 15.96, 18.444, 22, 18.7, 22, 0, 19.667, -20, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "Segments": [0, 0, 2, 0.167, 0, 0, 1.6, -3.12, 0, 3.2, 3.48, 1, 3.3, 3.48, 3.4, 3.627, 3.5, 2.81, 1, 3.933, -0.73, 4.367, -3.78, 4.8, -3.78, 0, 7.967, 2.158, 0, 8.867, -3.12, 0, 9.7, 2.158, 1, 9.744, 2.158, 9.789, 2.298, 9.833, 1.843, 1, 10.089, -0.773, 10.344, -3.12, 10.6, -3.12, 0, 12.8, 3.48, 1, 12.9, 3.48, 13, 3.627, 13.1, 2.81, 1, 13.533, -0.73, 13.967, -3.78, 14.4, -3.78, 0, 17.567, 2.158, 0, 18.467, -3.12, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "Segments": [0, 0, 2, 0.167, 0, 0, 0.9, 19.92, 0, 2.133, 0, 2, 2.267, 0, 1, 2.345, 0, 2.422, -0.1, 2.5, 0.578, 1, 2.833, 3.486, 3.167, 8.069, 3.5, 11.219, 1, 3.722, 13.319, 3.945, 13.92, 4.167, 13.92, 1, 5.434, 13.92, 6.7, 9.262, 7.967, 0.578, 1, 8.045, 0.045, 8.122, 0, 8.2, 0, 2, 8.333, 0, 0, 9.7, 0.578, 1, 9.744, 0.578, 9.789, 0.448, 9.833, 0.229, 1, 9.866, 0.064, 9.9, 0, 9.933, 0, 2, 10.067, 0, 2, 11.867, 0, 1, 11.945, 0, 12.022, -0.1, 12.1, 0.578, 1, 12.433, 3.486, 12.767, 8.069, 13.1, 11.219, 1, 13.322, 13.319, 13.545, 13.92, 13.767, 13.92, 1, 15.034, 13.92, 16.3, 9.262, 17.567, 0.578, 1, 17.645, 0.045, 17.722, 0, 17.8, 0, 2, 17.933, 0, 0, 19.167, 19.92, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 2, 0.167, 0, 0, 0.567, -21, 0, 1.333, 30, 1, 1.6, 30, 1.866, 17.244, 2.133, 0, 1, 2.255, -7.903, 2.378, -13.962, 2.5, -18.496, 1, 2.544, -20.145, 2.589, -20, 2.633, -20, 1, 2.922, -20, 3.211, -14.311, 3.5, -3.57, 1, 3.767, 6.346, 4.033, 11, 4.3, 11, 0, 7.967, -18.496, 0, 8.333, 0, 0, 9.7, -18.496, 1, 9.744, -18.496, 9.789, -17.968, 9.833, -15.454, 1, 9.911, -11.053, 9.989, -5.662, 10.067, 0, 1, 10.334, 19.411, 10.6, 30, 10.867, 30, 1, 11.278, 30, 11.689, 6.465, 12.1, -18.496, 1, 12.144, -21.195, 12.189, -20, 12.233, -20, 1, 12.522, -20, 12.811, -14.311, 13.1, -3.57, 1, 13.367, 6.346, 13.633, 11, 13.9, 11, 0, 17.567, -18.496, 1, 17.689, -18.496, 17.811, -9.946, 17.933, 0, 1, 18.2, 21.7, 18.466, 30, 18.733, 30, 0, 19.5, -21, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 2, 0.167, 0, 2, 1.933, 0, 0, 5.267, -1, 0, 6.967, 0, 1, 7.922, 0, 8.878, -0.378, 9.833, -0.849, 1, 10.144, -1.002, 10.456, -1, 10.767, -1, 0, 12.1, 0, 2, 12.267, 0, 2, 13.3, 0, 2, 14.167, 0, 2, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0.235, 2, 0.167, 0.235, 1, 0.189, 0.243, 0.211, 0.745, 0.233, 0.753, 1, 0.278, 0.759, 0.322, -0.006, 0.367, 0, 1, 0.411, -0.008, 0.456, 0.976, 0.5, 0.969, 1, 0.589, 0.967, 0.678, 0.594, 0.767, 0.592, 1, 0.789, 0.609, 0.811, 0.626, 0.833, 0.643, 1, 0.878, 0.644, 0.922, 0.509, 0.967, 0.51, 1, 1.034, 0.508, 1.1, 0.779, 1.167, 0.776, 1, 1.189, 0.774, 1.211, 0.603, 1.233, 0.6, 1, 1.255, 0.61, 1.278, 0.621, 1.3, 0.631, 1, 1.433, 0.629, 1.567, 0.002, 1.7, 0, 2, 2.3, 0, 1, 2.344, -0.007, 2.389, 0.96, 2.433, 0.953, 1, 2.455, 0.952, 2.478, 0.883, 2.5, 0.882, 1, 2.522, 0.894, 2.545, 0.906, 2.567, 0.918, 1, 2.634, 0.922, 2.7, 0.42, 2.767, 0.424, 1, 2.789, 0.429, 2.811, 0.798, 2.833, 0.804, 1, 2.878, 0.81, 2.922, -0.006, 2.967, 0, 1, 3.011, -0.006, 3.056, 0.814, 3.1, 0.808, 1, 3.144, 0.81, 3.189, 0.488, 3.233, 0.49, 1, 3.278, 0.489, 3.322, 0.581, 3.367, 0.58, 1, 3.389, 0.573, 3.411, 0.129, 3.433, 0.122, 1, 3.478, 0.118, 3.522, 0.616, 3.567, 0.612, 1, 3.589, 0.602, 3.611, 0.017, 3.633, 0.008, 1, 3.655, 0.02, 3.678, 0.757, 3.7, 0.769, 1, 3.722, 0.758, 3.745, 0.089, 3.767, 0.078, 1, 3.789, 0.088, 3.811, 0.661, 3.833, 0.671, 1, 3.878, 0.676, 3.922, 0.015, 3.967, 0.02, 1, 4.011, 0.013, 4.056, 0.901, 4.1, 0.894, 1, 4.122, 0.887, 4.145, 0.462, 4.167, 0.455, 1, 4.211, 0.451, 4.256, 0.933, 4.3, 0.929, 1, 4.389, 0.928, 4.478, 0.586, 4.567, 0.584, 1, 4.611, 0.583, 4.656, 0.814, 4.7, 0.812, 1, 4.744, 0.818, 4.789, -0.006, 4.833, 0, 1, 4.878, -0.005, 4.922, 0.644, 4.967, 0.639, 1, 4.989, 0.634, 5.011, 0.33, 5.033, 0.325, 1, 5.078, 0.323, 5.122, 0.661, 5.167, 0.659, 1, 5.189, 0.649, 5.211, 0.01, 5.233, 0, 2, 5.3, 0, 1, 5.344, -0.005, 5.389, 0.672, 5.433, 0.667, 1, 5.455, 0.663, 5.478, 0.454, 5.5, 0.451, 1, 5.522, 0.455, 5.545, 0.69, 5.567, 0.694, 1, 5.634, 0.7, 5.7, -0.001, 5.767, 0.004, 1, 5.811, 0.003, 5.856, 0.174, 5.9, 0.173, 1, 6.011, 0.172, 6.122, 0.001, 6.233, 0, 2, 6.5, 0, 1, 6.522, 0.001, 6.545, 0.003, 6.567, 0.004, 1, 6.589, 0.003, 6.611, 0.001, 6.633, 0, 2, 6.967, 0, 1, 7.078, 0.003, 7.189, 0.699, 7.3, 0.702, 1, 7.322, 0.7, 7.345, 0.571, 7.367, 0.569, 1, 7.389, 0.571, 7.411, 0.708, 7.433, 0.71, 1, 7.478, 0.712, 7.522, 0.485, 7.567, 0.486, 1, 7.589, 0.489, 7.611, 0.656, 7.633, 0.659, 1, 7.744, 0.656, 7.856, 0.003, 7.967, 0, 2, 8.567, 0, 1, 8.634, -0.007, 8.7, 0.889, 8.767, 0.882, 1, 8.856, 0.88, 8.944, 0.187, 9.033, 0.184, 1, 9.078, 0.181, 9.122, 0.611, 9.167, 0.608, 1, 9.189, 0.6, 9.211, 0.145, 9.233, 0.137, 2, 9.3, 0.137, 1, 9.322, 0.135, 9.345, 0.006, 9.367, 0.004, 1, 9.411, 0.003, 9.456, 0.075, 9.5, 0.075, 1, 9.611, 0.074, 9.722, 0, 9.833, 0, 2, 10.1, 0, 1, 10.122, 0.013, 10.145, 0.826, 10.167, 0.839, 1, 10.211, 0.846, 10.256, -0.003, 10.3, 0.004, 1, 10.322, 0.018, 10.345, 0.876, 10.367, 0.89, 1, 10.389, 0.877, 10.411, 0.083, 10.433, 0.071, 1, 10.455, 0.08, 10.478, 0.673, 10.5, 0.682, 1, 10.522, 0.68, 10.545, 0.528, 10.567, 0.525, 1, 10.589, 0.532, 10.611, 0.958, 10.633, 0.965, 1, 10.655, 0.953, 10.678, 0.2, 10.7, 0.188, 1, 10.722, 0.195, 10.745, 0.601, 10.767, 0.608, 1, 10.789, 0.603, 10.811, 0.597, 10.833, 0.592, 1, 10.855, 0.593, 10.878, 0.646, 10.9, 0.647, 1, 10.922, 0.642, 10.945, 0.338, 10.967, 0.333, 1, 10.989, 0.343, 11.011, 0.966, 11.033, 0.976, 1, 11.055, 0.971, 11.078, 0.656, 11.1, 0.651, 1, 11.122, 0.655, 11.145, 0.875, 11.167, 0.878, 1, 11.189, 0.873, 11.211, 0.566, 11.233, 0.561, 1, 11.278, 0.559, 11.322, 0.766, 11.367, 0.765, 1, 11.478, 0.762, 11.589, 0.003, 11.7, 0, 2, 11.767, 0, 1, 11.789, 0.007, 11.811, 0.013, 11.833, 0.02, 1, 11.9, 0.013, 11.966, 0.007, 12.033, 0, 2, 12.167, 0, 1, 12.234, -0.007, 12.3, 0.917, 12.367, 0.91, 1, 12.434, 0.917, 12.5, -0.007, 12.567, 0, 2, 12.767, 0, 1, 12.811, -0.006, 12.856, 0.767, 12.9, 0.761, 1, 12.944, 0.762, 12.989, 0.626, 13.033, 0.627, 1, 13.055, 0.632, 13.078, 0.882, 13.1, 0.886, 1, 13.144, 0.887, 13.189, 0.728, 13.233, 0.729, 1, 13.255, 0.731, 13.278, 0.826, 13.3, 0.827, 1, 13.322, 0.822, 13.345, 0.484, 13.367, 0.478, 1, 13.411, 0.477, 13.456, 0.652, 13.5, 0.651, 1, 13.544, 0.652, 13.589, 0.497, 13.633, 0.498, 1, 13.655, 0.504, 13.678, 0.892, 13.7, 0.898, 1, 13.767, 0.905, 13.833, 0.005, 13.9, 0.012, 1, 13.922, 0.017, 13.945, 0.332, 13.967, 0.337, 1, 13.989, 0.332, 14.011, 0.009, 14.033, 0.004, 1, 14.055, 0.013, 14.078, 0.548, 14.1, 0.557, 2, 14.167, 0.557, 1, 14.211, 0.556, 14.256, 0.62, 14.3, 0.62, 1, 14.322, 0.619, 14.345, 0.554, 14.367, 0.553, 1, 14.411, 0.55, 14.456, 0.905, 14.5, 0.902, 1, 14.611, 0.899, 14.722, 0.039, 14.833, 0.035, 1, 14.855, 0.049, 14.878, 0.919, 14.9, 0.933, 1, 14.967, 0.938, 15.033, 0.313, 15.1, 0.318, 1, 15.122, 0.32, 15.145, 0.472, 15.167, 0.475, 1, 15.189, 0.468, 15.211, 0.069, 15.233, 0.063, 1, 15.278, 0.058, 15.322, 0.663, 15.367, 0.659, 1, 15.411, 0.661, 15.456, 0.386, 15.5, 0.388, 1, 15.522, 0.392, 15.545, 0.631, 15.567, 0.635, 1, 15.589, 0.625, 15.611, 0.014, 15.633, 0.004, 1, 15.655, 0.01, 15.678, 0.359, 15.7, 0.365, 1, 15.744, 0.365, 15.789, 0.274, 15.833, 0.275, 1, 15.878, 0.272, 15.922, 0.547, 15.967, 0.545, 1, 16.034, 0.549, 16.1, -0.004, 16.167, 0, 2, 16.633, 0, 1, 16.678, -0.001, 16.722, 0.107, 16.767, 0.106, 1, 16.811, 0.107, 16.856, 0.019, 16.9, 0.02, 1, 16.944, 0.015, 16.989, 0.585, 17.033, 0.58, 1, 17.078, 0.585, 17.122, 0.019, 17.167, 0.024, 1, 17.189, 0.029, 17.211, 0.034, 17.233, 0.039, 1, 17.255, 0.026, 17.278, 0.013, 17.3, 0, 2, 17.367, 0, 1, 17.411, -0.006, 17.456, 0.739, 17.5, 0.733, 1, 17.633, 0.73, 17.767, 0.003, 17.9, 0, 2, 18.233, 0, 1, 18.278, -0.006, 18.322, 0.826, 18.367, 0.82, 1, 18.411, 0.825, 18.456, 0.069, 18.5, 0.075, 1, 18.522, 0.086, 18.545, 0.808, 18.567, 0.82, 1, 18.589, 0.815, 18.611, 0.557, 18.633, 0.553, 1, 18.655, 0.558, 18.678, 0.842, 18.7, 0.847, 1, 18.767, 0.851, 18.833, 0.298, 18.9, 0.302, 1, 18.944, 0.299, 18.989, 0.721, 19.033, 0.718, 1, 19.055, 0.709, 19.078, 0.699, 19.1, 0.69, 1, 19.122, 0.695, 19.145, 0.701, 19.167, 0.706, 1, 19.189, 0.698, 19.211, 0.216, 19.233, 0.208, 1, 19.278, 0.204, 19.322, 0.71, 19.367, 0.706, 1, 19.389, 0.703, 19.411, 0.544, 19.433, 0.541, 1, 19.455, 0.543, 19.478, 0.63, 19.5, 0.631, 1, 19.567, 0.636, 19.633, -0.005, 19.7, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.167, 1, 2, 0.667, 1, 0, 1, 0, 0, 1.333, 1, 2, 1.933, 1, 2, 3.833, 1, 0, 4.167, 0, 2, 4.9, 0, 0, 5.2, 1, 2, 6.967, 1, 0, 8, 1.2, 0, 8.533, 1, 0, 9.733, 1.2, 1, 9.766, 1.2, 9.8, 1.204, 9.833, 1.182, 1, 9.978, 1.084, 10.122, 1, 10.267, 1, 2, 12.1, 1, 2, 12.267, 1, 2, 12.967, 1, 0, 13.133, 0, 0, 13.3, 1, 2, 14.167, 1, 0, 14.367, 0, 0, 14.567, 1, 2, 15.033, 1, 2, 16.9, 1, 0, 17.167, 0, 2, 17.633, 0, 0, 17.967, 1, 2, 18.933, 1, 0, 19.267, 0, 0, 19.6, 1, 2, 20.1, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 2, 0.167, 0, 2, 1.933, 0, 2, 6.967, 0, 2, 9.833, 0, 2, 12.1, 0, 2, 12.267, 0, 2, 13.3, 0, 2, 14.167, 0, 2, 15.033, 0, 0, 17.167, 1, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.167, 1, 2, 0.667, 1, 0, 1, 0, 0, 1.333, 1, 2, 1.933, 1, 2, 3.833, 1, 0, 4.167, 0, 2, 4.9, 0, 0, 5.2, 1, 2, 6.967, 1, 0, 8, 1.2, 0, 8.533, 0.753, 0, 9.733, 1.2, 1, 9.766, 1.2, 9.8, 1.209, 9.833, 1.159, 1, 9.978, 0.94, 10.122, 0.753, 10.267, 0.753, 0, 12.1, 1, 2, 12.267, 1, 2, 12.967, 1, 0, 13.133, 0, 0, 13.3, 1, 2, 14.167, 1, 0, 14.367, 0, 0, 14.567, 1, 2, 15.033, 1, 2, 16.9, 1, 0, 17.167, 0, 2, 17.633, 0, 0, 17.967, 1, 2, 18.933, 1, 0, 19.267, 0, 0, 19.6, 1, 2, 20.1, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 2, 0.167, 0, 2, 1.933, 0, 2, 6.967, 0, 2, 9.833, 0, 2, 12.1, 0, 2, 12.267, 0, 2, 13.3, 0, 2, 14.167, 0, 2, 15.033, 0, 0, 17.167, 1, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 0.167, 0, 2, 1.933, 0, 2, 6.967, 0, 1, 7.922, 0, 8.878, 0.06, 9.833, 0.188, 1, 10.866, 0.327, 11.9, 0.4, 12.933, 0.4, 0, 13.433, -0.3, 0, 14.167, 0, 2, 15.033, 0, 0, 15.767, -0.5, 1, 15.967, -0.5, 16.167, -0.1, 16.367, 0, 1, 16.878, 0.255, 17.389, 0.3, 17.9, 0.3, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 0.167, 0, 2, 1.933, 0, 2, 6.967, 0, 1, 7.922, 0, 8.878, -0.12, 9.833, -0.377, 1, 10.866, -0.654, 11.9, -0.8, 12.933, -0.8, 0, 13.433, 0.5, 0, 14.167, 0, 2, 15.033, 0, 0, 15.767, -0.6, 1, 15.967, -0.6, 16.167, -0.189, 16.367, 0, 1, 16.878, 0.483, 17.389, 0.6, 17.9, 0.6, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "weak_strong", "Segments": [0, 0, 2, 0.167, 0, 2, 1.933, 0, 2, 6.967, 0, 2, 7.267, 0, 0, 8.533, 4, 2, 9.833, 4, 2, 10.267, 4, 0, 12.1, 0, 2, 12.267, 0, 2, 13.3, 0, 2, 14.167, 0, 2, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "Segments": [0, 0, 2, 0.167, 0, 2, 0.367, 0, 2, 0.467, 0, 2, 0.667, 0, 2, 0.7, 0, 0, 0.9, 21.511, 0, 1.2, -30, 0, 1.5, 17.505, 0, 1.8, -5.78, 0, 2.1, 1.995, 0, 2.433, -0.71, 0, 2.733, 0.251, 0, 3.033, -0.088, 0, 3.333, 0.031, 0, 3.667, -0.011, 0, 4.067, 21.512, 0, 4.333, -15.552, 0, 4.633, 5.182, 0, 5.133, -23.385, 0, 5.367, 15.893, 0, 5.7, -5.289, 0, 6, 1.865, 0, 6.3, -0.655, 0, 6.6, 0.229, 0, 6.933, -0.081, 0, 7, -0.058, 0, 7.333, -1, 0, 8.267, 2.672, 0, 8.633, -2.152, 1, 8.733, -2.152, 8.833, 0.485, 8.933, 0.754, 1, 9.233, 1.562, 9.533, 2.284, 9.833, 2.642, 1, 9.889, 2.709, 9.944, 2.672, 10, 2.672, 0, 10.367, -2.152, 0, 10.667, 0.754, 1, 10.767, 0.754, 10.867, -0.159, 10.967, -0.262, 1, 11.322, -0.631, 11.678, -0.71, 12.033, -0.71, 0, 12.333, 0.251, 0, 12.633, -0.088, 0, 12.933, 0.031, 0, 13, 0.027, 0, 13.133, 30, 0, 13.3, -30, 0, 13.567, 11.429, 0, 13.9, -3.871, 0, 14.367, 30, 0, 14.533, -30, 2, 14.6, -30, 0, 14.833, 12.139, 0, 15.133, -4.122, 0, 15.467, 1.435, 0, 15.767, -0.51, 0, 16.067, 0.18, 0, 16.367, -0.063, 0, 16.7, 0.022, 0, 17, -0.008, 0, 17.3, 0.003, 0, 17.5, 0, 0, 17.633, 30, 0, 17.867, -28.693, 0, 18.167, 14.966, 0, 18.467, -5.092, 0, 18.767, 1.783, 0, 18.967, -0.098, 0, 19.167, 21.198, 0, 19.467, -30, 0, 19.767, 17.488, 0, 20.067, -5.774, 1, 20.078, -5.774, 20.089, -0.182, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "Segments": [0, 0, 2, 0.167, 0, 2, 0.367, 0, 2, 0.467, 0, 2, 0.667, 0, 2, 0.7, 0, 0, 0.9, 21.511, 0, 1.2, -30, 0, 1.5, 17.505, 0, 1.8, -5.78, 0, 2.1, 1.995, 0, 2.433, -0.71, 0, 2.733, 0.251, 0, 3.033, -0.088, 0, 3.333, 0.031, 0, 3.667, -0.011, 0, 4.067, 21.512, 0, 4.333, -15.552, 0, 4.633, 5.182, 0, 5.133, -23.385, 0, 5.367, 15.893, 0, 5.7, -5.289, 0, 6, 1.865, 0, 6.3, -0.655, 0, 6.6, 0.229, 0, 6.933, -0.081, 0, 7, -0.058, 0, 7.333, -1, 0, 8.267, 5.932, 0, 8.633, -4.916, 1, 8.733, -4.916, 8.833, 0.325, 8.933, 1.004, 1, 9.233, 3.04, 9.533, 4.9, 9.833, 5.85, 1, 9.889, 6.025, 9.944, 5.932, 10, 5.932, 0, 10.367, -4.916, 0, 10.667, 1.004, 0, 11, -1.088, 1, 11.344, -1.088, 11.689, -1.025, 12.033, -0.71, 1, 12.133, -0.618, 12.233, 0.251, 12.333, 0.251, 0, 12.633, -0.088, 0, 12.933, 0.031, 0, 13, 0.027, 0, 13.133, 30, 0, 13.3, -30, 0, 13.567, 11.429, 0, 13.9, -3.871, 0, 14.367, 30, 0, 14.533, -30, 2, 14.6, -30, 0, 14.833, 12.139, 0, 15.133, -4.122, 0, 15.467, 1.435, 0, 15.767, -0.51, 0, 16.067, 0.18, 0, 16.367, -0.063, 0, 16.7, 0.022, 0, 17, -0.008, 0, 17.3, 0.003, 0, 17.5, 0, 0, 17.633, 30, 0, 17.867, -28.693, 0, 18.167, 14.966, 0, 18.467, -5.092, 0, 18.767, 1.783, 0, 18.967, -0.098, 0, 19.167, 21.198, 0, 19.467, -30, 0, 19.767, 17.488, 0, 20.067, -5.774, 1, 20.078, -5.774, 20.089, -0.182, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "Segments": [0, 0, 2, 0.167, 0, 2, 0.367, 0, 2, 0.467, 0, 2, 0.667, 0, 2, 0.7, 0, 0, 0.9, 21.511, 0, 1.2, -30, 0, 1.5, 17.505, 0, 1.8, -5.78, 0, 2.1, 1.995, 0, 2.433, -0.71, 0, 2.733, 0.251, 0, 3.033, -0.088, 0, 3.333, 0.031, 0, 3.667, -0.011, 0, 4.067, 21.512, 0, 4.333, -15.552, 0, 4.633, 5.182, 0, 5.133, -23.385, 0, 5.367, 15.893, 0, 5.7, -5.289, 0, 6, 1.865, 0, 6.3, -0.655, 0, 6.6, 0.229, 0, 6.933, -0.081, 0, 7, -0.058, 0, 7.333, -1, 0, 8.267, 2.672, 0, 8.633, -2.152, 1, 8.733, -2.152, 8.833, 0.485, 8.933, 0.754, 1, 9.233, 1.562, 9.533, 2.284, 9.833, 2.642, 1, 9.889, 2.709, 9.944, 2.672, 10, 2.672, 0, 10.367, -2.152, 0, 10.667, 0.754, 1, 10.767, 0.754, 10.867, -0.159, 10.967, -0.262, 1, 11.322, -0.631, 11.678, -0.71, 12.033, -0.71, 0, 12.333, 0.251, 0, 12.633, -0.088, 0, 12.933, 0.031, 0, 13, 0.027, 0, 13.133, 30, 0, 13.3, -30, 0, 13.567, 11.429, 0, 13.9, -3.871, 0, 14.367, 30, 0, 14.533, -30, 2, 14.6, -30, 0, 14.833, 12.139, 0, 15.133, -4.122, 0, 15.467, 1.435, 0, 15.767, -0.51, 0, 16.067, 0.18, 0, 16.367, -0.063, 0, 16.7, 0.022, 0, 17, -0.008, 0, 17.3, 0.003, 0, 17.5, 0, 0, 17.633, 30, 0, 17.867, -28.693, 0, 18.167, 14.966, 0, 18.467, -5.092, 0, 18.767, 1.783, 0, 18.967, -0.098, 0, 19.167, 21.198, 0, 19.467, -30, 0, 19.767, 17.488, 0, 20.067, -5.774, 1, 20.078, -5.774, 20.089, -0.182, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "Segments": [0, 0, 2, 0.167, 0, 2, 0.367, 0, 2, 0.467, 0, 2, 0.667, 0, 2, 0.7, 0, 0, 0.9, 21.511, 0, 1.2, -30, 0, 1.5, 17.505, 0, 1.8, -5.78, 0, 2.1, 1.995, 0, 2.433, -0.71, 0, 2.733, 0.251, 0, 3.033, -0.088, 0, 3.333, 0.031, 0, 3.667, -0.011, 0, 4.067, 21.512, 0, 4.333, -15.552, 0, 4.633, 5.182, 0, 5.133, -23.385, 0, 5.367, 15.893, 0, 5.7, -5.289, 0, 6, 1.865, 0, 6.3, -0.655, 0, 6.6, 0.229, 0, 6.933, -0.081, 0, 7, -0.058, 0, 7.333, -1, 0, 8.267, 5.932, 0, 8.633, -4.916, 1, 8.733, -4.916, 8.833, 0.325, 8.933, 1.004, 1, 9.233, 3.04, 9.533, 4.9, 9.833, 5.85, 1, 9.889, 6.025, 9.944, 5.932, 10, 5.932, 0, 10.367, -4.916, 0, 10.667, 1.004, 0, 11, -1.088, 1, 11.344, -1.088, 11.689, -1.025, 12.033, -0.71, 1, 12.133, -0.618, 12.233, 0.251, 12.333, 0.251, 0, 12.633, -0.088, 0, 12.933, 0.031, 0, 13, 0.027, 0, 13.133, 30, 0, 13.3, -30, 0, 13.567, 11.429, 0, 13.9, -3.871, 0, 14.367, 30, 0, 14.533, -30, 2, 14.6, -30, 0, 14.833, 12.139, 0, 15.133, -4.122, 0, 15.467, 1.435, 0, 15.767, -0.51, 0, 16.067, 0.18, 0, 16.367, -0.063, 0, 16.7, 0.022, 0, 17, -0.008, 0, 17.3, 0.003, 0, 17.5, 0, 0, 17.633, 30, 0, 17.867, -28.693, 0, 18.167, 14.966, 0, 18.467, -5.092, 0, 18.767, 1.783, 0, 18.967, -0.098, 0, 19.167, 21.198, 0, 19.467, -30, 0, 19.767, 17.488, 0, 20.067, -5.774, 1, 20.078, -5.774, 20.089, -0.182, 20.1, 0]}, {"Target": "Parameter", "Id": "Param89", "Segments": [0, 0, 2, 0.167, 0, 0, 4.133, 9.66, 0, 7.333, 0, 0, 8.533, 24, 2, 9.833, 24, 2, 10.267, 24, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param88", "Segments": [0, 0, 2, 0.167, 0, 0, 4.133, -9.24, 1, 5.2, -9.24, 6.266, -7.986, 7.333, 0, 1, 7.733, 2.995, 8.133, 30, 8.533, 30, 2, 9.833, 30, 2, 10.267, 30, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 2, 0.167, 0, 2, 1.933, 0, 2, 6.967, 0, 2, 7.333, 0, 0, 8.533, -1, 2, 9.833, -1, 2, 10.267, -1, 0, 12.1, 0, 2, 12.267, 0, 2, 13.3, 0, 2, 14.167, 0, 2, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 2, 0.167, 0, 2, 1.933, 0, 2, 6.967, 0, 2, 7.333, 0, 0, 8.533, -0.7, 2, 9.833, -0.7, 2, 10.267, -0.7, 0, 12.1, 0, 2, 12.267, 0, 2, 13.3, 0, 2, 14.167, 0, 2, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 2, 0.167, 0, 2, 0.633, 0, 0, 0.667, 30, 2, 9.833, 30, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "null_b", "Segments": [0, 0, 2, 0.167, 0, 0, 0.733, 0.5, 0, 1.933, 0, 2, 6.967, 0, 2, 7.5, 0, 0, 7.9, 1, 2, 9.633, 1, 1, 9.7, 1, 9.766, 1.011, 9.833, 0.998, 1, 12.533, 0.495, 15.233, 0, 17.933, 0, 0, 18.433, 0.532, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Hair_physics", "Segments": [0, 0, 2, 0.167, 0, 2, 0.967, 0, 0, 1.3, 1, 0, 1.933, 0, 2, 6.967, 0, 0, 7.3, 1, 0, 8.567, 0, 0, 8.9, 1, 1, 9.211, 1, 9.522, 0.692, 9.833, 0.259, 1, 9.989, 0.043, 10.144, 0, 10.3, 0, 0, 10.633, 1, 0, 14.5, 0, 0, 14.833, 1, 0, 17.6, 0, 0, 17.933, 1, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Clothes_physics", "Segments": [0, 0, 2, 0.167, 0, 2, 0.967, 0, 0, 1.3, 1, 0, 1.933, 0, 2, 6.967, 0, 0, 7.3, 1, 0, 8.567, 0, 0, 8.9, 1, 1, 9.211, 1, 9.522, 0.692, 9.833, 0.259, 1, 9.989, 0.043, 10.144, 0, 10.3, 0, 0, 10.633, 1, 0, 14.5, 0, 0, 14.833, 1, 0, 17.6, 0, 0, 17.933, 1, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHairFront1", "Segments": [0, 0, 2, 0.167, 0, 0, 0.5, -0.847, 0, 0.9, 1.671, 0, 1.233, -9.63, 0, 1.6, 12.948, 0, 2.033, -10.536, 0, 2.467, 6.102, 0, 2.867, -3.578, 0, 3.3, 2.13, 0, 3.7, -2.003, 0, 4.067, 1.519, 0, 4.5, -0.9, 0, 4.9, 0.531, 0, 5.333, -0.319, 0, 5.767, 0.187, 0, 6.233, -0.123, 0, 7, 0.479, 0, 7.233, -8.657, 0, 7.567, 9.346, 0, 8, -5.044, 0, 8.4, 3.307, 0, 8.833, -11.453, 1, 9.133, -11.453, 9.433, -9.48, 9.733, -5.044, 1, 9.766, -4.551, 9.8, -3.87, 9.833, -2.751, 1, 9.933, 0.609, 10.033, 3.307, 10.133, 3.307, 0, 10.567, -11.453, 0, 10.933, 13.432, 0, 12.033, -2.05, 0, 12.433, 0.986, 0, 12.833, -0.594, 0, 13.267, 0.664, 0, 13.7, -0.369, 0, 14.133, 0.258, 0, 14.767, -8.972, 0, 15.1, 10.007, 0, 15.533, -5.456, 0, 15.933, 3.552, 0, 16.367, -1.892, 0, 16.8, 1.193, 0, 17.233, -0.802, 0, 17.633, 0.189, 0, 17.867, -8.985, 0, 18.2, 10.101, 0, 18.633, -5.3, 0, 19.067, 3.246, 0, 19.467, -1.982, 0, 19.9, 0.932, 1, 19.967, 0.932, 20.033, 0.636, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHairFront2", "Segments": [0, 0, 2, 0.167, 0, 0, 0.333, 0.211, 0, 0.667, -1.846, 0, 1.1, 5.043, 0, 1.4, -19.847, 0, 1.767, 21.436, 0, 2.2, -15.028, 0, 2.6, 9.857, 0, 3, -5.94, 0, 3.433, 3.566, 0, 3.833, -3.929, 0, 4.2, 2.819, 0, 4.6, -1.585, 0, 5.033, 0.899, 0, 5.467, -0.516, 0, 5.9, 0.31, 0, 6.367, -0.186, 0, 7.133, 3.4, 0, 7.4, -17.54, 0, 7.733, 15.917, 0, 8.133, -8.482, 0, 8.533, 6.704, 0, 8.967, -22.11, 1, 9.256, -22.11, 9.544, -18.019, 9.833, -9.665, 1, 9.844, -9.344, 9.856, -9.25, 9.867, -8.482, 1, 10, 0.728, 10.134, 6.704, 10.267, 6.704, 0, 10.7, -22.11, 0, 11.067, 20.926, 0, 12.133, -3.889, 0, 12.567, 1.482, 0, 12.967, -1.366, 0, 13.433, 0.947, 0, 13.833, -0.613, 0, 14.267, 0.459, 0, 14.533, -0.006, 0, 14.667, 2.449, 0, 14.933, -18.272, 0, 15.267, 16.609, 0, 15.667, -9.226, 0, 16.067, 6.479, 0, 16.5, -2.505, 0, 16.9, 2.576, 0, 17.367, -0.825, 0, 17.767, 3.077, 0, 18.033, -18.518, 0, 18.367, 16.744, 0, 18.767, -8.716, 0, 19.167, 6.282, 0, 19.6, -2.622, 0, 20, 1.971, 1, 20.033, 1.971, 20.067, 0.386, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHairFront3", "Segments": [0, 0, 2, 0.167, 0, 0, 0.5, 0.602, 0, 0.833, -1.831, 0, 1.233, 7.928, 0, 1.533, -18.432, 0, 1.867, 19.845, 0, 2.267, -14.413, 0, 2.667, 10.541, 0, 3.067, -6.928, 0, 3.5, 4.224, 0, 3.933, -3.949, 0, 4.3, 3.496, 0, 4.667, -2.256, 0, 5.067, 1.18, 0, 5.5, -0.589, 0, 5.933, 0.341, 0, 6.333, -0.182, 0, 7.267, 6.458, 0, 7.533, -15.972, 0, 7.867, 16.731, 0, 8.2, -11.779, 1, 8.333, -11.779, 8.467, 4.878, 8.6, 7.859, 1, 8.933, 15.311, 9.267, 16.731, 9.6, 16.731, 1, 9.678, 16.731, 9.755, 7.138, 9.833, -5.593, 1, 9.866, -11.049, 9.9, -11.779, 9.933, -11.779, 0, 10.333, 7.859, 0, 10.833, -18.607, 1, 11.289, -18.607, 11.744, -15.048, 12.2, -4.851, 1, 12.333, -1.866, 12.467, 2.738, 12.6, 2.738, 0, 13, -1.428, 0, 13.5, 0.806, 0, 13.9, -0.692, 0, 14.3, 0.474, 0, 14.567, -0.026, 0, 14.8, 6.123, 0, 15.067, -16.06, 0, 15.367, 17.34, 0, 15.733, -12.492, 0, 16.133, 7.361, 0, 16.533, -3.981, 0, 16.967, 2.24, 0, 17.4, -1.269, 0, 17.9, 6.645, 0, 18.167, -16.554, 0, 18.5, 17.245, 0, 18.833, -12.009, 0, 19.233, 7.179, 0, 19.633, -3.805, 0, 20.067, 2.153, 1, 20.078, 2.153, 20.089, 0.076, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHairFront4", "Segments": [0, -0.001, 2, 0.167, -0.001, 0, 0.567, 0.634, 0, 0.933, -2.298, 0, 1.3, 9.083, 0, 1.6, -21.616, 0, 1.933, 26.205, 0, 2.333, -20.287, 0, 2.733, 15.593, 0, 3.133, -11.294, 0, 3.533, 7.415, 0, 4, -5.57, 0, 4.367, 5.425, 0, 4.733, -4.165, 0, 5.1, 2.484, 0, 5.5, -1.205, 0, 5.933, 0.551, 0, 6.4, -0.31, 0, 6.733, -0.007, 0, 6.967, -0.056, 0, 7.333, 6.662, 0, 7.6, -18.259, 0, 7.933, 23.055, 0, 8.3, -18.656, 1, 8.422, -18.656, 8.545, 10.386, 8.667, 13.362, 1, 9, 21.476, 9.334, 23.055, 9.667, 23.055, 1, 9.722, 23.055, 9.778, 17.616, 9.833, 5.002, 1, 9.9, -10.134, 9.966, -18.656, 10.033, -18.656, 0, 10.4, 13.362, 0, 10.9, -20.107, 0, 11.9, 12.987, 0, 12.3, -8.913, 0, 12.667, 5.509, 0, 13.033, -2.998, 0, 13.467, 1.322, 0, 13.967, -0.954, 0, 14.367, 0.792, 0, 14.6, -0.021, 0, 14.867, 6.207, 0, 15.133, -18.314, 0, 15.467, 23.712, 0, 15.833, -19.343, 0, 16.2, 13.053, 0, 16.6, -7.635, 0, 17, 4.114, 0, 17.433, -2.159, 0, 17.967, 7.022, 0, 18.233, -19.22, 0, 18.567, 23.771, 0, 18.933, -18.892, 0, 19.3, 12.732, 0, 19.7, -7.392, 0, 20.1, -0.001]}, {"Target": "Parameter", "Id": "ParamHairFront5", "Segments": [0, -0.001, 2, 0.167, -0.001, 0, 0.667, 0.67, 0, 1, -2.82, 0, 1.367, 10.504, 0, 1.667, -24.218, 0, 2, 30, 0, 2.4, -26.263, 0, 2.833, 21.401, 0, 3.233, -16.872, 0, 3.6, 12.147, 0, 4.033, -8.573, 0, 4.467, 8.037, 0, 4.833, -7.012, 0, 5.2, 4.857, 0, 5.567, -2.695, 0, 5.933, 1.175, 0, 6.4, -0.472, 0, 6.8, 0.1, 0, 7.067, -0.088, 0, 7.4, 6.74, 0, 7.7, -20.003, 0, 8.033, 28.475, 0, 8.4, -25.583, 1, 8.522, -25.583, 8.645, 17.149, 8.767, 19.829, 1, 9.1, 27.138, 9.434, 28.475, 9.767, 28.475, 1, 9.789, 28.475, 9.811, 29.595, 9.833, 23.772, 1, 9.933, -2.429, 10.033, -25.583, 10.133, -25.583, 0, 10.5, 19.829, 0, 10.933, -22.228, 0, 12, 19.976, 0, 12.367, -14.899, 0, 12.733, 10.207, 0, 13.1, -6.224, 0, 13.467, 3.015, 0, 14, -1.148, 0, 14.467, 1.161, 0, 14.7, -0.037, 0, 14.967, 6.095, 0, 15.2, -19.778, 0, 15.533, 29.116, 0, 15.933, -25.944, 0, 16.3, 19.982, 0, 16.667, -13.441, 0, 17.033, 7.953, 0, 17.433, -4.104, 0, 18.033, 7.107, 0, 18.3, -21.268, 0, 18.633, 29.251, 0, 19.033, -25.726, 0, 19.4, 19.677, 0, 19.767, -13.086, 1, 19.878, -13.086, 19.989, -9.342, 20.1, -0.001]}, {"Target": "Parameter", "Id": "ParamHairSide1", "Segments": [0, 0, 2, 0.167, 0, 0, 0.5, -0.847, 0, 0.9, 1.671, 0, 1.233, -9.63, 0, 1.6, 12.948, 0, 2.033, -10.536, 0, 2.467, 6.102, 0, 2.867, -3.578, 0, 3.3, 2.13, 0, 3.7, -2.003, 0, 4.067, 1.519, 0, 4.5, -0.9, 0, 4.9, 0.531, 0, 5.333, -0.319, 0, 5.767, 0.187, 0, 6.233, -0.123, 0, 7, 0.479, 0, 7.233, -8.657, 0, 7.567, 9.346, 0, 8, -5.044, 0, 8.4, 3.307, 0, 8.833, -11.453, 1, 9.133, -11.453, 9.433, -9.48, 9.733, -5.044, 1, 9.766, -4.551, 9.8, -3.87, 9.833, -2.751, 1, 9.933, 0.609, 10.033, 3.307, 10.133, 3.307, 0, 10.567, -11.453, 0, 10.933, 13.432, 0, 12.033, -2.05, 0, 12.433, 0.986, 0, 12.833, -0.594, 0, 13.267, 0.664, 0, 13.7, -0.369, 0, 14.133, 0.258, 0, 14.767, -8.972, 0, 15.1, 10.007, 0, 15.533, -5.456, 0, 15.933, 3.552, 0, 16.367, -1.892, 0, 16.8, 1.193, 0, 17.233, -0.802, 0, 17.633, 0.189, 0, 17.867, -8.985, 0, 18.2, 10.101, 0, 18.633, -5.3, 0, 19.067, 3.246, 0, 19.467, -1.982, 0, 19.9, 0.932, 1, 19.967, 0.932, 20.033, 0.636, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHairSide2", "Segments": [0, 0, 2, 0.167, 0, 0, 0.333, 0.211, 0, 0.667, -1.846, 0, 1.1, 5.043, 0, 1.4, -19.847, 0, 1.767, 21.436, 0, 2.2, -15.028, 0, 2.6, 9.857, 0, 3, -5.94, 0, 3.433, 3.566, 0, 3.833, -3.929, 0, 4.2, 2.819, 0, 4.6, -1.585, 0, 5.033, 0.899, 0, 5.467, -0.516, 0, 5.9, 0.31, 0, 6.367, -0.186, 0, 7.133, 3.4, 0, 7.4, -17.54, 0, 7.733, 15.917, 0, 8.133, -8.482, 0, 8.533, 6.704, 0, 8.967, -22.11, 1, 9.256, -22.11, 9.544, -18.019, 9.833, -9.665, 1, 9.844, -9.344, 9.856, -9.25, 9.867, -8.482, 1, 10, 0.728, 10.134, 6.704, 10.267, 6.704, 0, 10.7, -22.11, 0, 11.067, 20.926, 0, 12.133, -3.889, 0, 12.567, 1.482, 0, 12.967, -1.366, 0, 13.433, 0.947, 0, 13.833, -0.613, 0, 14.267, 0.459, 0, 14.533, -0.006, 0, 14.667, 2.449, 0, 14.933, -18.272, 0, 15.267, 16.609, 0, 15.667, -9.226, 0, 16.067, 6.479, 0, 16.5, -2.505, 0, 16.9, 2.576, 0, 17.367, -0.825, 0, 17.767, 3.077, 0, 18.033, -18.518, 0, 18.367, 16.744, 0, 18.767, -8.716, 0, 19.167, 6.282, 0, 19.6, -2.622, 0, 20, 1.971, 1, 20.033, 1.971, 20.067, 0.386, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHairSide3", "Segments": [0, 0, 2, 0.167, 0, 0, 0.5, 0.602, 0, 0.833, -1.831, 0, 1.233, 7.928, 0, 1.533, -18.432, 0, 1.867, 19.845, 0, 2.267, -14.413, 0, 2.667, 10.541, 0, 3.067, -6.928, 0, 3.5, 4.224, 0, 3.933, -3.949, 0, 4.3, 3.496, 0, 4.667, -2.256, 0, 5.067, 1.18, 0, 5.5, -0.589, 0, 5.933, 0.341, 0, 6.333, -0.182, 0, 7.267, 6.458, 0, 7.533, -15.972, 0, 7.867, 16.731, 0, 8.2, -11.779, 1, 8.333, -11.779, 8.467, 4.878, 8.6, 7.859, 1, 8.933, 15.311, 9.267, 16.731, 9.6, 16.731, 1, 9.678, 16.731, 9.755, 7.138, 9.833, -5.593, 1, 9.866, -11.049, 9.9, -11.779, 9.933, -11.779, 0, 10.333, 7.859, 0, 10.833, -18.607, 1, 11.289, -18.607, 11.744, -15.048, 12.2, -4.851, 1, 12.333, -1.866, 12.467, 2.738, 12.6, 2.738, 0, 13, -1.428, 0, 13.5, 0.806, 0, 13.9, -0.692, 0, 14.3, 0.474, 0, 14.567, -0.026, 0, 14.8, 6.123, 0, 15.067, -16.06, 0, 15.367, 17.34, 0, 15.733, -12.492, 0, 16.133, 7.361, 0, 16.533, -3.981, 0, 16.967, 2.24, 0, 17.4, -1.269, 0, 17.9, 6.645, 0, 18.167, -16.554, 0, 18.5, 17.245, 0, 18.833, -12.009, 0, 19.233, 7.179, 0, 19.633, -3.805, 0, 20.067, 2.153, 1, 20.078, 2.153, 20.089, 0.076, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHairSide4", "Segments": [0, -0.001, 2, 0.167, -0.001, 0, 0.567, 0.634, 0, 0.933, -2.298, 0, 1.3, 9.083, 0, 1.6, -21.616, 0, 1.933, 26.205, 0, 2.333, -20.287, 0, 2.733, 15.593, 0, 3.133, -11.294, 0, 3.533, 7.415, 0, 4, -5.57, 0, 4.367, 5.425, 0, 4.733, -4.165, 0, 5.1, 2.484, 0, 5.5, -1.205, 0, 5.933, 0.551, 0, 6.4, -0.31, 0, 6.733, -0.007, 0, 6.967, -0.056, 0, 7.333, 6.662, 0, 7.6, -18.259, 0, 7.933, 23.055, 0, 8.3, -18.656, 1, 8.422, -18.656, 8.545, 10.386, 8.667, 13.362, 1, 9, 21.476, 9.334, 23.055, 9.667, 23.055, 1, 9.722, 23.055, 9.778, 17.616, 9.833, 5.002, 1, 9.9, -10.134, 9.966, -18.656, 10.033, -18.656, 0, 10.4, 13.362, 0, 10.9, -20.107, 0, 11.9, 12.987, 0, 12.3, -8.913, 0, 12.667, 5.509, 0, 13.033, -2.998, 0, 13.467, 1.322, 0, 13.967, -0.954, 0, 14.367, 0.792, 0, 14.6, -0.021, 0, 14.867, 6.207, 0, 15.133, -18.314, 0, 15.467, 23.712, 0, 15.833, -19.343, 0, 16.2, 13.053, 0, 16.6, -7.635, 0, 17, 4.114, 0, 17.433, -2.159, 0, 17.967, 7.022, 0, 18.233, -19.22, 0, 18.567, 23.771, 0, 18.933, -18.892, 0, 19.3, 12.732, 0, 19.7, -7.392, 0, 20.1, -0.001]}, {"Target": "Parameter", "Id": "ParamHairSide5", "Segments": [0, -0.001, 2, 0.167, -0.001, 0, 0.667, 0.67, 0, 1, -2.82, 0, 1.367, 10.504, 0, 1.667, -24.218, 0, 2, 30, 0, 2.4, -26.263, 0, 2.833, 21.401, 0, 3.233, -16.872, 0, 3.6, 12.147, 0, 4.033, -8.573, 0, 4.467, 8.037, 0, 4.833, -7.012, 0, 5.2, 4.857, 0, 5.567, -2.695, 0, 5.933, 1.175, 0, 6.4, -0.472, 0, 6.8, 0.1, 0, 7.067, -0.088, 0, 7.4, 6.74, 0, 7.7, -20.003, 0, 8.033, 28.475, 0, 8.4, -25.583, 1, 8.522, -25.583, 8.645, 17.149, 8.767, 19.829, 1, 9.1, 27.138, 9.434, 28.475, 9.767, 28.475, 1, 9.789, 28.475, 9.811, 29.595, 9.833, 23.772, 1, 9.933, -2.429, 10.033, -25.583, 10.133, -25.583, 0, 10.5, 19.829, 0, 10.933, -22.228, 0, 12, 19.976, 0, 12.367, -14.899, 0, 12.733, 10.207, 0, 13.1, -6.224, 0, 13.467, 3.015, 0, 14, -1.148, 0, 14.467, 1.161, 0, 14.7, -0.037, 0, 14.967, 6.095, 0, 15.2, -19.778, 0, 15.533, 29.116, 0, 15.933, -25.944, 0, 16.3, 19.982, 0, 16.667, -13.441, 0, 17.033, 7.953, 0, 17.433, -4.104, 0, 18.033, 7.107, 0, 18.3, -21.268, 0, 18.633, 29.251, 0, 19.033, -25.726, 0, 19.4, 19.677, 0, 19.767, -13.086, 1, 19.878, -13.086, 19.989, -9.342, 20.1, -0.001]}, {"Target": "Parameter", "Id": "ParamHairBack1", "Segments": [0, 0, 2, 0.167, 0, 1, 0.178, 0, 0.189, -0.036, 0.2, 0.011, 1, 0.3, 0.434, 0.4, 1.744, 0.5, 1.744, 0, 0.933, -2.338, 0, 1, -2.111, 0, 1.233, -8.684, 0, 1.567, 13.109, 0, 2.033, -9.78, 0, 2.433, 5.673, 0, 2.867, -3.363, 0, 3.3, 1.991, 0, 3.933, -1.927, 0, 4.367, 1.148, 0, 4.8, -0.681, 0, 5.2, 0.4, 0, 5.633, -0.241, 0, 6.067, 0.141, 0, 6.533, -0.533, 0, 7, 0.024, 0, 7.233, -8.642, 0, 7.567, 11.207, 0, 8.033, -5.462, 0, 8.433, 2.146, 0, 8.833, -10.866, 1, 9.144, -10.866, 9.456, -9.154, 9.767, -5.462, 1, 9.789, -5.199, 9.811, -4.949, 9.833, -4.237, 1, 9.944, -0.68, 10.056, 2.146, 10.167, 2.146, 0, 10.567, -10.866, 0, 10.933, 12.954, 0, 12.033, -2.049, 0, 12.433, 0.986, 0, 12.833, -0.594, 0, 13.267, 0.664, 0, 13.7, -0.369, 0, 14.133, 0.258, 0, 14.767, -8.972, 0, 15.1, 10.33, 0, 15.567, -5.801, 0, 16, 3.692, 0, 16.433, -1.968, 0, 16.833, 1.231, 0, 17.267, -0.847, 0, 17.633, 0.147, 0, 17.867, -8.98, 0, 18.2, 9.972, 0, 18.633, -5.325, 0, 19.067, 3.21, 0, 19.467, -1.963, 0, 19.9, 0.982, 1, 19.967, 0.982, 20.033, 0.574, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHairBack2", "Segments": [0, 0, 2, 0.167, 0, 0, 0.2, 0.028, 0, 0.333, -0.352, 0, 0.7, 3.686, 0, 1.033, -2.569, 0, 1.133, -0.657, 0, 1.4, -17.654, 0, 1.733, 20.575, 0, 2.167, -14.123, 0, 2.6, 9.17, 0, 3, -5.581, 0, 3.4, 3.256, 0, 3.667, 0.726, 0, 3.833, 1.856, 0, 4.133, -2.861, 0, 4.5, 2.093, 0, 4.9, -1.191, 0, 5.333, 0.68, 0, 5.767, -0.392, 0, 6.233, 0.271, 0, 6.7, -1.002, 0, 7.133, 2.113, 0, 7.4, -18.311, 0, 7.733, 17.956, 0, 8.133, -7.481, 0, 8.533, 5.094, 1, 8.966, 5.094, 9.4, -1.262, 9.833, -7.458, 1, 9.844, -7.617, 9.856, -7.481, 9.867, -7.481, 0, 10.267, 5.094, 0, 10.733, -21.297, 0, 11.067, 20.361, 0, 12.133, -3.889, 0, 12.567, 1.482, 0, 12.967, -1.366, 0, 13.433, 0.947, 0, 13.833, -0.613, 0, 14.267, 0.459, 0, 14.533, -0.006, 0, 14.667, 2.449, 0, 14.933, -18.272, 0, 15.267, 16.837, 0, 15.7, -8.455, 0, 16.133, 6.391, 0, 16.533, -2.587, 0, 16.967, 2.64, 0, 17.4, -0.919, 0, 17.767, 3.063, 0, 18.033, -18.557, 0, 18.367, 16.498, 0, 18.767, -8.902, 0, 19.167, 6.049, 0, 19.6, -2.756, 0, 20, 1.922, 1, 20.033, 1.922, 20.067, 0.355, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHairBack3", "Segments": [0, 0, 2, 0.167, 0, 0, 0.2, 0.03, 0, 0.5, -1.212, 0, 0.833, 2.966, 0, 1.1, -1.084, 0, 1.3, 3.885, 0, 1.533, -14.604, 0, 1.867, 18.435, 0, 2.267, -13.652, 0, 2.667, 9.923, 0, 3.067, -6.502, 0, 3.467, 3.848, 0, 3.767, -1.868, 0, 4, 1.851, 0, 4.3, -2.877, 0, 4.633, 2.672, 0, 4.967, -1.756, 0, 5.367, 0.916, 0, 5.8, -0.449, 0, 6.367, 0.33, 0, 6.8, -0.438, 0, 7.267, 6.159, 0, 7.533, -16.199, 0, 7.867, 17.027, 0, 8.233, -11.176, 1, 8.355, -11.176, 8.478, 3.693, 8.6, 6.774, 1, 8.933, 15.176, 9.267, 17.027, 9.6, 17.027, 1, 9.678, 17.027, 9.755, 9, 9.833, -2.714, 1, 9.878, -9.408, 9.922, -11.176, 9.967, -11.176, 0, 10.333, 6.774, 0, 10.833, -18.293, 1, 11.289, -18.293, 11.744, -14.843, 12.2, -4.851, 1, 12.333, -1.926, 12.467, 2.738, 12.6, 2.738, 0, 13, -1.428, 0, 13.5, 0.806, 0, 13.9, -0.692, 0, 14.3, 0.474, 0, 14.567, -0.026, 0, 14.8, 6.123, 0, 15.067, -16.043, 0, 15.4, 16.746, 0, 15.767, -10.863, 0, 16.167, 6.36, 0, 16.6, -3.796, 0, 17, 2.344, 0, 17.433, -1.36, 0, 17.9, 6.875, 0, 18.167, -16.601, 0, 18.5, 17.247, 0, 18.833, -11.982, 0, 19.233, 7.154, 0, 19.633, -3.804, 0, 20.067, 2.13, 1, 20.078, 2.13, 20.089, 0.072, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHairBack4", "Segments": [0, -0.001, 2, 0.167, -0.001, 0, 0.233, 0.05, 0, 0.6, -1.339, 0, 0.933, 3.865, 0, 1.2, -1.456, 0, 1.367, 2.715, 0, 1.6, -15.285, 0, 1.933, 23.999, 0, 2.333, -19.505, 0, 2.733, 14.963, 0, 3.133, -10.717, 0, 3.533, 6.812, 0, 3.867, -3.941, 0, 4.133, 2.972, 0, 4.4, -4.084, 0, 4.733, 4.465, 0, 5.067, -3.476, 0, 5.433, 2.051, 0, 5.8, -0.976, 0, 6.267, 0.425, 0, 6.867, -0.579, 0, 7.333, 6.482, 0, 7.6, -18.636, 0, 7.933, 23.456, 0, 8.3, -17.672, 1, 8.422, -17.672, 8.545, 8.561, 8.667, 12.037, 1, 9, 21.519, 9.334, 23.456, 9.667, 23.456, 1, 9.722, 23.456, 9.778, 18.093, 9.833, 5.656, 1, 9.9, -9.269, 9.966, -17.672, 10.033, -17.672, 0, 10.4, 12.037, 0, 10.9, -19.881, 0, 11.9, 12.988, 0, 12.3, -8.913, 0, 12.667, 5.509, 0, 13.033, -2.998, 0, 13.467, 1.322, 0, 13.967, -0.954, 0, 14.367, 0.792, 0, 14.6, -0.021, 0, 14.867, 6.207, 0, 15.133, -18.271, 0, 15.467, 23.067, 0, 15.833, -17.213, 0, 16.233, 11.016, 0, 16.633, -6.612, 0, 17.067, 4.011, 0, 17.467, -2.355, 0, 17.967, 7.48, 0, 18.233, -19.364, 0, 18.567, 23.771, 0, 18.933, -18.889, 0, 19.3, 12.705, 0, 19.7, -7.392, 0, 20.1, -0.001]}, {"Target": "Parameter", "Id": "ParamHairBack5", "Segments": [0, -0.001, 2, 0.167, -0.001, 0, 0.3, 0.09, 0, 0.667, -1.495, 0, 1.033, 4.863, 0, 1.3, -2.573, 0, 1.467, 1.146, 0, 1.667, -14.746, 0, 2, 28.32, 0, 2.4, -25.385, 0, 2.8, 20.625, 0, 3.2, -16.124, 0, 3.6, 11.418, 0, 3.933, -7.432, 0, 4.233, 5.373, 0, 4.533, -6.107, 0, 4.833, 7.091, 0, 5.167, -6.282, 0, 5.5, 4.356, 0, 5.867, -2.379, 0, 6.233, 1.008, 0, 6.967, -0.685, 0, 7.4, 6.746, 0, 7.667, -20.454, 0, 8, 28.983, 0, 8.4, -24.519, 1, 8.522, -24.519, 8.645, 15.079, 8.767, 18.452, 1, 9.089, 27.343, 9.411, 28.983, 9.733, 28.983, 1, 9.766, 28.983, 9.8, 28.936, 9.833, 20.623, 1, 9.933, -4.317, 10.033, -24.519, 10.133, -24.519, 0, 10.5, 18.452, 0, 10.933, -21.356, 0, 12, 19.977, 0, 12.367, -14.899, 0, 12.733, 10.207, 0, 13.1, -6.224, 0, 13.467, 3.015, 0, 14, -1.148, 0, 14.467, 1.161, 0, 14.7, -0.037, 0, 14.967, 6.095, 0, 15.2, -19.76, 0, 15.533, 28.405, 0, 15.933, -24.182, 0, 16.333, 17.177, 0, 16.7, -11.289, 0, 17.1, 7.03, 0, 17.533, -4.13, 0, 18.033, 8.006, 0, 18.3, -21.653, 0, 18.633, 29.246, 0, 19.033, -25.783, 0, 19.4, 19.664, 0, 19.767, -13.095, 1, 19.878, -13.095, 19.989, -9.349, 20.1, -0.001]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_L", "Segments": [0, 0, 2, 0.167, 0, 2, 0.2, 0, 0, 0.467, 7.536, 0, 0.833, -6.262, 0, 1.133, -0.319, 0, 1.433, -2.069, 0, 2, 1.158, 0, 2.3, -0.403, 0, 2.633, 0.143, 0, 2.933, -0.051, 0, 3.233, 0.018, 0, 3.4, 0.003, 0, 3.633, 7.788, 0, 3.9, -6.532, 0, 4.2, 2.267, 0, 4.533, -0.802, 0, 4.833, 0.284, 0, 5.133, -0.1, 0, 5.433, 0.035, 0, 5.767, -0.012, 0, 6.067, 0.004, 0, 7.033, -2.825, 0, 7.667, 4.288, 0, 8.367, -2.512, 0, 8.667, 0.725, 1, 8.767, 0.725, 8.867, -0.315, 8.967, -0.579, 1, 9.256, -1.343, 9.544, -2.035, 9.833, -2.428, 1, 9.922, -2.549, 10.011, -2.512, 10.1, -2.512, 0, 10.4, 0.725, 0, 10.7, -0.579, 1, 10.789, -0.579, 10.878, -0.32, 10.967, -0.312, 1, 12.311, -0.202, 13.656, -0.111, 15, 0, 1, 15.089, 0.007, 15.178, 3.695, 15.267, 3.695, 0, 15.667, -2.651, 0, 15.967, 0.981, 0, 16.267, -0.317, 0, 16.567, 0.127, 0, 16.9, -0.038, 0, 17.2, 0.014, 0, 17.5, -0.008, 2, 17.533, -0.008, 0, 17.9, -0.361, 0, 18.067, -0.345, 0, 18.1, -0.347, 0, 18.133, -0.346, 0, 18.433, -0.414, 0, 18.467, -0.412, 0, 18.5, -0.418, 0, 18.533, -0.414, 0, 18.567, -0.417, 0, 18.6, -0.411, 0, 18.633, -0.413, 0, 18.667, -0.406, 0, 18.7, -0.408, 0, 18.733, -0.4, 0, 18.8, -0.402, 0, 18.867, -0.385, 2, 18.9, -0.385, 1, 19.3, -0.385, 19.7, -0.269, 20.1, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "Segments": [0, 0, 2, 0.167, 0, 2, 0.2, 0, 0, 0.5, 2.364, 0, 1, -0.383, 0, 1.3, -0.14, 0, 1.667, -4.621, 0, 2.033, 3.648, 0, 2.333, -1.277, 0, 2.633, 0.446, 0, 2.967, -0.145, 0, 3.233, 0.122, 0, 3.4, -0.049, 0, 3.633, 2.355, 0, 3.9, -1.806, 0, 4.233, 0.69, 0, 4.533, -0.198, 0, 4.833, 0.105, 0, 5.133, -0.014, 0, 5.433, 0.018, 0, 5.833, -0.002, 2, 5.967, -0.002, 0, 6.433, -1.094, 0, 7.2, 1.943, 0, 7.567, -2.008, 0, 7.9, -0.445, 0, 8.1, -0.634, 0, 8.4, 0.093, 1, 8.489, 0.093, 8.578, -0.161, 8.667, -0.19, 1, 8.989, -0.295, 9.311, -0.345, 9.633, -0.445, 1, 9.7, -0.465, 9.766, -0.634, 9.833, -0.634, 0, 10.133, 0.093, 0, 10.4, -0.19, 0, 10.767, 0.019, 1, 10.845, 0.019, 10.922, 0.06, 11, 0.002, 1, 11.489, -0.364, 11.978, -1.596, 12.467, -1.596, 0, 13.267, 0.897, 0, 13.567, -0.199, 0, 13.9, 0.285, 0, 14.133, 0.214, 0, 15.267, 2.707, 0, 15.667, -1.686, 0, 15.967, 0.862, 0, 16.267, -0.056, 0, 16.567, 0.259, 0, 16.9, 0.155, 0, 17.2, 0.21, 2, 17.233, 0.21, 0, 17.3, 0.213, 0, 17.333, 0.211, 0, 17.4, 0.214, 0, 17.433, 0.213, 0, 17.567, 0.22, 0, 19.067, -1.16, 1, 19.411, -1.16, 19.756, -0.816, 20.1, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_X", "Segments": [0, 0, 2, 0.167, 0, 2, 0.2, 0, 0, 0.367, 3.139, 0, 0.667, -5.307, 0, 0.967, 5.688, 0, 1.267, -3.544, 0, 1.6, 2.038, 0, 1.9, -0.647, 0, 2.033, -0.283, 0, 2.167, -0.56, 0, 2.433, 0.61, 0, 2.767, -0.362, 0, 3.067, 0.181, 0, 3.367, -0.083, 0, 3.567, 3.538, 0, 3.833, -8.033, 0, 4.067, 8.129, 0, 4.367, -5.068, 0, 4.667, 2.597, 0, 4.967, -1.207, 0, 5.267, 0.527, 0, 5.567, -0.221, 0, 5.9, 0.09, 0, 6.333, -0.374, 0, 6.6, 0.17, 0, 6.9, -0.241, 0, 6.933, -0.223, 0, 6.967, -0.244, 0, 7.267, 1.23, 0, 7.767, -1.019, 0, 8.1, 0.02, 0, 8.3, -0.75, 0, 8.533, 1.914, 0, 8.8, -1.552, 1, 9.033, -1.552, 9.267, -1.489, 9.5, -1.019, 1, 9.611, -0.796, 9.722, 0.02, 9.833, 0.02, 0, 10.033, -0.75, 0, 10.267, 1.914, 0, 10.533, -1.552, 0, 10.833, 0.841, 0, 15, 0, 0, 15.167, 1.5, 0, 15.467, -2.452, 0, 15.8, 2.905, 0, 16.1, -2, 0, 16.4, 1.052, 0, 16.7, -0.492, 0, 17, 0.214, 0, 17.333, -0.091, 0, 17.567, 0.019, 0, 17.733, -0.091, 0, 18, 0.08, 0, 18.3, -0.051, 0, 18.6, 0.03, 0, 18.633, 0.026, 0, 18.667, 0.028, 0, 18.8, -0.001, 0, 18.867, 0.003, 0, 18.933, -0.007, 0, 18.967, 0, 0, 19, -0.002, 0, 19.033, 0.006, 0, 19.067, 0.005, 0, 19.1, 0.012, 0, 19.133, 0.01, 0, 19.167, 0.016, 0, 19.2, 0.013, 0, 19.233, 0.017, 0, 19.267, 0.013, 0, 19.3, 0.016, 0, 19.333, 0.012, 0, 19.367, 0.015, 0, 19.4, 0.011, 0, 19.433, 0.014, 0, 19.467, 0.01, 0, 19.5, 0.014, 0, 19.533, 0.011, 0, 19.567, 0.015, 0, 19.6, 0.013, 0, 19.633, 0.016, 0, 19.667, 0.015, 0, 19.7, 0.018, 0, 19.733, 0.016, 0, 19.767, 0.019, 0, 19.8, 0.017, 0, 19.833, 0.019, 0, 19.867, 0.018, 0, 19.9, 0.02, 0, 19.933, 0.019, 0, 19.967, 0.02, 0, 20, 0.019, 0, 20.033, 0.02, 2, 20.067, 0.02, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_X", "Segments": [0, 0, 2, 0.167, 0, 2, 0.2, 0, 0, 0.367, -0.856, 0, 0.667, 1.119, 0, 1.033, -0.518, 0, 1.367, 0.229, 0, 1.4, 0.219, 0, 1.567, 1.842, 0, 1.867, -3.192, 0, 2.167, 3.978, 0, 2.467, -2.683, 0, 2.767, 1.399, 0, 3.1, -0.711, 0, 3.4, 0.453, 0, 3.567, -1.038, 0, 3.833, 2.357, 0, 4.067, -2.318, 0, 4.367, 1.457, 0, 4.667, -0.746, 0, 4.967, 0.347, 0, 5.267, -0.151, 0, 5.567, 0.064, 0, 5.9, -0.026, 0, 6.3, 0.412, 0, 6.6, -0.521, 0, 6.867, 0.122, 0, 7.133, -0.615, 0, 7.433, 1.631, 0, 7.7, -1.681, 0, 8, 0.917, 0, 8.3, -0.604, 0, 8.567, 0.425, 0, 8.867, -0.247, 0, 9.733, 0.917, 1, 9.766, 0.917, 9.8, 0.85, 9.833, 0.522, 1, 9.9, -0.135, 9.966, -0.604, 10.033, -0.604, 0, 10.3, 0.425, 0, 10.6, -0.247, 1, 10.7, -0.247, 10.8, 0.113, 10.9, 0.119, 1, 11.311, 0.141, 11.722, 0.146, 12.133, 0.146, 0, 12.367, -0.008, 0, 12.467, -0.001, 0, 12.7, -0.218, 0, 12.967, -0.015, 0, 13.233, -0.226, 0, 13.433, 0.642, 0, 13.733, -0.538, 0, 14.033, 0.288, 0, 14.333, -0.153, 0, 14.633, 0.053, 0, 14.933, -0.041, 0, 14.967, -0.032, 0, 15.167, -0.863, 0, 15.467, 1.558, 0, 15.8, -1.972, 0, 16.1, 1.377, 0, 16.4, -0.731, 0, 16.7, 0.342, 0, 17, -0.149, 0, 17.333, 0.061, 0, 17.567, -0.018, 0, 17.767, 0.03, 0, 18.067, -0.016, 0, 18.333, 0.021, 0, 18.633, 0, 0, 18.933, 0.361, 0, 19.2, -0.357, 0, 19.5, 0.157, 0, 19.8, -0.174, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ZD_BustLMotionY_L", "Segments": [0, 0, 2, 0.167, 0, 1, 0.178, 0, 0.189, -0.011, 0.2, 0.002, 1, 0.3, 0.123, 0.4, 3.883, 0.5, 3.883, 0, 0.9, -5.914, 0, 1.367, 2.602, 0, 1.733, -1.278, 0, 2.1, 1.586, 0, 2.533, -0.945, 0, 2.967, 0.559, 0, 3.367, -0.331, 0, 3.667, 3.901, 0, 4, -4.664, 0, 4.433, 2.762, 0, 4.867, -1.628, 0, 5.267, 0.971, 0, 5.7, -0.577, 0, 6.133, 0.341, 0, 6.6, -0.989, 0, 6.933, -0.646, 0, 7.033, -0.693, 0, 7.6, 3.006, 0, 8.3, -2.145, 0, 8.733, 1.185, 1, 9.1, 1.185, 9.466, -0.281, 9.833, -1.933, 1, 9.9, -2.233, 9.966, -2.145, 10.033, -2.145, 0, 10.467, 1.185, 0, 10.9, -0.873, 0, 12.1, 0, 2, 12.133, 0, 2, 12.533, 0, 2, 12.567, 0, 2, 12.9, 0, 2, 12.933, 0, 2, 12.967, 0, 2, 13, 0, 2, 13.033, 0, 2, 13.333, 0, 2, 13.367, 0, 2, 13.4, 0, 2, 13.433, 0, 2, 13.467, 0, 2, 13.5, 0, 2, 13.733, 0, 2, 13.767, 0, 2, 13.8, 0, 2, 13.9, 0, 2, 13.933, 0, 2, 13.967, 0, 2, 14.1, 0, 2, 14.133, 0, 2, 14.2, 0, 2, 14.333, 0, 2, 14.367, 0, 2, 14.433, 0, 2, 14.467, 0, 2, 14.567, 0, 2, 14.6, 0, 2, 14.7, 0, 2, 14.733, 0, 2, 14.833, 0, 2, 14.867, 0, 2, 15, 0, 0, 15.3, 1.91, 0, 15.733, -2.65, 0, 16.133, 1.575, 0, 16.567, -0.942, 0, 17, 0.55, 0, 17.4, -0.332, 0, 17.767, 0.044, 0, 18.167, -0.208, 0, 18.667, -0.015, 0, 19, -0.075, 0, 19.633, 0.048, 1, 19.644, 0.048, 19.656, 0.049, 19.667, 0.048, 1, 19.811, 0.033, 19.956, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ZD_BustLMotionX_L", "Segments": [0, 0, 2, 0.167, 0, 2, 0.2, 0, 0, 0.5, 2.364, 0, 1, -0.383, 0, 1.3, -0.14, 0, 1.667, -4.621, 0, 2.033, 3.648, 0, 2.333, -1.277, 0, 2.633, 0.446, 0, 2.967, -0.145, 0, 3.233, 0.122, 0, 3.4, -0.049, 0, 3.633, 2.355, 0, 3.9, -1.806, 0, 4.233, 0.69, 0, 4.533, -0.198, 0, 4.833, 0.105, 0, 5.133, -0.014, 0, 5.433, 0.018, 0, 5.833, -0.002, 2, 5.967, -0.002, 0, 6.433, -1.094, 0, 7.2, 1.943, 0, 7.567, -2.008, 0, 7.9, -0.445, 0, 8.1, -0.634, 0, 8.4, 0.093, 1, 8.489, 0.093, 8.578, -0.161, 8.667, -0.19, 1, 8.989, -0.295, 9.311, -0.345, 9.633, -0.445, 1, 9.7, -0.465, 9.766, -0.634, 9.833, -0.634, 0, 10.133, 0.093, 0, 10.4, -0.19, 0, 10.767, 0.019, 1, 10.845, 0.019, 10.922, 0.06, 11, 0.002, 1, 11.489, -0.364, 11.978, -1.596, 12.467, -1.596, 0, 13.267, 0.897, 0, 13.567, -0.199, 0, 13.9, 0.285, 0, 14.133, 0.214, 0, 15.267, 2.707, 0, 15.667, -1.686, 0, 15.967, 0.862, 0, 16.267, -0.056, 0, 16.567, 0.259, 0, 16.9, 0.155, 0, 17.2, 0.21, 2, 17.233, 0.21, 0, 17.3, 0.213, 0, 17.333, 0.211, 0, 17.4, 0.214, 0, 17.433, 0.213, 0, 17.567, 0.22, 0, 19.067, -1.16, 1, 19.411, -1.16, 19.756, -0.816, 20.1, 0]}, {"Target": "Parameter", "Id": "ZD_BustRMotionY_L", "Segments": [0, 0, 2, 0.167, 0, 1, 0.178, 0, 0.189, -0.011, 0.2, 0.002, 1, 0.3, 0.123, 0.4, 3.883, 0.5, 3.883, 0, 0.9, -5.914, 0, 1.367, 2.602, 0, 1.733, -1.278, 0, 2.1, 1.586, 0, 2.533, -0.945, 0, 2.967, 0.559, 0, 3.367, -0.331, 0, 3.667, 3.901, 0, 4, -4.664, 0, 4.433, 2.762, 0, 4.867, -1.628, 0, 5.267, 0.971, 0, 5.7, -0.577, 0, 6.133, 0.341, 0, 6.6, -0.989, 0, 6.933, -0.646, 0, 7.033, -0.693, 0, 7.6, 3.006, 0, 8.3, -2.145, 0, 8.733, 1.185, 1, 9.1, 1.185, 9.466, -0.281, 9.833, -1.933, 1, 9.9, -2.233, 9.966, -2.145, 10.033, -2.145, 0, 10.467, 1.185, 0, 10.9, -0.873, 0, 12.1, 0, 2, 12.133, 0, 2, 12.533, 0, 2, 12.567, 0, 2, 12.9, 0, 2, 12.933, 0, 2, 12.967, 0, 2, 13, 0, 2, 13.033, 0, 2, 13.333, 0, 2, 13.367, 0, 2, 13.4, 0, 2, 13.433, 0, 2, 13.467, 0, 2, 13.5, 0, 2, 13.733, 0, 2, 13.767, 0, 2, 13.8, 0, 2, 13.9, 0, 2, 13.933, 0, 2, 13.967, 0, 2, 14.1, 0, 2, 14.133, 0, 2, 14.2, 0, 2, 14.333, 0, 2, 14.367, 0, 2, 14.433, 0, 2, 14.467, 0, 2, 14.567, 0, 2, 14.6, 0, 2, 14.7, 0, 2, 14.733, 0, 2, 14.833, 0, 2, 14.867, 0, 2, 15, 0, 0, 15.3, 1.91, 0, 15.733, -2.65, 0, 16.133, 1.575, 0, 16.567, -0.942, 0, 17, 0.55, 0, 17.4, -0.332, 0, 17.767, 0.044, 0, 18.167, -0.208, 0, 18.667, -0.015, 0, 19, -0.075, 0, 19.633, 0.048, 1, 19.644, 0.048, 19.656, 0.049, 19.667, 0.048, 1, 19.811, 0.033, 19.956, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ZD_BustRMotionX_L", "Segments": [0, 0, 2, 0.167, 0, 2, 0.2, 0, 0, 0.5, 2.364, 0, 1, -0.383, 0, 1.3, -0.14, 0, 1.667, -4.621, 0, 2.033, 3.648, 0, 2.333, -1.277, 0, 2.633, 0.446, 0, 2.967, -0.145, 0, 3.233, 0.122, 0, 3.4, -0.049, 0, 3.633, 2.355, 0, 3.9, -1.806, 0, 4.233, 0.69, 0, 4.533, -0.198, 0, 4.833, 0.105, 0, 5.133, -0.014, 0, 5.433, 0.018, 0, 5.833, -0.002, 2, 5.967, -0.002, 0, 6.433, -1.094, 0, 7.2, 1.943, 0, 7.567, -2.008, 0, 7.9, -0.445, 0, 8.1, -0.634, 0, 8.4, 0.093, 1, 8.489, 0.093, 8.578, -0.161, 8.667, -0.19, 1, 8.989, -0.295, 9.311, -0.345, 9.633, -0.445, 1, 9.7, -0.465, 9.766, -0.634, 9.833, -0.634, 0, 10.133, 0.093, 0, 10.4, -0.19, 0, 10.767, 0.019, 1, 10.845, 0.019, 10.922, 0.06, 11, 0.002, 1, 11.489, -0.364, 11.978, -1.596, 12.467, -1.596, 0, 13.267, 0.897, 0, 13.567, -0.199, 0, 13.9, 0.285, 0, 14.133, 0.214, 0, 15.267, 2.707, 0, 15.667, -1.686, 0, 15.967, 0.862, 0, 16.267, -0.056, 0, 16.567, 0.259, 0, 16.9, 0.155, 0, 17.2, 0.21, 2, 17.233, 0.21, 0, 17.3, 0.213, 0, 17.333, 0.211, 0, 17.4, 0.214, 0, 17.433, 0.213, 0, 17.567, 0.22, 0, 19.067, -1.16, 1, 19.411, -1.16, 19.756, -0.816, 20.1, 0]}, {"Target": "Parameter", "Id": "ZD_BustLMotionY_X", "Segments": [0, 0, 2, 0.167, 0, 0, 0.333, -0.843, 0, 0.7, 8.24, 0, 1.1, -9.332, 0, 1.467, 3.739, 0, 1.867, -3.459, 0, 2.267, 2.703, 0, 2.667, -1.664, 0, 3.067, 0.942, 0, 3.533, -1.55, 0, 3.833, 8.714, 0, 4.167, -8.157, 0, 4.567, 5.091, 0, 4.967, -2.82, 0, 5.4, 1.599, 0, 5.833, -0.945, 0, 6.267, 0.641, 0, 6.767, -1.779, 0, 7, -1.571, 0, 7.267, -2.448, 0, 7.833, 4.771, 0, 8.5, -2.284, 1, 8.633, -2.284, 8.767, 0.461, 8.9, 1.918, 1, 9.122, 4.347, 9.345, 4.771, 9.567, 4.771, 1, 9.656, 4.771, 9.744, 4.145, 9.833, 2.284, 1, 9.966, -0.508, 10.1, -2.284, 10.233, -2.284, 0, 10.633, 1.918, 0, 11.033, -1.596, 0, 12.267, 0, 2, 12.3, 0, 2, 12.7, 0, 2, 13.133, 0, 2, 13.4, 0, 2, 13.433, 0, 2, 13.467, 0, 2, 13.5, 0, 2, 13.567, 0, 2, 13.633, 0, 2, 13.7, 0, 2, 13.733, 0, 2, 14, 0, 2, 14.133, 0, 2, 14.167, 0, 2, 14.367, 0, 2, 14.4, 0, 2, 14.433, 0, 2, 14.467, 0, 2, 14.667, 0, 2, 14.7, 0, 2, 14.933, 0, 2, 15, 0, 0, 15.133, -0.403, 0, 15.5, 4.047, 0, 15.9, -4.161, 0, 16.267, 2.835, 0, 16.7, -1.597, 0, 17.1, 0.924, 0, 17.533, -0.544, 0, 17.9, 0.132, 0, 18.333, -0.479, 0, 18.733, -0.249, 0, 19.1, -0.382, 1, 19.433, -0.382, 19.767, -0.276, 20.1, 0]}, {"Target": "Parameter", "Id": "ZD_BustLMotionX_X", "Segments": [0, 0, 2, 0.167, 0, 2, 0.2, 0, 0, 0.367, -0.856, 0, 0.667, 1.119, 0, 1.033, -0.518, 0, 1.367, 0.229, 0, 1.4, 0.219, 0, 1.567, 1.842, 0, 1.867, -3.192, 0, 2.167, 3.978, 0, 2.467, -2.683, 0, 2.767, 1.399, 0, 3.1, -0.711, 0, 3.4, 0.453, 0, 3.567, -1.038, 0, 3.833, 2.357, 0, 4.067, -2.318, 0, 4.367, 1.457, 0, 4.667, -0.746, 0, 4.967, 0.347, 0, 5.267, -0.151, 0, 5.567, 0.064, 0, 5.9, -0.026, 0, 6.3, 0.412, 0, 6.6, -0.521, 0, 6.867, 0.122, 0, 7.133, -0.615, 0, 7.433, 1.631, 0, 7.7, -1.681, 0, 8, 0.917, 0, 8.3, -0.604, 0, 8.567, 0.425, 0, 8.867, -0.247, 0, 9.733, 0.917, 1, 9.766, 0.917, 9.8, 0.85, 9.833, 0.522, 1, 9.9, -0.135, 9.966, -0.604, 10.033, -0.604, 0, 10.3, 0.425, 0, 10.6, -0.247, 1, 10.7, -0.247, 10.8, 0.113, 10.9, 0.119, 1, 11.311, 0.141, 11.722, 0.146, 12.133, 0.146, 0, 12.367, -0.008, 0, 12.467, -0.001, 0, 12.7, -0.218, 0, 12.967, -0.015, 0, 13.233, -0.226, 0, 13.433, 0.642, 0, 13.733, -0.538, 0, 14.033, 0.288, 0, 14.333, -0.153, 0, 14.633, 0.053, 0, 14.933, -0.041, 0, 14.967, -0.032, 0, 15.167, -0.863, 0, 15.467, 1.558, 0, 15.8, -1.972, 0, 16.1, 1.377, 0, 16.4, -0.731, 0, 16.7, 0.342, 0, 17, -0.149, 0, 17.333, 0.061, 0, 17.567, -0.018, 0, 17.767, 0.03, 0, 18.067, -0.016, 0, 18.333, 0.021, 0, 18.633, 0, 0, 18.933, 0.361, 0, 19.2, -0.357, 0, 19.5, 0.157, 0, 19.8, -0.174, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ZD_BustRMotionY_X", "Segments": [0, 0, 2, 0.167, 0, 0, 0.333, -0.843, 0, 0.7, 8.24, 0, 1.1, -9.332, 0, 1.467, 3.739, 0, 1.867, -3.459, 0, 2.267, 2.703, 0, 2.667, -1.664, 0, 3.067, 0.942, 0, 3.533, -1.55, 0, 3.833, 8.714, 0, 4.167, -8.157, 0, 4.567, 5.091, 0, 4.967, -2.82, 0, 5.4, 1.599, 0, 5.833, -0.945, 0, 6.267, 0.641, 0, 6.767, -1.779, 0, 7, -1.571, 0, 7.267, -2.448, 0, 7.833, 4.771, 0, 8.5, -2.284, 1, 8.633, -2.284, 8.767, 0.461, 8.9, 1.918, 1, 9.122, 4.347, 9.345, 4.771, 9.567, 4.771, 1, 9.656, 4.771, 9.744, 4.145, 9.833, 2.284, 1, 9.966, -0.508, 10.1, -2.284, 10.233, -2.284, 0, 10.633, 1.918, 0, 11.033, -1.596, 0, 12.267, 0, 2, 12.3, 0, 2, 12.7, 0, 2, 13.133, 0, 2, 13.4, 0, 2, 13.433, 0, 2, 13.467, 0, 2, 13.5, 0, 2, 13.567, 0, 2, 13.633, 0, 2, 13.7, 0, 2, 13.733, 0, 2, 14, 0, 2, 14.133, 0, 2, 14.167, 0, 2, 14.367, 0, 2, 14.4, 0, 2, 14.433, 0, 2, 14.467, 0, 2, 14.667, 0, 2, 14.7, 0, 2, 14.933, 0, 2, 15, 0, 0, 15.133, -0.403, 0, 15.5, 4.047, 0, 15.9, -4.161, 0, 16.267, 2.835, 0, 16.7, -1.597, 0, 17.1, 0.924, 0, 17.533, -0.544, 0, 17.9, 0.132, 0, 18.333, -0.479, 0, 18.733, -0.249, 0, 19.1, -0.382, 1, 19.433, -0.382, 19.767, -0.276, 20.1, 0]}, {"Target": "Parameter", "Id": "ZD_BustRMotionX_X", "Segments": [0, 0, 2, 0.167, 0, 2, 0.2, 0, 0, 0.367, -0.856, 0, 0.667, 1.119, 0, 1.033, -0.518, 0, 1.367, 0.229, 0, 1.4, 0.219, 0, 1.567, 1.842, 0, 1.867, -3.192, 0, 2.167, 3.978, 0, 2.467, -2.683, 0, 2.767, 1.399, 0, 3.1, -0.711, 0, 3.4, 0.453, 0, 3.567, -1.038, 0, 3.833, 2.357, 0, 4.067, -2.318, 0, 4.367, 1.457, 0, 4.667, -0.746, 0, 4.967, 0.347, 0, 5.267, -0.151, 0, 5.567, 0.064, 0, 5.9, -0.026, 0, 6.3, 0.412, 0, 6.6, -0.521, 0, 6.867, 0.122, 0, 7.133, -0.615, 0, 7.433, 1.631, 0, 7.7, -1.681, 0, 8, 0.917, 0, 8.3, -0.604, 0, 8.567, 0.425, 0, 8.867, -0.247, 0, 9.733, 0.917, 1, 9.766, 0.917, 9.8, 0.85, 9.833, 0.522, 1, 9.9, -0.135, 9.966, -0.604, 10.033, -0.604, 0, 10.3, 0.425, 0, 10.6, -0.247, 1, 10.7, -0.247, 10.8, 0.113, 10.9, 0.119, 1, 11.311, 0.141, 11.722, 0.146, 12.133, 0.146, 0, 12.367, -0.008, 0, 12.467, -0.001, 0, 12.7, -0.218, 0, 12.967, -0.015, 0, 13.233, -0.226, 0, 13.433, 0.642, 0, 13.733, -0.538, 0, 14.033, 0.288, 0, 14.333, -0.153, 0, 14.633, 0.053, 0, 14.933, -0.041, 0, 14.967, -0.032, 0, 15.167, -0.863, 0, 15.467, 1.558, 0, 15.8, -1.972, 0, 16.1, 1.377, 0, 16.4, -0.731, 0, 16.7, 0.342, 0, 17, -0.149, 0, 17.333, 0.061, 0, 17.567, -0.018, 0, 17.767, 0.03, 0, 18.067, -0.016, 0, 18.333, 0.021, 0, 18.633, 0, 0, 18.933, 0.361, 0, 19.2, -0.357, 0, 19.5, 0.157, 0, 19.8, -0.174, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0.036, 2, 0.167, 0.036, 0, 0.2, 0.03, 0, 0.5, 3.444, 0, 0.933, -4.638, 0, 1.367, 1.871, 0, 1.733, -2.585, 0, 2.1, 3.543, 0, 2.533, -2.099, 0, 2.967, 1.238, 0, 3.367, -0.737, 0, 3.667, 3.628, 0, 4, -4.062, 0, 4.433, 2.412, 0, 4.867, -1.43, 0, 5.267, 0.84, 0, 5.7, -0.506, 0, 6.133, 0.296, 0, 6.533, -1.083, 0, 7.367, 0.998, 0, 8.333, -1.368, 0, 8.767, 0.816, 1, 9.122, 0.816, 9.478, -0.11, 9.833, -1.182, 1, 9.911, -1.417, 9.989, -1.368, 10.067, -1.368, 0, 10.5, 0.816, 0, 10.933, -0.547, 1, 11.255, -0.547, 11.578, -0.47, 11.9, -0.315, 1, 12.011, -0.262, 12.122, -0.236, 12.233, -0.236, 0, 12.467, -0.302, 0, 13.267, 0.538, 0, 13.667, -0.268, 0, 14.1, 0.239, 0, 14.5, -0.03, 0, 14.933, 0.165, 0, 15, 0.164, 0, 15.3, 2.152, 0, 15.7, -3.124, 0, 16.133, 1.87, 0, 16.567, -1.091, 0, 16.967, 0.669, 0, 17.4, -0.362, 0, 17.767, 0.119, 0, 18.2, -0.212, 0, 18.633, -0.008, 0, 19.1, -0.517, 0, 19.667, 0.151, 1, 19.722, 0.151, 19.778, 0.155, 19.833, 0.135, 1, 19.922, 0.103, 20.011, 0.036, 20.1, 0.036]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0.213, 2, 0.167, 0.213, 1, 0.178, 0.213, 0.189, 0.246, 0.2, 0.176, 1, 0.244, -0.102, 0.289, -0.63, 0.333, -0.63, 0, 0.7, 7.245, 0, 1.1, -6.884, 0, 1.5, 3.084, 0, 1.9, -6.162, 0, 2.267, 5.826, 0, 2.667, -3.735, 0, 3.067, 2.123, 0, 3.533, -2.084, 0, 3.833, 8.088, 0, 4.167, -7.132, 0, 4.567, 4.456, 0, 4.967, -2.421, 0, 5.4, 1.401, 0, 5.833, -0.824, 0, 6.267, 0.622, 0, 6.7, -2.065, 0, 7.067, -0.804, 0, 7.167, -0.939, 0, 7.567, 1.047, 0, 7.7, 0.939, 0, 8, 1.403, 0, 8.533, -1.981, 1, 8.655, -1.981, 8.778, 1.185, 8.9, 1.249, 1, 9.178, 1.395, 9.455, 1.403, 9.733, 1.403, 1, 9.766, 1.403, 9.8, 1.471, 9.833, 1.091, 1, 9.978, -0.556, 10.122, -1.981, 10.267, -1.981, 0, 10.633, 1.249, 0, 11.067, -1.061, 1, 11.4, -1.061, 11.734, -0.901, 12.067, -0.711, 1, 12.145, -0.667, 12.222, -0.674, 12.3, -0.674, 0, 12.7, -1.077, 0, 13.467, 0.612, 0, 13.833, -0.442, 0, 14.233, 0.459, 0, 14.633, 0.034, 0, 15, 0.39, 0, 15.133, -0.016, 0, 15.5, 4.748, 0, 15.9, -4.717, 0, 16.267, 3.439, 0, 16.667, -1.766, 0, 17.1, 1.209, 0, 17.533, -0.504, 0, 17.9, 0.356, 0, 18.333, -0.366, 0, 18.833, -0.099, 0, 19.3, -1.164, 0, 19.767, -0.346, 0, 19.933, -0.38, 0, 19.967, -0.371, 0, 20, -0.373, 1, 20.033, -0.373, 20.067, 0.095, 20.1, 0.213]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0.012, 2, 0.167, 0.012, 0, 0.2, 0.01, 0, 0.5, 1.148, 0, 0.933, -1.546, 0, 1.367, 0.624, 0, 1.733, -0.862, 0, 2.1, 1.181, 0, 2.533, -0.7, 0, 2.967, 0.413, 0, 3.367, -0.246, 0, 3.667, 1.209, 0, 4, -1.354, 0, 4.433, 0.804, 0, 4.867, -0.477, 0, 5.267, 0.28, 0, 5.7, -0.169, 0, 6.133, 0.099, 0, 6.533, -0.361, 0, 7.367, 0.333, 0, 8.333, -0.456, 0, 8.767, 0.272, 1, 9.122, 0.272, 9.478, -0.036, 9.833, -0.394, 1, 9.911, -0.472, 9.989, -0.456, 10.067, -0.456, 0, 10.5, 0.272, 0, 10.933, -0.182, 1, 11.255, -0.182, 11.578, -0.157, 11.9, -0.105, 1, 12.011, -0.087, 12.122, -0.079, 12.233, -0.079, 0, 12.467, -0.101, 0, 13.267, 0.179, 0, 13.667, -0.089, 0, 14.1, 0.08, 0, 14.5, -0.01, 0, 14.933, 0.055, 2, 15, 0.055, 0, 15.3, 0.717, 0, 15.7, -1.041, 0, 16.133, 0.623, 0, 16.567, -0.364, 0, 16.967, 0.223, 0, 17.4, -0.121, 0, 17.767, 0.04, 0, 18.2, -0.071, 0, 18.633, -0.003, 0, 19.1, -0.172, 0, 19.667, 0.05, 1, 19.722, 0.05, 19.778, 0.052, 19.833, 0.045, 1, 19.922, 0.034, 20.011, 0.012, 20.1, 0.012]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0.071, 2, 0.167, 0.071, 1, 0.178, 0.071, 0.189, 0.082, 0.2, 0.059, 1, 0.244, -0.035, 0.289, -0.21, 0.333, -0.21, 0, 0.7, 2.415, 0, 1.1, -2.295, 0, 1.5, 1.028, 0, 1.9, -2.054, 0, 2.267, 1.942, 0, 2.667, -1.245, 0, 3.067, 0.708, 0, 3.533, -0.695, 0, 3.833, 2.696, 0, 4.167, -2.377, 0, 4.567, 1.485, 0, 4.967, -0.807, 0, 5.4, 0.467, 0, 5.833, -0.275, 0, 6.267, 0.207, 0, 6.7, -0.688, 0, 7.067, -0.268, 0, 7.167, -0.313, 0, 7.567, 0.349, 0, 7.7, 0.313, 0, 8, 0.468, 0, 8.533, -0.66, 1, 8.655, -0.66, 8.778, 0.395, 8.9, 0.416, 1, 9.178, 0.465, 9.455, 0.468, 9.733, 0.468, 1, 9.766, 0.468, 9.8, 0.491, 9.833, 0.364, 1, 9.978, -0.188, 10.122, -0.66, 10.267, -0.66, 0, 10.633, 0.416, 0, 11.067, -0.354, 1, 11.4, -0.354, 11.734, -0.3, 12.067, -0.237, 1, 12.145, -0.222, 12.222, -0.225, 12.3, -0.225, 0, 12.7, -0.359, 0, 13.467, 0.204, 0, 13.833, -0.147, 0, 14.233, 0.153, 0, 14.633, 0.011, 0, 15, 0.13, 0, 15.133, -0.005, 0, 15.5, 1.583, 0, 15.9, -1.572, 0, 16.267, 1.146, 0, 16.667, -0.589, 0, 17.1, 0.403, 0, 17.533, -0.168, 0, 17.9, 0.119, 0, 18.333, -0.122, 0, 18.833, -0.033, 0, 19.3, -0.388, 0, 19.767, -0.115, 0, 19.933, -0.127, 0, 19.967, -0.124, 2, 20, -0.124, 1, 20.033, -0.124, 20.067, 0.032, 20.1, 0.071]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0.036, 2, 0.167, 0.036, 0, 0.2, 0.03, 0, 0.5, 3.444, 0, 0.933, -4.638, 0, 1.367, 1.871, 0, 1.733, -2.585, 0, 2.1, 3.543, 0, 2.533, -2.099, 0, 2.967, 1.238, 0, 3.367, -0.737, 0, 3.667, 3.628, 0, 4, -4.062, 0, 4.433, 2.412, 0, 4.867, -1.43, 0, 5.267, 0.84, 0, 5.7, -0.506, 0, 6.133, 0.296, 0, 6.533, -1.083, 0, 7.367, 0.998, 0, 8.333, -1.368, 0, 8.767, 0.816, 1, 9.122, 0.816, 9.478, -0.11, 9.833, -1.182, 1, 9.911, -1.417, 9.989, -1.368, 10.067, -1.368, 0, 10.5, 0.816, 0, 10.933, -0.547, 1, 11.255, -0.547, 11.578, -0.47, 11.9, -0.315, 1, 12.011, -0.262, 12.122, -0.236, 12.233, -0.236, 0, 12.467, -0.302, 0, 13.267, 0.538, 0, 13.667, -0.268, 0, 14.1, 0.239, 0, 14.5, -0.03, 0, 14.933, 0.165, 0, 15, 0.164, 0, 15.3, 2.152, 0, 15.7, -3.124, 0, 16.133, 1.87, 0, 16.567, -1.091, 0, 16.967, 0.669, 0, 17.4, -0.362, 0, 17.767, 0.119, 0, 18.2, -0.212, 0, 18.633, -0.008, 0, 19.1, -0.517, 0, 19.667, 0.151, 1, 19.722, 0.151, 19.778, 0.155, 19.833, 0.135, 1, 19.922, 0.103, 20.011, 0.036, 20.1, 0.036]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0.213, 2, 0.167, 0.213, 1, 0.178, 0.213, 0.189, 0.246, 0.2, 0.176, 1, 0.244, -0.102, 0.289, -0.63, 0.333, -0.63, 0, 0.7, 7.245, 0, 1.1, -6.884, 0, 1.5, 3.084, 0, 1.9, -6.162, 0, 2.267, 5.826, 0, 2.667, -3.735, 0, 3.067, 2.123, 0, 3.533, -2.084, 0, 3.833, 8.088, 0, 4.167, -7.132, 0, 4.567, 4.456, 0, 4.967, -2.421, 0, 5.4, 1.401, 0, 5.833, -0.824, 0, 6.267, 0.622, 0, 6.7, -2.065, 0, 7.067, -0.804, 0, 7.167, -0.939, 0, 7.567, 1.047, 0, 7.7, 0.939, 0, 8, 1.403, 0, 8.533, -1.981, 1, 8.655, -1.981, 8.778, 1.185, 8.9, 1.249, 1, 9.178, 1.395, 9.455, 1.403, 9.733, 1.403, 1, 9.766, 1.403, 9.8, 1.471, 9.833, 1.091, 1, 9.978, -0.556, 10.122, -1.981, 10.267, -1.981, 0, 10.633, 1.249, 0, 11.067, -1.061, 1, 11.4, -1.061, 11.734, -0.901, 12.067, -0.711, 1, 12.145, -0.667, 12.222, -0.674, 12.3, -0.674, 0, 12.7, -1.077, 0, 13.467, 0.612, 0, 13.833, -0.442, 0, 14.233, 0.459, 0, 14.633, 0.034, 0, 15, 0.39, 0, 15.133, -0.016, 0, 15.5, 4.748, 0, 15.9, -4.717, 0, 16.267, 3.439, 0, 16.667, -1.766, 0, 17.1, 1.209, 0, 17.533, -0.504, 0, 17.9, 0.356, 0, 18.333, -0.366, 0, 18.833, -0.099, 0, 19.3, -1.164, 0, 19.767, -0.346, 0, 19.933, -0.38, 0, 19.967, -0.371, 0, 20, -0.373, 1, 20.033, -0.373, 20.067, 0.095, 20.1, 0.213]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0.036, 2, 0.167, 0.036, 0, 0.2, 0.03, 0, 0.5, 3.444, 0, 0.933, -4.638, 0, 1.367, 1.871, 0, 1.733, -2.585, 0, 2.1, 3.543, 0, 2.533, -2.099, 0, 2.967, 1.238, 0, 3.367, -0.737, 0, 3.667, 3.628, 0, 4, -4.062, 0, 4.433, 2.412, 0, 4.867, -1.43, 0, 5.267, 0.84, 0, 5.7, -0.506, 0, 6.133, 0.296, 0, 6.533, -1.083, 0, 7.367, 0.998, 0, 8.333, -1.368, 0, 8.767, 0.816, 1, 9.122, 0.816, 9.478, -0.11, 9.833, -1.182, 1, 9.911, -1.417, 9.989, -1.368, 10.067, -1.368, 0, 10.5, 0.816, 0, 10.933, -0.547, 1, 11.255, -0.547, 11.578, -0.47, 11.9, -0.315, 1, 12.011, -0.262, 12.122, -0.236, 12.233, -0.236, 0, 12.467, -0.302, 0, 13.267, 0.538, 0, 13.667, -0.268, 0, 14.1, 0.239, 0, 14.5, -0.03, 0, 14.933, 0.165, 0, 15, 0.164, 0, 15.3, 2.152, 0, 15.7, -3.124, 0, 16.133, 1.87, 0, 16.567, -1.091, 0, 16.967, 0.669, 0, 17.4, -0.362, 0, 17.767, 0.119, 0, 18.2, -0.212, 0, 18.633, -0.008, 0, 19.1, -0.517, 0, 19.667, 0.151, 1, 19.722, 0.151, 19.778, 0.155, 19.833, 0.135, 1, 19.922, 0.103, 20.011, 0.036, 20.1, 0.036]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0.213, 2, 0.167, 0.213, 1, 0.178, 0.213, 0.189, 0.246, 0.2, 0.176, 1, 0.244, -0.102, 0.289, -0.63, 0.333, -0.63, 0, 0.7, 7.245, 0, 1.1, -6.884, 0, 1.5, 3.084, 0, 1.9, -6.162, 0, 2.267, 5.826, 0, 2.667, -3.735, 0, 3.067, 2.123, 0, 3.533, -2.084, 0, 3.833, 8.088, 0, 4.167, -7.132, 0, 4.567, 4.456, 0, 4.967, -2.421, 0, 5.4, 1.401, 0, 5.833, -0.824, 0, 6.267, 0.622, 0, 6.7, -2.065, 0, 7.067, -0.804, 0, 7.167, -0.939, 0, 7.567, 1.047, 0, 7.7, 0.939, 0, 8, 1.403, 0, 8.533, -1.981, 1, 8.655, -1.981, 8.778, 1.185, 8.9, 1.249, 1, 9.178, 1.395, 9.455, 1.403, 9.733, 1.403, 1, 9.766, 1.403, 9.8, 1.471, 9.833, 1.091, 1, 9.978, -0.556, 10.122, -1.981, 10.267, -1.981, 0, 10.633, 1.249, 0, 11.067, -1.061, 1, 11.4, -1.061, 11.734, -0.901, 12.067, -0.711, 1, 12.145, -0.667, 12.222, -0.674, 12.3, -0.674, 0, 12.7, -1.077, 0, 13.467, 0.612, 0, 13.833, -0.442, 0, 14.233, 0.459, 0, 14.633, 0.034, 0, 15, 0.39, 0, 15.133, -0.016, 0, 15.5, 4.748, 0, 15.9, -4.717, 0, 16.267, 3.439, 0, 16.667, -1.766, 0, 17.1, 1.209, 0, 17.533, -0.504, 0, 17.9, 0.356, 0, 18.333, -0.366, 0, 18.833, -0.099, 0, 19.3, -1.164, 0, 19.767, -0.346, 0, 19.933, -0.38, 0, 19.967, -0.371, 0, 20, -0.373, 1, 20.033, -0.373, 20.067, 0.095, 20.1, 0.213]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0.144, 2, 0.167, 0.144, 1, 0.189, 0.144, 0.211, 0.159, 0.233, 0.137, 1, 0.322, 0.052, 0.411, -2.375, 0.5, -2.375, 0, 0.833, 5.809, 0, 1.2, -6.651, 0, 1.6, 5.347, 0, 2, -5.828, 0, 2.367, 6.309, 0, 2.733, -4.812, 0, 3.133, 2.847, 0, 3.667, -3.324, 0, 3.967, 7.683, 0, 4.3, -8.436, 0, 4.633, 6.106, 0, 5.033, -3.466, 0, 5.433, 1.719, 0, 5.867, -0.909, 0, 6.4, 0.863, 0, 6.8, -0.989, 0, 7.1, 0.489, 0, 7.367, -0.683, 0, 7.667, 0.571, 0, 7.9, -0.176, 0, 8.233, 0.737, 0, 8.667, -1.513, 1, 8.989, -1.513, 9.311, -1.099, 9.633, -0.176, 1, 9.7, 0.015, 9.766, 0.29, 9.833, 0.516, 1, 9.878, 0.668, 9.922, 0.737, 9.967, 0.737, 0, 10.4, -1.513, 0, 10.733, 1.609, 0, 11.1, -1.115, 1, 11.422, -1.115, 11.745, -0.821, 12.067, -0.217, 1, 12.2, 0.033, 12.334, 0.167, 12.467, 0.167, 0, 12.833, -0.314, 0, 13.2, 0.069, 0, 13.333, 0.012, 0, 13.6, 0.414, 0, 13.933, -0.556, 0, 14.3, 0.415, 0, 14.7, -0.253, 0, 15.033, 0.09, 0, 15.3, -1.401, 0, 15.633, 3.71, 0, 16, -5.056, 0, 16.367, 4.167, 0, 16.733, -2.591, 0, 17.133, 1.365, 0, 17.567, -0.725, 0, 17.967, 0.487, 0, 18.367, -0.274, 0, 19.033, 0.238, 0, 19.4, -0.426, 0, 19.767, 0.251, 1, 19.878, 0.251, 19.989, 0.228, 20.1, 0.144]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0.002, 2, 0.167, 0.002, 0, 0.3, 0.154, 0, 0.6, -2.635, 0, 0.933, 7.486, 0, 1.3, -9.78, 0, 1.667, 8.87, 0, 2.067, -8.668, 0, 2.467, 9.327, 0, 2.833, -8.087, 0, 3.2, 5.506, 0, 3.667, -3.838, 0, 4.067, 9.449, 0, 4.4, -12.374, 0, 4.733, 10.652, 0, 5.1, -7.04, 0, 5.467, 3.771, 0, 5.867, -1.697, 0, 6.4, 1.023, 0, 6.867, -1.369, 0, 7.2, 0.925, 0, 7.5, -1.02, 0, 7.767, 0.925, 0, 8.033, -0.393, 0, 8.333, 1.021, 0, 8.733, -2.023, 0, 9.5, 0.925, 0, 9.767, -0.393, 1, 9.789, -0.393, 9.811, -0.407, 9.833, -0.214, 1, 9.911, 0.462, 9.989, 1.021, 10.067, 1.021, 0, 10.467, -2.023, 0, 10.833, 2.471, 0, 12.167, -0.438, 0, 12.533, 0.331, 0, 12.9, -0.45, 0, 13.267, 0.195, 0, 13.467, 0.059, 0, 13.7, 0.43, 0, 14.033, -0.786, 0, 14.4, 0.728, 0, 14.767, -0.503, 0, 15.1, 0.216, 0, 15.4, -1.591, 0, 15.733, 4.812, 0, 16.1, -7.216, 0, 16.433, 6.956, 0, 16.8, -4.987, 0, 17.167, 2.839, 0, 17.567, -1.377, 0, 18, 0.796, 0, 18.433, -0.497, 0, 18.833, 0.282, 0, 19.5, -0.55, 0, 19.833, 0.455, 1, 19.922, 0.455, 20.011, 0.372, 20.1, 0.002]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0.036, 2, 0.167, 0.036, 0, 0.2, 0.03, 0, 0.5, 3.444, 0, 0.933, -4.638, 0, 1.367, 1.871, 0, 1.733, -2.585, 0, 2.1, 3.543, 0, 2.533, -2.099, 0, 2.967, 1.238, 0, 3.367, -0.737, 0, 3.667, 3.628, 0, 4, -4.062, 0, 4.433, 2.412, 0, 4.867, -1.43, 0, 5.267, 0.84, 0, 5.7, -0.506, 0, 6.133, 0.296, 0, 6.533, -1.083, 0, 7.367, 0.998, 0, 8.333, -1.368, 0, 8.767, 0.816, 1, 9.122, 0.816, 9.478, -0.11, 9.833, -1.182, 1, 9.911, -1.417, 9.989, -1.368, 10.067, -1.368, 0, 10.5, 0.816, 0, 10.933, -0.547, 1, 11.255, -0.547, 11.578, -0.47, 11.9, -0.315, 1, 12.011, -0.262, 12.122, -0.236, 12.233, -0.236, 0, 12.467, -0.302, 0, 13.267, 0.538, 0, 13.667, -0.268, 0, 14.1, 0.239, 0, 14.5, -0.03, 0, 14.933, 0.165, 0, 15, 0.164, 0, 15.3, 2.152, 0, 15.7, -3.124, 0, 16.133, 1.87, 0, 16.567, -1.091, 0, 16.967, 0.669, 0, 17.4, -0.362, 0, 17.767, 0.119, 0, 18.2, -0.212, 0, 18.633, -0.008, 0, 19.1, -0.517, 0, 19.667, 0.151, 1, 19.722, 0.151, 19.778, 0.155, 19.833, 0.135, 1, 19.922, 0.103, 20.011, 0.036, 20.1, 0.036]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0.213, 2, 0.167, 0.213, 1, 0.178, 0.213, 0.189, 0.246, 0.2, 0.176, 1, 0.244, -0.102, 0.289, -0.63, 0.333, -0.63, 0, 0.7, 7.245, 0, 1.1, -6.884, 0, 1.5, 3.084, 0, 1.9, -6.162, 0, 2.267, 5.826, 0, 2.667, -3.735, 0, 3.067, 2.123, 0, 3.533, -2.084, 0, 3.833, 8.088, 0, 4.167, -7.132, 0, 4.567, 4.456, 0, 4.967, -2.421, 0, 5.4, 1.401, 0, 5.833, -0.824, 0, 6.267, 0.622, 0, 6.7, -2.065, 0, 7.067, -0.804, 0, 7.167, -0.939, 0, 7.567, 1.047, 0, 7.7, 0.939, 0, 8, 1.403, 0, 8.533, -1.981, 1, 8.655, -1.981, 8.778, 1.185, 8.9, 1.249, 1, 9.178, 1.395, 9.455, 1.403, 9.733, 1.403, 1, 9.766, 1.403, 9.8, 1.471, 9.833, 1.091, 1, 9.978, -0.556, 10.122, -1.981, 10.267, -1.981, 0, 10.633, 1.249, 0, 11.067, -1.061, 1, 11.4, -1.061, 11.734, -0.901, 12.067, -0.711, 1, 12.145, -0.667, 12.222, -0.674, 12.3, -0.674, 0, 12.7, -1.077, 0, 13.467, 0.612, 0, 13.833, -0.442, 0, 14.233, 0.459, 0, 14.633, 0.034, 0, 15, 0.39, 0, 15.133, -0.016, 0, 15.5, 4.748, 0, 15.9, -4.717, 0, 16.267, 3.439, 0, 16.667, -1.766, 0, 17.1, 1.209, 0, 17.533, -0.504, 0, 17.9, 0.356, 0, 18.333, -0.366, 0, 18.833, -0.099, 0, 19.3, -1.164, 0, 19.767, -0.346, 0, 19.933, -0.38, 0, 19.967, -0.371, 0, 20, -0.373, 1, 20.033, -0.373, 20.067, 0.095, 20.1, 0.213]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0.144, 2, 0.167, 0.144, 1, 0.189, 0.144, 0.211, 0.159, 0.233, 0.137, 1, 0.322, 0.052, 0.411, -2.375, 0.5, -2.375, 0, 0.833, 5.809, 0, 1.2, -6.651, 0, 1.6, 5.347, 0, 2, -5.828, 0, 2.367, 6.309, 0, 2.733, -4.812, 0, 3.133, 2.847, 0, 3.667, -3.324, 0, 3.967, 7.683, 0, 4.3, -8.436, 0, 4.633, 6.106, 0, 5.033, -3.466, 0, 5.433, 1.719, 0, 5.867, -0.909, 0, 6.4, 0.863, 0, 6.8, -0.989, 0, 7.1, 0.489, 0, 7.367, -0.683, 0, 7.667, 0.571, 0, 7.9, -0.176, 0, 8.233, 0.737, 0, 8.667, -1.513, 1, 8.989, -1.513, 9.311, -1.099, 9.633, -0.176, 1, 9.7, 0.015, 9.766, 0.29, 9.833, 0.516, 1, 9.878, 0.668, 9.922, 0.737, 9.967, 0.737, 0, 10.4, -1.513, 0, 10.733, 1.609, 0, 11.1, -1.115, 1, 11.422, -1.115, 11.745, -0.821, 12.067, -0.217, 1, 12.2, 0.033, 12.334, 0.167, 12.467, 0.167, 0, 12.833, -0.314, 0, 13.2, 0.069, 0, 13.333, 0.012, 0, 13.6, 0.414, 0, 13.933, -0.556, 0, 14.3, 0.415, 0, 14.7, -0.253, 0, 15.033, 0.09, 0, 15.3, -1.401, 0, 15.633, 3.71, 0, 16, -5.056, 0, 16.367, 4.167, 0, 16.733, -2.591, 0, 17.133, 1.365, 0, 17.567, -0.725, 0, 17.967, 0.487, 0, 18.367, -0.274, 0, 19.033, 0.238, 0, 19.4, -0.426, 0, 19.767, 0.251, 1, 19.878, 0.251, 19.989, 0.228, 20.1, 0.144]}, {"Target": "Parameter", "Id": "Param86", "Segments": [0, 0, 2, 0.167, 0, 1, 0.178, 0, 0.189, 0.026, 0.2, -0.022, 1, 0.289, -0.408, 0.378, -0.832, 0.467, -0.832, 0, 0.9, 1.661, 0, 1.333, -0.834, 0, 1.567, -0.48, 0, 1.633, -0.494, 0, 2.1, 0.722, 0, 2.533, -0.427, 0, 2.933, 0.253, 0, 3.367, -0.151, 0, 3.667, 1.757, 0, 4, -2.07, 0, 4.433, 1.228, 0, 4.867, -0.726, 0, 5.267, 0.431, 0, 5.7, -0.258, 0, 6.133, 0.15, 0, 6.533, -0.544, 0, 7.367, 0.498, 0, 7.8, -7.286, 0, 8.133, 9.228, 0, 8.567, -5.033, 0, 8.967, 3.342, 0, 9.533, -7.286, 1, 9.633, -7.286, 9.733, 0.883, 9.833, 8.769, 1, 9.844, 9.645, 9.856, 9.228, 9.867, 9.228, 0, 10.3, -5.033, 0, 10.7, 3.342, 0, 11.9, -2.732, 0, 12.333, 1.402, 0, 12.733, -0.924, 0, 13.2, 0.782, 0, 13.633, -0.434, 0, 14.067, 0.294, 0, 14.467, -0.122, 0, 14.9, 0.144, 0, 15, 0.121, 0, 15.3, 1.037, 0, 15.7, -1.541, 0, 16.133, 0.924, 0, 16.567, -0.539, 0, 16.967, 0.331, 0, 17.4, -0.179, 0, 17.767, 0.059, 0, 18.233, -3.276, 0, 18.633, 4.567, 0, 19.067, -2.511, 0, 19.467, 1.528, 0, 19.9, -1.003, 1, 19.967, -1.003, 20.033, -0.336, 20.1, 0]}, {"Target": "Parameter", "Id": "Param87", "Segments": [0, 0, 2, 0.167, 0, 0, 0.2, -0.026, 0, 0.333, 0.16, 0, 0.667, -1.812, 0, 1.067, 2.854, 0, 1.433, -1.112, 0, 1.667, -0.028, 0, 1.967, -0.951, 0, 2.3, 1.152, 0, 2.667, -0.787, 0, 3.067, 0.446, 0, 3.533, -0.691, 0, 3.833, 3.917, 0, 4.167, -3.624, 0, 4.567, 2.286, 0, 4.967, -1.247, 0, 5.4, 0.716, 0, 5.833, -0.419, 0, 6.267, 0.315, 0, 6.7, -1.036, 0, 7.067, -0.399, 0, 7.167, -0.47, 0, 7.667, 2.455, 0, 7.967, -15.627, 0, 8.3, 15.505, 1, 8.433, 15.505, 8.567, -6.213, 8.7, -8.615, 1, 9.033, -14.621, 9.367, -15.627, 9.7, -15.627, 1, 9.744, -15.627, 9.789, -12.864, 9.833, -4.651, 1, 9.9, 7.669, 9.966, 15.505, 10.033, 15.505, 0, 10.433, -8.615, 0, 10.833, 6.305, 0, 12.067, -4.608, 0, 12.467, 2.232, 0, 12.867, -1.933, 0, 13.333, 1.035, 0, 13.767, -0.688, 0, 14.2, 0.512, 0, 14.6, -0.162, 0, 15, 0.305, 0, 15.133, 0.053, 0, 15.5, 2.332, 0, 15.9, -2.338, 0, 16.267, 1.71, 0, 16.667, -0.876, 0, 17.1, 0.599, 0, 17.533, -0.248, 0, 17.9, 0.176, 0, 17.967, 0.147, 0, 18.1, 0.738, 0, 18.433, -7.045, 0, 18.8, 7.433, 0, 19.2, -4.257, 0, 19.6, 2.888, 0, 20.033, -1.44, 1, 20.055, -1.44, 20.078, -0.1, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngleX", "Segments": [0, 0, 2, 0.167, 0, 2, 8.1, 0, 0, 8.333, -0.3, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngleY", "Segments": [0, 0, 2, 0.167, 0, 2, 8.1, 0, 0, 8.333, 3.54, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "Segments": [0, 0, 2, 0.167, 0, 2, 1.933, 0, 2, 6.967, 0, 1, 7.289, 0.79, 7.611, 1.58, 7.933, 2.37, 2, 9.833, 2.37, 1, 10.589, 1.42, 11.344, 0.47, 12.1, -0.48, 2, 13.3, -0.48, 2, 14.167, -0.48, 0, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "Segments": [0, 0, 2, 0.167, 0, 2, 1.933, 0, 2, 6.967, 0, 1, 7.289, -0.773, 7.611, -1.545, 7.933, -2.318, 2, 9.833, -2.318, 1, 10.589, -1.825, 11.344, -1.333, 12.1, -0.84, 2, 13.3, -0.84, 2, 14.167, -0.84, 0, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngleX", "Segments": [0, 0, 2, 0.167, 0, 2, 1.933, 0, 2, 6.967, 0, 2, 9.833, 0, 2, 12.1, 0, 0, 12.133, -4.02, 0, 12.267, 0, 2, 13.3, 0, 2, 14.167, 0, 2, 15.033, 0, 2, 17.633, 0, 2, 18.133, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "Segments": [0, 0, 2, 0.167, 0, 2, 1.933, 0, 2, 6.967, 0, 1, 7.922, -0.369, 8.878, -0.737, 9.833, -1.106, 1, 10.589, -1.397, 11.344, -1.689, 12.1, -1.98, 1, 12.956, -1.997, 13.811, -1.994, 14.667, -1.994, 0, 14.9, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 0.167, 0, 2, 1.933, 0, 2, 6.967, 0, 2, 9.833, 0, 2, 12.1, 0, 2, 12.267, 0, 2, 13.3, 0, 2, 14.167, 0, 2, 15.033, 0, 2, 17.467, 0, 0, 17.633, -1, 0, 17.967, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 0.167, 0, 2, 1.933, 0, 2, 6.967, 0, 2, 9.833, 0, 2, 12.1, 0, 2, 12.267, 0, 2, 13.3, 0, 2, 14.167, 0, 2, 15.033, 0, 2, 17.467, 0, 0, 17.633, -1, 0, 17.967, 0, 2, 20.1, 0]}, {"Target": "Parameter", "Id": "MB_shuiwenDH", "Segments": [0, 0.6, 0, 20.1, 0.6]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param101", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "MB_suduxianjiaoduBGB", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "MB_sucaiAjiya", "Segments": [0, 0.1, 0, 20.1, 0.1]}, {"Target": "Parameter", "Id": "MB_sucaiAlashen", "Segments": [0, 0.2, 0, 20.1, 0.2]}, {"Target": "Parameter", "Id": "MB_suduxianxunhuanBGB", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "MB_sucaiBjiya", "Segments": [0, 0.15, 0, 20.1, 0.15]}, {"Target": "Parameter", "Id": "MB_sucaiBlashen", "Segments": [0, 0.3, 0, 20.1, 0.3]}, {"Target": "Parameter", "Id": "MB_beijingbanjiaodu", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "All_X", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "All_Y", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "All_Angle", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "All_Size", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param97", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param98", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param100", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 30, 0, 20.1, 30]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 30, 0, 20.1, 30]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param85", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param79", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param82", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 30, 0, 20.1, 30]}, {"Target": "Parameter", "Id": "Param80", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param81", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param78", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param83", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param84", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param93", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param94", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param95", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param96", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param102", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "FG_Black", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "FG_White", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "BG_Black", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "BG_White", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandLREDisplay", "Segments": [0, 30, 0, 20.1, 30]}, {"Target": "Parameter", "Id": "ParamHandL3Display", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandL4Display", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandL6Display", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandL7Display", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandL8Display", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandL9Display", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandRREDisplay", "Segments": [0, 30, 0, 20.1, 30]}, {"Target": "Parameter", "Id": "ParamHandR2Display", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandR3Display", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandR6Display", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandR7Display", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandR8Display", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param243", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param120", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param99", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param242", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamArmL_A", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamArmL_B", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamArmL_C", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamArmR_A", "Segments": [0, 30, 0, 20.1, 30]}, {"Target": "Parameter", "Id": "ParamArmR_B", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamArmR_C", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandR_DMZ", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamWaistZ", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamButtZ", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "NULL1", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "NULL2", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "NULL3", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "cold_heat", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRsize", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeGGDisplay", "Segments": [0, 1, 0, 20.1, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallDisplay", "Segments": [0, 1, 0, 20.1, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenYSD", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamMouthsize", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamMouthY", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamMouthX", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamMouthADisplay", "Segments": [0, 1, 0, 20.1, 1]}, {"Target": "Parameter", "Id": "ParamMouthBDisplay", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamMouthCDisplay", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "MB_yiwenTMD", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "MB_yiwenJD", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "MB_fuhaoyinyue", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "MB_fuhaohan", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "MB_fuhaogantan", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "MB_f<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "MB_yiwenSS", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "nua", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "MB_fuhaojuhaoDH", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "xiu", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "null_a", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "null_c", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "null_d", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "null_e", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "OP_physics", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "OP_Change", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_L", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_X", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "NULL4", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "NULL5", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "NULL6", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param105", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param106", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param104", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param107", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param108", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngleY", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 20.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 20.1, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.167, "Value": ""}, {"Time": 19.6, "Value": ""}]}