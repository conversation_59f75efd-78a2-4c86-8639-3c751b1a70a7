#!/usr/bin/env python3
"""
简化的语音对话系统测试
"""

import sys
import os
import json

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("🔄 开始语音对话系统测试...")

# 1. 测试配置文件读取
print("\n1. 测试配置文件...")
try:
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    voice_config = config.get('voice_dialogue', {})
    if voice_config:
        print("✅ 语音对话配置已找到")
        print(f"   STT模型路径: {voice_config.get('stt_config', {}).get('model_path', '未配置')}")
        print(f"   TTS API地址: {voice_config.get('tts_config', {}).get('api_url', '未配置')}")
    else:
        print("❌ 语音对话配置未找到")
except Exception as e:
    print(f"❌ 配置文件读取失败: {e}")

# 2. 测试PyAudio
print("\n2. 测试PyAudio...")
try:
    import pyaudio
    print("✅ PyAudio可用")
    
    # 测试音频设备
    p = pyaudio.PyAudio()
    device_count = p.get_device_count()
    print(f"   找到 {device_count} 个音频设备")
    
    # 查找默认输入设备
    default_input = p.get_default_input_device_info()
    print(f"   默认输入设备: {default_input['name']}")
    
    p.terminate()
except ImportError:
    print("❌ PyAudio未安装")
except Exception as e:
    print(f"❌ PyAudio测试失败: {e}")

# 3. 测试faster-whisper
print("\n3. 测试faster-whisper...")
try:
    from faster_whisper import WhisperModel
    print("✅ faster-whisper可用")
    
    # 检查模型路径
    model_path = "D:/huggingface_cache/hub/models--Systran--faster-whisper-large-v3"
    if os.path.exists(model_path):
        print(f"✅ 模型路径存在: {model_path}")
    else:
        print(f"❌ 模型路径不存在: {model_path}")
        
except ImportError:
    print("❌ faster-whisper未安装")
except Exception as e:
    print(f"❌ faster-whisper测试失败: {e}")

# 4. 测试网络连接（TTS API）
print("\n4. 测试TTS API连接...")
try:
    import requests
    
    api_url = "http://localhost:9880"
    response = requests.get(f"{api_url}/health", timeout=5)
    
    if response.status_code == 200:
        print(f"✅ TTS API服务正常: {api_url}")
    else:
        print(f"⚠️ TTS API响应异常: {response.status_code}")
        
except requests.exceptions.ConnectionError:
    print(f"❌ 无法连接TTS API: {api_url}")
except ImportError:
    print("❌ requests库未安装")
except Exception as e:
    print(f"❌ TTS API测试失败: {e}")

# 5. 测试语音模块导入
print("\n5. 测试语音模块导入...")
try:
    # 测试麦克风管理器
    from dialogue_system.voice.microphone_manager import MicrophoneManager
    print("✅ MicrophoneManager导入成功")
    
    # 测试语音处理器
    from dialogue_system.voice.voice_processor import VoiceProcessor
    print("✅ VoiceProcessor导入成功")
    
    # 测试按键触发输入
    from dialogue_system.voice.key_triggered_input import KeyTriggeredInput
    print("✅ KeyTriggeredInput导入成功")
    
    # 测试实时输入
    from dialogue_system.voice.realtime_input import RealtimeInput
    print("✅ RealtimeInput导入成功")
    
except ImportError as e:
    print(f"❌ 语音模块导入失败: {e}")
    import traceback
    traceback.print_exc()

# 6. 测试STT模块导入
print("\n6. 测试STT模块导入...")
try:
    from dialogue_system.stt.faster_whisper_client import FasterWhisperClient
    print("✅ FasterWhisperClient导入成功")
    
    from dialogue_system.stt.stt_manager import STTManager
    print("✅ STTManager导入成功")
    
except ImportError as e:
    print(f"❌ STT模块导入失败: {e}")
    import traceback
    traceback.print_exc()

# 7. 测试TTS模块导入
print("\n7. 测试TTS模块导入...")
try:
    from dialogue_system.tts.gptsovits_client import GPTSoVITSClient
    print("✅ GPTSoVITSClient导入成功")
    
    from dialogue_system.tts.audio_player import AudioPlayer
    print("✅ AudioPlayer导入成功")
    
    from dialogue_system.tts.tts_manager import TTSManager
    print("✅ TTSManager导入成功")
    
except ImportError as e:
    print(f"❌ TTS模块导入失败: {e}")
    import traceback
    traceback.print_exc()

# 8. 创建简单的功能测试
print("\n8. 创建基础组件测试...")
try:
    # 创建简单的配置对象
    class SimpleConfig:
        def __init__(self):
            with open('config.json', 'r', encoding='utf-8') as f:
                self.config = json.load(f)
    
    config = SimpleConfig()
    print("✅ 简单配置对象创建成功")
    
    # 测试麦克风管理器创建
    from dialogue_system.voice.microphone_manager import MicrophoneManager
    mic_manager = MicrophoneManager(config)
    print("✅ 麦克风管理器创建成功")
    
    # 测试语音处理器创建
    from dialogue_system.voice.voice_processor import VoiceProcessor
    voice_processor = VoiceProcessor(config)
    print("✅ 语音处理器创建成功")
    
    # 测试STT管理器创建
    from dialogue_system.stt.stt_manager import STTManager
    stt_manager = STTManager(config)
    print("✅ STT管理器创建成功")
    
    # 测试TTS管理器创建
    from dialogue_system.tts.tts_manager import TTSManager
    tts_manager = TTSManager(config)
    print("✅ TTS管理器创建成功")
    
except Exception as e:
    print(f"❌ 基础组件测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n🎉 语音对话系统测试完成！")
print("\n📋 测试总结:")
print("- 如果所有组件都显示✅，说明系统准备就绪")
print("- 如果有❌，请根据错误信息安装相应依赖或检查配置")
print("- 建议运行: pip install -r requirements.txt")
