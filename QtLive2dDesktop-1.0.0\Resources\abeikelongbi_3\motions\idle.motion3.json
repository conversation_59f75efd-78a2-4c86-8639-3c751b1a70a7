{"Version": 3, "Meta": {"Duration": 7.133, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 244, "TotalSegmentCount": 9000, "TotalPointCount": 13520, "UserDataCount": 1, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.178, 1, 0.355, 0.9, 0.533, 0.817, 1, 0.655, 0.76, 0.778, 0.746, 0.9, 0.7, 1, 0.922, 0.692, 0.945, 0, 0.967, 0, 0, 1.1, 0.602, 0, 1.2, 0, 0, 1.4, 1, 2, 2.067, 1, 0, 3, 0.9, 0, 4.533, 1, 0, 5.567, 0.941, 0, 6.767, 1, 2, 7.133, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.178, 1, 0.355, 0.9, 0.533, 0.817, 1, 0.655, 0.76, 0.778, 0.746, 0.9, 0.7, 1, 0.922, 0.692, 0.945, 0, 0.967, 0, 0, 1.1, 0.602, 0, 1.2, 0, 0, 1.4, 1, 2, 2.067, 1, 0, 3, 0.9, 0, 4.533, 1, 0, 5.567, 0.941, 0, 6.767, 1, 2, 7.133, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 2, 0.2, 0, 1, 0.789, 0, 1.378, -0.251, 1.967, -0.3, 1, 3.089, -0.393, 4.211, -0.4, 5.333, -0.4, 1, 5.933, -0.4, 6.533, -0.014, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 1.2, 0, 0, 2.667, 0.201, 0, 5.333, 0, 2, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLX", "Segments": [0, 0, 1, 0.789, 0.212, 1.578, 0.413, 2.367, 0.413, 0, 5.8, -0.132, 1, 6.244, -0.132, 6.689, -0.135, 7.133, -0.01]}, {"Target": "Parameter", "Id": "ParamEyeBallRX", "Segments": [0, 0, 1, 0.789, 0.212, 1.578, 0.413, 2.367, 0.413, 0, 5.8, -0.132, 1, 6.244, -0.132, 6.689, -0.135, 7.133, -0.01]}, {"Target": "Parameter", "Id": "ParamPHYInputX", "Segments": [0, 0, 1, 0.189, 0, 0.378, -5.626, 0.567, -9.364, 1, 0.622, -10.464, 0.678, -10.106, 0.733, -10.106, 0, 1.767, 8.31, 1, 1.867, 8.31, 1.967, 8.79, 2.067, 8.045, 1, 2.522, 4.653, 2.978, -12.272, 3.433, -12.272, 1, 3.489, -12.272, 3.544, -12.622, 3.6, -11.859, 1, 3.956, -6.971, 4.311, 2.117, 4.667, 8.895, 1, 4.711, 9.742, 4.756, 9.991, 4.8, 9.991, 0, 6.433, -10.113, 1, 6.522, -10.113, 6.611, -10.414, 6.7, -8.911, 1, 6.844, -6.47, 6.989, -0.879, 7.133, -0.093]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 0, 1.5, 4.256, 1, 1.689, 4.256, 1.878, 4.551, 2.067, 3.768, 1, 2.567, 1.695, 3.067, -2.957, 3.567, -2.957, 1, 3.689, -2.957, 3.811, -3.003, 3.933, -2.943, 1, 4.655, -2.587, 5.378, 3.963, 6.1, 3.963, 1, 6.244, 3.963, 6.389, 4.09, 6.533, 3.519, 1, 6.733, 2.728, 6.933, 0.277, 7.133, 0.022]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.189, 0, 0.378, -3.247, 0.567, -5.472, 1, 0.622, -6.126, 0.678, -5.906, 0.733, -5.906, 0, 1.767, 4.867, 1, 1.867, 4.867, 1.967, 5.157, 2.067, 4.712, 1, 2.522, 2.684, 2.978, -7.173, 3.433, -7.173, 1, 3.489, -7.173, 3.544, -7.382, 3.6, -6.931, 1, 3.956, -4.046, 4.311, 1.244, 4.667, 5.209, 1, 4.711, 5.705, 4.756, 5.85, 4.8, 5.85, 0, 6.433, -5.91, 1, 6.522, -5.91, 6.611, -6.103, 6.7, -5.207, 1, 6.844, -3.751, 6.989, -0.509, 7.133, -0.054]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 0, 1.233, 4.015, 1, 1.411, 4.015, 1.589, 4.165, 1.767, 3.526, 1, 2.256, 1.768, 2.744, -4.02, 3.233, -4.606, 1, 3.366, -4.766, 3.5, -4.668, 3.633, -4.668, 0, 5.8, 3.722, 1, 5.933, 3.722, 6.067, 3.842, 6.2, 3.278, 1, 6.511, 1.963, 6.822, 0.131, 7.133, 0.007]}, {"Target": "Parameter", "Id": "ParamArmUpperLAngle", "Segments": [0, 0, 1, 0.711, -0.011, 1.422, -0.017, 2.133, -0.017, 0, 5.3, 0.014, 1, 5.911, 0.014, 6.522, 0.01, 7.133, 0.001]}, {"Target": "Parameter", "Id": "ParamArm<PERSON>pperRAngle", "Segments": [0, 0, 1, 0.522, 0.178, 1.045, 0.2, 1.567, 0.2, 0, 2.833, -0.413, 2, 3.133, -0.413, 0, 4.633, 0.103, 0, 6.7, -0.413, 1, 6.844, -0.413, 6.989, -0.1, 7.133, -0.015]}, {"Target": "Parameter", "Id": "ParamArmLowerRAngle", "Segments": [0, 7.4, 0, 1.633, 8.37, 0, 3.5, 7.395, 0, 5.8, 8.513, 1, 6.244, 8.513, 6.689, 7.454, 7.133, 7.402]}, {"Target": "Parameter", "Id": "ParamArmHandRAngle", "Segments": [0, 0, 2, 0.233, 0, 0, 0.9, -0.835, 0, 1.833, 2.185, 0, 3.9, -1.474, 1, 4.156, -1.474, 4.411, -1.56, 4.667, -1.02, 1, 5.134, -0.035, 5.6, 1.577, 6.067, 1.577, 1, 6.422, 1.576, 6.778, 0.094, 7.133, 0.004]}, {"Target": "Parameter", "Id": "ParamArmLowerRH", "Segments": [0, 0, 0, 1.533, -11, 0, 3.467, 0, 0, 5.967, -9, 1, 6.356, -9, 6.744, -0.493, 7.133, -0.02]}, {"Target": "Parameter", "Id": "ParamHandT2R", "Segments": [0, 0, 2, 0.3, 0, 2, 0.6, 0, 0, 2.167, 0.2, 0, 2.967, 0, 2, 3.367, 0, 0, 4.733, 0.14, 0, 5.967, 0, 0, 6.433, 0.046, 1, 6.666, 0.046, 6.9, 0.004, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 0.5, 0.499, 0, 0.8, 0, 2, 1.667, 0, 0, 2.833, 0.499, 2, 3.4, 0.499, 0, 3.9, 0, 2, 4.6, 0, 0, 5.8, 0.499, 2, 6.367, 0.499, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.278, -0.509, 0.555, -0.921, 0.833, -0.921, 0, 1.4, 1.522, 1, 1.589, 1.522, 1.778, 1.583, 1.967, 1.337, 1, 2.445, 0.715, 2.922, -1.532, 3.4, -1.743, 1, 3.533, -1.802, 3.667, -1.766, 3.8, -1.766, 0, 5.967, 1.411, 1, 6.1, 1.411, 6.234, 1.404, 6.367, 1.243, 1, 6.622, 0.934, 6.878, 0.524, 7.133, 0.061]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 0, 0.533, -1.523, 0, 1.133, 2.307, 1, 1.222, 2.307, 1.311, 2.549, 1.4, 2.185, 1, 1.889, 0.184, 2.378, -6.173, 2.867, -6.173, 1, 2.956, -6.173, 3.044, -6.352, 3.133, -5.982, 1, 3.466, -4.595, 3.8, -0.234, 4.133, 2.576, 1, 4.178, 2.951, 4.222, 3.081, 4.267, 3.081, 0, 5.967, -6.178, 1, 6.056, -6.178, 6.144, -6.398, 6.233, -5.982, 1, 6.533, -4.579, 6.833, -0.319, 7.133, -0.017]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.367, 0, 0, 1.433, 0.401, 1, 1.611, 0.401, 1.789, 0.403, 1.967, 0.352, 1, 2.178, 0.291, 2.389, 0.115, 2.6, 0, 1, 2.889, -0.157, 3.178, -0.441, 3.467, -0.465, 1, 3.6, -0.476, 3.734, -0.471, 3.867, -0.471, 1, 4.245, -0.471, 4.622, -0.267, 5, 0, 1, 5.4, 0.283, 5.8, 0.372, 6.2, 0.372, 1, 6.333, 0.372, 6.467, 0.384, 6.6, 0.327, 1, 6.778, 0.251, 6.955, 0.028, 7.133, 0.002]}, {"Target": "Parameter", "Id": "ParamBodyAngleY2", "Segments": [0, 0, 0, 0.333, -1, 1, 0.489, -1, 0.644, 0.328, 0.8, 0.65, 1, 0.911, 0.88, 1.022, 0.824, 1.133, 0.824, 0, 2.7, -3.875, 1, 2.789, -3.875, 2.878, -3.969, 2.967, -3.742, 1, 3.3, -2.89, 3.634, -0.335, 3.967, 0.649, 1, 4.1, 1.043, 4.234, 0.974, 4.367, 0.974, 1, 4.722, 0.974, 5.078, -3.051, 5.433, -4.06, 1, 5.6, -4.533, 5.766, -4.346, 5.933, -4.346, 1, 6.333, -4.346, 6.733, -0.232, 7.133, -0.009]}, {"Target": "Parameter", "Id": "ParamBodyBreechX", "Segments": [0, 0, 0, 0.567, 0.566, 0, 1.833, -1.989, 0, 3.267, 2.478, 0, 4.833, -1.705, 0, 6.333, 2.404, 1, 6.6, 2.404, 6.866, 0.188, 7.133, 0.011]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, 0, 0, 1.167, -0.401, 1, 1.356, -0.401, 1.544, -0.412, 1.733, -0.352, 1, 1.966, -0.278, 2.2, 0, 2.433, 0, 0, 3.5, -0.339, 1, 3.633, -0.339, 3.767, -0.345, 3.9, -0.333, 1, 4.222, -0.303, 4.545, 0, 4.867, 0, 0, 5.867, -0.372, 1, 6.056, -0.372, 6.244, -0.383, 6.433, -0.327, 1, 6.666, -0.258, 6.9, -0.023, 7.133, -0.002]}, {"Target": "Parameter", "Id": "ParamShoulderR", "Segments": [0, 0, 1, 0.2, 1.154, 0.4, 3.854, 0.6, 3.854, 1, 0.8, 3.854, 1, -2.813, 1.2, -4.282, 1, 1.289, -4.935, 1.378, -4.665, 1.467, -4.665, 0, 2.967, 4.396, 1, 3.156, 4.396, 3.344, -4.397, 3.533, -4.917, 1, 3.644, -5.223, 3.756, -5.099, 3.867, -5.099, 0, 5.2, 3.624, 0, 5.9, -4.5, 1, 5.989, -4.5, 6.078, -4.592, 6.167, -4.354, 1, 6.489, -3.492, 6.811, -2.025, 7.133, -0.191]}, {"Target": "Parameter", "Id": "ParamLegR1Z", "Segments": [0, 0, 1, 0.544, 0, 1.089, 0.184, 1.633, 0.399, 1, 1.822, 0.474, 2.011, 0.471, 2.2, 0.471, 1, 2.9, 0.471, 3.6, 0.115, 4.3, -0.396, 1, 4.433, -0.493, 4.567, -0.487, 4.7, -0.487, 0, 6.867, 0.339, 1, 6.956, 0.339, 7.044, 0.071, 7.133, 0.012]}, {"Target": "Parameter", "Id": "ParamLegR2Z", "Segments": [0, 0, 0, 0.3, -0.403, 1, 0.578, -0.403, 0.855, -0.411, 1.133, -0.385, 1, 1.689, -0.333, 2.244, -0.088, 2.8, -0.002, 1, 3.089, 0.043, 3.378, 0.039, 3.667, 0.039, 1, 4.234, 0.039, 4.8, -0.245, 5.367, -0.46, 1, 5.556, -0.532, 5.744, -0.512, 5.933, -0.512, 1, 6.333, -0.512, 6.733, -0.027, 7.133, -0.001]}, {"Target": "Parameter", "Id": "ParamLegR3Z", "Segments": [0, 0, 1, 0.022, 0.066, 0.045, 0.453, 0.067, 0.453, 0, 2.2, -0.533, 1, 2.433, -0.533, 2.667, -0.544, 2.9, -0.519, 1, 3.367, -0.469, 3.833, 0.242, 4.3, 0.437, 1, 4.444, 0.497, 4.589, 0.477, 4.733, 0.477, 1, 5.222, 0.477, 5.711, -0.155, 6.2, -0.293, 1, 6.433, -0.359, 6.667, -0.332, 6.9, -0.332, 1, 6.978, -0.332, 7.055, -0.28, 7.133, -0.09]}, {"Target": "Parameter", "Id": "ParamLegR3Y", "Segments": [0, 0, 0, 0.5, 1.041, 0, 1.133, 1, 2, 1.167, 1, 1, 1.489, 1, 1.811, -0.895, 2.133, -1.193, 1, 2.4, -1.44, 2.666, -1.382, 2.933, -1.382, 0, 5.3, 1.305, 1, 5.644, 1.305, 5.989, 1.321, 6.333, 1.18, 1, 6.6, 1.071, 6.866, 0.084, 7.133, 0.005]}, {"Target": "Parameter", "Id": "ParamFootRX", "Segments": [0, 0, 0, 1.533, -1.321, 1, 1.7, -1.321, 1.866, -1.365, 2.033, -1.271, 1, 2.533, -0.988, 3.033, 0.516, 3.533, 0.897, 1, 3.722, 1.041, 3.911, 0.993, 4.1, 0.993, 1, 4.667, 0.993, 5.233, -0.868, 5.8, -1.399, 1, 6.044, -1.628, 6.289, -1.534, 6.533, -1.534, 1, 6.733, -1.534, 6.933, -0.157, 7.133, -0.012]}, {"Target": "Parameter", "Id": "ParamFootLX", "Segments": [0, 0, 1, 0.289, 2.067, 0.578, 4.173, 0.867, 4.173, 1, 1.4, 4.173, 1.934, 3.508, 2.467, 2.616, 1, 2.7, 2.226, 2.934, 2.173, 3.167, 2.173, 0, 4.6, 3.113, 1, 4.822, 3.113, 5.045, 3.254, 5.267, 2.916, 1, 5.756, 2.172, 6.244, -1.028, 6.733, -1.028, 1, 6.866, -1.028, 7, -1.033, 7.133, -0.221]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyX", "Segments": [0, 0, 0, 0.867, 3.347, 0, 2.467, -1.812, 0, 4.1, 6.521, 0, 5.367, -5.239, 0, 6.4, 3.059, 1, 6.644, 3.059, 6.889, 0.26, 7.133, 0.017]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyY", "Segments": [0, 0, 1, 0.422, 2.139, 0.845, 2.312, 1.267, 2.312, 0, 2.867, -2.34, 0, 4.5, 5.174, 0, 5.8, -5.43, 1, 6.244, -5.43, 6.689, -2.459, 7.133, -0.17]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyZ", "Segments": [0, 0, 0, 1.267, 5.861, 0, 2.667, -1.28, 0, 4.1, 7.41, 0, 5.633, -3.889, 0, 6.667, 8.025, 1, 6.822, 8.025, 6.978, 1.034, 7.133, 0.102]}, {"Target": "Parameter", "Id": "ParamManjuuRPositionZ", "Segments": [0, 0, 0, 0.867, 0.929, 0, 2, -0.854, 0, 3.467, 0.365, 0, 5.1, -0.853, 0, 6.3, 0.587, 1, 6.578, 0.587, 6.855, 0.044, 7.133, 0.003]}, {"Target": "Parameter", "Id": "ParamManjuuRMouth", "Segments": [0, 0, 0, 0.733, -4.354, 1, 0.878, -4.354, 1.022, -4.527, 1.167, -4.096, 1, 1.422, -3.333, 1.678, 4.763, 1.933, 4.763, 0, 2.9, -2.684, 0, 3.7, 1.124, 0, 4.367, -1.748, 0, 5.8, 2.392, 0, 7.033, -1, 1, 7.066, -1, 7.1, -0.437, 7.133, -0.156]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyX", "Segments": [0, 0, 1, 0.222, -6.232, 0.445, -7.021, 0.667, -7.021, 0, 1.7, 12.347, 0, 2.8, -3.668, 0, 3.7, 22.937, 0, 4.867, -18.34, 0, 6.3, 16.782, 1, 6.578, 16.782, 6.855, 8.757, 7.133, 0.937]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyY", "Segments": [0, 0, 1, 0.244, -8.51, 0.489, -9.865, 0.733, -9.865, 0, 2, 18.079, 0, 3.3, -9.865, 0, 4.3, 27.794, 0, 5.3, -13.378, 0, 6.667, 18.079, 1, 6.822, 18.079, 6.978, 7.429, 7.133, 1.25]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyZ", "Segments": [0, 0, 1, 0.3, 3.727, 0.6, 8.452, 0.9, 8.452, 0, 2.467, -7.987, 0, 4.233, 10.148, 0, 6.033, -5.585, 1, 6.4, -5.585, 6.766, -4.877, 7.133, -0.416]}, {"Target": "Parameter", "Id": "ParamManjuuUEyesForm", "Segments": [0, 0, 2, 0.667, 0, 0, 1.533, -1, 2, 2.433, -1, 2, 2.867, -1, 2, 3.767, -1, 2, 4.267, -1, 2, 5.167, -1, 2, 5.667, -1, 2, 6.467, -1, 1, 6.689, -1, 6.911, -0.093, 7.133, -0.007]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyX", "Segments": [0, 0, 1, 0.267, 9.165, 0.533, 22.588, 0.8, 22.588, 0, 3.3, -30, 0, 4.733, 30, 0, 6.033, -17.239, 1, 6.4, -17.239, 6.766, -14.213, 7.133, -1.21]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyY", "Segments": [0, 0, 0, 0.133, -1.62, 0, 0.3, 2.925, 0, 0.467, -2.598, 0, 0.6, 3.787, 0, 0.733, -3.047, 0, 0.867, 2.925, 0, 1, -2.703, 0, 1.133, 2.925, 0, 1.267, -3.424, 0, 1.367, 3.066, 0, 1.467, -3.979, 0, 1.567, 2.925, 0, 1.7, -2.598, 0, 1.867, 3.787, 0, 1.967, -3.047, 0, 2.1, 2.925, 0, 2.267, -2.703, 0, 2.367, 2.925, 0, 2.5, -3.424, 0, 2.633, 2.925, 0, 2.767, -2.598, 0, 2.933, 3.787, 0, 3.033, -3.047, 0, 3.167, 2.925, 0, 3.333, -2.703, 0, 3.433, 2.925, 0, 3.567, -3.424, 0, 3.7, 3.066, 0, 3.767, -3.979, 0, 3.933, 4.531, 0, 4.033, -2.598, 0, 4.133, 2.925, 0, 4.3, -2.598, 0, 4.433, 3.787, 0, 4.567, -3.047, 0, 4.7, 2.925, 0, 4.833, -2.703, 0, 4.967, 2.925, 0, 5.1, -3.424, 0, 5.2, 3.066, 0, 5.3, -3.979, 0, 5.467, 2.925, 0, 5.6, -2.598, 0, 5.8, 3.252, 0, 6.033, -1.62, 0, 6.233, 2.925, 0, 6.4, -2.598, 0, 6.667, 3.252, 0, 6.9, -2.598, 1, 6.978, -2.598, 7.055, -0.609, 7.133, -0.112]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyZ", "Segments": [0, 0, 1, 0.033, -14.022, 0.067, -30, 0.1, -30, 0, 0.267, 30, 0, 0.433, -30, 0, 0.567, 30, 0, 0.667, -30, 0, 0.833, 30, 1, 0.878, 30, 0.922, -30, 0.967, -30, 0, 1.1, 30, 0, 1.2, -30, 0, 1.333, 30, 0, 1.467, -30, 1, 1.489, -30, 1.511, 30, 1.533, 30, 0, 1.667, -30, 0, 1.833, 30, 0, 1.933, -30, 0, 2.067, 30, 0, 2.233, -30, 0, 2.333, 30, 1, 2.366, 30, 2.4, -30, 2.433, -30, 0, 2.6, 30, 0, 2.733, -30, 0, 2.867, 30, 0, 3, -30, 0, 3.133, 30, 0, 3.3, -30, 1, 3.333, -30, 3.367, 30, 3.4, 30, 0, 3.5, -30, 0, 3.667, 30, 0, 3.767, -30, 0, 3.9, 30, 0, 4.067, -30, 0, 4.167, 30, 0, 4.267, -30, 0, 4.4, 30, 0, 4.5, -30, 0, 4.667, 30, 0, 4.8, -30, 0, 4.933, 30, 0, 5.033, -30, 0, 5.167, 30, 0, 5.3, -30, 0, 5.433, 30, 0, 5.567, -30, 0, 5.733, 29.601, 0, 5.867, -30, 0, 6.167, 28, 0, 6.367, -29.798, 0, 6.6, 29.601, 0, 6.8, -30, 0, 7, 29.298, 1, 7.044, 29.298, 7.089, 25.31, 7.133, 11.904]}, {"Target": "Parameter", "Id": "ParamManjuuUArmZ", "Segments": [0, 0, 1, 0.044, -19.929, 0.089, -30, 0.133, -30, 0, 0.3, 30, 0, 0.467, -30, 0, 0.6, 30, 0, 0.733, -30, 1, 0.778, -30, 0.822, 30, 0.867, 30, 0, 1, -30, 0, 1.133, 30, 0, 1.267, -30, 0, 1.367, 30, 0, 1.5, -30, 0, 1.567, 30, 0, 1.7, -30, 0, 1.867, 30, 0, 1.967, -30, 0, 2.1, 30, 0, 2.267, -30, 1, 2.3, -30, 2.334, 30, 2.367, 30, 0, 2.5, -30, 0, 2.633, 30, 0, 2.767, -30, 0, 2.933, 30, 0, 3.033, -30, 0, 3.167, 30, 0, 3.333, -30, 1, 3.366, -30, 3.4, 30, 3.433, 30, 0, 3.567, -30, 0, 3.7, 30, 0, 3.8, -30, 0, 3.933, 30, 0, 4.033, -30, 0, 4.133, 30, 0, 4.3, -30, 0, 4.433, 30, 0, 4.567, -30, 0, 4.7, 30, 0, 4.833, -30, 0, 4.967, 30, 0, 5.1, -30, 0, 5.2, 30, 0, 5.333, -30, 0, 5.467, 30, 0, 5.6, -30, 0, 5.8, 30, 0, 6, -27.782, 0, 6.233, 30, 0, 6.433, -28.551, 0, 6.667, 30, 0, 6.9, -27.782, 0, 7.033, 29.787, 1, 7.066, 29.787, 7.1, 24.242, 7.133, 13.061]}, {"Target": "Parameter", "Id": "ParamSpeedLineU", "Segments": [0, 0, 2, 0.167, 0, 1, 0.234, 0.333, 0.3, 0.667, 0.367, 1, 2, 0.4, 0, 2, 0.5, 0, 1, 0.544, 0.333, 0.589, 0.667, 0.633, 1, 2, 0.667, 0, 2, 0.767, 0, 1, 0.811, 0.333, 0.856, 0.667, 0.9, 1, 2, 0.933, 0, 2, 1.033, 0, 1, 1.078, 0.333, 1.122, 0.667, 1.167, 1, 2, 1.2, 0, 2, 1.3, 0, 1, 1.333, 0.333, 1.367, 0.667, 1.4, 1, 2, 1.433, 0, 2, 1.467, 0, 2, 1.5, 0, 1, 1.533, 0.333, 1.567, 0.667, 1.6, 1, 2, 1.633, 0, 2, 1.733, 0, 1, 1.789, 0.333, 1.844, 0.667, 1.9, 1, 2, 1.933, 0, 2, 2, 0, 1, 2.044, 0.333, 2.089, 0.667, 2.133, 1, 2, 2.2, 0, 2, 2.3, 0, 1, 2.333, 0.333, 2.367, 0.667, 2.4, 1, 2, 2.433, 0, 2, 2.567, 0, 1, 2.6, 0.333, 2.634, 0.667, 2.667, 1, 2, 2.7, 0, 2, 2.8, 0, 1, 2.856, 0.333, 2.911, 0.667, 2.967, 1, 2, 3, 0, 2, 3.067, 0, 1, 3.111, 0.333, 3.156, 0.667, 3.2, 1, 2, 3.233, 0, 2, 3.367, 0, 1, 3.4, 0.333, 3.434, 0.667, 3.467, 1, 2, 3.5, 0, 2, 3.6, 0, 1, 3.644, 0.333, 3.689, 0.667, 3.733, 1, 2, 3.767, 0, 2, 3.8, 0, 1, 3.856, 0.333, 3.911, 0.667, 3.967, 1, 2, 4, 0, 0, 4.2, 1, 2, 4.233, 0, 2, 4.333, 0, 1, 4.378, 0.333, 4.422, 0.667, 4.467, 1, 2, 4.5, 0, 2, 4.6, 0, 1, 4.644, 0.333, 4.689, 0.667, 4.733, 1, 2, 4.767, 0, 2, 4.867, 0, 1, 4.911, 0.333, 4.956, 0.667, 5, 1, 2, 5.033, 0, 2, 5.133, 0, 1, 5.166, 0.333, 5.2, 0.667, 5.233, 1, 2, 5.267, 0, 2, 5.333, 0, 1, 5.389, 0.333, 5.444, 0.667, 5.5, 1, 2, 5.533, 0, 2, 5.633, 0, 1, 5.7, 0.333, 5.766, 0.667, 5.833, 1, 2, 5.867, 0, 2, 6, 0, 0, 6.267, 1, 2, 6.3, 0, 2, 6.4, 0, 0, 6.667, 1, 2, 6.7, 0, 2, 6.867, 0, 0, 7.067, 1, 2, 7.1, 0, 2, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineD", "Segments": [0, 0, 2, 0.033, 0, 0, 0.167, 1, 2, 0.2, 0, 2, 0.367, 0, 1, 0.411, 0.333, 0.456, 0.667, 0.5, 1, 2, 0.533, 0, 2, 0.633, 0, 1, 0.678, 0.333, 0.722, 0.667, 0.767, 1, 2, 0.8, 0, 2, 0.9, 0, 1, 0.944, 0.333, 0.989, 0.667, 1.033, 1, 2, 1.067, 0, 2, 1.167, 0, 1, 1.211, 0.333, 1.256, 0.667, 1.3, 1, 2, 1.333, 0, 2, 1.4, 0, 1, 1.433, 0.333, 1.467, 0.667, 1.5, 1, 2, 1.533, 0, 2, 1.6, 0, 1, 1.644, 0.333, 1.689, 0.667, 1.733, 1, 2, 1.767, 0, 2, 1.9, 0, 1, 1.933, 0.333, 1.967, 0.667, 2, 1, 2, 2.033, 0, 2, 2.133, 0, 1, 2.189, 0.333, 2.244, 0.667, 2.3, 1, 2, 2.333, 0, 2, 2.4, 0, 1, 2.456, 0.333, 2.511, 0.667, 2.567, 1, 2, 2.6, 0, 2, 2.667, 0, 1, 2.711, 0.333, 2.756, 0.667, 2.8, 1, 2, 2.833, 0, 2, 2.967, 0, 1, 3, 0.333, 3.034, 0.667, 3.067, 1, 2, 3.1, 0, 2, 3.2, 0, 1, 3.256, 0.333, 3.311, 0.667, 3.367, 1, 2, 3.4, 0, 2, 3.467, 0, 1, 3.511, 0.333, 3.556, 0.667, 3.6, 1, 2, 3.633, 0, 2, 3.733, 0, 1, 3.789, 0.333, 3.844, 0.667, 3.9, 1, 2, 3.933, 0, 1, 3.966, 0.333, 4, 0.667, 4.033, 1, 2, 4.067, 0, 2, 4.133, 0, 2, 4.167, 0, 1, 4.222, 0.333, 4.278, 0.667, 4.333, 1, 2, 4.367, 0, 2, 4.467, 0, 1, 4.511, 0.333, 4.556, 0.667, 4.6, 1, 2, 4.633, 0, 2, 4.733, 0, 1, 4.778, 0.333, 4.822, 0.667, 4.867, 1, 2, 4.9, 0, 2, 5, 0, 1, 5.044, 0.333, 5.089, 0.667, 5.133, 1, 2, 5.167, 0, 2, 5.233, 0, 1, 5.266, 0.333, 5.3, 0.667, 5.333, 1, 2, 5.367, 0, 2, 5.5, 0, 1, 5.544, 0.333, 5.589, 0.667, 5.633, 1, 2, 5.667, 0, 2, 5.767, 0, 0, 6, 1, 2, 6.033, 0, 2, 6.2, 0, 0, 6.467, 1, 2, 6.5, 0, 2, 6.633, 0, 0, 6.9, 1, 2, 6.933, 0, 2, 7.033, 0, 0, 7.133, 1]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyW", "Segments": [0, 0, 0, 0.1, -0.6, 0, 0.267, 0.4, 0, 0.433, -0.5, 0, 0.6, 0.6, 0, 0.667, -0.5, 0, 0.8, 0.4, 0, 0.933, 0, 2, 1.1, 0, 0, 1.2, -0.3, 0, 1.333, 0, 0, 1.5, -0.3, 0, 1.533, 0.4, 0, 1.667, -0.5, 0, 1.867, 0.6, 0, 1.933, -0.5, 0, 2.033, 0.4, 0, 2.2, 0, 2, 2.333, 0, 0, 2.433, -0.3, 0, 2.6, 0.4, 0, 2.733, -0.5, 0, 2.933, 0.6, 0, 3, -0.5, 0, 3.1, 0.4, 0, 3.233, 0, 2, 3.4, 0, 0, 3.5, -0.3, 0, 3.667, 0, 0, 3.8, -0.3, 2, 4.1, -0.3, 0, 4.267, -0.5, 0, 4.433, 0.6, 0, 4.5, -0.5, 0, 4.633, 0.4, 0, 4.767, 0, 2, 4.933, 0, 0, 5.033, -0.3, 0, 5.167, 0, 0, 5.333, -0.3, 0, 5.433, 0.4, 0, 5.567, -0.5, 0, 5.767, 0.4, 0, 5.967, -0.3, 0, 6.2, 0.3, 0, 6.367, -0.3, 0, 6.633, 0.4, 0, 6.933, -0.155, 1, 7, -0.155, 7.066, -0.041, 7.133, -0.009]}, {"Target": "Parameter", "Id": "ParamPandaBodyX", "Segments": [0, 0, 1, 0.211, 4.341, 0.422, 4.939, 0.633, 4.939, 0, 1.333, -7.021, 0, 2.667, 12.347, 0, 4.067, -3.668, 0, 5.2, 10, 0, 6.367, -11.213, 1, 6.622, -11.213, 6.878, -5.951, 7.133, -0.686]}, {"Target": "Parameter", "Id": "ParamPandaBodyY", "Segments": [0, 0, 0, 0.967, 4.225, 0, 1.967, -3.672, 0, 3.033, 6.904, 0, 4.733, -3.672, 0, 6.033, 10.581, 1, 6.4, 10.581, 6.766, 0.613, 7.133, 0.027]}, {"Target": "Parameter", "Id": "ParamPandaBodyZ", "Segments": [0, 0, 1, 0.356, 4.01, 0.711, 9.125, 1.067, 9.125, 0, 2.967, -7.987, 0, 4.433, 10.148, 0, 5.9, -5.585, 1, 6.311, -5.585, 6.722, -4.805, 7.133, -0.368]}, {"Target": "Parameter", "Id": "ParamCannonBodyX", "Segments": [0, 0, 1, 0.333, -3.65, 0.667, -4.826, 1, -4.826, 0, 2.867, 3.384, 0, 5.4, -3.842, 0, 6.467, 4.761, 1, 6.689, 4.761, 6.911, 2.76, 7.133, 0.362]}, {"Target": "Parameter", "Id": "ParamCannonBodyY", "Segments": [0, 0, 1, 0.322, -1.187, 0.645, -1.599, 0.967, -1.599, 0, 2.467, 1.177, 0, 4.867, -1.157, 0, 6.633, 1.236, 1, 6.8, 1.236, 6.966, 0.725, 7.133, 0.122]}, {"Target": "Parameter", "Id": "ParamCannonZ", "Segments": [0, 0, 1, 0.489, 0.977, 0.978, 8.73, 1.467, 8.73, 0, 2.733, -2.048, 0, 4.267, 7.986, 0, 6.067, -0.523, 1, 6.422, -0.523, 6.778, -0.72, 7.133, -0.064]}, {"Target": "Parameter", "Id": "ParamCannonGaY", "Segments": [0, 0, 1, 0.378, -1.371, 0.755, -1.392, 1.133, -1.392, 0, 2.6, 3.53, 0, 4.5, 0, 0, 5.933, 4, 1, 6.333, 4, 6.733, 1.625, 7.133, 0.123]}, {"Target": "Parameter", "Id": "ParamCannonCupY", "Segments": [0, 0, 1, 0.433, 0.068, 0.867, 0.126, 1.3, 0.126, 0, 2.8, -0.04, 0, 4.067, 0.056, 0, 5.367, -0.123, 1, 5.956, -0.123, 6.544, -0.095, 7.133, -0.005]}, {"Target": "Parameter", "Id": "ParamCannonCupZ", "Segments": [0, 0, 0, 0.767, -0.149, 0, 2.067, 0.113, 0, 3.367, -0.04, 0, 4.9, 0.097, 0, 6.067, -0.075, 1, 6.422, -0.075, 6.778, -0.004, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamCannonHandZ", "Segments": [0, 0, 0, 0.8, -1.213, 0, 1.4, 1.258, 0, 2.667, -0.396, 0, 3.733, 0.557, 0, 5.967, -1.119, 1, 6.356, -1.119, 6.744, -0.061, 7.133, -0.003]}, {"Target": "Parameter", "Id": "ParamParamStrongCatZ", "Segments": [0, 0, 1, 0.956, -1.227, 1.911, -1.501, 2.867, -1.501, 0, 5.6, 1.72, 1, 6.111, 1.72, 6.622, 0.715, 7.133, 0.043]}, {"Target": "Parameter", "Id": "ParamParamSCBodyZ", "Segments": [0, 0, 0, 1.533, 1.077, 0, 4.2, -1.277, 0, 6.733, 1.754, 1, 6.866, 1.754, 7, 0.259, 7.133, 0.03]}, {"Target": "Parameter", "Id": "ParamSCDishY", "Segments": [0, 0, 1, 0.9, -2.538, 1.8, -3.309, 2.7, -3.309, 0, 5.1, 3.629, 1, 5.778, 3.629, 6.455, 1.997, 7.133, 0.094]}, {"Target": "Parameter", "Id": "ParamSCDishZ", "Segments": [0, 0, 0, 1.933, 3.433, 0, 4.367, 0.338, 0, 5.8, 4.371, 1, 6.244, 4.371, 6.689, 0.211, 7.133, 0.008]}, {"Target": "Parameter", "Id": "ParamSCCupZ", "Segments": [0, 0, 1, 0.333, -2.823, 0.667, -3.653, 1, -3.653, 0, 2.533, 3.433, 0, 4.8, -4, 0, 6.333, 4.371, 1, 6.6, 4.371, 6.866, 2.511, 7.133, 0.281]}, {"Target": "Parameter", "Id": "MB_yanwubaozha", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "MB_DRFWXZKTMD", "Segments": [0, 1, 0, 7.133, 1]}, {"Target": "Parameter", "Id": "ParamAllSizeFix", "Segments": [0, 1, 0, 7.133, 1]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamBGHide", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamBG2Hide", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamBGX", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamBGY", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN3", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamBlackY", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamBlackCollar", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamBlackOrder", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamWhiteIN", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamCHHide", "Segments": [0, 1, 0, 7.133, 1]}, {"Target": "Parameter", "Id": "ParamDeskHide", "Segments": [0, 1, 0, 7.133, 1]}, {"Target": "Parameter", "Id": "ParamStoolHide", "Segments": [0, 1, 0, 7.133, 1]}, {"Target": "Parameter", "Id": "ParamCupDesk", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamCHX", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamCHY", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamCHZ", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamChaSize", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamCcharacterZ", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionX", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionY", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionX", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionY", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamAllZ", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamAllSize", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamALLSize2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamDeskShow", "Segments": [0, 10, 0, 7.133, 10]}, {"Target": "Parameter", "Id": "ParamStrongCatShow", "Segments": [0, 10, 0, 7.133, 10]}, {"Target": "Parameter", "Id": "ParamCannonShow", "Segments": [0, 10, 0, 7.133, 10]}, {"Target": "Parameter", "Id": "ParamLightPositionX", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamFixT", "Segments": [0, 1, 0, 7.133, 1]}, {"Target": "Parameter", "Id": "ParamFlap", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamScare", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamPupilExp", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeSmileL", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeSmileR", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpen2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamMouthType", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamMouthSize", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamMouthY", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeEmotion", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamBlackFace", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamTeethLight", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamHeart2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamMark", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamShameLine", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamMarkShake", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLY", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeL", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRY", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeR", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeRLightOpen", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLightLine1", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLightLine2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLightLine3", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLightShine", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo1", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo3", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1Y", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow1", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamTearLight", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamTears", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamAngleH", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamAngleS", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamArmHandLAngle", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerLAngle", "Segments": [0, 10, 0, 7.133, 10]}, {"Target": "Parameter", "Id": "ParamArmLowerLH", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerLAngle", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamHandT2L", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamFanOpenR", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamChili", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamChiliX", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperRH", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamHandR", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRAngle", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRY", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamHand_Cl", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamHandT1R", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamHandRCup", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamHandRMail", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "Segments": [0, 1, 0, 7.133, 1]}, {"Target": "Parameter", "Id": "ParamHandLIQY1", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY3", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamHandCupZ", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamHandCupY", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechW", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamShoulderL", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLegR1X", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Y", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Y", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Z", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Z", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Z", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Y", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Y", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Y", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLegLF", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRHide", "Segments": [0, 1, 0, 7.133, 1]}, {"Target": "Parameter", "Id": "ParamMJRFlap", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuR", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuR", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuR", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuR", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRInput", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamMRCupSet", "Segments": [0, 1, 0, 7.133, 1]}, {"Target": "Parameter", "Id": "ParamMalpositionManjuuR", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuREyeOpen", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRArmB", "Segments": [0, 30, 0, 7.133, 30]}, {"Target": "Parameter", "Id": "ParamManjuuRSigh", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow1", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow3", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow4", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowB", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamMRCupFZ", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX1", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqH", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX1", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX3", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLHide", "Segments": [0, 1, 0, 7.133, 1]}, {"Target": "Parameter", "Id": "ParamMJLSigh", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuL", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuL", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuL", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamMjLFlip", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionZManjuuL", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLEyeOpen", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyW", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuL", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamClawFX", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamClawFY", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamClawBX", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamClawBY", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUHide", "Segments": [0, 1, 0, 7.133, 1]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuU", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuU", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuU", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamPandaHide", "Segments": [0, 1, 0, 7.133, 1]}, {"Target": "Parameter", "Id": "ParamPositionXPanda", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionYPanda", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamSizePanda", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2Panda", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegFZ", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegBZ", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY1", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY2", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY3", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupIce", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupZ", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupInput", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamSCDishRO", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamSCCupRO", "Segments": [0, 0, 0, 7.133, 0]}, {"Target": "Parameter", "Id": "ParamSCCupY", "Segments": [0, 0, 0, 7.133, 0]}], "UserData": [{"Time": 0.0, "Value": ""}]}