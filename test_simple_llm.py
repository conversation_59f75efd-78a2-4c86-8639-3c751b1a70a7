#!/usr/bin/env python3
"""
简单测试LLM配置
直接测试修复后的配置
"""

import json
import sys
import os

def test_direct_llm_config():
    """直接测试LLM配置"""
    print("🔍 直接测试LLM配置")
    print("=" * 60)
    
    try:
        from openai import OpenAI
        
        # 读取配置
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        # 使用llm配置（这是main_window.py实际使用的配置）
        llm_config = config.get("llm", {})
        api_config = llm_config.get("api_config", {})
        default_params = llm_config.get("default_params", {})
        
        print(f"📋 LLM配置:")
        print(f"   - API地址: {api_config.get('base_url')}")
        print(f"   - 模型: {api_config.get('model')}")
        print(f"   - API密钥: {'已设置' if api_config.get('api_key') else '未设置'}")
        
        if not api_config.get("api_key"):
            print("❌ API密钥未设置")
            return False
        
        # 创建客户端
        client = OpenAI(
            base_url=api_config["base_url"],
            api_key=api_config["api_key"],
            timeout=api_config.get("timeout", 30.0)
        )
        
        # 准备请求参数
        request_params = {
            "messages": [{"role": "user", "content": "你好"}],
            "model": api_config["model"],
            **{k: v for k, v in default_params.items() if v is not None}
        }
        
        print("📤 发送的请求格式:")
        print(json.dumps(request_params, indent=2, ensure_ascii=False))
        
        # 发送请求
        response = client.chat.completions.create(**request_params)
        
        print("\n📥 收到回复:")
        if hasattr(response, 'choices') and response.choices:
            choice = response.choices[0]
            if hasattr(choice, 'message') and hasattr(choice.message, 'content'):
                content = choice.message.content
                role = choice.message.role
                print(f"✅ 成功收到回复: {content}")
                print(f"🎯 角色: {role}")
                return True
        
        print("❌ 未能提取回复内容")
        return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_direct_llm_config()
    
    if success:
        print("\n🎉 LLM配置修复成功！")
        print("💡 现在快速输入应该能正常工作了")
        print("🚀 请重新启动Live2D应用并测试快速输入功能")
    else:
        print("\n❌ 配置仍有问题，需要进一步检查")
