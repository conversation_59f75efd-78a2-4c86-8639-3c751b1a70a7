#!/usr/bin/env python3
"""
测试conversation_settings修复
验证LLM客户端正确使用max_tokens参数
"""

import json
import sys
import os

def test_llm_client_initialization():
    """测试LLM客户端初始化"""
    print("🔍 测试LLM客户端初始化")
    print("=" * 60)
    
    try:
        # 添加项目路径
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from dialogue_system.config.config_manager import ConfigManager
        from dialogue_system.core.llm_client import LLMClient
        
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 创建LLM客户端
        llm_client = LLMClient(config_manager)
        
        print("✅ LLM客户端初始化成功")
        
        print(f"\n📋 conversation_settings:")
        for key, value in llm_client.conversation_settings.items():
            print(f"   - {key}: {value}")
        
        print(f"\n📋 default_params:")
        for key, value in llm_client.default_params.items():
            if value is not None:
                print(f"   - {key}: {value}")
        
        # 检查关键参数
        conv_max_tokens = llm_client.conversation_settings.get("max_tokens")
        default_max_tokens = llm_client.default_params.get("max_tokens")
        
        print(f"\n🔍 max_tokens 对比:")
        print(f"   - conversation_settings.max_tokens: {conv_max_tokens}")
        print(f"   - default_params.max_tokens: {default_max_tokens}")
        
        if conv_max_tokens == default_max_tokens == 65535:
            print("✅ max_tokens 配置正确")
            return True
        else:
            print("❌ max_tokens 配置不一致")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_update():
    """测试配置更新"""
    print("\n🔍 测试配置更新")
    print("=" * 60)
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from dialogue_system.config.config_manager import ConfigManager
        from dialogue_system.core.llm_client import LLMClient
        
        # 初始化
        config_manager = ConfigManager()
        llm_client = LLMClient(config_manager)
        
        print("📋 更新前的参数:")
        print(f"   - conversation_settings.max_tokens: {llm_client.conversation_settings.get('max_tokens')}")
        print(f"   - default_params.max_tokens: {llm_client.default_params.get('max_tokens')}")
        
        # 模拟预设更新（类似main_window.py中的操作）
        config_update = {
            "conversation_settings": {
                "system_prompt": "你是我的ai女儿",
                "max_history_length": 20,
                "save_history": True
                # 注意：这里没有包含max_tokens，应该保持原值
            }
        }
        
        print(f"\n🔄 应用配置更新: {config_update}")
        llm_client.update_config(config_update)
        
        print("\n📋 更新后的参数:")
        print(f"   - conversation_settings.max_tokens: {llm_client.conversation_settings.get('max_tokens')}")
        print(f"   - default_params.max_tokens: {llm_client.default_params.get('max_tokens')}")
        
        # 检查max_tokens是否保持正确值
        if llm_client.default_params.get('max_tokens') == 65535:
            print("✅ 配置更新后max_tokens保持正确")
            return True
        else:
            print("❌ 配置更新后max_tokens被错误修改")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chat_request():
    """测试聊天请求参数"""
    print("\n🔍 测试聊天请求参数")
    print("=" * 60)
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from dialogue_system.config.config_manager import ConfigManager
        from dialogue_system.core.llm_client import LLMClient
        
        # 初始化
        config_manager = ConfigManager()
        llm_client = LLMClient(config_manager)
        
        # 模拟预设更新
        config_update = {
            "conversation_settings": {
                "system_prompt": "你是我的ai女儿",
                "max_history_length": 20,
                "save_history": True
            }
        }
        llm_client.update_config(config_update)
        
        # 检查聊天请求会使用什么参数
        print("📋 聊天请求将使用的参数:")
        for key, value in llm_client.default_params.items():
            if value is not None:
                print(f"   - {key}: {value}")
        
        # 特别检查max_tokens
        max_tokens = llm_client.default_params.get('max_tokens')
        print(f"\n🎯 关键检查 - max_tokens: {max_tokens}")
        
        if max_tokens == 65535:
            print("✅ 聊天请求将使用正确的max_tokens")
            return True
        else:
            print("❌ 聊天请求将使用错误的max_tokens")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 测试初始化
    success1 = test_llm_client_initialization()
    
    # 测试配置更新
    success2 = test_config_update()
    
    # 测试聊天请求
    success3 = test_chat_request()
    
    if success1 and success2 and success3:
        print("\n🎉 conversation_settings修复成功！")
        print("💡 现在快速输入应该能正确使用65535 max_tokens了")
        print("🚀 请重新启动Live2D应用并测试")
    else:
        print("\n❌ 仍有问题，需要进一步调试")
