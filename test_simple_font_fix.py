#!/usr/bin/env python3
"""
简单测试字体警告修复
"""

import os
import sys

def test_font_fix():
    """测试字体修复"""
    print("🔍 测试字体警告修复")
    print("=" * 50)
    
    # 设置环境变量抑制Qt字体警告
    os.environ["QT_LOGGING_RULES"] = "qt.qpa.fonts.warning=false"
    print("✅ 设置环境变量: QT_LOGGING_RULES=qt.qpa.fonts.warning=false")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtGui import QFont
        
        print("✅ Qt模块导入成功")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        print("✅ QApplication创建成功")
        
        # 设置默认字体
        default_font = QFont("Microsoft YaHei", 9)
        app.setFont(default_font)
        print("✅ 默认字体设置成功")
        
        print("\n🎉 字体修复测试完成")
        print("💡 如果没有看到DirectWrite字体错误，说明修复成功")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_font_fix()
    
    if success:
        print("\n✅ 字体警告修复成功！")
        print("🚀 现在启动Live2D应用应该不会看到字体警告了")
    else:
        print("\n❌ 字体警告修复失败")
