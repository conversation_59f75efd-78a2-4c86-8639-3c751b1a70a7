{"Version": 3, "Meta": {"Duration": 10.833, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 325, "TotalSegmentCount": 3260, "TotalPointCount": 38060, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param109", "Segments": [0, 0, 2, 0.333, 0, 0, 0.833, 30, 2, 9.9, 30, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "Param79", "Segments": [0, -0.065, 0, 0.3, 0.093, 0, 0.367, 0.079, 0, 0.767, 0.534, 0, 1.6, -0.383, 0, 2, 0.142, 0, 2.467, -0.212, 0, 2.7, -0.094, 0, 2.933, -2.937, 0, 3.3, 3.957, 0, 3.733, -2.995, 0, 4.133, 1.824, 0, 4.567, -1.089, 0, 5, 0.616, 0, 5.433, -0.76, 0, 5.933, 0.384, 0, 6.233, 0.167, 0, 6.467, 1.856, 0, 6.7, -1.376, 0, 6.9, 0.209, 0, 7.133, -0.08, 0, 7.5, 0.192, 0, 8.1, -0.248, 1, 8.178, -0.248, 8.255, -0.195, 8.333, -0.076, 1, 8.422, 0.059, 8.511, 0.133, 8.6, 0.133, 0, 9, -0.091, 0, 9.3, 0.018, 0, 9.667, -0.613, 0, 10.267, 0.318, 0, 10.4, 0.309, 0, 10.533, 0.354, 1, 10.633, 0.354, 10.733, 0.039, 10.833, -0.119]}, {"Target": "Parameter", "Id": "Param82", "Segments": [0, -0.155, 0, 0.133, -0.224, 0, 0.4, 0.144, 0, 0.433, 0.135, 0, 0.5, 0.154, 0, 0.567, 0.112, 0, 0.967, 1.129, 0, 1.833, -0.438, 0, 2.167, 0.233, 0, 2.6, -0.439, 0, 2.833, 0.587, 0, 3.1, -6.513, 0, 3.467, 6.839, 0, 3.867, -4.897, 0, 4.267, 3.082, 0, 4.7, -1.858, 0, 5.133, 1.004, 0, 5.6, -1.333, 0, 6.067, 0.233, 0, 6.367, -0.427, 0, 6.633, 3.976, 0, 6.867, -1.915, 0, 7.133, 1.297, 0, 7.433, 0.029, 0, 7.7, 0.752, 1, 7.911, 0.752, 8.122, 0.291, 8.333, -0.184, 1, 8.344, -0.209, 8.356, -0.191, 8.367, -0.191, 0, 8.733, 0.236, 0, 9.133, -0.144, 0, 9.467, 0.155, 0, 9.867, -1.262, 0, 10.367, -0.039, 0, 10.467, -0.047, 0, 10.767, 0.404, 1, 10.789, 0.404, 10.811, 0.34, 10.833, 0.265]}, {"Target": "Parameter", "Id": "Param80", "Segments": [0, -0.155, 0, 0.133, -0.224, 0, 0.4, 0.144, 0, 0.433, 0.135, 0, 0.5, 0.154, 0, 0.567, 0.112, 0, 0.967, 1.129, 0, 1.833, -0.438, 0, 2.167, 0.233, 0, 2.6, -0.439, 0, 2.833, 0.587, 0, 3.1, -6.513, 0, 3.467, 6.839, 0, 3.867, -4.897, 0, 4.267, 3.082, 0, 4.7, -1.858, 0, 5.133, 1.004, 0, 5.6, -1.333, 0, 6.067, 0.233, 0, 6.367, -0.427, 0, 6.633, 3.976, 0, 6.867, -1.915, 0, 7.133, 1.297, 0, 7.433, 0.029, 0, 7.7, 0.752, 1, 7.911, 0.752, 8.122, 0.291, 8.333, -0.184, 1, 8.344, -0.209, 8.356, -0.191, 8.367, -0.191, 0, 8.733, 0.236, 0, 9.133, -0.144, 0, 9.467, 0.155, 0, 9.867, -1.262, 0, 10.367, -0.039, 0, 10.467, -0.047, 0, 10.767, 0.404, 1, 10.789, 0.404, 10.811, 0.34, 10.833, 0.265]}, {"Target": "Parameter", "Id": "Param81", "Segments": [0, -0.001, 0, 0.233, -0.123, 0, 0.467, 0.039, 0, 0.5, 0.034, 0, 0.533, 0.051, 0, 0.767, -0.232, 0, 1.1, 0.349, 0, 1.433, -0.178, 0, 1.7, 0.111, 0, 1.967, -0.221, 0, 2.3, 0.274, 0, 2.633, -0.192, 0, 2.967, 1.556, 0, 3.233, -4.041, 0, 3.567, 4.854, 0, 3.967, -3.749, 0, 4.333, 2.548, 0, 4.733, -1.499, 0, 5.167, 0.844, 0, 5.667, -0.513, 0, 6.1, 0.339, 0, 6.5, -0.993, 0, 6.767, 2.251, 0, 7.033, -2.087, 0, 7.3, 1.286, 0, 7.6, -0.687, 0, 7.867, 0.465, 0, 8.2, -0.233, 1, 8.244, -0.233, 8.289, -0.196, 8.333, -0.123, 1, 8.378, -0.051, 8.422, -0.014, 8.467, -0.014, 0, 8.567, -0.019, 0, 8.867, 0.124, 0, 9.233, -0.117, 0, 9.633, 0.325, 0, 9.967, -0.438, 0, 10.333, 0.237, 0, 10.633, -0.148, 1, 10.7, -0.148, 10.766, 0.081, 10.833, 0.196]}, {"Target": "Parameter", "Id": "ParamHandL2Display", "Segments": [0, 0, 2, 0.333, 0, 2, 1.8, 0, 2, 1.833, 30, 2, 8.967, 30, 2, 9, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandL5Display", "Segments": [0, 30, 2, 0.333, 30, 2, 1.8, 30, 2, 1.833, 0, 2, 8.967, 0, 2, 9, 30, 2, 10.467, 30, 2, 10.833, 30]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.333, 0, 2, 1.333, 0, 2, 1.367, -30, 2, 9.433, -30, 2, 9.467, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandR4Display", "Segments": [0, 0, 2, 0.333, 0, 2, 1.833, 0, 2, 1.867, 30, 2, 9.367, 30, 2, 9.4, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandR5Display", "Segments": [0, 30, 2, 0.333, 30, 2, 1.833, 30, 2, 1.867, 0, 2, 9.367, 0, 2, 9.4, 30, 2, 10.467, 30, 2, 10.833, 30]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 2, 0.333, 0, 2, 2.633, 0, 2, 2.733, 0, 0, 2.767, 30, 2, 3.067, 30, 2, 3.667, 30, 2, 6.5, 30, 1, 6.656, 30, 6.811, 25.551, 6.967, 15, 1, 6.978, 14.246, 6.989, 0, 7, 0, 2, 8.333, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 2, 0.333, 0, 2, 1.067, 0, 0, 1.167, -19, 0, 1.267, 16, 0, 1.333, -12, 0, 1.467, 11, 0, 1.6, 0, 2, 2.333, 0, 0, 2.833, 30, 1, 3, 30, 3.166, 20, 3.333, 0, 1, 3.5, -20, 3.666, -30, 3.833, -30, 1, 4, -30, 4.166, -20, 4.333, 0, 1, 4.5, 20, 4.666, 30, 4.833, 30, 1, 5, 30, 5.166, 20, 5.333, 0, 1, 5.5, -20, 5.666, -30, 5.833, -30, 1, 6, -30, 6.166, -20, 6.333, 0, 1, 6.5, 20, 6.666, 30, 6.833, 30, 1, 6.9, 30, 6.966, 21.496, 7.033, 0, 1, 7.066, -10.748, 7.1, -19, 7.133, -19, 0, 7.233, 16, 0, 7.3, -12, 0, 7.433, 11, 0, 7.567, 0, 2, 8.333, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 2, 0.333, 0, 2, 1.067, 0, 0, 1.167, -1, 0, 1.267, 10, 0, 1.333, -10, 1, 1.378, -10, 1.422, -10.102, 1.467, -9, 1, 1.511, -7.898, 1.556, 0, 1.6, 0, 0, 2.333, -30, 1, 2.5, -30, 2.666, -20, 2.833, 0, 1, 3, 20, 3.166, 30, 3.333, 30, 1, 3.5, 30, 3.666, 20, 3.833, 0, 1, 4, -20, 4.166, -30, 4.333, -30, 1, 4.5, -30, 4.666, -20, 4.833, 0, 1, 5, 20, 5.166, 30, 5.333, 30, 1, 5.5, 30, 5.666, 20, 5.833, 0, 1, 6, -20, 6.166, -30, 6.333, -30, 0, 7.033, 0, 0, 7.133, -1, 0, 7.233, 10, 0, 7.3, -10, 1, 7.344, -10, 7.389, -10.102, 7.433, -9, 1, 7.478, -7.898, 7.522, 0, 7.567, 0, 2, 8.333, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "Segments": [0, 0, 2, 0.333, 0, 2, 1.333, 0, 1, 1.522, 0, 1.711, 11.006, 1.9, 18.302, 1, 2.156, 28.173, 2.411, 30, 2.667, 30, 2, 3.667, 30, 2, 8.133, 30, 1, 8.2, 30, 8.266, 30.191, 8.333, 29.339, 1, 8.478, 27.492, 8.622, 24.104, 8.767, 19.104, 1, 8.889, 14.874, 9.011, 0, 9.133, 0, 2, 9.467, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "Segments": [0, 0, 2, 0.333, 0, 2, 1.333, 0, 2, 2.3, 0, 2, 2.667, 0, 2, 3.667, 0, 0, 4.067, -3.42, 0, 8.133, 0, 2, 8.333, 0, 2, 8.5, 0, 2, 9.467, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "Segments": [0, 0, 2, 0.333, 0, 0, 0.833, -1, 2, 1.167, -1, 0, 1.467, 0.969, 0, 1.833, -4.637, 0, 2.233, 1.531, 1, 2.378, 1.531, 2.522, 0.311, 2.667, -0.76, 1, 2.767, -1.501, 2.867, -1.54, 2.967, -1.54, 1, 3.2, -1.54, 3.434, -1.584, 3.667, -1.48, 1, 4.089, -1.291, 4.511, 1.047, 4.933, 1.047, 1, 5.3, 1.047, 5.666, 1.034, 6.033, 0.826, 1, 6.211, 0.725, 6.389, -1.257, 6.567, -1.267, 1, 7.089, -1.296, 7.611, -1.3, 8.133, -1.3, 1, 8.2, -1.3, 8.266, -0.92, 8.333, -0.05, 1, 8.411, 0.964, 8.489, 1.531, 8.567, 1.531, 1, 8.667, 1.531, 8.767, 1.438, 8.867, -0.148, 1, 8.945, -1.381, 9.022, -4.637, 9.1, -4.637, 0, 9.467, -1, 2, 9.967, -1, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "Segments": [0, -12.6, 2, 0.333, -12.6, 1, 0.5, -12.6, 0.666, -5.895, 0.833, -0.921, 1, 0.878, 0.406, 0.922, 0, 0.967, 0, 2, 1.333, 0, 2, 2.3, 0, 0, 2.8, -6, 0, 3.667, 0, 0, 4.733, -2.7, 0, 6.133, 4.476, 0, 8.133, 0, 2, 8.333, 0, 2, 8.5, 0, 2, 9.467, 0, 2, 9.833, 0, 1, 9.878, 0, 9.922, 0.406, 9.967, -0.921, 1, 10.134, -5.895, 10.3, -12.6, 10.467, -12.6, 2, 10.833, -12.6]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, 7, 2, 0.333, 7, 1, 1.311, 4.667, 2.289, 2.333, 3.267, 0, 2, 4.767, 0, 2, 8.333, 0, 2, 8.5, 0, 0, 10.467, 7, 2, 10.833, 7]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "Segments": [0, -30, 2, 0.333, -30, 2, 0.667, -30, 0, 1.033, 30, 1, 1.133, 30, 1.233, 14.884, 1.333, 0, 1, 1.489, -23.152, 1.644, -30, 1.8, -30, 0, 2.233, 30, 0, 2.533, 0, 2, 2.833, 0, 0, 3.833, 10, 0, 4.833, 0, 0, 5.833, 10, 0, 6.833, 0, 0, 7.3, 20, 1, 7.611, 20, 7.922, 17.136, 8.233, 10, 1, 8.266, 9.235, 8.3, 5.706, 8.333, 0.005, 1, 8.411, -13.3, 8.489, -30, 8.567, -30, 0, 8.967, 10, 0, 9, -30, 0, 9.667, 30, 0, 10.467, -30, 2, 10.833, -30]}, {"Target": "Parameter", "Id": "ParamHandLChange", "Segments": [0, 0, 2, 0.333, 0, 2, 1.333, 0, 0, 1.733, -30, 2, 1.833, -30, 1, 1.9, -30, 1.966, -12.922, 2.033, 0, 1, 2.155, 23.69, 2.278, 30, 2.4, 30, 0, 2.767, 0, 2, 3.667, 0, 2, 8.133, 0, 1, 8.2, 0, 8.266, -6.13, 8.333, -17.016, 1, 8.389, -26.088, 8.444, -30, 8.5, -30, 2, 8.567, -30, 2, 8.967, -30, 2, 9.067, -30, 0, 9.467, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "Segments": [0, -7.4, 2, 0.333, -7.4, 0, 0.833, 2.97, 0, 1.067, -0.6, 1, 1.3, -0.6, 1.534, -0.567, 1.767, -0.26, 1, 1.856, -0.143, 1.944, 2.8, 2.033, 2.8, 0, 2.4, -4.4, 1, 2.522, -4.4, 2.645, 1.409, 2.767, 2.233, 1, 2.956, 3.506, 3.144, 3.508, 3.333, 3.508, 0, 4.167, -0.235, 0, 5.033, 1.816, 0, 5.833, 1.123, 1, 6.133, 1.123, 6.433, 1.203, 6.733, 2.113, 1, 7.055, 3.09, 7.378, 4.379, 7.7, 4.379, 1, 7.911, 4.379, 8.122, 4.15, 8.333, 3.538, 1, 8.389, 3.378, 8.444, 3.419, 8.5, 2.8, 1, 8.611, 1.561, 8.722, -11.778, 8.833, -11.778, 0, 9.467, -0.6, 2, 9.733, -0.6, 0, 10.467, -7.4, 2, 10.833, -7.4]}, {"Target": "Parameter", "Id": "ParamHandL_SZ", "Segments": [0, 0, 2, 0.333, 0, 1, 0.389, -4.333, 0.444, -8.667, 0.5, -13, 0, 0.867, 23, 0, 1.3, -30, 2, 1.333, -30, 1, 1.622, -30, 1.911, -28.209, 2.2, -23.462, 1, 2.233, -22.914, 2.267, -0.984, 2.3, 0, 1, 2.422, 3.606, 2.545, 6.559, 2.667, 9.082, 1, 2.978, 15.504, 3.289, 18.137, 3.6, 18.137, 1, 3.622, 18.137, 3.645, 18.841, 3.667, 17.86, 1, 4.111, -1.761, 4.556, -23.462, 5, -23.462, 0, 8.133, 9.082, 1, 8.2, 9.082, 8.266, 8.687, 8.333, 6.809, 1, 8.389, 5.243, 8.444, 3.114, 8.5, 0, 1, 8.533, -1.868, 8.567, -22.914, 8.6, -23.462, 1, 8.889, -28.209, 9.178, -30, 9.467, -30, 2, 9.5, -30, 0, 9.933, 23, 0, 10.3, -13, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandL_ZZ", "Segments": [0, 0, 2, 0.333, 0, 1, 0.422, 10, 0.511, 20, 0.6, 30, 0, 1.067, 0, 2, 1.3, 0, 2, 1.333, 0, 2, 2.3, 0, 2, 2.667, 0, 2, 3.667, 0, 2, 8.133, 0, 2, 8.333, 0, 2, 8.5, 0, 2, 9.467, 0, 2, 9.5, 0, 2, 9.733, 0, 0, 10.2, 30, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandL_WMZ", "Segments": [0, 0, 2, 0.333, 0, 1, 0.411, 10, 0.489, 20, 0.567, 30, 2, 1.3, 30, 2, 1.333, 30, 1, 1.655, 30, 1.978, 22.254, 2.3, 0, 1, 2.367, -4.604, 2.433, -18.076, 2.5, -18.076, 1, 2.556, -18.076, 2.611, -18.795, 2.667, -15.632, 1, 2.945, 0.186, 3.222, 14.799, 3.5, 14.799, 1, 3.556, 14.799, 3.611, 15.648, 3.667, 13.945, 1, 4.189, -2.063, 4.711, -18.076, 5.233, -18.076, 0, 8.133, -15.632, 0, 8.3, -18.076, 1, 8.311, -18.076, 8.322, -18.06, 8.333, -17.041, 1, 8.389, -11.947, 8.444, -3.647, 8.5, 0, 1, 8.822, 21.151, 9.145, 30, 9.467, 30, 2, 9.5, 30, 2, 10.233, 30, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "Param115", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 30, 2, 8.333, 30, 2, 9.833, 30, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "Segments": [0, 2.4, 2, 0.333, 2.4, 1, 0.666, 1.6, 1, 0.8, 1.333, 0, 0, 2.3, 12.16, 2, 2.667, 12.16, 2, 3.667, 12.16, 2, 8.133, 12.16, 2, 8.333, 12.16, 2, 8.5, 12.16, 0, 9.467, 0, 0, 10.467, 2.4, 2, 10.833, 2.4]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "Segments": [0, -16, 2, 0.333, -16, 1, 0.666, -10.667, 1, -5.333, 1.333, 0, 0, 2.3, -30, 2, 2.667, -30, 2, 3.667, -30, 2, 8.133, -30, 2, 8.333, -30, 2, 8.5, -30, 0, 9.467, 0, 0, 10.467, -16, 2, 10.833, -16]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "Segments": [0, -4.4, 2, 0.333, -4.4, 1, 0.478, -4.4, 0.622, -2.821, 0.767, 0.9, 1, 0.811, 2.045, 0.856, 2.989, 0.9, 2.989, 0, 1.2, 1.8, 2, 1.633, 1.8, 0, 2.3, -5.04, 2, 2.667, -5.04, 2, 3.667, -5.04, 2, 8.133, -5.04, 2, 8.333, -5.04, 2, 8.5, -5.04, 1, 8.678, -5.04, 8.855, -2.122, 9.033, 0, 1, 9.178, 1.724, 9.322, 1.8, 9.467, 1.8, 2, 9.6, 1.8, 0, 9.9, 2.989, 1, 9.944, 2.989, 9.989, 2.045, 10.033, 0.9, 1, 10.178, -2.821, 10.322, -4.4, 10.467, -4.4, 2, 10.833, -4.4]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "Segments": [0, 17, 2, 0.333, 17, 1, 0.389, 17, 0.444, 17.144, 0.5, 15.896, 1, 0.578, 14.148, 0.655, 9.74, 0.733, 9.74, 1, 1.066, 9.74, 1.4, 10.007, 1.733, 11, 1, 1.8, 11.199, 1.866, 16.578, 1.933, 16.578, 0, 2.3, 12.5, 2, 2.667, 12.5, 2, 3.667, 12.5, 2, 8.133, 12.5, 2, 8.333, 12.5, 2, 8.5, 12.5, 0, 8.867, 16.578, 0, 9.367, 12, 0, 9.967, 20, 0, 10.3, 17, 2, 10.467, 17, 2, 10.833, 17]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 30, 0, 1.333, 0, 0, 2.3, 30, 0, 2.667, 0, 0, 3.667, 30, 2, 8.133, 30, 1, 8.2, 30, 8.266, 23.87, 8.333, 12.984, 1, 8.389, 3.912, 8.444, 0, 8.5, 0, 0, 8.8, 30, 2, 8.967, 30, 0, 9.467, -30, 0, 10.2, 30, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandR_SZ", "Segments": [0, 0, 2, 0.333, 0, 1, 0.4, 10, 0.466, 20, 0.533, 30, 1, 0.611, 30, 0.689, 8.945, 0.767, 0, 1, 0.956, -21.723, 1.144, -27, 1.333, -27, 0, 2.3, 0, 2, 2.667, 0, 2, 3.667, 0, 2, 8.133, 0, 2, 8.333, 0, 2, 8.5, 0, 0, 9.467, -27, 1, 9.656, -27, 9.844, -21.723, 10.033, 0, 1, 10.111, 8.945, 10.189, 30, 10.267, 30, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandR_ZZ", "Segments": [0, 0, 2, 0.333, 0, 1, 0.444, 4.463, 0.556, 8.927, 0.667, 13.39, 0, 1.333, 0, 2, 2.3, 0, 2, 2.667, 0, 2, 3.667, 0, 2, 8.133, 0, 2, 8.333, 0, 2, 8.5, 0, 2, 9.467, 0, 0, 10.133, 13.39, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandR_WMZ", "Segments": [0, 0, 2, 0.333, 0, 1, 0.533, -6.333, 0.733, -12.667, 0.933, -19, 2, 1.333, -19, 0, 2.3, 0, 2, 2.667, 0, 2, 3.667, 0, 2, 8.133, 0, 2, 8.333, 0, 2, 8.5, 0, 0, 9.467, -19, 2, 9.867, -19, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandR_XMZ", "Segments": [0, 0, 2, 0.333, 0, 1, 0.533, -10, 0.733, -20, 0.933, -30, 2, 1.333, -30, 0, 2.3, 0, 2, 2.667, 0, 2, 3.667, 0, 2, 8.133, 0, 2, 8.333, 0, 2, 8.5, 0, 0, 9.467, -30, 2, 9.867, -30, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 1.533, -21, 1, 2, -21, 2.466, -18.828, 2.933, -12, 1, 3.211, -7.935, 3.489, 3.357, 3.767, 4, 1, 4.211, 5.028, 4.656, 4.849, 5.1, 6, 1, 5.467, 6.95, 5.833, 20, 6.2, 20, 1, 6.433, 20, 6.667, -13.755, 6.9, -15, 1, 7.378, -17.55, 7.855, -19.286, 8.333, -20.354, 1, 8.644, -21.05, 8.956, -21, 9.267, -21, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.333, 0, 2, 2.3, 0, 2, 2.667, 0, 0, 3, -19, 0, 3.667, 0, 2, 6.433, 0, 0, 6.7, -15, 0, 8.133, 0, 2, 8.333, 0, 2, 8.5, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.333, 0, 2, 2.3, 0, 2, 2.433, 0, 0, 3.233, -11, 0, 3.667, 0, 2, 8.133, 0, 2, 8.333, 0, 2, 8.5, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.833, 5, 0, 1.1, -7, 0, 2.3, 0, 2, 2.433, 0, 0, 2.933, -9.72, 0, 4.467, 0, 2, 8.133, 0, 1, 8.2, 0, 8.266, -3.188, 8.333, -8.848, 1, 8.389, -13.565, 8.444, -15.6, 8.5, -15.6, 0, 9.267, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.333, 0, 2, 2.3, 0, 1, 2.422, 0, 2.545, -2.504, 2.667, -4.733, 1, 2.889, -8.788, 3.111, -10, 3.333, -10, 1, 3.444, -10, 3.556, -10.047, 3.667, -9.978, 1, 4.089, -9.715, 4.511, 5.579, 4.933, 7, 1, 5.3, 8.234, 5.666, 8, 6.033, 8, 0, 6.567, -10, 0, 6.967, -8.581, 0, 7.3, -9.362, 1, 7.578, -9.362, 7.855, -8.016, 8.133, -4.733, 1, 8.2, -3.946, 8.266, -2.626, 8.333, -1.496, 1, 8.389, -0.555, 8.444, 0, 8.5, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamChestZ", "Segments": [0, 0, 2, 0.333, 0, 2, 2.3, 0, 0, 2.667, -6.3, 0, 3.667, 0, 2, 8.133, 0, 1, 8.2, 0, 8.266, -0.332, 8.333, -2.853, 1, 8.489, -8.736, 8.644, -13.225, 8.8, -13.225, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 0, 1.367, -2, 0, 2.3, 0, 0, 3.533, -3, 2, 3.667, -3, 2, 8.133, -3, 1, 8.2, -3, 8.266, -2.695, 8.333, -1.188, 1, 8.466, 1.826, 8.6, 4, 8.733, 4, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 3.84, 0, 2.333, -4.085, 0, 3.867, 7.495, 1, 5.356, 7.495, 6.844, 2.466, 8.333, -3.165, 1, 8.589, -4.131, 8.844, -3.822, 9.1, -3.822, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -11.28, 0, 1.333, 9.78, 0, 2.833, -6.42, 0, 5.067, 2.64, 0, 8.133, -5.699, 1, 8.2, -5.699, 8.266, -5.898, 8.333, -3.57, 1, 8.578, 4.969, 8.822, 12.32, 9.067, 12.32, 0, 9.7, -5.28, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -20, 0, 1.533, 22, 0, 3.067, -19, 0, 5.467, 11, 0, 6.133, 7.3, 0, 6.467, 23.117, 0, 6.967, -9, 1, 7.111, -9, 7.256, -8.473, 7.4, -2.38, 1, 7.711, 10.744, 8.022, 22, 8.333, 22, 0, 8.867, -18, 0, 9.5, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "Segments": [0, 0, 2, 0.333, 0, 0, 1.767, -3.12, 0, 3.367, 3.48, 1, 3.467, 3.48, 3.567, 3.627, 3.667, 2.81, 1, 4.1, -0.73, 4.534, -3.78, 4.967, -3.78, 0, 8.133, 2.158, 1, 8.2, 2.158, 8.266, 2.211, 8.333, 1.492, 1, 8.566, -1.025, 8.8, -3.12, 9.033, -3.12, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "Segments": [0, 0, 2, 0.333, 0, 0, 1.067, 19.92, 0, 2.3, 0, 2, 2.433, 0, 1, 2.511, 0, 2.589, -0.1, 2.667, 0.578, 1, 3, 3.486, 3.334, 8.069, 3.667, 11.219, 1, 3.889, 13.319, 4.111, 13.92, 4.333, 13.92, 1, 5.6, 13.92, 6.866, 8.868, 8.133, 0.578, 1, 8.2, 0.142, 8.266, 0.079, 8.333, 0.004, 1, 8.344, -0.008, 8.356, 0, 8.367, 0, 2, 8.5, 0, 0, 9.733, 19.92, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 2, 0.333, 0, 0, 0.733, -21, 0, 1.5, 30, 0, 2.8, -4, 0, 4.467, 11, 0, 6.133, 1.529, 0, 6.433, 10.245, 1, 6.544, 10.245, 6.656, 1.375, 6.767, 0, 1, 7.222, -5.638, 7.678, -7.2, 8.133, -7.2, 1, 8.2, -7.2, 8.266, -3.121, 8.333, 7.449, 1, 8.422, 21.542, 8.511, 30, 8.6, 30, 0, 9.2, -10, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 2, 0.333, 0, 0, 1.3, 1, 1, 2.044, 1, 2.789, 0.457, 3.533, -0.4, 1, 3.933, -0.861, 4.333, -1, 4.733, -1, 0, 7.167, 1, 1, 7.556, 1, 7.944, 0.944, 8.333, 0.713, 1, 9.044, 0.292, 9.756, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0.314, 2, 0.333, 0.314, 1, 0.355, 0.323, 0.378, 0.924, 0.4, 0.933, 1, 0.422, 0.925, 0.445, 0.404, 0.467, 0.396, 1, 0.489, 0.403, 0.511, 0.828, 0.533, 0.835, 1, 0.578, 0.839, 0.622, 0.302, 0.667, 0.306, 1, 0.689, 0.312, 0.711, 0.68, 0.733, 0.686, 1, 0.755, 0.676, 0.778, 0.011, 0.8, 0, 1, 0.844, -0.007, 0.889, 0.866, 0.933, 0.859, 1, 0.978, 0.862, 1.022, 0.46, 1.067, 0.463, 1, 1.089, 0.466, 1.111, 0.671, 1.133, 0.675, 1, 1.2, 0.676, 1.266, 0.469, 1.333, 0.471, 1, 1.355, 0.472, 1.378, 0.532, 1.4, 0.533, 1, 1.533, 0.531, 1.667, 0.002, 1.8, 0, 2, 3, 0, 1, 3.067, -0.007, 3.133, 0.921, 3.2, 0.914, 1, 3.244, 0.918, 3.289, 0.309, 3.333, 0.314, 1, 3.378, 0.312, 3.422, 0.523, 3.467, 0.522, 1, 3.489, 0.513, 3.511, 0.008, 3.533, 0, 2, 3.6, 0, 1, 3.644, -0.003, 3.689, 0.368, 3.733, 0.365, 1, 3.8, 0.368, 3.866, -0.003, 3.933, 0, 2, 4.2, 0, 1, 4.244, -0.007, 4.289, 0.921, 4.333, 0.914, 1, 4.355, 0.902, 4.378, 0.149, 4.4, 0.137, 1, 4.467, 0.132, 4.533, 0.762, 4.6, 0.757, 1, 4.622, 0.754, 4.645, 0.556, 4.667, 0.553, 1, 4.711, 0.552, 4.756, 0.707, 4.8, 0.706, 1, 4.822, 0.698, 4.845, 0.69, 4.867, 0.682, 1, 4.889, 0.685, 4.911, 0.841, 4.933, 0.843, 1, 4.978, 0.849, 5.022, 0.144, 5.067, 0.149, 1, 5.089, 0.159, 5.111, 0.778, 5.133, 0.788, 1, 5.178, 0.792, 5.222, 0.286, 5.267, 0.29, 1, 5.289, 0.298, 5.311, 0.8, 5.333, 0.808, 1, 5.355, 0.804, 5.378, 0.596, 5.4, 0.592, 1, 5.422, 0.593, 5.445, 0.65, 5.467, 0.651, 1, 5.534, 0.656, 5.6, -0.005, 5.667, 0, 1, 5.689, 0.008, 5.711, 0.533, 5.733, 0.541, 1, 5.755, 0.531, 5.778, 0.52, 5.8, 0.51, 1, 5.822, 0.512, 5.845, 0.66, 5.867, 0.663, 1, 5.911, 0.668, 5.956, -0.005, 6, 0, 2, 6.067, 0, 1, 6.089, 0.009, 6.111, 0.575, 6.133, 0.584, 1, 6.178, 0.589, 6.222, -0.005, 6.267, 0, 2, 6.733, 0, 1, 6.778, -0.007, 6.822, 0.854, 6.867, 0.847, 1, 6.889, 0.842, 6.911, 0.519, 6.933, 0.514, 1, 6.955, 0.519, 6.978, 0.853, 7, 0.859, 1, 7.022, 0.856, 7.045, 0.674, 7.067, 0.671, 1, 7.089, 0.675, 7.111, 0.941, 7.133, 0.945, 1, 7.155, 0.94, 7.178, 0.648, 7.2, 0.643, 1, 7.222, 0.648, 7.245, 0.96, 7.267, 0.965, 1, 7.311, 0.967, 7.356, 0.649, 7.4, 0.651, 1, 7.422, 0.654, 7.445, 0.832, 7.467, 0.835, 1, 7.489, 0.832, 7.511, 0.608, 7.533, 0.604, 1, 7.555, 0.605, 7.578, 0.677, 7.6, 0.678, 1, 7.667, 0.682, 7.733, 0.224, 7.8, 0.227, 2, 7.833, 0.227, 2, 7.867, 0.227, 1, 7.889, 0.234, 7.911, 0.24, 7.933, 0.247, 1, 8, 0.249, 8.066, -0.002, 8.133, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.333, 1, 2, 0.833, 1, 0, 1.167, 0, 0, 1.5, 1, 2, 2.3, 1, 2, 2.667, 1, 2, 3.667, 1, 2, 4, 1, 0, 4.333, 0, 0, 4.667, 1, 2, 6.167, 1, 0, 6.333, 0, 0, 6.667, 1, 1, 7.156, 1, 7.644, 0.951, 8.133, 0.799, 1, 8.2, 0.778, 8.266, 0, 8.333, 0, 2, 9.633, 0, 0, 9.967, 1, 2, 10.467, 1, 2, 10.833, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.333, 1, 2, 0.833, 1, 0, 1.167, 0, 0, 1.5, 1, 2, 2.3, 1, 2, 2.667, 1, 2, 3.667, 1, 2, 4, 1, 0, 4.333, 0, 0, 4.667, 1, 2, 6.167, 1, 0, 6.333, 0, 0, 6.667, 1, 1, 7.156, 1, 7.644, 0.941, 8.133, 0.763, 1, 8.2, 0.739, 8.266, 0, 8.333, 0, 2, 9.633, 0, 0, 9.967, 1, 2, 10.467, 1, 2, 10.833, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 0.333, 0, 0, 2.667, -0.9, 0, 2.833, 0.6, 1, 4.666, 0.6, 6.5, -0.027, 8.333, -0.658, 1, 8.355, -0.666, 8.378, -0.659, 8.4, -0.659, 2, 8.767, -0.659, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 0.333, 0, 0, 2.667, -0.2, 0, 2.833, 0.1, 1, 4.666, 0.1, 6.5, -0.278, 8.333, -0.659, 1, 8.355, -0.663, 8.378, -0.659, 8.4, -0.659, 2, 8.767, -0.659, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "weak_strong", "Segments": [0, 0, 2, 0.333, 0, 2, 2.3, 0, 2, 2.667, 0, 2, 3.667, 0, 0, 4.967, -4, 0, 6.367, 0, 2, 7, 0, 2, 8.133, 0, 2, 8.333, 0, 2, 8.5, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "cold_heat", "Segments": [0, 0, 2, 0.333, 0, 2, 2.3, 0, 2, 2.667, 0, 2, 3.667, 0, 2, 6.367, 0, 0, 7, -10, 2, 8.133, -10, 1, 8.2, -10, 8.266, -7.956, 8.333, -4.328, 1, 8.389, -1.304, 8.444, 0, 8.5, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "Segments": [0, 0, 2, 0.133, 0, 2, 0.233, 0, 2, 0.267, 0, 2, 0.3, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 0.5, 0, 2, 0.867, 0, 0, 1.067, 21.511, 0, 1.367, -30, 0, 1.667, 17.505, 0, 1.967, -5.78, 0, 2.267, 1.995, 0, 2.6, -0.71, 0, 2.9, 0.251, 0, 3.2, -0.088, 0, 3.5, 0.031, 0, 3.833, -0.011, 0, 4.233, 21.512, 0, 4.533, -30, 0, 4.833, 17.505, 0, 5.133, -5.78, 0, 5.433, 1.995, 0, 5.767, -0.71, 0, 6.067, 0.251, 0, 6.2, 0.085, 0, 6.333, 30, 0, 6.567, -28.72, 0, 6.867, 15.057, 0, 7.167, -4.852, 0, 7.467, 2.145, 0, 7.767, -0.116, 0, 8.1, 0.913, 0, 8.167, 0.892, 0, 8.3, 24.343, 1, 8.311, 24.343, 8.322, 25.357, 8.333, 22.736, 1, 8.411, 4.387, 8.489, -13.066, 8.567, -13.066, 0, 8.867, 4.481, 0, 9.167, -1.568, 0, 9.467, 0.546, 0, 9.867, -21.616, 0, 10.133, 15.584, 0, 10.433, -5.193, 0, 10.767, 1.804, 1, 10.789, 1.804, 10.811, 1.522, 10.833, 1.184]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "Segments": [0, 0, 2, 0.133, 0, 2, 0.233, 0, 2, 0.267, 0, 2, 0.3, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 0.5, 0, 2, 0.867, 0, 0, 1.067, 21.511, 0, 1.367, -30, 0, 1.667, 17.505, 0, 1.967, -5.78, 0, 2.267, 1.995, 0, 2.6, -0.71, 0, 2.9, 0.251, 0, 3.2, -0.088, 0, 3.5, 0.031, 0, 3.833, -0.011, 0, 4.233, 21.512, 0, 4.533, -30, 0, 4.833, 17.505, 0, 5.133, -5.78, 0, 5.433, 1.995, 0, 5.767, -0.71, 0, 6.067, 0.251, 0, 6.2, 0.085, 0, 6.333, 30, 0, 6.567, -28.72, 0, 6.867, 15.072, 0, 7.167, -4.808, 0, 7.467, 2.209, 0, 7.767, -0.026, 0, 8.1, 1.036, 0, 8.167, 1.022, 0, 8.3, 23.281, 1, 8.311, 23.281, 8.322, 24.256, 8.333, 21.732, 1, 8.411, 4.059, 8.489, -12.773, 8.567, -12.773, 0, 8.867, 4.395, 0, 9.167, -1.541, 0, 9.467, 0.538, 0, 9.867, -21.613, 0, 10.133, 15.584, 0, 10.433, -5.193, 0, 10.767, 1.804, 1, 10.789, 1.804, 10.811, 1.522, 10.833, 1.184]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "Segments": [0, 0, 2, 0.133, 0, 2, 0.233, 0, 2, 0.267, 0, 2, 0.3, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 0.5, 0, 2, 0.867, 0, 0, 1.067, 21.511, 0, 1.367, -30, 0, 1.667, 17.505, 0, 1.967, -5.78, 0, 2.267, 1.995, 0, 2.6, -0.71, 0, 2.9, 0.251, 0, 3.2, -0.088, 0, 3.5, 0.031, 0, 3.833, -0.011, 0, 4.233, 21.512, 0, 4.533, -30, 0, 4.833, 17.505, 0, 5.133, -5.78, 0, 5.433, 1.995, 0, 5.767, -0.71, 0, 6.067, 0.251, 0, 6.2, 0.085, 0, 6.333, 30, 0, 6.567, -28.72, 0, 6.867, 15.057, 0, 7.167, -4.852, 0, 7.467, 2.145, 0, 7.767, -0.116, 0, 8.1, 0.913, 0, 8.167, 0.892, 0, 8.3, 24.343, 1, 8.311, 24.343, 8.322, 25.357, 8.333, 22.736, 1, 8.411, 4.387, 8.489, -13.066, 8.567, -13.066, 0, 8.867, 4.481, 0, 9.167, -1.568, 0, 9.467, 0.546, 0, 9.867, -21.616, 0, 10.133, 15.584, 0, 10.433, -5.193, 0, 10.767, 1.804, 1, 10.789, 1.804, 10.811, 1.522, 10.833, 1.184]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "Segments": [0, 0, 2, 0.133, 0, 2, 0.233, 0, 2, 0.267, 0, 2, 0.3, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 0.5, 0, 2, 0.867, 0, 0, 1.067, 21.511, 0, 1.367, -30, 0, 1.667, 17.505, 0, 1.967, -5.78, 0, 2.267, 1.995, 0, 2.6, -0.71, 0, 2.9, 0.251, 0, 3.2, -0.088, 0, 3.5, 0.031, 0, 3.833, -0.011, 0, 4.233, 21.512, 0, 4.533, -30, 0, 4.833, 17.505, 0, 5.133, -5.78, 0, 5.433, 1.995, 0, 5.767, -0.71, 0, 6.067, 0.251, 0, 6.2, 0.085, 0, 6.333, 30, 0, 6.567, -28.72, 0, 6.867, 15.072, 0, 7.167, -4.808, 0, 7.467, 2.209, 0, 7.767, -0.026, 0, 8.1, 1.036, 0, 8.167, 1.022, 0, 8.3, 23.281, 1, 8.311, 23.281, 8.322, 24.256, 8.333, 21.732, 1, 8.411, 4.059, 8.489, -12.773, 8.567, -12.773, 0, 8.867, 4.395, 0, 9.167, -1.541, 0, 9.467, 0.538, 0, 9.867, -21.613, 0, 10.133, 15.584, 0, 10.433, -5.193, 0, 10.767, 1.804, 1, 10.789, 1.804, 10.811, 1.522, 10.833, 1.184]}, {"Target": "Parameter", "Id": "Param89", "Segments": [0, 0, 2, 0.333, 0, 2, 3.667, 0, 0, 4.833, -7, 1, 6, -7, 7.166, -5.108, 8.333, -2.251, 1, 9.044, -0.51, 9.756, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "Param88", "Segments": [0, 0, 2, 0.333, 0, 1, 1.444, 0, 2.556, -3.084, 3.667, -12, 1, 4.056, -15.121, 4.444, -19, 4.833, -19, 1, 6, -19, 7.166, -13.862, 8.333, -6.11, 1, 9.044, -1.385, 9.756, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 2, 0.333, 0, 2, 3.667, 0, 0, 4.333, -0.6, 1, 5.389, -0.6, 6.444, -0.579, 7.5, -0.5, 1, 7.711, -0.484, 7.922, 0, 8.133, 0, 2, 8.333, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 2, 0.333, 0, 2, 3.667, 0, 0, 4.333, -0.6, 1, 5.389, -0.6, 6.444, -0.579, 7.5, -0.5, 1, 7.711, -0.484, 7.922, 0, 8.133, 0, 2, 8.333, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "null_b", "Segments": [0, 0, 2, 0.333, 0, 2, 1.1, 0, 0, 1.733, 1, 0, 3.667, 0, 2, 7.9, 0, 2, 8.333, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "Hair_physics", "Segments": [0, 0, 2, 0.333, 0, 2, 0.8, 0, 0, 1.133, 1, 0, 3.067, 0, 2, 3.1, 0, 0, 3.433, 1, 1, 3.511, 1, 3.589, 0.882, 3.667, 0.819, 1, 4.378, 0.244, 5.089, 0, 5.8, 0, 0, 6.133, 1, 2, 8.333, 1, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "OP_physics", "Segments": [0, 0, 2, 0.333, 0, 2, 3.667, 0, 2, 5.8, 0, 0, 6.267, 1, 1, 6.956, 1, 7.644, 0.84, 8.333, 0.512, 1, 9.044, 0.173, 9.756, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "Clothes_physics", "Segments": [0, 0, 2, 0.333, 0, 2, 0.8, 0, 0, 1.133, 1, 0, 3.1, 0, 0, 3.433, 1, 1, 3.511, 1, 3.589, 0.882, 3.667, 0.819, 1, 4.378, 0.244, 5.089, 0, 5.8, 0, 0, 6.133, 1, 0, 8, 0, 0, 8.333, 1, 0, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHairFront1", "Segments": [0, 0.002, 0, 0.367, -0.001, 0, 0.733, 0.309, 0, 1.067, -9.028, 0, 1.4, 10.063, 0, 1.833, -5.34, 0, 2.233, 3.408, 0, 2.667, -2.18, 0, 2.767, -2.129, 0, 2.8, -2.138, 0, 3.133, 1.455, 0, 3.367, -7.544, 0, 3.7, 10.336, 0, 4.133, -5.934, 0, 4.533, 3.453, 0, 4.967, -2.149, 0, 5.367, 0.966, 0, 5.8, -0.817, 0, 5.833, -0.789, 0, 6.067, -8.805, 0, 6.4, 10.596, 0, 6.8, -5.894, 0, 7.233, 3.365, 0, 7.667, -1.967, 0, 8.1, 1.061, 1, 8.178, 1.061, 8.255, 0.614, 8.333, -0.042, 1, 8.378, -0.417, 8.422, -0.515, 8.467, -0.515, 0, 8.867, 0.93, 0, 9.367, -0.133, 0, 9.733, -0.053, 0, 10.3, -0.192, 0, 10.467, -0.186, 2, 10.5, -0.186, 1, 10.611, -0.186, 10.722, -0.011, 10.833, 0.07]}, {"Target": "Parameter", "Id": "ParamHairFront2", "Segments": [0, 0.001, 0, 0.133, 0.003, 0, 0.5, -0.052, 0, 0.967, 3.142, 0, 1.233, -18.374, 0, 1.567, 16.834, 0, 1.967, -8.791, 0, 2.367, 6.555, 0, 2.8, -2.432, 0, 2.933, -2.143, 0, 3.067, -2.45, 0, 3.3, 3.702, 0, 3.533, -16.873, 0, 3.867, 17.651, 0, 4.267, -9.639, 0, 4.667, 6.432, 0, 5.1, -3.123, 0, 5.5, 1.898, 0, 5.833, -1.113, 0, 5.967, 1.224, 0, 6.233, -17.934, 0, 6.567, 17.678, 0, 6.933, -10.327, 0, 7.367, 5.713, 0, 7.8, -2.986, 0, 8.2, 1.893, 1, 8.244, 1.893, 8.289, 1.762, 8.333, 1.122, 1, 8.422, -0.158, 8.511, -1.072, 8.6, -1.072, 0, 9, 1.882, 0, 9.4, 0.482, 0, 9.667, 0.69, 0, 10.7, -0.201, 1, 10.744, -0.201, 10.789, -0.117, 10.833, -0.046]}, {"Target": "Parameter", "Id": "ParamHairFront3", "Segments": [0, 0, 0, 0.167, 0.003, 0, 0.7, -0.192, 0, 1.1, 6.548, 0, 1.367, -16.455, 0, 1.7, 17.306, 0, 2.033, -12.124, 0, 2.433, 7.296, 0, 2.8, -3.5, 0, 3.067, 0.095, 0, 3.167, -0.361, 0, 3.4, 7.273, 0, 3.667, -16.437, 0, 4, 18.094, 0, 4.333, -12.765, 0, 4.733, 7.691, 0, 5.133, -4.156, 0, 5.567, 2.361, 0, 5.867, -0.885, 0, 6.1, 5.586, 0, 6.367, -15.387, 0, 6.667, 18.011, 0, 7.033, -13.342, 0, 7.4, 7.394, 0, 7.833, -3.703, 0, 8.267, 2.114, 1, 8.289, 2.114, 8.311, 2.211, 8.333, 1.871, 1, 8.455, 0.004, 8.578, -1.676, 8.7, -1.676, 0, 9.067, 1.234, 0, 9.433, -0.641, 0, 9.8, 0.327, 0, 10.133, -0.068, 0, 10.433, 0.03, 0, 10.833, -0.151]}, {"Target": "Parameter", "Id": "ParamHairFront4", "Segments": [0, -0.002, 0, 0.233, 0.006, 0, 0.8, -0.215, 0, 1.167, 6.881, 0, 1.433, -18.958, 0, 1.767, 23.788, 0, 2.133, -19.031, 0, 2.5, 12.86, 0, 2.867, -7.072, 0, 3.2, 2.162, 0, 3.267, 1.943, 0, 3.5, 6.611, 0, 3.767, -18.63, 0, 4.067, 24.349, 0, 4.433, -19.864, 0, 4.8, 13.484, 0, 5.2, -7.975, 0, 5.6, 4.403, 0, 5.933, -1.825, 0, 6.2, 5.823, 0, 6.433, -17.331, 0, 6.767, 24.209, 0, 7.133, -20.249, 0, 7.5, 13.352, 0, 7.867, -7.419, 0, 8.3, 3.751, 1, 8.311, 3.751, 8.322, 3.903, 8.333, 3.647, 1, 8.466, 0.574, 8.6, -2.548, 8.733, -2.548, 0, 9.133, 2.03, 0, 9.5, -1.267, 0, 9.867, 0.756, 0, 10.2, -0.313, 0, 10.5, 0.141, 1, 10.611, 0.141, 10.722, -0.096, 10.833, -0.175]}, {"Target": "Parameter", "Id": "ParamHairFront5", "Segments": [0, -0.007, 0, 0.267, 0.01, 0, 0.867, -0.244, 0, 1.233, 7.06, 0, 1.5, -20.814, 0, 1.833, 29.221, 0, 2.233, -25.845, 0, 2.6, 19.824, 0, 2.967, -12.686, 0, 3.3, 6.023, 0, 3.467, 4.684, 0, 3.533, 5.092, 0, 3.833, -20.122, 0, 4.167, 29.662, 0, 4.533, -26.796, 0, 4.9, 20.585, 0, 5.267, -13.863, 0, 5.667, 8.387, 0, 6, -3.902, 0, 6.267, 6.71, 0, 6.5, -18.555, 0, 6.833, 29.53, 0, 7.2, -26.797, 0, 7.6, 20.285, 0, 7.967, -13.261, 0, 8.333, 7.486, 0, 8.767, -4.172, 0, 9.2, 3.11, 0, 9.567, -2.259, 0, 9.933, 1.53, 0, 10.267, -0.823, 0, 10.6, 0.436, 1, 10.678, 0.436, 10.755, 0.053, 10.833, -0.177]}, {"Target": "Parameter", "Id": "ParamHairSide1", "Segments": [0, 0.002, 0, 0.367, -0.001, 0, 0.733, 0.309, 0, 1.067, -9.028, 0, 1.4, 10.063, 0, 1.833, -5.34, 0, 2.233, 3.408, 0, 2.667, -2.18, 0, 2.767, -2.129, 0, 2.8, -2.138, 0, 3.133, 1.455, 0, 3.367, -7.544, 0, 3.7, 10.336, 0, 4.133, -5.934, 0, 4.533, 3.453, 0, 4.967, -2.149, 0, 5.367, 0.966, 0, 5.8, -0.817, 0, 5.833, -0.789, 0, 6.067, -8.805, 0, 6.4, 10.596, 0, 6.8, -5.894, 0, 7.233, 3.365, 0, 7.667, -1.967, 0, 8.1, 1.061, 1, 8.178, 1.061, 8.255, 0.614, 8.333, -0.042, 1, 8.378, -0.417, 8.422, -0.515, 8.467, -0.515, 0, 8.867, 0.93, 0, 9.367, -0.133, 0, 9.733, -0.053, 0, 10.3, -0.192, 0, 10.467, -0.186, 2, 10.5, -0.186, 1, 10.611, -0.186, 10.722, -0.011, 10.833, 0.07]}, {"Target": "Parameter", "Id": "ParamHairSide2", "Segments": [0, 0.001, 0, 0.133, 0.003, 0, 0.5, -0.052, 0, 0.967, 3.142, 0, 1.233, -18.374, 0, 1.567, 16.834, 0, 1.967, -8.791, 0, 2.367, 6.555, 0, 2.8, -2.432, 0, 2.933, -2.143, 0, 3.067, -2.45, 0, 3.3, 3.702, 0, 3.533, -16.873, 0, 3.867, 17.651, 0, 4.267, -9.639, 0, 4.667, 6.432, 0, 5.1, -3.123, 0, 5.5, 1.898, 0, 5.833, -1.113, 0, 5.967, 1.224, 0, 6.233, -17.934, 0, 6.567, 17.678, 0, 6.933, -10.327, 0, 7.367, 5.713, 0, 7.8, -2.986, 0, 8.2, 1.893, 1, 8.244, 1.893, 8.289, 1.762, 8.333, 1.122, 1, 8.422, -0.158, 8.511, -1.072, 8.6, -1.072, 0, 9, 1.882, 0, 9.4, 0.482, 0, 9.667, 0.69, 0, 10.7, -0.201, 1, 10.744, -0.201, 10.789, -0.117, 10.833, -0.046]}, {"Target": "Parameter", "Id": "ParamHairSide3", "Segments": [0, 0, 0, 0.167, 0.003, 0, 0.7, -0.192, 0, 1.1, 6.548, 0, 1.367, -16.455, 0, 1.7, 17.306, 0, 2.033, -12.124, 0, 2.433, 7.296, 0, 2.8, -3.5, 0, 3.067, 0.095, 0, 3.167, -0.361, 0, 3.4, 7.273, 0, 3.667, -16.437, 0, 4, 18.094, 0, 4.333, -12.765, 0, 4.733, 7.691, 0, 5.133, -4.156, 0, 5.567, 2.361, 0, 5.867, -0.885, 0, 6.1, 5.586, 0, 6.367, -15.387, 0, 6.667, 18.011, 0, 7.033, -13.342, 0, 7.4, 7.394, 0, 7.833, -3.703, 0, 8.267, 2.114, 1, 8.289, 2.114, 8.311, 2.211, 8.333, 1.871, 1, 8.455, 0.004, 8.578, -1.676, 8.7, -1.676, 0, 9.067, 1.234, 0, 9.433, -0.641, 0, 9.8, 0.327, 0, 10.133, -0.068, 0, 10.433, 0.03, 0, 10.833, -0.151]}, {"Target": "Parameter", "Id": "ParamHairSide4", "Segments": [0, -0.002, 0, 0.233, 0.006, 0, 0.8, -0.215, 0, 1.167, 6.881, 0, 1.433, -18.958, 0, 1.767, 23.788, 0, 2.133, -19.031, 0, 2.5, 12.86, 0, 2.867, -7.072, 0, 3.2, 2.162, 0, 3.267, 1.943, 0, 3.5, 6.611, 0, 3.767, -18.63, 0, 4.067, 24.349, 0, 4.433, -19.864, 0, 4.8, 13.484, 0, 5.2, -7.975, 0, 5.6, 4.403, 0, 5.933, -1.825, 0, 6.2, 5.823, 0, 6.433, -17.331, 0, 6.767, 24.209, 0, 7.133, -20.249, 0, 7.5, 13.352, 0, 7.867, -7.419, 0, 8.3, 3.751, 1, 8.311, 3.751, 8.322, 3.903, 8.333, 3.647, 1, 8.466, 0.574, 8.6, -2.548, 8.733, -2.548, 0, 9.133, 2.03, 0, 9.5, -1.267, 0, 9.867, 0.756, 0, 10.2, -0.313, 0, 10.5, 0.141, 1, 10.611, 0.141, 10.722, -0.096, 10.833, -0.175]}, {"Target": "Parameter", "Id": "ParamHairSide5", "Segments": [0, -0.007, 0, 0.267, 0.01, 0, 0.867, -0.244, 0, 1.233, 7.06, 0, 1.5, -20.814, 0, 1.833, 29.221, 0, 2.233, -25.845, 0, 2.6, 19.824, 0, 2.967, -12.686, 0, 3.3, 6.023, 0, 3.467, 4.684, 0, 3.533, 5.092, 0, 3.833, -20.122, 0, 4.167, 29.662, 0, 4.533, -26.796, 0, 4.9, 20.585, 0, 5.267, -13.863, 0, 5.667, 8.387, 0, 6, -3.902, 0, 6.267, 6.71, 0, 6.5, -18.555, 0, 6.833, 29.53, 0, 7.2, -26.797, 0, 7.6, 20.285, 0, 7.967, -13.261, 0, 8.333, 7.486, 0, 8.767, -4.172, 0, 9.2, 3.11, 0, 9.567, -2.259, 0, 9.933, 1.53, 0, 10.267, -0.823, 0, 10.6, 0.436, 1, 10.678, 0.436, 10.755, 0.053, 10.833, -0.177]}, {"Target": "Parameter", "Id": "ParamHairBack1", "Segments": [0, 0.002, 0, 0.367, -0.001, 0, 0.733, 0.309, 0, 1.067, -9.028, 0, 1.4, 10.063, 0, 1.833, -5.34, 0, 2.233, 3.408, 0, 2.667, -2.18, 0, 2.967, 1.492, 0, 3.367, -11.128, 0, 3.7, 13.173, 0, 4.133, -7.522, 0, 4.567, 4.368, 0, 5, -2.667, 0, 5.4, 1.28, 0, 5.8, -0.983, 0, 5.833, -0.982, 0, 6.067, -8.863, 0, 6.4, 10.684, 0, 6.867, -6.917, 0, 7.3, 3.882, 0, 7.733, -2.327, 0, 8.167, 1.49, 1, 8.222, 1.49, 8.278, 1.222, 8.333, 0.601, 1, 8.4, -0.144, 8.466, -0.563, 8.533, -0.563, 0, 8.933, 0.925, 0, 9.5, -0.264, 0, 9.867, 0, 0, 10.333, -0.246, 1, 10.5, -0.246, 10.666, 0.048, 10.833, 0.117]}, {"Target": "Parameter", "Id": "ParamHairBack2", "Segments": [0, 0.002, 0, 0.1, 0.003, 0, 0.5, -0.052, 0, 0.967, 3.143, 0, 1.233, -18.374, 0, 1.567, 16.834, 0, 1.967, -8.791, 0, 2.367, 6.555, 0, 2.833, -3.205, 0, 3.2, 4.535, 0, 3.533, -22.32, 0, 3.867, 20.943, 0, 4.267, -11.63, 0, 4.7, 7.752, 0, 5.1, -3.984, 0, 5.533, 2.376, 0, 5.833, -1.155, 0, 5.967, 0.913, 0, 6.233, -17.911, 0, 6.567, 16.618, 0, 7.033, -9.621, 0, 7.433, 6.153, 0, 7.867, -3.993, 0, 8.3, 2.402, 1, 8.311, 2.402, 8.322, 2.496, 8.333, 2.318, 1, 8.444, 0.545, 8.556, -1.145, 8.667, -1.145, 0, 9.1, 1.954, 0, 9.6, 0.414, 0, 9.933, 0.54, 0, 10.667, -0.222, 1, 10.722, -0.222, 10.778, -0.092, 10.833, 0.005]}, {"Target": "Parameter", "Id": "ParamHairBack3", "Segments": [0, 0.001, 0, 0.167, 0.004, 0, 0.7, -0.192, 0, 1.1, 6.548, 0, 1.367, -16.455, 0, 1.7, 17.306, 0, 2.033, -12.124, 0, 2.433, 7.296, 0, 2.867, -4.332, 0, 3.3, 7.867, 0, 3.633, -19.627, 0, 3.967, 20.148, 0, 4.367, -13.925, 0, 4.767, 8.617, 0, 5.167, -5.043, 0, 5.567, 2.995, 0, 5.9, -0.938, 0, 6.1, 5.252, 0, 6.367, -15.175, 0, 6.667, 16.192, 0, 7.067, -9.836, 0, 7.5, 6.605, 0, 7.933, -4.464, 0, 8.333, 2.745, 0, 8.733, -1.995, 0, 9.133, 1.321, 0, 9.5, -0.584, 0, 9.9, 0.162, 0, 10.8, -0.122, 1, 10.811, -0.122, 10.822, -0.117, 10.833, -0.109]}, {"Target": "Parameter", "Id": "ParamHairBack4", "Segments": [0, 0, 0, 0.2, 0.007, 0, 0.8, -0.215, 0, 1.167, 6.881, 0, 1.433, -18.958, 0, 1.767, 23.788, 0, 2.133, -19.031, 0, 2.5, 12.86, 0, 2.9, -7.775, 0, 3.367, 9.752, 0, 3.7, -22.658, 0, 4.067, 26.428, 0, 4.433, -20.509, 0, 4.833, 14.308, 0, 5.233, -8.945, 0, 5.633, 5.368, 0, 5.967, -2.083, 0, 6.2, 5.384, 0, 6.433, -16.908, 0, 6.767, 22.354, 0, 7.167, -15.78, 0, 7.567, 10.504, 0, 7.967, -7.296, 1, 8.089, -7.296, 8.211, -1.84, 8.333, 3.978, 1, 8.355, 5.036, 8.378, 4.75, 8.4, 4.75, 0, 8.8, -3.298, 0, 9.2, 2.311, 0, 9.567, -1.274, 0, 9.933, 0.547, 0, 10.267, -0.045, 0, 10.433, 0.01, 0, 10.8, -0.142, 1, 10.811, -0.142, 10.822, -0.14, 10.833, -0.136]}, {"Target": "Parameter", "Id": "ParamHairBack5", "Segments": [0, -0.006, 0, 0.233, 0.011, 0, 0.867, -0.243, 0, 1.233, 7.059, 0, 1.5, -20.814, 0, 1.833, 29.221, 0, 2.233, -25.845, 0, 2.6, 19.824, 0, 2.967, -13.406, 0, 3.4, 12.889, 0, 3.767, -25.679, 0, 4.1, 30, 2, 4.133, 30, 0, 4.533, -26.902, 0, 4.9, 20.733, 0, 5.3, -14.685, 0, 5.7, 9.533, 0, 6.033, -4.469, 0, 6.267, 5.928, 0, 6.5, -17.692, 0, 6.833, 27.647, 0, 7.233, -22.451, 0, 7.633, 15.959, 0, 8.033, -11.506, 1, 8.133, -11.506, 8.233, -4.077, 8.333, 4.98, 1, 8.366, 7.999, 8.4, 8.033, 8.433, 8.033, 0, 8.833, -5.527, 0, 9.267, 3.904, 0, 9.633, -2.468, 0, 10, 1.334, 0, 10.333, -0.463, 0, 10.6, 0.127, 1, 10.678, 0.127, 10.755, -0.087, 10.833, -0.182]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_L", "Segments": [0, 0, 2, 2.7, 0, 0, 2.9, 6.009, 0, 3.2, -6.233, 0, 3.6, 0.989, 0, 3.633, 0.983, 0, 3.733, 1.199, 0, 4.033, -0.421, 0, 4.333, 0.147, 0, 4.667, -0.052, 0, 4.967, 0.018, 0, 5.267, -0.006, 0, 5.567, 0.002, 0, 6.067, -12.612, 0, 6.4, 10.457, 0, 6.833, -4.423, 0, 7.167, 1.495, 0, 7.467, -0.469, 0, 7.867, 0.544, 0, 7.933, 0.529, 0, 8.233, 0.836, 1, 8.266, 0.836, 8.3, 0.819, 8.333, 0.738, 1, 8.4, 0.575, 8.466, 0.459, 8.533, 0.459, 0, 8.8, 0.547, 0, 9.2, 0.419, 0, 9.233, 0.42, 0, 9.267, 0.403, 0, 9.3, 0.404, 0, 10.533, -0.141, 0, 10.833, 0.049]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "Segments": [0, 0, 2, 0.367, 0, 0, 0.7, 0.762, 0, 1.6, -0.384, 0, 1.9, 0.042, 0, 2.3, -0.19, 0, 2.433, -0.18, 0, 3.233, -0.655, 0, 3.833, 0.337, 0, 4.167, -0.137, 0, 4.467, 0.029, 0, 4.833, -0.048, 0, 4.967, -0.045, 0, 5.433, -0.536, 0, 6.5, 2.733, 0, 6.967, -1.542, 0, 7.3, 0.643, 0, 7.6, -0.153, 0, 7.9, 0.107, 0, 8.233, -0.003, 1, 8.266, -0.003, 8.3, -0.002, 8.333, 0.004, 1, 8.389, 0.014, 8.444, 0.021, 8.5, 0.021, 0, 8.933, -0.001, 2, 9, -0.001, 0, 9.633, -0.759, 0, 10.533, 0.382, 1, 10.633, 0.382, 10.733, -0.026, 10.833, -0.108]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_X", "Segments": [0, 0, 2, 2.7, 0, 0, 2.833, 2.919, 0, 3.067, -6.849, 0, 3.367, 6.236, 0, 3.633, -3.074, 0, 4.067, 0.7, 0, 4.433, -0.358, 0, 4.767, 0.175, 0, 5.067, -0.081, 0, 5.367, 0.035, 0, 5.7, -0.015, 0, 5.833, -0.004, 0, 6, -5.36, 0, 6.267, 11.06, 0, 6.533, -10.518, 0, 6.967, 4.682, 0, 7.267, -3.205, 0, 7.6, 1.731, 0, 7.9, -0.766, 0, 8.2, 0.386, 1, 8.244, 0.386, 8.289, 0.238, 8.333, -0.011, 1, 8.366, -0.197, 8.4, -0.27, 8.433, -0.27, 0, 8.7, 0.163, 0, 9, -0.098, 0, 9.233, 0.027, 0, 9.267, 0.025, 0, 9.3, 0.035, 0, 9.333, 0.024, 0, 9.367, 0.025, 0, 9.533, -0.027, 0, 9.567, -0.023, 0, 9.6, -0.032, 0, 9.633, -0.024, 0, 9.667, -0.029, 0, 9.7, -0.019, 0, 9.733, -0.021, 0, 9.867, -0.007, 0, 10.5, -0.017, 0, 10.7, 0.098, 1, 10.744, 0.098, 10.789, 0.043, 10.833, -0.005]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_X", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -0.281, 0, 0.833, 0.295, 0, 1.1, -0.111, 0, 1.433, 0.143, 0, 1.767, -0.292, 0, 2.067, 0.234, 0, 2.367, -0.121, 0, 2.667, 0.067, 0, 2.967, -0.018, 0, 3.133, 0.12, 0, 3.433, -0.203, 0, 3.7, 0.014, 0, 3.8, -0.028, 0, 4, 0.247, 0, 4.3, -0.213, 0, 4.6, 0.123, 0, 4.9, -0.056, 0, 5.3, 0.18, 0, 5.6, -0.198, 0, 5.9, 0.068, 0, 6.2, -0.105, 0, 6.233, -0.102, 0, 6.4, -0.893, 0, 6.667, 1.367, 0, 7.133, -1.379, 0, 7.433, 1.074, 0, 7.733, -0.588, 0, 8.033, 0.284, 0, 8.333, -0.125, 0, 8.633, 0.054, 0, 8.967, -0.022, 0, 9.267, 0.009, 0, 9.3, 0.008, 0, 9.467, 0.256, 0, 9.767, -0.285, 0, 10.033, 0.099, 0, 10.367, -0.14, 0, 10.7, 0.318, 1, 10.744, 0.318, 10.789, 0.15, 10.833, 0.006]}, {"Target": "Parameter", "Id": "ZD_BustLMotionY_L", "Segments": [0, 0.002, 0, 0.367, -0.001, 0, 0.767, 0.001, 2, 0.8, 0.001, 0, 1.2, -0.001, 0, 1.633, 0, 2, 2.033, 0, 2, 2.067, 0, 2, 2.467, 0, 2, 2.7, 0, 0, 2.933, 4.314, 0, 3.3, -6.515, 0, 3.733, 5.238, 0, 4.167, -3.107, 0, 4.6, 1.836, 0, 5, -1.089, 0, 5.433, 0.649, 0, 6.1, -9.686, 0, 6.5, 12.903, 0, 6.933, -10.746, 0, 7.367, 6.234, 0, 7.767, -3.423, 0, 8.2, 2.661, 1, 8.244, 2.661, 8.289, 2.544, 8.333, 1.744, 1, 8.433, -0.058, 8.533, -1.406, 8.633, -1.406, 0, 9.033, 0.937, 0, 9.467, -0.531, 0, 9.9, 0.252, 0, 10.333, -0.322, 1, 10.5, -0.322, 10.666, 0.096, 10.833, 0.148]}, {"Target": "Parameter", "Id": "ZD_BustLMotionX_L", "Segments": [0, 0, 2, 0.367, 0, 0, 0.7, 1.523, 0, 1.6, -0.768, 0, 1.9, 0.085, 0, 2.3, -0.379, 0, 2.433, -0.36, 0, 3.233, -1.31, 0, 3.833, 0.675, 0, 4.167, -0.275, 0, 4.467, 0.057, 0, 4.833, -0.096, 0, 4.967, -0.091, 0, 5.433, -1.071, 0, 6.5, 5.466, 0, 6.967, -3.083, 0, 7.3, 1.286, 0, 7.6, -0.306, 0, 7.9, 0.213, 0, 8.233, -0.007, 1, 8.266, -0.007, 8.3, -0.003, 8.333, 0.009, 1, 8.389, 0.029, 8.444, 0.042, 8.5, 0.042, 0, 8.933, -0.002, 2, 9, -0.002, 0, 9.633, -1.519, 0, 10.533, 0.763, 1, 10.633, 0.763, 10.733, -0.052, 10.833, -0.215]}, {"Target": "Parameter", "Id": "ZD_BustRMotionY_L", "Segments": [0, 0.002, 0, 0.367, -0.001, 0, 0.767, 0.001, 2, 0.8, 0.001, 0, 1.2, -0.001, 0, 1.633, 0, 2, 2.033, 0, 2, 2.067, 0, 2, 2.467, 0, 2, 2.7, 0, 0, 2.933, 4.314, 0, 3.3, -6.515, 0, 3.733, 5.238, 0, 4.167, -3.107, 0, 4.6, 1.836, 0, 5, -1.089, 0, 5.433, 0.649, 0, 6.1, -9.686, 0, 6.5, 12.903, 0, 6.933, -10.746, 0, 7.367, 6.234, 0, 7.767, -3.423, 0, 8.2, 2.661, 1, 8.244, 2.661, 8.289, 2.544, 8.333, 1.744, 1, 8.433, -0.058, 8.533, -1.406, 8.633, -1.406, 0, 9.033, 0.937, 0, 9.467, -0.531, 0, 9.9, 0.252, 0, 10.333, -0.322, 1, 10.5, -0.322, 10.666, 0.096, 10.833, 0.148]}, {"Target": "Parameter", "Id": "ZD_BustRMotionX_L", "Segments": [0, 0, 2, 0.367, 0, 0, 0.7, 1.523, 0, 1.6, -0.768, 0, 1.9, 0.085, 0, 2.3, -0.379, 0, 2.433, -0.36, 0, 3.233, -1.31, 0, 3.833, 0.675, 0, 4.167, -0.275, 0, 4.467, 0.057, 0, 4.833, -0.096, 0, 4.967, -0.091, 0, 5.433, -1.071, 0, 6.5, 5.466, 0, 6.967, -3.083, 0, 7.3, 1.286, 0, 7.6, -0.306, 0, 7.9, 0.213, 0, 8.233, -0.007, 1, 8.266, -0.007, 8.3, -0.003, 8.333, 0.009, 1, 8.389, 0.029, 8.444, 0.042, 8.5, 0.042, 0, 8.933, -0.002, 2, 9, -0.002, 0, 9.633, -1.519, 0, 10.533, 0.763, 1, 10.633, 0.763, 10.733, -0.052, 10.833, -0.215]}, {"Target": "Parameter", "Id": "ZD_BustLMotionY_X", "Segments": [0, 0.003, 0, 0.067, 0.004, 0, 0.5, -0.002, 0, 0.9, 0.001, 0, 1.333, -0.001, 0, 1.767, 0.001, 0, 2.167, 0, 2, 2.2, 0, 2, 2.6, 0, 0, 2.833, -1.268, 0, 3.1, 9.401, 0, 3.467, -11.728, 0, 3.9, 7.868, 0, 4.3, -5.176, 0, 4.7, 3.078, 0, 5.133, -1.816, 0, 5.567, 1.071, 0, 5.833, -0.219, 0, 5.967, 1.681, 0, 6.3, -20.472, 0, 6.667, 20.109, 0, 7.1, -16.696, 0, 7.5, 10.517, 0, 7.9, -5.809, 0, 8.333, 4.704, 0, 8.767, -1.772, 0, 9.167, 2.108, 0, 9.6, -0.392, 0, 10, 0.81, 0, 10.5, -0.301, 1, 10.611, -0.301, 10.722, -0.008, 10.833, 0.127]}, {"Target": "Parameter", "Id": "ZD_BustLMotionX_X", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -0.562, 0, 0.833, 0.591, 0, 1.1, -0.222, 0, 1.433, 0.286, 0, 1.767, -0.583, 0, 2.067, 0.468, 0, 2.367, -0.242, 0, 2.667, 0.135, 0, 2.967, -0.037, 0, 3.133, 0.241, 0, 3.433, -0.407, 0, 3.7, 0.028, 0, 3.8, -0.055, 0, 4, 0.493, 0, 4.3, -0.426, 0, 4.6, 0.247, 0, 4.9, -0.112, 0, 5.3, 0.36, 0, 5.6, -0.395, 0, 5.9, 0.137, 0, 6.2, -0.209, 0, 6.233, -0.204, 0, 6.4, -1.786, 0, 6.667, 2.734, 0, 7.133, -2.759, 0, 7.433, 2.149, 0, 7.733, -1.175, 0, 8.033, 0.567, 0, 8.333, -0.25, 0, 8.633, 0.107, 0, 8.967, -0.043, 0, 9.267, 0.018, 0, 9.3, 0.017, 0, 9.467, 0.511, 0, 9.767, -0.57, 0, 10.033, 0.198, 0, 10.367, -0.28, 0, 10.7, 0.636, 1, 10.744, 0.636, 10.789, 0.3, 10.833, 0.012]}, {"Target": "Parameter", "Id": "ZD_BustRMotionY_X", "Segments": [0, 0.003, 0, 0.067, 0.004, 0, 0.5, -0.002, 0, 0.9, 0.001, 0, 1.333, -0.001, 0, 1.767, 0.001, 0, 2.167, 0, 2, 2.2, 0, 2, 2.6, 0, 0, 2.833, -1.268, 0, 3.1, 9.401, 0, 3.467, -11.728, 0, 3.9, 7.868, 0, 4.3, -5.176, 0, 4.7, 3.078, 0, 5.133, -1.816, 0, 5.567, 1.071, 0, 5.833, -0.219, 0, 5.967, 1.681, 0, 6.3, -20.472, 0, 6.667, 20.109, 0, 7.1, -16.696, 0, 7.5, 10.517, 0, 7.9, -5.809, 0, 8.333, 4.704, 0, 8.767, -1.772, 0, 9.167, 2.108, 0, 9.6, -0.392, 0, 10, 0.81, 0, 10.5, -0.301, 1, 10.611, -0.301, 10.722, -0.008, 10.833, 0.127]}, {"Target": "Parameter", "Id": "ZD_BustRMotionX_X", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -0.562, 0, 0.833, 0.591, 0, 1.1, -0.222, 0, 1.433, 0.286, 0, 1.767, -0.583, 0, 2.067, 0.468, 0, 2.367, -0.242, 0, 2.667, 0.135, 0, 2.967, -0.037, 0, 3.133, 0.241, 0, 3.433, -0.407, 0, 3.7, 0.028, 0, 3.8, -0.055, 0, 4, 0.493, 0, 4.3, -0.426, 0, 4.6, 0.247, 0, 4.9, -0.112, 0, 5.3, 0.36, 0, 5.6, -0.395, 0, 5.9, 0.137, 0, 6.2, -0.209, 0, 6.233, -0.204, 0, 6.4, -1.786, 0, 6.667, 2.734, 0, 7.133, -2.759, 0, 7.433, 2.149, 0, 7.733, -1.175, 0, 8.033, 0.567, 0, 8.333, -0.25, 0, 8.633, 0.107, 0, 8.967, -0.043, 0, 9.267, 0.018, 0, 9.3, 0.017, 0, 9.467, 0.511, 0, 9.767, -0.57, 0, 10.033, 0.198, 0, 10.367, -0.28, 0, 10.7, 0.636, 1, 10.744, 0.636, 10.789, 0.3, 10.833, 0.012]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -0.075, 0, 0.333, 0.09, 0, 0.367, 0.086, 0, 0.733, 0.599, 0, 1.333, -0.286, 0, 1.4, -0.285, 0, 1.6, -0.363, 0, 2.033, 0.131, 0, 2.467, -0.206, 0, 2.933, 2.813, 0, 3.3, -4.727, 0, 3.733, 3.987, 0, 4.167, -2.327, 0, 4.6, 1.382, 0, 5.033, -0.85, 0, 5.433, 0.104, 0, 5.767, -0.226, 0, 6.633, 4.183, 0, 7, -5.764, 0, 7.433, 3.193, 0, 7.833, -1.856, 0, 8.267, 1.35, 1, 8.289, 1.35, 8.311, 1.407, 8.333, 1.212, 1, 8.455, 0.141, 8.578, -0.81, 8.7, -0.81, 0, 9.1, 0.47, 0, 9.6, -0.819, 0, 10.133, 0.312, 0, 10.367, 0.196, 0, 10.567, 0.321, 1, 10.656, 0.321, 10.744, 0.054, 10.833, -0.092]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, -0.115, 0, 0.167, -0.217, 0, 0.433, 0.108, 0, 0.567, -0.026, 0, 0.933, 1.224, 0, 1.4, 0.09, 0, 1.5, 0.094, 0, 1.833, -0.442, 0, 2.167, 0.218, 0, 2.6, -0.431, 0, 2.7, -0.387, 0, 2.833, -1.114, 0, 3.1, 6.019, 0, 3.467, -8.779, 0, 3.9, 5.665, 0, 4.3, -3.835, 0, 4.733, 2.312, 0, 5.133, -1.418, 0, 5.5, 0.206, 0, 5.9, -0.767, 0, 6.267, 0.119, 0, 6.367, 0.052, 0, 6.8, 9.009, 0, 7.167, -9.091, 0, 7.567, 5.473, 0, 7.967, -3.447, 1, 8.089, -3.447, 8.211, -0.914, 8.333, 1.86, 1, 8.355, 2.365, 8.378, 2.223, 8.4, 2.223, 0, 8.833, -1.324, 0, 9.233, 0.797, 0, 9.8, -1.412, 0, 10.233, -0.029, 0, 10.5, -0.232, 0, 10.8, 0.457, 1, 10.811, 0.457, 10.822, 0.437, 10.833, 0.408]}, {"Target": "Parameter", "Id": "Param105", "Segments": [0, -0.038, 0, 0.167, -0.072, 0, 0.433, 0.036, 0, 0.567, -0.009, 0, 0.933, 0.408, 0, 1.4, 0.03, 0, 1.5, 0.031, 0, 1.833, -0.147, 0, 2.167, 0.073, 0, 2.6, -0.144, 0, 2.7, -0.129, 0, 2.833, -0.371, 0, 3.1, 2.006, 0, 3.467, -2.926, 0, 3.9, 1.888, 0, 4.3, -1.278, 0, 4.733, 0.771, 0, 5.133, -0.473, 0, 5.5, 0.069, 0, 5.9, -0.256, 0, 6.267, 0.04, 0, 6.367, 0.017, 0, 6.8, 3.003, 0, 7.167, -3.03, 0, 7.567, 1.824, 0, 7.967, -1.149, 1, 8.089, -1.149, 8.211, -0.317, 8.333, 0.62, 1, 8.355, 0.79, 8.378, 0.741, 8.4, 0.741, 0, 8.833, -0.441, 0, 9.233, 0.266, 0, 9.8, -0.471, 0, 10.233, -0.01, 0, 10.5, -0.077, 0, 10.8, 0.152, 1, 10.811, 0.152, 10.822, 0.146, 10.833, 0.136]}, {"Target": "Parameter", "Id": "Param106", "Segments": [0, 0, 2, 0.367, 0, 0, 0.7, 1.143, 0, 1.633, -0.628, 0, 2, 0.162, 0, 2.4, -0.305, 0, 2.933, 4.68, 0, 3.233, -7.284, 0, 3.7, 3.854, 0, 4.1, -1.724, 0, 4.467, 0.815, 0, 4.833, -0.431, 0, 5.167, 0.094, 0, 5.5, -0.747, 0, 6.633, 7.342, 0, 6.967, -7.913, 0, 7.333, 3.352, 0, 7.7, -1.796, 0, 8.1, 1.053, 1, 8.178, 1.053, 8.255, 0.688, 8.333, 0.098, 1, 8.389, -0.323, 8.444, -0.475, 8.5, -0.475, 0, 8.867, 0.221, 0, 9.233, -0.117, 0, 9.3, -0.099, 0, 9.667, -0.959, 0, 10.567, 0.525, 1, 10.656, 0.525, 10.744, 0.103, 10.833, -0.127]}, {"Target": "Parameter", "Id": "Param104", "Segments": [0, -0.025, 0, 0.333, 0.03, 0, 0.367, 0.029, 0, 0.733, 0.2, 0, 1.333, -0.095, 2, 1.4, -0.095, 0, 1.6, -0.121, 0, 2.033, 0.044, 0, 2.467, -0.069, 0, 2.933, 0.938, 0, 3.3, -1.576, 0, 3.733, 1.329, 0, 4.167, -0.776, 0, 4.6, 0.461, 0, 5.033, -0.283, 0, 5.433, 0.035, 0, 5.767, -0.075, 0, 6.633, 1.394, 0, 7, -1.921, 0, 7.433, 1.064, 0, 7.833, -0.619, 0, 8.267, 0.45, 1, 8.289, 0.45, 8.311, 0.469, 8.333, 0.404, 1, 8.455, 0.046, 8.578, -0.27, 8.7, -0.27, 0, 9.1, 0.157, 0, 9.6, -0.273, 0, 10.133, 0.104, 0, 10.367, 0.065, 0, 10.567, 0.107, 1, 10.656, 0.107, 10.744, 0.018, 10.833, -0.031]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, -0.019, 0, 0.167, -0.036, 0, 0.433, 0.018, 0, 0.567, -0.004, 0, 0.933, 0.204, 0, 1.4, 0.015, 0, 1.5, 0.016, 0, 1.833, -0.074, 0, 2.167, 0.036, 0, 2.6, -0.072, 0, 2.7, -0.065, 0, 2.833, -0.186, 0, 3.1, 1.003, 0, 3.467, -1.463, 0, 3.9, 0.944, 0, 4.3, -0.639, 0, 4.733, 0.385, 0, 5.133, -0.236, 0, 5.5, 0.034, 0, 5.9, -0.128, 0, 6.267, 0.02, 0, 6.367, 0.009, 0, 6.8, 1.502, 0, 7.167, -1.515, 0, 7.567, 0.912, 0, 7.967, -0.575, 1, 8.089, -0.575, 8.211, -0.16, 8.333, 0.31, 1, 8.355, 0.395, 8.378, 0.371, 8.4, 0.371, 0, 8.833, -0.221, 0, 9.233, 0.133, 0, 9.8, -0.235, 0, 10.233, -0.005, 0, 10.5, -0.039, 0, 10.8, 0.076, 1, 10.811, 0.076, 10.822, 0.073, 10.833, 0.068]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0.009, 0, 0.267, -0.03, 0, 0.533, 0.013, 0, 0.733, -0.071, 0, 1.067, 0.106, 0, 1.4, -0.059, 0, 1.7, 0.043, 0, 1.967, -0.062, 0, 2.3, 0.07, 0, 2.667, -0.047, 0, 2.733, -0.041, 0, 2.967, -0.338, 0, 3.233, 1.062, 0, 3.567, -1.351, 0, 3.967, 1.01, 0, 4.367, -0.703, 0, 4.8, 0.459, 0, 5.2, -0.271, 0, 5.567, 0.163, 0, 5.967, -0.098, 0, 6.3, 0.012, 0, 6.633, -0.296, 0, 6.967, 1.213, 0, 7.3, -1.557, 0, 7.633, 1.208, 0, 8.033, -0.746, 1, 8.133, -0.746, 8.233, -0.309, 8.333, 0.236, 1, 8.366, 0.418, 8.4, 0.418, 8.433, 0.418, 0, 8.867, -0.255, 0, 9.267, 0.154, 0, 9.9, -0.1, 0, 10.267, 0.073, 0, 10.6, -0.065, 1, 10.678, -0.065, 10.755, 0.007, 10.833, 0.05]}, {"Target": "Parameter", "Id": "Param107", "Segments": [0, 0.046, 0, 0.3, -0.067, 0, 0.667, 0.046, 0, 0.867, -0.098, 0, 1.167, 0.203, 0, 1.5, -0.16, 0, 1.8, 0.115, 0, 2.1, -0.134, 0, 2.4, 0.158, 0, 2.767, -0.129, 0, 2.8, -0.128, 0, 3.033, -0.438, 0, 3.333, 1.765, 0, 3.667, -2.716, 0, 4.033, 2.366, 0, 4.433, -1.66, 0, 4.833, 1.114, 0, 5.233, -0.687, 0, 5.633, 0.429, 0, 6, -0.27, 0, 6.367, 0.083, 0, 6.7, -0.493, 0, 7.033, 2.028, 0, 7.367, -3.063, 0, 7.733, 2.802, 0, 8.1, -1.962, 1, 8.178, -1.962, 8.255, -1.081, 8.333, 0.206, 1, 8.378, 0.942, 8.422, 1.136, 8.467, 1.136, 0, 8.9, -0.623, 0, 9.333, 0.366, 0, 9.933, -0.14, 0, 10.367, 0.169, 0, 10.7, -0.154, 1, 10.744, -0.154, 10.789, -0.064, 10.833, 0.013]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, -0.038, 0, 0.333, 0.045, 0, 0.367, 0.043, 0, 0.733, 0.299, 0, 1.333, -0.143, 0, 1.4, -0.142, 0, 1.6, -0.182, 0, 2.033, 0.066, 0, 2.467, -0.103, 0, 2.933, 1.407, 0, 3.3, -2.363, 0, 3.733, 1.993, 0, 4.167, -1.164, 0, 4.6, 0.691, 0, 5.033, -0.425, 0, 5.433, 0.052, 0, 5.767, -0.113, 0, 6.633, 2.091, 0, 7, -2.882, 0, 7.433, 1.596, 0, 7.833, -0.928, 0, 8.267, 0.675, 1, 8.289, 0.675, 8.311, 0.704, 8.333, 0.606, 1, 8.455, 0.069, 8.578, -0.405, 8.7, -0.405, 0, 9.1, 0.235, 0, 9.6, -0.409, 0, 10.133, 0.156, 0, 10.367, 0.098, 0, 10.567, 0.16, 1, 10.656, 0.16, 10.744, 0.027, 10.833, -0.046]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, -0.057, 0, 0.167, -0.108, 0, 0.433, 0.054, 0, 0.567, -0.013, 0, 0.933, 0.612, 0, 1.4, 0.045, 0, 1.5, 0.047, 0, 1.833, -0.221, 0, 2.167, 0.109, 0, 2.6, -0.215, 0, 2.7, -0.194, 0, 2.833, -0.557, 0, 3.1, 3.01, 0, 3.467, -4.39, 0, 3.9, 2.832, 0, 4.3, -1.918, 0, 4.733, 1.156, 0, 5.133, -0.709, 0, 5.5, 0.103, 0, 5.9, -0.383, 0, 6.267, 0.059, 0, 6.367, 0.026, 0, 6.8, 4.505, 0, 7.167, -4.546, 0, 7.567, 2.737, 0, 7.967, -1.724, 1, 8.089, -1.724, 8.211, -0.472, 8.333, 0.93, 1, 8.355, 1.185, 8.378, 1.112, 8.4, 1.112, 0, 8.833, -0.662, 0, 9.233, 0.398, 0, 9.8, -0.706, 0, 10.233, -0.015, 0, 10.5, -0.116, 0, 10.8, 0.228, 1, 10.811, 0.228, 10.822, 0.219, 10.833, 0.204]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, -0.041, 0, 0.6, 0.053, 0, 1.7, -1.02, 0, 2.2, 0.864, 0, 2.633, -0.261, 0, 2.967, 0.408, 0, 3.3, -0.511, 0, 3.767, 0.439, 0, 4.2, -0.258, 0, 4.6, 0.152, 0, 5.033, -0.094, 0, 5.433, 0.015, 0, 5.8, -0.022, 0, 6.633, 0.419, 0, 7, -0.582, 0, 7.433, 0.324, 0, 7.833, -0.189, 1, 8, -0.189, 8.166, -0.044, 8.333, 0.313, 1, 8.544, 0.766, 8.756, 1.03, 8.967, 1.03, 0, 9.333, -2.08, 0, 9.733, 1.173, 0, 10.167, -0.704, 0, 10.6, 0.465, 1, 10.678, 0.465, 10.755, 0.124, 10.833, -0.08]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, -0.043, 0, 0.1, -0.063, 0, 0.167, -0.058, 0, 0.2, -0.071, 0, 0.5, 0.054, 0, 0.6, 0.044, 0, 0.867, 0.095, 0, 1.367, 0.03, 0, 1.5, 0.201, 0, 1.9, -2.052, 0, 2.333, 0.886, 0, 2.767, -0.614, 0, 3.1, 0.886, 0, 3.467, -1.011, 0, 3.9, 0.646, 0, 4.333, -0.431, 0, 4.733, 0.255, 0, 5.167, -0.157, 0, 5.533, 0.024, 0, 5.933, -0.076, 0, 6.267, 0.009, 0, 6.333, 0.005, 0, 6.8, 0.914, 0, 7.167, -0.937, 0, 7.567, 0.569, 0, 7.967, -0.354, 1, 8.089, -0.354, 8.211, -0.271, 8.333, 0.331, 1, 8.611, 1.7, 8.889, 2.718, 9.167, 2.718, 0, 9.5, -3.304, 0, 9.867, 2.095, 0, 10.3, -1.275, 0, 10.7, 0.753, 1, 10.744, 0.753, 10.789, 0.529, 10.833, 0.337]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0.052, 0, 0.167, -0.097, 0, 0.2, -0.088, 0, 0.3, -0.144, 0, 0.567, 0.08, 0, 0.8, -0.016, 0, 1.067, 0.025, 0, 1.367, -0.011, 0, 1.667, 0.653, 0, 2.033, -1.274, 0, 2.4, 1.147, 0, 2.833, -0.807, 0, 3.233, 1.033, 0, 3.567, -1.042, 0, 3.967, 0.734, 0, 4.4, -0.482, 0, 4.8, 0.307, 0, 5.2, -0.178, 0, 5.6, 0.102, 0, 5.967, -0.059, 0, 6.3, 0.003, 0, 6.667, -0.183, 0, 6.967, 0.759, 0, 7.3, -0.99, 0, 7.633, 0.789, 0, 8.033, -0.477, 1, 8.133, -0.477, 8.233, -0.176, 8.333, 0.146, 1, 8.344, 0.182, 8.356, 0.164, 8.367, 0.164, 0, 8.733, -0.207, 0, 8.867, -0.172, 0, 9, -0.267, 0, 9.3, 2.251, 0, 9.633, -3.373, 0, 9.967, 2.817, 0, 10.333, -1.747, 0, 10.733, 0.919, 1, 10.766, 0.919, 10.8, 0.768, 10.833, 0.616]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0.145, 0, 0.333, -0.14, 0, 0.667, 0.14, 0, 0.933, -0.049, 0, 1.2, 0.039, 0, 1.433, -0.014, 0, 1.767, 0.74, 0, 2.133, -1.715, 0, 2.5, 1.83, 0, 2.867, -1.432, 0, 3.3, 1.459, 0, 3.667, -1.637, 0, 4.033, 1.32, 0, 4.433, -0.862, 0, 4.833, 0.537, 0, 5.233, -0.318, 0, 5.633, 0.193, 0, 6.033, -0.117, 0, 6.367, 0.031, 0, 6.733, -0.213, 0, 7.067, 0.916, 0, 7.4, -1.432, 0, 7.733, 1.372, 0, 8.1, -0.963, 1, 8.178, -0.963, 8.255, -0.486, 8.333, 0.152, 1, 8.366, 0.425, 8.4, 0.461, 8.433, 0.461, 0, 8.8, -0.387, 0, 9.4, 2.502, 0, 9.733, -4.589, 0, 10.067, 4.64, 0, 10.433, -3.349, 0, 10.8, 1.938, 1, 10.811, 1.938, 10.822, 1.869, 10.833, 1.767]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, -0.075, 0, 0.333, 0.09, 0, 0.367, 0.086, 0, 0.733, 0.599, 0, 1.333, -0.286, 0, 1.4, -0.285, 0, 1.6, -0.363, 0, 2.033, 0.131, 0, 2.467, -0.206, 0, 2.933, 2.813, 0, 3.3, -4.727, 0, 3.733, 3.987, 0, 4.167, -2.327, 0, 4.6, 1.382, 0, 5.033, -0.85, 0, 5.433, 0.104, 0, 5.767, -0.226, 0, 6.633, 4.183, 0, 7, -5.764, 0, 7.433, 3.193, 0, 7.833, -1.856, 0, 8.267, 1.35, 1, 8.289, 1.35, 8.311, 1.407, 8.333, 1.212, 1, 8.455, 0.141, 8.578, -0.81, 8.7, -0.81, 0, 9.1, 0.47, 0, 9.6, -0.819, 0, 10.133, 0.312, 0, 10.367, 0.196, 0, 10.567, 0.321, 1, 10.656, 0.321, 10.744, 0.054, 10.833, -0.092]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, -0.115, 0, 0.167, -0.217, 0, 0.433, 0.108, 0, 0.567, -0.026, 0, 0.933, 1.224, 0, 1.4, 0.09, 0, 1.5, 0.094, 0, 1.833, -0.442, 0, 2.167, 0.218, 0, 2.6, -0.431, 0, 2.7, -0.387, 0, 2.833, -1.114, 0, 3.1, 6.019, 0, 3.467, -8.779, 0, 3.9, 5.665, 0, 4.3, -3.835, 0, 4.733, 2.312, 0, 5.133, -1.418, 0, 5.5, 0.206, 0, 5.9, -0.767, 0, 6.267, 0.119, 0, 6.367, 0.052, 0, 6.8, 9.009, 0, 7.167, -9.091, 0, 7.567, 5.473, 0, 7.967, -3.447, 1, 8.089, -3.447, 8.211, -0.914, 8.333, 1.86, 1, 8.355, 2.365, 8.378, 2.223, 8.4, 2.223, 0, 8.833, -1.324, 0, 9.233, 0.797, 0, 9.8, -1.412, 0, 10.233, -0.029, 0, 10.5, -0.232, 0, 10.8, 0.457, 1, 10.811, 0.457, 10.822, 0.437, 10.833, 0.408]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0.057, 0, 0.267, -0.179, 0, 0.533, 0.077, 0, 0.733, -0.424, 0, 1.067, 0.634, 0, 1.4, -0.355, 0, 1.7, 0.257, 0, 1.967, -0.375, 0, 2.3, 0.418, 0, 2.667, -0.282, 0, 2.733, -0.246, 0, 2.967, -2.029, 0, 3.233, 6.373, 0, 3.567, -8.108, 0, 3.967, 6.057, 0, 4.367, -4.218, 0, 4.8, 2.757, 0, 5.2, -1.629, 0, 5.567, 0.975, 0, 5.967, -0.586, 0, 6.3, 0.069, 0, 6.633, -1.778, 0, 6.967, 7.276, 0, 7.3, -9.345, 0, 7.633, 7.245, 0, 8.033, -4.475, 1, 8.133, -4.475, 8.233, -1.823, 8.333, 1.416, 1, 8.366, 2.495, 8.4, 2.507, 8.433, 2.507, 0, 8.867, -1.532, 0, 9.267, 0.926, 0, 9.9, -0.598, 0, 10.267, 0.439, 0, 10.6, -0.392, 1, 10.678, -0.392, 10.755, 0.042, 10.833, 0.303]}, {"Target": "Parameter", "Id": "Param108", "Segments": [0, 0, 2, 0.367, 0, 0, 0.867, 0.773, 0, 1.7, -0.175, 0, 2, -0.082, 0, 2.7, -0.299, 0, 2.933, 2.447, 0, 3.333, -2.77, 0, 3.867, 0.524, 0, 4.267, -0.117, 0, 4.633, -0.014, 0, 5.6, -0.556, 0, 6.667, 5.004, 0, 7.033, -1.375, 0, 7.433, -0.133, 0, 7.733, -0.282, 0, 8.267, 0.131, 1, 8.289, 0.131, 8.311, 0.134, 8.333, 0.124, 1, 8.466, 0.062, 8.6, 0.006, 8.733, 0.006, 0, 8.933, 0.01, 2, 8.967, 0.01, 0, 9.833, -0.779, 0, 10.633, 0.159, 1, 10.7, 0.159, 10.766, 0.072, 10.833, 0.014]}, {"Target": "Parameter", "Id": "Param86", "Segments": [0, -0.001, 2, 0.067, -0.001, 0, 0.733, 0.309, 0, 1.433, -4.626, 0, 1.867, 6.519, 0, 2.3, -3.191, 0, 2.8, 2.499, 0, 3.233, -3.365, 0, 3.7, 1.826, 0, 4.133, -1.04, 0, 4.567, 0.617, 0, 5, -0.379, 0, 5.367, 0.05, 0, 5.7, -0.142, 0, 6.633, 2.094, 0, 7, -2.896, 0, 7.433, 1.613, 0, 7.833, -0.94, 0, 8.267, 0.682, 1, 8.289, 0.682, 8.311, 0.711, 8.333, 0.612, 1, 8.455, 0.07, 8.578, -0.409, 8.7, -0.409, 0, 9.1, 0.238, 0, 9.6, -0.41, 0, 10.133, 0.156, 0, 10.367, 0.098, 0, 10.567, 0.16, 1, 10.656, 0.16, 10.744, 0.03, 10.833, -0.041]}, {"Target": "Parameter", "Id": "Param87", "Segments": [0, 0, 0, 0.2, -0.002, 0, 0.367, -0.001, 0, 0.5, -0.049, 0, 0.933, 0.619, 0, 1.133, 0.343, 0, 1.267, 1.04, 0, 1.633, -9.262, 0, 2.067, 9.693, 0, 2.433, -5.164, 0, 3, 4.319, 0, 3.433, -4.747, 0, 3.833, 2.768, 0, 4.267, -1.693, 0, 4.7, 1.011, 0, 5.1, -0.636, 0, 5.467, 0.13, 0, 5.833, -0.425, 0, 6.267, 0.093, 0, 6.367, 0.022, 0, 6.8, 4.563, 0, 7.167, -4.649, 0, 7.567, 2.815, 0, 7.967, -1.756, 1, 8.089, -1.756, 8.211, -0.485, 8.333, 0.939, 1, 8.355, 1.198, 8.378, 1.123, 8.4, 1.123, 0, 8.8, -0.669, 0, 9.233, 0.403, 0, 9.8, -0.707, 0, 10.233, -0.014, 0, 10.5, -0.117, 0, 10.767, 0.222, 1, 10.789, 0.222, 10.811, 0.196, 10.833, 0.165]}, {"Target": "Parameter", "Id": "Param110", "Segments": [0, 0, 2, 0.367, 0, 0, 1.133, 3.389, 0, 3.467, -3.22, 0, 4.667, -0.159, 0, 5.833, -2.376, 0, 6.733, 8.425, 1, 7.266, 8.425, 7.8, 6.651, 8.333, 2.873, 1, 8.911, -1.22, 9.489, -3.387, 10.067, -3.387, 1, 10.322, -3.387, 10.578, -0.814, 10.833, -0.221]}, {"Target": "Parameter", "Id": "Param111", "Segments": [0, -0.128, 0, 0.067, -0.141, 0, 0.467, 0.143, 0, 0.867, -0.144, 0, 1.267, 0.146, 0, 1.667, -0.148, 0, 2.067, 0.149, 0, 2.467, -0.151, 0, 2.933, 5.802, 0, 3.3, -8.609, 0, 3.733, 6.871, 0, 4.167, -4.051, 0, 4.6, 2.391, 0, 5, -1.412, 0, 5.433, 0.842, 0, 5.867, -0.499, 0, 6.3, 0.241, 0, 6.467, 0.08, 0, 6.667, 5.261, 0, 7, -5.775, 0, 7.433, 3.001, 0, 7.833, -1.718, 0, 8.267, 1.53, 1, 8.289, 1.53, 8.311, 1.589, 8.333, 1.35, 1, 8.444, 0.153, 8.556, -0.911, 8.667, -0.911, 0, 9.1, 0.541, 0, 9.533, -0.306, 0, 9.967, 0.124, 0, 10.367, -0.126, 0, 10.767, 0.127, 1, 10.789, 0.127, 10.811, 0.113, 10.833, 0.097]}, {"Target": "Parameter", "Id": "Param118", "Segments": [0, -0.025, 0, 0.333, 0.03, 0, 0.367, 0.029, 0, 0.733, 0.2, 0, 1.333, -0.095, 2, 1.4, -0.095, 0, 1.6, -0.121, 0, 2.033, 0.044, 0, 2.467, -0.069, 0, 2.933, 0.938, 0, 3.3, -1.576, 0, 3.733, 1.329, 0, 4.167, -0.776, 0, 4.6, 0.461, 0, 5.033, -0.283, 0, 5.433, 0.035, 0, 5.767, -0.075, 0, 6.633, 1.394, 0, 7, -1.921, 0, 7.433, 1.064, 0, 7.833, -0.619, 0, 8.267, 0.45, 1, 8.289, 0.45, 8.311, 0.469, 8.333, 0.404, 1, 8.455, 0.046, 8.578, -0.27, 8.7, -0.27, 0, 9.1, 0.157, 0, 9.6, -0.273, 0, 10.133, 0.104, 0, 10.367, 0.065, 0, 10.567, 0.107, 1, 10.656, 0.107, 10.744, 0.018, 10.833, -0.031]}, {"Target": "Parameter", "Id": "Param117", "Segments": [0, -0.038, 0, 0.167, -0.072, 0, 0.433, 0.036, 0, 0.567, -0.009, 0, 0.933, 0.408, 0, 1.4, 0.03, 0, 1.5, 0.031, 0, 1.833, -0.147, 0, 2.167, 0.073, 0, 2.6, -0.144, 0, 2.7, -0.129, 0, 2.833, -0.371, 0, 3.1, 2.006, 0, 3.467, -2.926, 0, 3.9, 1.888, 0, 4.3, -1.278, 0, 4.733, 0.771, 0, 5.133, -0.473, 0, 5.5, 0.069, 0, 5.9, -0.256, 0, 6.267, 0.04, 0, 6.367, 0.017, 0, 6.8, 3.003, 0, 7.167, -3.03, 0, 7.567, 1.824, 0, 7.967, -1.149, 1, 8.089, -1.149, 8.211, -0.317, 8.333, 0.62, 1, 8.355, 0.79, 8.378, 0.741, 8.4, 0.741, 0, 8.833, -0.441, 0, 9.233, 0.266, 0, 9.8, -0.471, 0, 10.233, -0.01, 0, 10.5, -0.077, 0, 10.8, 0.152, 1, 10.811, 0.152, 10.822, 0.146, 10.833, 0.136]}, {"Target": "Parameter", "Id": "Param116", "Segments": [0, 0.019, 0, 0.267, -0.06, 0, 0.533, 0.026, 0, 0.733, -0.141, 0, 1.067, 0.211, 0, 1.4, -0.118, 0, 1.7, 0.086, 0, 1.967, -0.125, 0, 2.3, 0.139, 0, 2.667, -0.094, 0, 2.733, -0.082, 0, 2.967, -0.676, 0, 3.233, 2.124, 0, 3.567, -2.703, 0, 3.967, 2.019, 0, 4.367, -1.406, 0, 4.8, 0.919, 0, 5.2, -0.543, 0, 5.567, 0.325, 0, 5.967, -0.195, 0, 6.3, 0.023, 0, 6.633, -0.593, 0, 6.967, 2.425, 0, 7.3, -3.115, 0, 7.633, 2.415, 0, 8.033, -1.492, 1, 8.133, -1.492, 8.233, -0.616, 8.333, 0.472, 1, 8.366, 0.835, 8.4, 0.836, 8.433, 0.836, 0, 8.867, -0.511, 0, 9.267, 0.309, 0, 9.9, -0.199, 0, 10.267, 0.146, 0, 10.6, -0.131, 1, 10.678, -0.131, 10.755, 0.014, 10.833, 0.101]}, {"Target": "Parameter", "Id": "Param119", "Segments": [0, 0.066, 0, 0.3, -0.096, 0, 0.667, 0.065, 0, 0.867, -0.14, 0, 1.167, 0.29, 0, 1.5, -0.229, 0, 1.8, 0.164, 0, 2.1, -0.191, 0, 2.4, 0.225, 0, 2.767, -0.185, 0, 2.8, -0.183, 0, 3.033, -0.626, 0, 3.333, 2.522, 0, 3.667, -3.881, 0, 4.033, 3.381, 0, 4.433, -2.372, 0, 4.833, 1.591, 0, 5.233, -0.982, 0, 5.633, 0.612, 0, 6, -0.386, 0, 6.367, 0.119, 0, 6.7, -0.705, 0, 7.033, 2.897, 0, 7.367, -4.376, 0, 7.733, 4.003, 0, 8.1, -2.803, 1, 8.178, -2.803, 8.255, -1.543, 8.333, 0.295, 1, 8.378, 1.345, 8.422, 1.622, 8.467, 1.622, 0, 8.9, -0.89, 0, 9.333, 0.523, 0, 9.933, -0.2, 0, 10.367, 0.242, 0, 10.7, -0.22, 1, 10.744, -0.22, 10.789, -0.092, 10.833, 0.018]}, {"Target": "Parameter", "Id": "Param121", "Segments": [0, 0.139, 0, 0.367, -0.147, 0, 0.733, 0.073, 0, 0.967, -0.155, 0, 1.267, 0.379, 0, 1.6, -0.384, 0, 1.9, 0.301, 0, 2.2, -0.309, 0, 2.5, 0.358, 0, 3.1, -0.553, 0, 3.433, 2.908, 0, 3.767, -5.137, 0, 4.133, 5.142, 0, 4.5, -3.904, 0, 4.9, 2.714, 0, 5.3, -1.759, 0, 5.7, 1.118, 0, 6.067, -0.737, 0, 6.433, 0.329, 0, 6.767, -0.89, 0, 7.133, 3.393, 0, 7.467, -5.799, 0, 7.833, 5.905, 0, 8.2, -4.655, 1, 8.244, -4.655, 8.289, -4.168, 8.333, -2.334, 1, 8.411, 0.876, 8.489, 3.081, 8.567, 3.081, 0, 8.933, -1.746, 0, 9.367, 0.921, 0, 9.767, -0.375, 0, 10.467, 0.292, 0, 10.8, -0.34, 1, 10.811, -0.34, 10.822, -0.323, 10.833, -0.297]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 0.333, 0, 2, 2.3, 0, 2, 2.667, 0, 2, 3.667, 0, 2, 6.167, 0, 0, 6.333, -1, 0, 6.667, 0, 2, 8.133, 0, 2, 8.333, 0, 2, 8.5, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 0.333, 0, 2, 2.3, 0, 2, 2.667, 0, 2, 3.667, 0, 2, 6.167, 0, 0, 6.333, -1, 0, 6.667, 0, 2, 8.133, 0, 2, 8.333, 0, 2, 8.5, 0, 2, 10.467, 0, 2, 10.833, 0]}, {"Target": "Parameter", "Id": "Param114", "Segments": [0, 3, 0, 10.833, 3]}, {"Target": "Parameter", "Id": "Param113", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "MB_shuiwenDH", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param112", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param101", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "MB_suduxianjiaoduBGB", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "MB_sucaiAjiya", "Segments": [0, 0.1, 0, 10.833, 0.1]}, {"Target": "Parameter", "Id": "MB_sucaiAlashen", "Segments": [0, 0.2, 0, 10.833, 0.2]}, {"Target": "Parameter", "Id": "MB_suduxianxunhuanBGB", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "MB_sucaiBjiya", "Segments": [0, 0.15, 0, 10.833, 0.15]}, {"Target": "Parameter", "Id": "MB_sucaiBlashen", "Segments": [0, 0.3, 0, 10.833, 0.3]}, {"Target": "Parameter", "Id": "MB_beijingbanjiaodu", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "All_X", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "All_Y", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "All_Angle", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "All_Size", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param97", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param98", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param100", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 30, 0, 10.833, 30]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 30, 0, 10.833, 30]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param85", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 30, 0, 10.833, 30]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param78", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param83", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param84", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param93", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param94", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param95", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param96", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param102", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param122", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param123", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "FG_Black", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "FG_White", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "BG_Black", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "BG_White", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandLREDisplay", "Segments": [0, 30, 0, 10.833, 30]}, {"Target": "Parameter", "Id": "ParamHandL1Display", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandL3Display", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandL4Display", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandL6Display", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandL7Display", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandL8Display", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandL9Display", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRREDisplay", "Segments": [0, 30, 0, 10.833, 30]}, {"Target": "Parameter", "Id": "ParamHandR1Display", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandR2Display", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandR3Display", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandR6Display", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandR7Display", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandR8Display", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandR9Display", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param243", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param120", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param99", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param242", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamArmL_A", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamArmL_B", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamArmL_C", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "Segments": [0, 30, 0, 10.833, 30]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandL_DMZ", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandL_XMZ", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "siwala", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamArmR_A", "Segments": [0, 30, 0, 10.833, 30]}, {"Target": "Parameter", "Id": "ParamArmR_B", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamArmR_C", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandR_DMZ", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamWaistZ", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamButtZ", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "NULL1", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "NULL2", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "NULL3", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRsize", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeGGDisplay", "Segments": [0, 1, 0, 10.833, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallDisplay", "Segments": [0, 1, 0, 10.833, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenYSD", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthsize", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthY", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthX", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthADisplay", "Segments": [0, 1, 0, 10.833, 1]}, {"Target": "Parameter", "Id": "ParamMouthBDisplay", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthCDisplay", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "MB_yiwenTMD", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "MB_yiwenJD", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "MB_fuhaoyinyue", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "MB_fuhaohan", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "MB_fuhaogantan", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "MB_f<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "MB_yiwenSS", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "nua", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "MB_fuhaojuhaoDH", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "xiu", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "null_a", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "null_c", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "null_d", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "null_e", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "OP_Change", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_L", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_X", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "NULL4", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "NULL5", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "NULL6", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngleX", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngleY", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngleX", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngleY", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 10.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 10.833, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.333, "Value": ""}, {"Time": 10.333, "Value": ""}]}