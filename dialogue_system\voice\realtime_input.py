#!/usr/bin/env python3
"""
Live2D语音对话系统 - 实时语音输入

这个模块提供了实时语音输入功能，包括：
- 持续监听麦克风
- 自动检测语音活动
- 自动开始/结束录音
- 静音检测和语音分段

使用示例：
    from dialogue_system.voice.realtime_input import RealtimeInput
    from dialogue_system.voice.voice_processor import VoiceProcessor
    
    # 创建实时语音输入
    realtime_input = RealtimeInput(voice_processor)
    
    # 开始监听
    realtime_input.start()
    
    # 停止监听
    realtime_input.stop()
"""

import threading
import time
import numpy as np
from typing import Optional, Callable, Any
from PySide6.QtCore import QObject, Signal, QTimer
from .voice_processor import VoiceProcessor


class RealtimeInput(QObject):
    """实时语音输入管理器"""
    
    # 信号定义
    speech_detected = Signal()
    speech_ended = Signal()
    audio_processed = Signal(object)  # 传递音频数据
    status_changed = Signal(str)  # 状态变化
    volume_level_changed = Signal(float)  # 音量级别变化
    
    def __init__(self, voice_processor: VoiceProcessor, config_manager=None):
        """初始化实时语音输入"""
        super().__init__()
        
        self.voice_processor = voice_processor
        self.config_manager = config_manager
        
        # 默认配置
        self.default_config = {
            "volume_threshold": 0.01,
            "silence_duration": 2.0,
            "min_speech_duration": 0.5,
            "max_speech_duration": 30.0,
            "auto_restart": True,
            "sensitivity": 1.0  # 灵敏度调节
        }
        
        # 加载配置
        self.realtime_config = self._load_realtime_config()
        
        # 状态控制
        self.is_active = False
        self.is_listening = False
        self.is_recording = False
        self.monitoring_thread = None
        
        # 语音活动检测状态
        self.last_audio_time = 0
        self.speech_start_time = 0
        self.silence_start_time = 0
        self.current_audio_buffer = []
        
        # 回调函数
        self.on_audio_ready: Optional[Callable] = None
        self.on_status_change: Optional[Callable] = None
        self.on_speech_activity: Optional[Callable] = None
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_status)
        
        print("🔄 实时语音输入管理器初始化完成")
        
    def _load_realtime_config(self) -> dict:
        """加载实时配置"""
        if self.config_manager:
            voice_config = self.config_manager.config.get("voice_dialogue", {})
            realtime_config = voice_config.get("realtime_config", {})
            
            config = self.default_config.copy()
            config.update(realtime_config)
            return config
        else:
            return self.default_config.copy()
            
    def set_volume_threshold(self, threshold: float):
        """设置音量阈值"""
        self.realtime_config["volume_threshold"] = max(0.001, min(1.0, threshold))
        print(f"🔊 音量阈值已设置为: {self.realtime_config['volume_threshold']:.3f}")
        
    def set_silence_duration(self, duration: float):
        """设置静音持续时间"""
        self.realtime_config["silence_duration"] = max(0.5, min(10.0, duration))
        print(f"🔇 静音持续时间已设置为: {self.realtime_config['silence_duration']:.1f}秒")
        
    def set_callbacks(self, 
                     on_audio_ready: Optional[Callable] = None,
                     on_status_change: Optional[Callable] = None,
                     on_speech_activity: Optional[Callable] = None):
        """设置回调函数"""
        self.on_audio_ready = on_audio_ready
        self.on_status_change = on_status_change
        self.on_speech_activity = on_speech_activity
        
    def start(self) -> bool:
        """开始实时监听"""
        if self.is_active:
            print("⚠️ 实时监听已在运行")
            return True
            
        # 确保麦克风管理器已启动
        if not self.voice_processor.microphone_manager.is_running:
            if not self.voice_processor.microphone_manager.start():
                print("❌ 无法启动麦克风")
                return False
                
        try:
            # 启动监听线程
            self.is_active = True
            self.is_listening = True
            self.monitoring_thread = threading.Thread(target=self._realtime_monitoring_loop)
            self.monitoring_thread.daemon = True
            self.monitoring_thread.start()
            
            # 启动状态更新定时器
            self.status_timer.start(100)  # 100ms更新一次
            
            self._emit_status("请开始说话")
            print("🔄 实时语音监听已启动")
            return True
            
        except Exception as e:
            print(f"❌ 启动实时监听失败: {e}")
            self.is_active = False
            return False
            
    def stop(self):
        """停止实时监听"""
        if not self.is_active:
            return
            
        self.is_active = False
        self.is_listening = False
        
        # 停止状态定时器
        self.status_timer.stop()
        
        # 如果正在录音，停止录音
        if self.is_recording:
            self._stop_recording()
            
        # 等待监听线程结束
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=1.0)
            
        self._emit_status("实时监听已停止")
        print("🔄 实时语音监听已停止")
        
    def _realtime_monitoring_loop(self):
        """实时监听循环"""
        try:
            while self.is_active:
                current_time = time.time()
                
                # 获取当前音量级别
                audio_level = self.voice_processor.microphone_manager.get_audio_level()
                
                # 发送音量级别信号
                self.volume_level_changed.emit(audio_level)
                
                # 调整阈值（考虑灵敏度）
                adjusted_threshold = (self.realtime_config["volume_threshold"] / 
                                    self.realtime_config["sensitivity"])
                
                # 检查语音活动
                if audio_level > adjusted_threshold:
                    self._handle_speech_detected(current_time)
                else:
                    self._handle_silence_detected(current_time)
                    
                time.sleep(0.05)  # 50ms检查间隔
                
        except Exception as e:
            print(f"❌ 实时监听循环出错: {e}")
            self.is_active = False
            
    def _handle_speech_detected(self, current_time: float):
        """处理检测到语音"""
        self.last_audio_time = current_time
        
        if not self.is_recording:
            # 开始录音
            self.speech_start_time = current_time
            self.silence_start_time = 0
            self._start_recording()
            
    def _handle_silence_detected(self, current_time: float):
        """处理检测到静音"""
        if self.is_recording:
            # 检查静音持续时间
            if self.silence_start_time == 0:
                self.silence_start_time = current_time
                
            silence_duration = current_time - self.silence_start_time
            
            if silence_duration >= self.realtime_config["silence_duration"]:
                # 静音时间足够长，停止录音
                self._stop_recording()
                
    def _start_recording(self):
        """开始录音"""
        if self.is_recording:
            return
            
        print("🎙️ 检测到语音，开始录音")
        self.is_recording = True
        self.current_audio_buffer = []
        
        # 清空音频缓冲区
        self.voice_processor.microphone_manager.clear_audio_buffer()
        
        # 发送信号
        self.speech_detected.emit()
        self._emit_status("正在录音...")
        
        if self.on_speech_activity:
            self.on_speech_activity(True)
            
    def _stop_recording(self):
        """停止录音"""
        if not self.is_recording:
            return
            
        print("🎙️ 检测到静音，停止录音")
        self.is_recording = False
        self.silence_start_time = 0
        
        # 收集录音数据
        audio_data = self._collect_recorded_audio()
        
        # 发送信号
        self.speech_ended.emit()
        self._emit_status("正在处理...")
        
        if self.on_speech_activity:
            self.on_speech_activity(False)
            
        # 处理音频数据
        if audio_data is not None:
            self._process_audio_data(audio_data)
        else:
            self._reset_to_listening()
            
    def _collect_recorded_audio(self) -> Optional[np.ndarray]:
        """收集录音数据"""
        # 计算录音时长
        recording_duration = time.time() - self.speech_start_time
        
        # 检查最小时长
        if recording_duration < self.realtime_config["min_speech_duration"]:
            print(f"⚠️ 录音时长过短: {recording_duration:.2f}秒")
            return None
            
        # 从麦克风管理器收集音频数据
        collected_audio = []
        start_time = time.time()
        
        # 收集最近的音频数据（估算需要的样本数）
        expected_samples = int(recording_duration * 
                             self.voice_processor.processing_config["sample_rate"])
        collected_samples = 0
        
        while collected_samples < expected_samples and time.time() - start_time < 1.0:
            audio_chunk = self.voice_processor.microphone_manager.read_audio(timeout=0.1)
            if audio_chunk is not None:
                collected_audio.append(audio_chunk)
                collected_samples += len(audio_chunk)
            else:
                break
                
        if collected_audio:
            audio_data = np.concatenate(collected_audio)
            print(f"📊 收集音频数据: {len(audio_data)} 样本 "
                  f"({len(audio_data) / self.voice_processor.processing_config['sample_rate']:.2f}秒)")
            return audio_data
        else:
            print("⚠️ 没有收集到音频数据")
            return None
            
    def _process_audio_data(self, audio_data):
        """处理音频数据"""
        # 验证音频数据
        if not self.voice_processor.is_valid_speech(audio_data):
            print("⚠️ 音频数据无效")
            self._reset_to_listening()
            return
            
        # 获取音频信息
        audio_info = self.voice_processor.get_audio_info(audio_data)
        print(f"📊 音频信息: 时长 {audio_info['duration']:.2f}秒, "
              f"音量 {audio_info['normalized_rms']:.3f}")
              
        # 发送音频数据信号
        self.audio_processed.emit(audio_data)
        
        # 触发回调
        if self.on_audio_ready:
            self.on_audio_ready(audio_data)
            
        # 重置到监听状态
        self._reset_to_listening()
        
    def _reset_to_listening(self):
        """重置到监听状态"""
        if self.realtime_config["auto_restart"]:
            self.is_listening = True
            self._emit_status("请开始说话")
        else:
            self.is_listening = False
            self._emit_status("等待重新启动")
            
    def _emit_status(self, status: str):
        """发送状态信号"""
        self.status_changed.emit(status)
        
        if self.on_status_change:
            self.on_status_change(status)
            
    def _update_status(self):
        """更新状态显示"""
        if not self.is_active:
            return
            
        if self.is_recording:
            # 显示录音时长
            duration = time.time() - self.speech_start_time
            self._emit_status(f"正在录音... {duration:.1f}秒")
            
            # 检查最大录音时长
            if duration > self.realtime_config['max_speech_duration']:
                print(f"⏰ 录音时长超过限制，自动停止")
                self._stop_recording()
                
    def get_current_status(self) -> str:
        """获取当前状态"""
        if not self.is_active:
            return "实时监听未启动"
        elif self.is_recording:
            return "正在录音..."
        elif self.is_listening:
            return "请开始说话"
        else:
            return "等待重新启动"
            
    def is_realtime_active(self) -> bool:
        """检查实时监听是否激活"""
        return self.is_active
        
    def is_currently_recording(self) -> bool:
        """检查是否正在录音"""
        return self.is_recording
        
    def get_volume_level(self) -> float:
        """获取当前音量级别"""
        return self.voice_processor.microphone_manager.get_audio_level()
        
    def restart_listening(self):
        """重新开始监听"""
        if self.is_active and not self.is_listening:
            self.is_listening = True
            self._emit_status("请开始说话")
            print("🔄 重新开始监听")
