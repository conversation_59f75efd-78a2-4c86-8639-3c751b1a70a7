{"Version": 3, "Meta": {"Duration": 9.5, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 244, "TotalSegmentCount": 15950, "TotalPointCount": 19190, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.5, 1, 2, 0.533, 1, 0, 0.6, 0, 0, 0.767, 0.9, 0, 0.867, 0.7, 2, 1.633, 0.7, 0, 1.833, 1, 2, 2.067, 1, 0, 2.2, 0, 0, 2.333, 1, 0, 2.5, 0.5, 1, 2.833, 0.499, 3.167, 0.498, 3.5, 0.497, 0, 3.6, 0, 2, 3.667, 0, 0, 3.767, 0.696, 2, 4.233, 0.696, 0, 4.333, 0, 2, 4.433, 0, 0, 4.567, 0.5, 2, 6.967, 0.5, 0, 7.233, 1, 0, 7.4, 0, 2, 8.6, 0, 0, 8.9, 1, 0, 9, 0, 0, 9.1, 1, 2, 9.5, 1]}, {"Target": "Parameter", "Id": "ParamEyeSmileL", "Segments": [0, 0, 2, 0.5, 0, 0, 0.733, 1, 2, 2.1, 1, 0, 2.267, 0, 2, 2.367, 0, 0, 2.4, 1, 2, 3.467, 1, 0, 3.567, 0, 2, 4.867, 0, 0, 5.133, 1, 2, 7.2, 1, 2, 8.7, 1, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.5, 1, 2, 0.533, 1, 0, 0.6, 0, 0, 0.767, 0.9, 0, 0.867, 0.7, 2, 1.633, 0.7, 0, 1.833, 1, 2, 2.067, 1, 0, 2.2, 0, 0, 2.333, 1, 0, 2.5, 0.5, 1, 2.833, 0.499, 3.167, 0.498, 3.5, 0.497, 0, 3.6, 0, 2, 3.667, 0, 0, 3.767, 0.696, 2, 4.233, 0.696, 0, 4.333, 0, 2, 4.433, 0, 0, 4.567, 0.5, 2, 6.967, 0.5, 0, 7.233, 1, 0, 7.4, 0, 2, 8.6, 0, 0, 8.9, 1, 0, 9, 0, 0, 9.1, 1, 2, 9.5, 1]}, {"Target": "Parameter", "Id": "ParamEyeSmileR", "Segments": [0, 0, 2, 0.5, 0, 0, 0.733, 1, 2, 2.1, 1, 0, 2.267, 0, 2, 2.367, 0, 0, 2.4, 1, 2, 3.467, 1, 0, 3.567, 0, 2, 4.867, 0, 0, 5.133, 1, 2, 7.2, 1, 2, 8.7, 1, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 0, 0.167, 1, 0, 0.5, 0, 0, 0.833, 0.3, 1, 0.955, 0.3, 1.078, -0.207, 1.2, -0.4, 1, 1.378, -0.681, 1.555, -0.7, 1.733, -0.7, 1, 1.866, -0.7, 2, -0.575, 2.133, 0, 1, 2.244, 0.48, 2.356, 1, 2.467, 1, 2, 3.233, 1, 0, 3.733, -0.5, 0, 5.1, 1, 1, 5.178, 1, 5.255, 0.501, 5.333, 0, 1, 5.4, -0.429, 5.466, -0.5, 5.533, -0.5, 0, 6, 0, 0, 6.2, -0.4, 0, 7.2, 1, 0, 7.267, -0.5, 0, 7.4, 0, 0, 7.433, -0.6, 0, 7.667, 0.744, 1, 7.878, 0.744, 8.089, 0.754, 8.3, 0.674, 1, 8.478, 0.607, 8.655, 0, 8.833, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 0.267, 1, 0, 0.567, 0, 0, 0.833, 0.5, 0, 0.9, 0.473, 0, 1.1, 1, 0, 1.2, 0.854, 0, 1.333, 1, 0, 1.5, 0.854, 0, 1.667, 1, 0, 2, 0.4, 2, 2.267, 0.4, 0, 2.667, 0.5, 2, 3.567, 0.5, 0, 3.833, 1, 0, 4, 0.79, 0, 4.233, 1, 1, 4.266, 1, 4.3, 0.967, 4.333, 0.792, 1, 4.355, 0.675, 4.378, 0.5, 4.4, 0.5, 0, 4.533, 1, 0, 4.667, 0.595, 0, 4.867, 0.98, 0, 5.033, 0, 0, 5.333, 1, 0, 5.433, 0.595, 0, 5.6, 1, 0, 5.7, 0.595, 0, 5.867, 1, 0, 6, 0.3, 0, 6.333, 1, 2, 6.467, 1, 0, 6.9, 0, 2, 7.067, 0, 0, 7.267, 1, 0, 7.433, 0.595, 0, 7.6, 1, 0, 8.1, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamMouthY", "Segments": [0, 0, 2, 0.5, 0, 2, 1.833, 0, 0, 2.1, 0.3, 0, 2.4, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeEmotion", "Segments": [0, 0, 2, 0.5, 0, 0, 1.2, 1, 0, 1.733, 0, 0, 2.1, 1, 2, 2.467, 1, 0, 2.6, -1, 2, 7.567, -1, 0, 7.667, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamMarkShake", "Segments": [0, 0, 2, 3.033, 0, 0, 3.1, -0.8, 0, 3.4, 0.9, 0, 3.5, -0.638, 0, 3.733, 0.638, 0, 3.867, -0.2, 0, 4, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLX", "Segments": [0, 0, 2, 3.433, 0, 0, 3.633, -0.6, 2, 4.333, -0.6, 0, 4.567, 0.3, 2, 4.867, 0.3, 0, 5.1, -0.6, 2, 6.167, -0.6, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRX", "Segments": [0, 0, 2, 3.433, 0, 0, 3.633, -0.802, 2, 4.333, -0.802, 0, 4.567, 0.1, 2, 4.867, 0.1, 0, 5.1, -0.802, 2, 6.167, -0.802, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPHYInputX", "Segments": [0, 0, 2, 0.5, 0, 0, 0.767, -6, 0, 1.167, 12, 0, 1.367, 2.312, 0, 1.433, 9.264, 0, 1.533, 1.599, 0, 1.667, 9.135, 0, 1.767, 1.923, 0, 1.867, 5.614, 0, 2.167, 3, 2, 2.5, 3, 0, 2.633, 0, 0, 2.833, 4.323, 0, 3.333, -12, 0, 3.733, -4.284, 0, 4, -10.284, 2, 4.333, -10.284, 0, 5, 8.232, 0, 5.3, -8.232, 0, 5.733, 6.661, 0, 6.067, -8.232, 0, 6.2, 2.545, 0, 6.433, -5.383, 0, 6.6, 3.668, 0, 6.767, -5.165, 0, 6.933, 2.545, 0, 7.033, 0, 2, 7.167, 0, 1, 7.334, 0, 7.5, -0.645, 7.667, -3.751, 1, 7.734, -4.993, 7.8, -9.702, 7.867, -9.702, 0, 8.233, 4.323, 0, 8.667, -1.86, 0, 8.867, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 0, 0.433, 7, 0, 0.7, 6, 0, 1.133, 6.74, 0, 1.467, 6, 0, 1.9, 6.408, 0, 2.367, 5.445, 0, 2.633, 6, 1, 2.733, 6, 2.833, 1.934, 2.933, 0.847, 1, 3.022, -0.12, 3.111, 0, 3.2, 0, 2, 3.633, 0, 2, 6.733, 0, 1, 6.822, 0, 6.911, -0.816, 7, -0.873, 1, 7.189, -0.994, 7.378, -1, 7.567, -1, 0, 7.867, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.233, 0, 0, 0.5, -3.938, 0, 0.867, 7, 0, 1.033, 2.992, 0, 1.2, 5.07, 0, 1.367, 2.992, 0, 1.633, 5.292, 0, 1.833, 3.283, 0, 2, 5.411, 0, 2.167, 3.194, 0, 2.433, 4.091, 0, 2.6, 3, 1, 2.689, 3, 2.778, 4.731, 2.867, 6.513, 1, 3.011, 9.409, 3.156, 10.319, 3.3, 10.319, 0, 3.533, 7.263, 0, 3.9, 9, 0, 4.067, 7.263, 1, 4.167, 7.263, 4.267, 7.191, 4.367, 7.396, 1, 4.511, 7.692, 4.656, 13, 4.8, 13, 0, 5.133, -3, 0, 5.5, 4, 0, 5.8, -3, 0, 5.967, 3, 0, 6.2, -3, 0, 6.4, 3, 0, 6.567, -3, 0, 6.733, 1, 0, 7, -5.759, 0, 7.167, 3.283, 1, 7.245, 3.283, 7.322, 2.705, 7.4, -6, 1, 7.467, -13.461, 7.533, -30, 7.6, -30, 1, 7.722, -30, 7.845, -30, 7.967, -27.239, 1, 8.267, -17.896, 8.567, 2.398, 8.867, 2.398, 0, 9.133, -0.971, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 0, 0.467, 1.036, 0, 0.8, -8, 0, 1.167, 2.537, 0, 1.367, -6, 0, 1.533, 2, 0, 1.833, -1, 0, 2.067, 0, 2, 2.3, 0, 0, 2.533, 1.036, 0, 3.3, -12, 0, 3.567, -11.263, 0, 3.7, -12, 2, 4.467, -12, 0, 5.233, 5.777, 0, 5.9, 0, 2, 6.8, 0, 1, 6.867, 0, 6.933, 1.349, 7, 1.48, 1, 7.222, 1.916, 7.445, 2, 7.667, 2, 0, 8.533, -1.154, 0, 9, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "Segments": [0, 0, 2, 0.467, 0, 2, 2.3, 0, 0, 2.467, -3, 0, 2.667, 0, 2, 7, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamAngleH", "Segments": [0, 0, 2, 0.467, 0, 2, 2.4, 0, 0, 2.667, 2, 0, 3, -3, 2, 3.233, -3, 2, 3.933, -3, 0, 4.733, 0, 2, 7, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamAngleS", "Segments": [0, 0, 2, 0.467, 0, 2, 2.667, 0, 0, 3.133, 1, 2, 4.167, 1, 0, 4.6, 0, 2, 7, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamArm<PERSON>pperRAngle", "Segments": [0, 0, 2, 0.2, 0, 0, 0.4, -1.053, 0, 0.833, 3, 0, 1.033, 2, 0, 1.2, 3.097, 0, 1.4, 2.37, 1, 1.5, 2.37, 1.6, 2.47, 1.7, 2.776, 1, 1.811, 3.116, 1.922, 3.356, 2.033, 3.356, 1, 2.689, 3.356, 3.344, 3.278, 4, 3, 1, 4.122, 2.948, 4.245, 1.578, 4.367, 1, 1, 4.522, 0.264, 4.678, 0, 4.833, 0, 2, 5.567, 0, 0, 5.833, 0.21, 0, 6, 0, 0, 6.2, 0.36, 1, 6.522, 0.36, 6.845, 0.33, 7.167, 0, 1, 7.3, -0.137, 7.434, -2.262, 7.567, -2.262, 0, 8.133, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRAngle", "Segments": [0, 7.4, 2, 0.1, 7.4, 0, 0.233, 8.501, 1, 0.344, 8.501, 0.456, 7.322, 0.567, 3.965, 1, 0.589, 3.294, 0.611, 0.072, 0.633, -1.517, 1, 0.678, -4.695, 0.722, -6, 0.767, -6, 0, 1, -0.767, 0, 1.167, -5.438, 0, 1.5, -1.897, 0, 1.767, -4, 0, 1.967, -2.876, 1, 2.034, -2.876, 2.1, -2.861, 2.167, -3, 1, 2.222, -3.116, 2.278, -4, 2.333, -4, 0, 2.633, 7.4, 0, 2.8, 7.056, 1, 2.889, 7.056, 2.978, 7.399, 3.067, 7.4, 1, 4.445, 7.42, 5.822, 7.435, 7.2, 7.455, 1, 7.3, 7.457, 7.4, 8.679, 7.5, 8.679, 0, 7.867, 7.4, 2, 9.5, 7.4]}, {"Target": "Parameter", "Id": "ParamArmHandRAngle", "Segments": [0, 0, 0, 0.133, -2.29, 0, 0.433, 10, 2, 0.667, 10, 0, 0.9, -10, 0, 1.1, 4.588, 0, 1.267, -10, 0, 1.533, 2.369, 0, 1.8, -4.912, 0, 2.067, -2.29, 1, 2.156, -2.29, 2.244, -3.26, 2.333, -5.65, 1, 2.378, -6.845, 2.422, -7.729, 2.467, -7.729, 0, 2.667, -4.912, 0, 3, -6.059, 0, 4.167, -6.06, 0, 4.467, -7.187, 0, 4.7, -7, 0, 5.4, -10, 2, 5.6, -10, 0, 5.9, -9.222, 0, 6.067, -10, 0, 6.267, -9.611, 0, 6.433, -9.761, 0, 6.6, -9.284, 2, 6.767, -9.284, 0, 7.4, -9.611, 0, 7.9, -4.315, 0, 8.2, -5.253, 1, 8.456, -5.253, 8.711, -1.308, 8.967, -0.266, 1, 9.078, 0.187, 9.189, 0, 9.3, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRH", "Segments": [0, 0, 1, 0.1, 0, 0.2, 6.511, 0.3, 6.915, 1, 0.389, 7.274, 0.478, 7.187, 0.567, 7.187, 1, 0.656, 7.187, 0.744, -10.872, 0.833, -10.876, 1, 2.911, -10.962, 4.989, -11, 7.067, -11, 1, 7.156, -11, 7.244, -11.178, 7.333, -10.876, 1, 7.611, -9.934, 7.889, 0, 8.167, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamHandR", "Segments": [0, 0, 2, 0.6, 0, 2, 0.633, 0.5, 2, 2.467, 0.5, 2, 2.5, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRAngle", "Segments": [0, 0, 2, 0.267, 0, 0, 0.6, -10, 0, 0.9, 10, 0, 1.133, -10, 0, 1.367, 10, 0, 1.767, -10, 1, 1.834, -10, 1.9, -4.953, 1.967, 0, 1, 2.034, 4.953, 2.1, 6, 2.167, 6, 1, 2.222, 6, 2.278, 4.703, 2.333, 0, 1, 2.4, -5.643, 2.466, -10, 2.533, -10, 2, 2.567, 0, 0, 2.767, -2.42, 0, 3.1, 0, 2, 4.5, 0, 1, 4.667, 0, 4.833, 5.329, 5, 7, 1, 5.2, 9.006, 5.4, 9, 5.6, 9, 0, 5.9, 6.523, 0, 6.333, 7.463, 0, 7.067, 0, 2, 8.167, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamHandT2R", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 1, 0, 1.067, 0, 2, 1.7, 0, 0, 1.9, 0.1, 0, 2.167, 0, 2, 2.4, 0, 0, 2.567, 0.5, 0, 2.767, 0, 2, 8.167, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.1, 0, 0, 0.533, -7.982, 0, 0.767, -4.155, 0, 1.133, -7.401, 0, 1.3, -3.624, 0, 1.5, -7.74, 0, 1.833, -3.624, 0, 2.033, -7.401, 0, 2.2, -6.474, 0, 2.667, -7.37, 0, 3.067, -5, 0, 3.733, -6.776, 0, 4.2, -6.557, 0, 4.933, -7, 2, 6.667, -7, 1, 6.745, -7, 6.822, -7.702, 6.9, -7.74, 1, 7.289, -7.931, 7.678, -7.982, 8.067, -7.982, 0, 8.567, 0.696, 0, 8.867, -0.598, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 0, 0.6, 5.58, 0, 1.033, -5.591, 0, 1.3, 0, 0, 1.7, -1.129, 0, 1.967, 0.844, 0, 2.133, -0.248, 1, 2.166, -0.248, 2.2, -0.292, 2.233, 0, 1, 2.3, 0.584, 2.366, 1.441, 2.433, 1.441, 0, 2.833, -4, 0, 3.2, -1.428, 0, 3.467, -3.428, 2, 3.867, -3.428, 0, 4.533, 3.234, 0, 4.867, -3.234, 0, 5.333, 2.617, 0, 5.667, -3.234, 0, 5.8, 1, 0, 6.067, -2.115, 0, 6.233, 1.441, 0, 6.433, -2.029, 0, 6.6, 1, 0, 6.7, 0, 2, 6.833, 0, 0, 7.2, 2.617, 0, 7.767, -10, 1, 7.945, -10, 8.122, -9.522, 8.3, -5.591, 1, 8.444, -2.396, 8.589, 3.234, 8.733, 3.234, 0, 9, -0.62, 0, 9.2, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.2, 0, 0, 0.433, 1.936, 0, 0.9, -1, 0, 1.267, 1.562, 0, 1.733, -0.502, 0, 1.9, 0, 2, 2.433, 0, 0, 2.933, 5, 2, 3.933, 5, 0, 4.833, -1, 0, 5.467, 0, 2, 6.9, 0, 1, 7.111, 0, 7.322, 0.013, 7.533, 0.095, 1, 7.6, 0.121, 7.666, 0.92, 7.733, 0.92, 0, 8.6, 0, 2, 8.967, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX2", "Segments": [0, 0, 2, 0.467, 0, 0, 0.667, -0.436, 0, 0.867, 0, 0, 1.233, -6.36, 0, 1.3, -4.782, 0, 1.4, -6.36, 0, 1.5, -5.436, 0, 1.6, -6.36, 0, 1.7, -5.342, 0, 1.833, -5.644, 0, 2.033, -2.67, 0, 2.233, -3, 0, 2.5, 0, 2, 6.9, 0, 2, 7.533, 0, 2, 8.5, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY2", "Segments": [0, 0, 0, 0.267, -2, 0, 0.733, 5, 0, 1.1, -4, 0, 1.433, 0, 2, 2.1, 0, 0, 2.2, -1, 0, 2.433, 1, 0, 3.033, -2.638, 0, 3.333, -0.034, 0, 3.5, -2.034, 2, 3.967, -2.034, 0, 4.467, 3.234, 0, 4.767, -3.234, 0, 5.3, 2.617, 0, 5.633, -2.617, 0, 5.767, 0.172, 0, 6.033, -1.379, 0, 6.2, 0.542, 0, 6.4, -0.837, 0, 6.567, 0.558, 0, 6.667, 0, 2, 6.9, 0, 2, 7.533, 0, 2, 8.5, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechX", "Segments": [0, 0, 2, 0.467, 0, 2, 0.7, 0, 0, 1.133, -3, 0, 1.967, 0.365, 0, 2.2, 0, 2, 2.7, 0, 0, 3.2, 4, 2, 4.467, 4, 0, 5.067, 6.162, 0, 5.767, -1, 0, 6.267, 1, 1, 6.478, 1, 6.689, 0.338, 6.9, 0, 1, 7.111, -0.338, 7.322, -0.34, 7.533, -0.34, 0, 8.367, 3, 0, 8.833, -0.531, 0, 9.233, 0.256, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechW", "Segments": [0, 0, 2, 0.467, 0, 2, 1.067, 0, 0, 1.233, 1.08, 0, 1.3, 0, 0, 1.4, 1.034, 0, 1.5, 0, 0, 1.6, 0.914, 0, 1.7, 0, 2, 6.9, 0, 2, 7.533, 0, 2, 8.5, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, 0, 2, 0.467, 0, 0, 0.767, 3, 0, 1.1, -1, 0, 1.333, 1, 0, 1.767, -0.011, 1, 2.5, -0.011, 3.234, -0.009, 3.967, 0, 1, 4.1, 0.002, 4.234, 0.375, 4.367, 0.375, 0, 4.6, 0, 2, 6.9, 0, 2, 7.533, 0, 2, 8.5, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamShoulderR", "Segments": [0, 0, 2, 0.467, 0, 2, 2.233, 0, 0, 2.5, 9, 0, 2.8, -14, 0, 3.067, 0, 2, 5, 0, 0, 5.367, 11, 0, 5.633, 0, 0, 5.8, 9, 0, 6.033, 0, 0, 6.233, 6, 0, 6.4, 0, 2, 6.9, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamShoulderL", "Segments": [0, 0, 2, 0.467, 0, 2, 5, 0, 0, 5.367, 9.243, 0, 5.633, 0, 0, 5.8, 5.243, 0, 6.033, 0, 0, 6.233, 4.243, 0, 6.4, 0, 2, 6.9, 0, 2, 7.533, 0, 2, 8.5, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Z", "Segments": [0, 0, 2, 0.5, 0, 0, 0.967, -1.596, 0, 1.7, 0.595, 0, 2.533, -0.415, 0, 3.067, 0, 0, 3.733, -1.596, 0, 4.1, -1.339, 0, 4.533, -1.596, 2, 5, -1.596, 0, 5.933, 0.595, 0, 6.567, -0.415, 0, 7.067, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLegR1X", "Segments": [0, 0, 2, 0.7, 0, 0, 1.1, -3, 0, 1.8, 1.509, 0, 2.6, 0, 2, 2.933, 0, 0, 3.667, -3, 2, 4.833, -3, 0, 5.667, 13, 0, 6.367, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Z", "Segments": [0, 0, 2, 0.5, 0, 2, 0.6, 0, 2, 0.767, 0, 0, 1.2, -2, 0, 1.9, 0.138, 0, 2.733, -1, 0, 3.267, 0, 0, 3.8, -1.675, 0, 4.3, -1.211, 0, 4.767, -1.675, 2, 5.2, -1.675, 0, 6.067, 0.701, 0, 6.767, -0.51, 0, 7.367, 0.138, 0, 7.867, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Z", "Segments": [0, 0, 0, 0.5, -0.632, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyX", "Segments": [0, 0, 2, 0.6, 0, 0, 0.9, 7, 0, 1.267, -3, 0, 1.567, 4, 1, 1.656, 4, 1.744, 1.258, 1.833, 0, 1, 2.211, -5.346, 2.589, -7.178, 2.967, -7.178, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyY", "Segments": [0, 0, 2, 0.633, 0, 0, 0.933, 7, 0, 1.3, -3, 0, 1.6, 4, 1, 1.689, 4, 1.778, 2.72, 1.867, 0, 1, 2.122, -7.819, 2.378, -11.811, 2.633, -11.811, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyZ", "Segments": [0, 0, 2, 0.5, 0, 0, 0.9, 2, 0, 1.267, -11.745, 0, 1.833, -5.725, 0, 2.167, -7, 0, 4.133, 1.845, 1, 4.322, 1.845, 4.511, 1.729, 4.7, 0.15, 1, 4.822, -0.871, 4.945, -4.109, 5.067, -4.109, 2, 5.167, -4.109, 0, 5.733, 7.669, 2, 5.833, 7.669, 1, 6.044, 7.669, 6.256, -5.329, 6.467, -5.82, 1, 6.5, -5.897, 6.534, -5.837, 6.567, -5.837, 0, 7.233, 7.979, 1, 7.278, 7.979, 7.322, 8.102, 7.367, 7.928, 1, 7.578, 7.103, 7.789, -4.524, 8, -4.524, 2, 8.1, -4.524, 0, 8.967, 2, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRPositionZ", "Segments": [0, 0, 2, 0.5, 0, 2, 0.6, 0, 0, 1, 2, 0, 1.367, -9.745, 0, 1.933, -5.725, 0, 2.267, -7, 0, 4.033, 0.584, 1, 4.222, 0.584, 4.411, 0.512, 4.6, -0.539, 1, 4.722, -1.219, 4.845, -3.362, 4.967, -3.362, 2, 5.067, -3.362, 0, 5.633, 4.443, 2, 5.733, 4.443, 1, 5.944, 4.443, 6.156, -4.146, 6.367, -4.495, 1, 6.4, -4.55, 6.434, -4.507, 6.467, -4.507, 0, 7.133, 4.649, 1, 7.178, 4.649, 7.222, 4.732, 7.267, 4.615, 1, 7.478, 4.057, 7.689, -3.637, 7.9, -3.637, 2, 8, -3.637, 0, 8.867, 0.687, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyX", "Segments": [0, 0, 2, 0.5, 0, 2, 0.667, 0, 0, 0.9, 7, 0, 1.267, -3, 0, 1.567, 4, 1, 1.656, 4, 1.744, 1.258, 1.833, 0, 1, 2.211, -5.346, 2.589, -7.178, 2.967, -7.178, 0, 3.767, 4.747, 1, 3.945, 4.747, 4.122, 4.91, 4.3, 0, 1, 4.511, -5.83, 4.722, -19, 4.933, -19, 0, 5.367, 15.636, 0, 5.767, -15.592, 0, 6.133, 15.636, 0, 6.533, -12.685, 0, 6.933, 13.376, 0, 7.3, -11.343, 0, 7.733, 11.829, 0, 8.1, -5, 0, 8.4, 3.787, 0, 8.833, -2.598, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyY", "Segments": [0, 0, 2, 0.5, 0, 2, 0.533, 0, 2, 0.7, 0, 0, 0.933, 7, 0, 1.3, -3, 0, 1.6, 4, 1, 1.689, 4, 1.778, 2.72, 1.867, 0, 1, 2.122, -7.819, 2.378, -11.811, 2.633, -11.811, 0, 3.833, 10.277, 1, 4, 10.277, 4.166, 9.333, 4.333, 0, 1, 4.544, -11.822, 4.756, -26.049, 4.967, -26.049, 0, 5.433, 24.049, 0, 5.8, -15.592, 0, 6.167, 22.998, 0, 6.567, -6.282, 0, 6.967, 20.205, 0, 7.333, -10.341, 0, 7.767, 18.294, 0, 8.1, -7.766, 0, 8.433, 3.787, 0, 8.867, -2.598, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyZ", "Segments": [0, 0, 2, 0.5, 0, 2, 0.567, 0, 0, 0.733, -6, 0, 1.133, 3, 0, 1.433, -1.059, 0, 1.767, 0, 0, 2.433, -5.624, 2, 2.533, -5.624, 0, 4.2, 7.585, 1, 4.289, 7.585, 4.378, 7.229, 4.467, 0, 1, 4.622, -12.651, 4.778, -26.049, 4.933, -26.049, 2, 5.033, -26.049, 0, 5.8, 19.953, 2, 5.9, 19.953, 1, 6.111, 19.953, 6.322, -20.604, 6.533, -22.035, 1, 6.566, -22.261, 6.6, -22.088, 6.633, -22.088, 0, 7.3, 20.919, 1, 7.344, 20.919, 7.389, 21.258, 7.433, 20.758, 1, 7.644, 18.382, 7.856, -18, 8.067, -18, 2, 8.167, -18, 0, 9.033, 2, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUEyesForm", "Segments": [0, 0, 2, 0.333, 0, 2, 0.533, 0, 2, 1.1, 0, 0, 1.667, -1, 2, 2, -1, 0, 2.533, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyY", "Segments": [0, 0, 2, 0.333, 0, 2, 0.7, 0, 0, 1.067, 6.26, 0, 1.333, -1.62, 0, 1.467, 2.925, 0, 1.6, -2.598, 0, 1.7, 3.787, 0, 1.8, -3.047, 0, 1.933, 2.925, 0, 2.033, -2.703, 0, 2.167, 2, 1, 2.367, 2, 2.567, 1.275, 2.767, 0, 1, 2.945, -1.133, 3.122, -1.62, 3.3, -1.62, 0, 4.333, 2, 0, 4.967, -26.049, 0, 5.433, 24.049, 0, 5.8, -15.592, 0, 6.167, 22.998, 0, 6.567, -6.282, 0, 6.967, 20.205, 0, 7.333, -10.341, 0, 7.767, 18.294, 0, 8.1, -7.766, 0, 8.433, 3.787, 0, 8.867, -2.598, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyZ", "Segments": [0, 0, 2, 0.333, 0, 2, 0.633, 0, 0, 1.033, 20.858, 2, 1.1, 20.858, 0, 1.3, -27.181, 0, 1.433, 27.181, 0, 1.567, -27.181, 0, 1.667, 27.181, 0, 1.767, -27.181, 0, 1.9, 27.181, 0, 2.067, -10, 0, 2.2, 4, 0, 2.467, -2.382, 0, 2.767, 2.816, 0, 3.2, -1.3, 0, 3.6, 1.227, 0, 4.233, -2.382, 0, 4.6, 1.227, 0, 5.033, -26.049, 0, 5.8, 19.953, 0, 6.533, -22.035, 0, 7.3, 20.919, 0, 8.067, -18, 0, 9.033, 2, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUArmZ", "Segments": [0, 0, 2, 0.333, 0, 2, 0.567, 0, 1, 0.634, 0, 0.7, 0.236, 0.767, -2, 1, 0.956, -8.336, 1.144, -15, 1.333, -15, 0, 1.467, 15.881, 0, 1.6, -17.598, 0, 1.7, 16.598, 0, 1.8, -18.598, 0, 1.933, 16.598, 0, 2.133, -12, 0, 2.3, 11, 0, 2.633, -5.118, 0, 2.9, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineU", "Segments": [0, 0, 2, 0.333, 0, 2, 1.367, 0, 1, 1.411, 0.333, 1.456, 0.667, 1.5, 1, 2, 1.533, 0, 2, 1.633, 0, 1, 1.666, 0.333, 1.7, 0.667, 1.733, 1, 2, 1.767, 0, 2, 1.833, 0, 2, 1.933, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineD", "Segments": [0, 0, 2, 0.333, 0, 2, 1.367, 0, 2, 1.5, 0, 1, 1.544, 0.333, 1.589, 0.667, 1.633, 1, 2, 1.667, 0, 2, 1.733, 0, 1, 1.766, 0.333, 1.8, 0.667, 1.833, 1, 2, 1.867, 0, 2, 1.933, 0, 1, 1.978, 0.333, 2.022, 0.667, 2.067, 1, 2, 2.1, 0, 2, 2.133, 0, 2, 2.433, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyW", "Segments": [0, 0, 2, 0.333, 0, 2, 0.933, 0, 0, 1.3, -0.6, 0, 1.433, 0.4, 0, 1.567, -0.5, 0, 1.7, 0.6, 0, 1.767, -0.5, 0, 1.867, 0.2, 1, 1.911, 0.2, 1.956, 0.103, 2, -0.029, 1, 2.067, -0.226, 2.133, -0.3, 2.2, -0.3, 0, 2.467, 0.2, 0, 2.7, -0.2, 0, 2.967, 0.2, 0, 3.3, -0.1, 0, 3.6, 0.1, 0, 3.967, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPositionXPanda", "Segments": [0, 0, 2, 0.5, 0, 1, 0.678, 0, 0.855, 0.15, 1.033, 1, 1, 1.8, 4.665, 2.566, 7.874, 3.333, 12.745, 1, 3.722, 15.216, 4.111, 20, 4.5, 20, 2, 4.9, 20, 1, 5.044, 20, 5.189, 18.3, 5.333, 17.079, 1, 5.778, 13.323, 6.222, 11.038, 6.667, 7.012, 1, 7.067, 3.388, 7.467, 0, 7.867, 0, 2, 8.3, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.5, 0, 2, 4.567, 0, 0, 4.9, 1, 2, 7.867, 1, 0, 8.3, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyX", "Segments": [0, 0, 2, 0.7, 0, 0, 0.8, 2.538, 0, 0.967, -2.538, 0, 1.1, 2.538, 0, 1.2, -2.538, 0, 1.4, 2.538, 0, 1.6, -2.538, 0, 1.8, 2.538, 0, 1.9, -2.538, 0, 2.1, 2.538, 0, 2.2, -2.538, 0, 2.4, 2.538, 0, 2.567, -2.538, 0, 2.7, 2.538, 0, 2.9, -2.538, 0, 3.033, 2.538, 0, 3.133, -2.538, 0, 3.4, 2.538, 0, 3.533, -2.538, 0, 3.667, 2.538, 0, 3.833, -2.538, 0, 4, 2.538, 0, 4.133, -2.538, 0, 4.333, 2.538, 0, 4.5, -2.538, 0, 4.633, 2.538, 0, 4.8, 0, 2, 5.167, 0, 0, 5.4, -2.538, 0, 5.633, 2.538, 0, 5.8, -2.538, 0, 6.033, 2.538, 0, 6.133, -2.538, 0, 6.3, 2.538, 0, 6.433, -2.538, 0, 6.6, 2.538, 0, 6.8, -2.538, 0, 6.933, 2.538, 0, 7.1, -2.538, 0, 7.233, 2.538, 0, 7.367, -2.538, 0, 7.633, 2.538, 0, 7.867, -2.538, 0, 8.067, 2.538, 0, 8.2, 0, 2, 8.3, 0, 2, 8.4, 0, 2, 8.533, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyY", "Segments": [0, 0, 2, 0.633, 0, 0, 0.733, 3.183, 0, 0.9, -3.183, 0, 1.033, 3.183, 0, 1.133, -3.183, 0, 1.333, 3.183, 0, 1.533, -3.183, 0, 1.733, 3.183, 0, 1.833, -3.183, 0, 2.033, 3.183, 0, 2.133, -3.183, 0, 2.333, 3.183, 0, 2.5, -3.183, 0, 2.633, 3.183, 0, 2.833, -3.183, 0, 2.967, 3.183, 0, 3.1, -3.183, 0, 3.333, 3.183, 0, 3.5, -3.183, 0, 3.6, 3.183, 0, 3.767, -3.183, 0, 3.933, 3.183, 0, 4.1, -3.183, 0, 4.267, 3.183, 0, 4.433, -3.183, 0, 4.533, 3.183, 0, 4.733, 0, 2, 5.1, 0, 0, 5.333, -3.183, 0, 5.567, 3.183, 0, 5.733, -3.183, 0, 5.967, 3.183, 0, 6.067, -3.183, 0, 6.233, 3.183, 0, 6.367, -3.183, 0, 6.533, 3.183, 0, 6.733, -3.183, 0, 6.867, 3.183, 0, 7.033, -3.183, 0, 7.2, 3.183, 0, 7.3, -3.183, 0, 7.533, 3.183, 0, 7.767, -3.183, 0, 7.967, 3.183, 0, 8.1, 0, 2, 8.3, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2Panda", "Segments": [0, 0, 2, 0.5, 0, 0, 0.567, -6.229, 0, 0.633, 6.229, 0, 0.7, -6.229, 0, 0.8, 6.229, 0, 0.867, -6.229, 0, 0.933, 6.229, 0, 1.033, -6.229, 0, 1.1, 6.229, 0, 1.2, -6.229, 0, 1.233, 6.229, 0, 1.3, -6.229, 0, 1.4, 6.229, 0, 1.5, -6.229, 0, 1.567, 6.229, 0, 1.633, -6.229, 0, 1.7, 6.229, 0, 1.8, -6.229, 0, 1.867, 6.229, 0, 1.933, -6.229, 0, 2.033, 6.229, 0, 2.1, -6.229, 0, 2.167, 6.229, 0, 2.233, -6.229, 0, 2.333, 6.229, 0, 2.4, -6.229, 0, 2.5, 6.229, 0, 2.533, -6.229, 0, 2.633, 6.229, 0, 2.7, -6.229, 0, 2.8, 6.229, 0, 2.867, -6.229, 0, 2.933, 6.229, 0, 3.033, -6.229, 0, 3.1, 6.229, 0, 3.133, -6.229, 0, 3.233, 6.229, 0, 3.333, -6.229, 0, 3.4, 6.229, 0, 3.467, -6.229, 0, 3.533, 6.229, 0, 3.633, -6.229, 0, 3.7, 6.229, 0, 3.8, -6.229, 0, 3.867, 6.229, 0, 3.933, -6.229, 0, 4, 6.229, 0, 4.067, -6.229, 0, 4.167, 6.229, 0, 4.233, -6.229, 0, 4.333, 6.229, 0, 4.4, -6.229, 0, 4.467, 6.229, 0, 4.533, 0, 2, 4.967, 0, 0, 5.033, -6.229, 0, 5.1, 6.229, 0, 5.167, -6.229, 0, 5.233, 6.229, 0, 5.333, -6.229, 0, 5.4, 6.229, 0, 5.5, -6.229, 0, 5.6, 6.229, 0, 5.7, -6.229, 0, 5.733, 6.229, 0, 5.867, -6.229, 0, 5.9, 6.229, 0, 5.967, -6.229, 0, 6.033, 6.229, 0, 6.1, -6.229, 0, 6.2, 6.229, 0, 6.267, -6.229, 0, 6.333, 6.229, 0, 6.4, -6.229, 0, 6.5, 6.229, 0, 6.567, -6.229, 0, 6.633, 6.229, 0, 6.7, -6.229, 0, 6.8, 6.229, 0, 6.867, -6.229, 0, 6.933, 6.229, 0, 7.033, -6.229, 0, 7.1, 6.229, 0, 7.2, -6.229, 0, 7.233, 6.229, 0, 7.3, -6.229, 0, 7.4, 6.229, 0, 7.5, -6.229, 0, 7.6, 6.229, 0, 7.7, -6.229, 0, 7.767, 6.229, 0, 7.833, -6.229, 0, 7.967, 0, 2, 8.3, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyZ", "Segments": [0, 0, 0, 0.7, 1.431, 0, 0.867, -1.431, 0, 1, 1.431, 0, 1.1, -1.431, 0, 1.3, 1.431, 0, 1.5, -1.431, 0, 1.7, 1.431, 0, 1.8, -1.431, 0, 2, 1.431, 0, 2.1, -1.431, 0, 2.3, 1.431, 0, 2.467, -1.431, 0, 2.6, 1.431, 0, 2.8, -1.431, 0, 2.933, 1.431, 0, 3.033, -1.431, 0, 3.3, 1.431, 0, 3.433, -1.431, 0, 3.567, 1.431, 0, 3.733, -1.431, 0, 3.9, 1.431, 0, 4.033, -1.431, 0, 4.233, 1.431, 0, 4.4, -1.431, 0, 4.5, 1.431, 1, 4.567, 1.431, 4.633, 1.53, 4.7, 0, 1, 4.756, -1.275, 4.811, -30, 4.867, -30, 0, 5.133, 1.431, 0, 5.3, -1.431, 0, 5.533, 1.431, 0, 5.7, -1.431, 0, 5.933, 1.431, 0, 6.033, -1.431, 0, 6.2, 1.431, 0, 6.333, -1.431, 0, 6.5, 1.431, 0, 6.7, -1.431, 0, 6.833, 1.431, 0, 7, -1.431, 0, 7.133, 1.431, 0, 7.267, -1.431, 0, 7.5, 1.431, 0, 7.733, -1.431, 0, 7.933, 1.431, 1, 7.978, 1.431, 8.022, 1.594, 8.067, 0, 1, 8.111, -1.594, 8.156, -21, 8.2, -21, 0, 8.4, 9, 0, 8.667, -9.429, 0, 9, 5.312, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegFZ", "Segments": [0, 0, 2, 0.433, 0, 0, 0.567, 0.6, 0, 0.7, -1, 0, 0.867, 1, 0, 1.033, -1, 0, 1.233, 1, 0, 1.333, -1, 0, 1.5, 1, 0, 1.633, -1, 0, 1.8, 1, 0, 1.933, -1, 0, 2.1, 1, 0, 2.233, -1, 0, 2.4, 1, 0, 2.567, -1, 0, 2.733, 1, 0, 2.833, -1, 0, 3.033, 1, 0, 3.133, -1, 0, 3.333, 1, 0, 3.5, -1, 0, 3.633, 1, 0, 3.8, -1, 0, 3.967, 1, 0, 4.133, -1, 0, 4.333, 1, 0, 4.433, -1, 0, 4.533, 0, 2, 4.867, 0, 0, 5, 1, 0, 5.133, -1, 0, 5.3, 1, 0, 5.467, -1, 0, 5.633, 1, 0, 5.767, -1, 0, 5.933, 1, 0, 6.033, -1, 0, 6.233, 1, 0, 6.367, -1, 0, 6.533, 1, 0, 6.667, -1, 0, 6.833, 1, 0, 7, -1, 0, 7.133, 1, 0, 7.267, -1, 0, 7.433, 1, 0, 7.6, -1, 0, 7.867, 1, 0, 7.967, 0, 2, 8.3, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegBZ", "Segments": [0, 0, 2, 0.433, 0, 0, 0.567, 0.6, 0, 0.7, -1, 0, 0.867, 1, 0, 1.033, -1, 0, 1.233, 1, 0, 1.333, -1, 0, 1.5, 1, 0, 1.633, -1, 0, 1.8, 1, 0, 1.933, -1, 0, 2.1, 1, 0, 2.233, -1, 0, 2.4, 1, 0, 2.567, -1, 0, 2.733, 1, 0, 2.833, -1, 0, 3.033, 1, 0, 3.133, -1, 0, 3.333, 1, 0, 3.5, -1, 0, 3.633, 1, 0, 3.8, -1, 0, 3.967, 1, 0, 4.133, -1, 0, 4.333, 1, 0, 4.433, -1, 0, 4.533, 0, 2, 4.867, 0, 0, 5, 1, 0, 5.133, -1, 0, 5.3, 1, 0, 5.467, -1, 0, 5.633, 1, 0, 5.767, -1, 0, 5.933, 1, 0, 6.033, -1, 0, 6.233, 1, 0, 6.367, -1, 0, 6.533, 1, 0, 6.667, -1, 0, 6.833, 1, 0, 7, -1, 0, 7.133, 1, 0, 7.267, -1, 0, 7.433, 1, 0, 7.6, -1, 0, 7.867, 1, 0, 7.967, 0, 2, 8.3, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyY", "Segments": [0, 0, 2, 0.5, 0, 2, 0.667, 0, 0, 0.9, -7, 0, 1.267, 3, 0, 1.567, -4, 0, 1.833, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamCannonZ", "Segments": [0, 0, 2, 0.533, 0, 0, 0.7, 6, 0, 1.1, -3, 0, 1.4, 1.059, 0, 1.733, 0, 2, 2.767, 0, 0, 3, 2.231, 0, 3.433, -13, 0, 3.667, -11.678, 0, 3.967, -12.606, 2, 4.533, -12.606, 0, 5.367, 11.702, 0, 5.967, 0, 0, 6.4, 2, 0, 6.733, -2, 0, 7.067, 0.493, 0, 7.333, 0, 1, 7.478, 0, 7.622, 0.04, 7.767, 0.285, 1, 7.811, 0.36, 7.856, 2.76, 7.9, 2.76, 0, 8.367, 0, 2, 8.733, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamCannonGaY", "Segments": [0, 0, 2, 0.6, 0, 0, 0.767, -8.384, 0, 1.167, 3.845, 0, 1.467, -3.618, 0, 1.767, 3.37, 0, 2.067, -1.16, 0, 2.4, 1.234, 1, 2.544, 1.234, 2.689, 1.071, 2.833, -0.231, 1, 2.911, -0.933, 2.989, -3.248, 3.067, -3.248, 0, 3.5, 11.418, 0, 3.733, 6, 0, 4.033, 10.888, 0, 4.3, 6.475, 0, 4.6, 10.888, 0, 5.433, -11, 0, 6.033, 3.37, 0, 6.567, -2.389, 0, 6.833, 1.41, 0, 7.2, -1.075, 0, 7.467, 0.472, 1, 7.522, 0.472, 7.578, 0.562, 7.633, -0.231, 1, 7.744, -1.818, 7.856, -3.981, 7.967, -3.981, 0, 8.433, 0, 0, 8.867, -1.075, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupY", "Segments": [0, 0, 2, 0.633, 0, 0, 0.8, 0.2, 0, 1.2, -0.1, 0, 1.5, 0.035, 0, 1.833, 0, 2, 2.867, 0, 0, 3.1, 0.074, 0, 3.533, -0.433, 0, 3.767, -0.389, 0, 4.067, -0.42, 2, 4.633, -0.42, 0, 5.467, 0.39, 0, 6.067, 0, 2, 7.433, 0, 1, 7.578, 0, 7.722, 0.001, 7.867, 0.01, 1, 7.911, 0.013, 7.956, 0.092, 8, 0.092, 0, 8.467, 0, 2, 8.833, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupZ", "Segments": [0, 0, 2, 0.5, 0, 0, 0.667, 0.2, 0, 1.067, -0.1, 0, 1.367, 0.035, 0, 1.7, 0, 2, 2.733, 0, 0, 2.967, 0.074, 0, 3.4, -0.433, 0, 3.633, -0.389, 0, 3.933, -0.42, 2, 4.5, -0.42, 0, 5.333, 0.39, 0, 5.933, 0, 2, 7.3, 0, 1, 7.444, 0, 7.589, 0.001, 7.733, 0.01, 1, 7.778, 0.013, 7.822, 0.092, 7.867, 0.092, 0, 8.333, 0, 2, 8.7, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamCannonHandZ", "Segments": [0, 0, 2, 0.5, 0, 2, 4.3, 0, 2, 5.7, 0, 0, 6.267, 8, 2, 8.1, 8, 0, 8.367, 10, 0, 9.133, 0, 2, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamParamStrongCatZ", "Segments": [0, 0, 2, 0.5, 0, 0, 0.9, 2, 0, 1.267, -11.745, 0, 1.833, -5.725, 0, 2.167, -7, 0, 4.033, 1.845, 1, 4.222, 1.845, 4.411, 1.728, 4.6, 0.15, 1, 4.722, -0.871, 4.845, -4.109, 4.967, -4.109, 2, 5.067, -4.109, 0, 5.633, 7.669, 2, 5.733, 7.669, 1, 5.944, 7.669, 6.156, -5.332, 6.367, -5.82, 1, 6.4, -5.897, 6.434, -5.837, 6.467, -5.837, 0, 7.133, 7.979, 1, 7.178, 7.979, 7.222, 8.104, 7.267, 7.928, 1, 7.478, 7.091, 7.689, -4.524, 7.9, -4.524, 2, 8, -4.524, 0, 8.867, 2, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamParamSCBodyZ", "Segments": [0, 0, 2, 0.5, 0, 2, 0.6, 0, 0, 1, 2, 0, 1.367, -9.745, 0, 1.933, -5.725, 0, 2.267, -7, 0, 4.133, 1.845, 1, 4.322, 1.845, 4.511, 1.728, 4.7, 0.15, 1, 4.822, -0.871, 4.945, -4.109, 5.067, -4.109, 2, 5.167, -4.109, 0, 5.733, 7.669, 2, 5.833, 7.669, 1, 6.044, 7.669, 6.256, -5.332, 6.467, -5.82, 1, 6.5, -5.897, 6.534, -5.837, 6.567, -5.837, 0, 7.233, 7.979, 1, 7.278, 7.979, 7.322, 8.104, 7.367, 7.928, 1, 7.578, 7.091, 7.789, -4.524, 8, -4.524, 2, 8.1, -4.524, 0, 8.967, 2, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamSCDishY", "Segments": [0, 0, 2, 0.5, 0, 2, 0.7, 0, 0, 1.1, 2, 0, 1.467, -2.745, 0, 2, 1.275, 0, 2.333, 0, 0, 4.133, 1.845, 1, 4.322, 1.845, 4.511, 1.729, 4.7, 0.15, 1, 4.822, -0.871, 4.945, -4.109, 5.067, -4.109, 2, 5.167, -4.109, 0, 5.733, 7.669, 2, 5.833, 7.669, 1, 6.044, 7.669, 6.256, -5.329, 6.467, -5.82, 1, 6.5, -5.897, 6.534, -5.837, 6.567, -5.837, 0, 7.233, 7.979, 1, 7.278, 7.979, 7.322, 8.102, 7.367, 7.928, 1, 7.578, 7.103, 7.789, -4.524, 8, -4.524, 2, 8.1, -4.524, 0, 8.967, 2, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamSCDishZ", "Segments": [0, 0, 2, 0.5, 0, 2, 0.733, 0, 0, 1.133, 2, 0, 1.5, -2.745, 0, 2.033, 1.275, 0, 2.367, 0, 0, 4.267, 1.845, 1, 4.456, 1.845, 4.644, 1.729, 4.833, 0.15, 1, 4.955, -0.871, 5.078, -4.109, 5.2, -4.109, 2, 5.3, -4.109, 0, 5.867, 7.669, 2, 5.967, 7.669, 1, 6.178, 7.669, 6.389, -5.329, 6.6, -5.82, 1, 6.633, -5.897, 6.667, -5.837, 6.7, -5.837, 0, 7.367, 7.979, 1, 7.411, 7.979, 7.456, 8.102, 7.5, 7.928, 1, 7.711, 7.103, 7.922, -4.524, 8.133, -4.524, 2, 8.233, -4.524, 0, 9.1, 2, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamSCCupZ", "Segments": [0, 0, 2, 0.5, 0, 2, 0.8, 0, 0, 1.233, 5.028, 0, 1.6, -5.773, 0, 2.167, 3.378, 0, 2.433, -1.036, 0, 4.333, 1.845, 1, 4.522, 1.845, 4.711, 1.729, 4.9, 0.15, 1, 5.022, -0.871, 5.145, -4.109, 5.267, -4.109, 2, 5.367, -4.109, 0, 5.933, 7.669, 2, 6.033, 7.669, 1, 6.244, 7.669, 6.456, -5.329, 6.667, -5.82, 1, 6.7, -5.897, 6.734, -5.837, 6.767, -5.837, 0, 7.433, 7.979, 1, 7.478, 7.979, 7.522, 8.102, 7.567, 7.928, 1, 7.778, 7.103, 7.989, -4.524, 8.2, -4.524, 2, 8.3, -4.524, 0, 9.167, 2, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "MB_yanwubaozha", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "MB_DRFWXZKTMD", "Segments": [0, 1, 0, 9.5, 1]}, {"Target": "Parameter", "Id": "ParamAllSizeFix", "Segments": [0, 1, 0, 9.5, 1]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBGHide", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBG2Hide", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBGX", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBGY", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN2", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN3", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBlackY", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBlackCollar", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBlackOrder", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamWhiteIN", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamCHHide", "Segments": [0, 1, 0, 9.5, 1]}, {"Target": "Parameter", "Id": "ParamDeskHide", "Segments": [0, 1, 0, 9.5, 1]}, {"Target": "Parameter", "Id": "ParamStoolHide", "Segments": [0, 1, 0, 9.5, 1]}, {"Target": "Parameter", "Id": "ParamCupDesk", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamCHX", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamCHY", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamCHZ", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamChaSize", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamCcharacterZ", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionX", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionY", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionX", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionY", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamAllZ", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamAllSize", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamALLSize2", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamDeskShow", "Segments": [0, 10, 0, 9.5, 10]}, {"Target": "Parameter", "Id": "ParamStrongCatShow", "Segments": [0, 10, 0, 9.5, 10]}, {"Target": "Parameter", "Id": "ParamCannonShow", "Segments": [0, 10, 0, 9.5, 10]}, {"Target": "Parameter", "Id": "ParamLightPositionX", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamFixT", "Segments": [0, 1, 0, 9.5, 1]}, {"Target": "Parameter", "Id": "ParamFlap", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamScare", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPupilExp", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpen2", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamMouthType", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamMouthSize", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBlackFace", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamTeethLight", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamHeart2", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamMark", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamShameLine", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLY", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeL", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRY", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeR", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeRLightOpen", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLightLine1", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLightLine2", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLightLine3", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLightShine", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo1", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo2", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo3", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1Y", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle2", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow1", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow2", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamTearLight", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamTears", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperLAngle", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamArmHandLAngle", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerLAngle", "Segments": [0, 10, 0, 9.5, 10]}, {"Target": "Parameter", "Id": "ParamArmLowerLH", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerLAngle", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamHandT2L", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamFanOpenR", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamChili", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamChiliX", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperRH", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRY", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamHand_Cl", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamHandT1R", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRCup", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRMail", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "Segments": [0, 1, 0, 9.5, 1]}, {"Target": "Parameter", "Id": "ParamHandLIQY1", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY2", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY3", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamHandCupZ", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamHandCupY", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Y", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Y", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Y", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamFootRX", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Z", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Z", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Z", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Y", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Y", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Y", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamFootLX", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLegLF", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRHide", "Segments": [0, 1, 0, 9.5, 1]}, {"Target": "Parameter", "Id": "ParamMJRFlap", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuR", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuR", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuR", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuR", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRInput", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamMRCupSet", "Segments": [0, 1, 0, 9.5, 1]}, {"Target": "Parameter", "Id": "ParamMalpositionManjuuR", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuREyeOpen", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRArmB", "Segments": [0, 30, 0, 9.5, 30]}, {"Target": "Parameter", "Id": "ParamManjuuRMouth", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRSigh", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow1", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow2", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow3", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow4", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowB", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW2", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamMRCupFZ", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX1", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX2", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLiqH", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX1", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX2", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX3", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLHide", "Segments": [0, 1, 0, 9.5, 1]}, {"Target": "Parameter", "Id": "ParamMJLSigh", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuL", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuL", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuL", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamMjLFlip", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPositionZManjuuL", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLEyeOpen", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyW", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuL", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamClawFX", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamClawFY", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamClawBX", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamClawBY", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUHide", "Segments": [0, 1, 0, 9.5, 1]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuU", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuU", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuU", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyX", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamPandaHide", "Segments": [0, 1, 0, 9.5, 1]}, {"Target": "Parameter", "Id": "ParamPositionYPanda", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamSizePanda", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyX", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY1", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY2", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY3", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupIce", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupZ", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupInput", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamSCDishRO", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamSCCupRO", "Segments": [0, 0, 0, 9.5, 0]}, {"Target": "Parameter", "Id": "ParamSCCupY", "Segments": [0, 0, 0, 9.5, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 9.0, "Value": ""}]}