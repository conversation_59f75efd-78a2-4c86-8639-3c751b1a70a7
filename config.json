{"window": {"always_on_top": true, "transparent": true, "transparency_mode": "windows_api_colorkey", "alpha_value": 240, "width": 450, "height": 550, "x": 150, "y": 200}, "model": {"current_model_path": "", "scan_directories": ["D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Resources"], "auto_scan": true, "manual_scale_factor": 1.0, "sync_scale_enabled": true, "base_window_width": 400, "base_window_height": 500, "min_scale": 0.3, "max_scale": 3.0, "random_motion_enabled": true, "random_motion_interval_min": 10.0, "random_motion_interval_max": 30.0, "random_expression_enabled": true, "random_expression_interval_min": 15.0, "random_expression_interval_max": 45.0, "available_expressions": [], "selected_expressions": [], "available_motions": [], "selected_motions": [], "multi_model_enabled": false, "selected_models": []}, "llm": {"api_config": {"base_url": "https://hzmeaaogifcw.ap-northeast-1.clawcloudrun.com/v1", "api_key": "qq1230", "model": "[PAY]gemini-2.5-pro-openai", "timeout": 30.0, "max_retries": 3}, "default_params": {"temperature": 1.15, "max_tokens": 65535, "max_completion_tokens": null, "stream": false, "presence_penalty": 0, "frequency_penalty": 0, "top_p": 0.98, "top_k": null, "stop": null, "logit_bias": null, "seed": null, "n": null, "logprobs": null, "top_logprobs": null}, "conversation_settings": {"max_history_length": 20, "save_history": true, "system_prompt": "你是一个友好的AI助手。", "max_tokens": 65535}}, "openai_config": {"base_url": "https://hzmeaaogifcw.ap-northeast-1.clawcloudrun.com/v1", "api_key": "qq1230", "model": "[PAY]gemini-2.5-pro-openai", "default_params": {"temperature": 1.15, "max_tokens": 65535, "max_completion_tokens": null, "stream": false, "presence_penalty": 0, "frequency_penalty": 0, "top_p": 0.98, "top_k": null, "stop": null, "logit_bias": null, "seed": null, "n": null, "logprobs": null, "top_logprobs": null}}, "llm_presets": {"default": {"name": "默认预设", "system_prompt": "你是一个友好的AI助手。", "api_config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "max_tokens": 1000}}}, "text_display": {"typing_speed": 50, "auto_hide_delay": 5000, "max_chars_per_line": 20, "max_lines": 3, "typing_animation": true}, "voice_dialogue": {"microphone_config": {"sample_rate": 16000, "channels": 1, "chunk_size": 1024, "format": "paInt16", "input_device_index": null, "device_index": null, "auto_select_device": true, "volume_threshold": 0.01, "noise_reduction": true}, "key_triggered_config": {"trigger_key": "space", "max_recording_duration": 30.0, "show_recording_indicator": true, "visual_feedback": true, "hold_to_record": true, "min_recording_duration": 0.5, "silence_timeout": 2.0, "auto_stop_on_silence": true}, "realtime_config": {"volume_threshold": 0.01, "silence_duration": 2.0, "min_speech_duration": 0.5, "max_speech_duration": 30.0, "auto_restart": true, "sensitivity": 1.0, "voice_activity_threshold": 0.02, "energy_threshold": 300, "dynamic_energy_threshold": true}, "stt_config": {"model_path": "D:/huggingface_cache/hub/models--Systran--faster-whisper-large-v3", "device": "auto", "compute_type": "float16", "language": "auto", "task": "transcribe", "beam_size": 5, "temperature": [0.0, 0.2, 0.4, 0.6, 0.8, 1.0], "compression_ratio_threshold": 2.4, "log_prob_threshold": -1.0, "no_speech_threshold": 0.6, "vad_filter": true, "vad_parameters": {"threshold": 0.5, "min_speech_duration_ms": 250, "max_speech_duration_s": 30, "min_silence_duration_ms": 2000, "speech_pad_ms": 400}}, "tts_config": {"api_url": "http://localhost:9880", "default_params": {"text_lang": "zh", "cut_punc": ",.;?!、，。？！；：", "top_k": 20, "top_p": 0.6, "temperature": 0.6, "speed_factor": 1.0}, "audio_settings": {"output_dir": "output/audio", "auto_play": true, "save_audio": false, "format": "wav", "volume": 1.0, "playback_rate": 1.0}, "current_preset": "<PERSON><PERSON><PERSON><PERSON>", "presets": {"default": {"name": "默认语音", "refer_wav_path": "", "prompt_text": "", "prompt_lang": "zh", "text_lang": "zh", "cut_punc": ",.;?!、，。？！；：", "top_k": 20, "top_p": 0.6, "temperature": 0.6, "speed_factor": 1.0}, "shizuru": {"name": "静流角色语音", "refer_wav_path": "D:/BaiduNetdiskDownload/SOVITS/静流参考音频/z2053#00456.ogg", "prompt_text": "井上さん、葉っぱか何かを靴に巻いて、わざと足跡を消してるっぽい…", "prompt_lang": "ja", "text_lang": "zh", "cut_punc": ",.;?!、，。？！；：", "top_k": 5, "top_p": 1.0, "temperature": 1.0, "speed_factor": 1.0, "text_split_method": "cut5", "batch_size": 1, "batch_threshold": 0.75, "split_bucket": true, "fragment_interval": 0.3, "seed": -1, "media_type": "wav", "streaming_mode": true, "parallel_infer": true, "repetition_penalty": 1.35, "sample_steps": 32, "super_sampling": false}}}, "ui_settings": {"current_mode": "key_triggered", "show_volume_indicator": true, "show_status_text": true, "enable_live2d_sync": true, "show_voice_indicator": true, "show_volume_meter": true, "voice_button_position": "bottom_right", "hotkey_enabled": true, "notification_enabled": true, "auto_hide_ui": true, "ui_timeout": 3.0}, "enabled": true, "integration_settings": {"llm_integration": true, "auto_send_to_llm": true, "llm_response_tts": true, "context_memory": true, "conversation_history": true, "max_history_length": 10}}}