#!/usr/bin/env python3
"""
对话系统UI组件模块

包含文本显示、快速输入、语音设置等用户界面组件。
"""

from .text_overlay import TextOverlay, TextDisplayManager
from .quick_input import QuickInputOverlay

# 尝试导入语音设置对话框
try:
    from .voice_settings_dialog import VoiceSettingsDialog
    _voice_available = True
except ImportError as e:
    print(f"⚠️ 语音设置对话框导入失败: {e}")
    VoiceSettingsDialog = None
    _voice_available = False

__all__ = [
    "TextOverlay",
    "TextDisplayManager",
    "QuickInputOverlay",
]

if _voice_available:
    __all__.append("VoiceSettingsDialog")
