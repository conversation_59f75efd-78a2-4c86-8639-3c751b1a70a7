#!/usr/bin/env python3
"""
GPT-SoVITS API连接测试

使用正确的API参数格式测试GPT-SoVITS服务连接
"""

import sys
import os
import json
import requests
import time

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config['voice_dialogue']['tts_config']
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return None

def test_basic_connection(api_url):
    """测试基础连接"""
    print(f"🔄 测试基础连接: {api_url}")
    
    try:
        # 任何请求都可以，只要能得到响应就说明服务在运行
        response = requests.get(f"{api_url}/", timeout=5)
        print(f"✅ 服务正在运行 - 状态码: {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 服务未运行或地址错误")
        return False
    except requests.exceptions.Timeout:
        print("❌ 连接超时")
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def test_tts_api_simple(api_url):
    """测试简单TTS API调用"""
    print(f"\n🔄 测试简单TTS API调用...")
    
    # 使用最基本的参数
    test_data = {
        "text": "你好",
        "text_lang": "zh"
    }
    
    try:
        response = requests.post(f"{api_url}/tts", json=test_data, timeout=10)
        
        if response.status_code == 200:
            print("✅ TTS API调用成功")
            print(f"   响应类型: {response.headers.get('content-type', 'unknown')}")
            print(f"   响应大小: {len(response.content)} bytes")
            return True
        else:
            print(f"⚠️ TTS API响应异常 - 状态码: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ TTS API调用失败: {e}")
        return False

def test_tts_api_with_preset(api_url, preset_config):
    """使用预设配置测试TTS API"""
    print(f"\n🔄 测试TTS API (使用预设配置)...")
    
    # 构建完整的请求参数
    test_data = {
        "text": "这是一个测试语音合成的文本。",
        "text_lang": preset_config.get("text_lang", "zh"),
        "ref_audio_path": preset_config.get("refer_wav_path", ""),
        "prompt_text": preset_config.get("prompt_text", ""),
        "prompt_lang": preset_config.get("prompt_lang", "zh"),
        "top_k": preset_config.get("top_k", 5),
        "top_p": preset_config.get("top_p", 1.0),
        "temperature": preset_config.get("temperature", 1.0),
        "speed_factor": preset_config.get("speed_factor", 1.0)
    }
    
    print("请求参数:")
    for key, value in test_data.items():
        if key == "ref_audio_path" and len(str(value)) > 50:
            print(f"  {key}: {str(value)[:50]}...")
        else:
            print(f"  {key}: {value}")
    
    try:
        response = requests.post(f"{api_url}/tts", json=test_data, timeout=30)
        
        if response.status_code == 200:
            print("✅ TTS API调用成功 (预设配置)")
            print(f"   响应类型: {response.headers.get('content-type', 'unknown')}")
            print(f"   响应大小: {len(response.content)} bytes")
            
            # 如果是音频响应，可以保存测试文件
            if 'audio' in response.headers.get('content-type', ''):
                try:
                    with open('test_output.wav', 'wb') as f:
                        f.write(response.content)
                    print("   测试音频已保存为: test_output.wav")
                except Exception as e:
                    print(f"   保存音频失败: {e}")
            
            return True
        else:
            print(f"⚠️ TTS API响应异常 - 状态码: {response.status_code}")
            try:
                error_info = response.json()
                print(f"   错误信息: {error_info}")
            except:
                print(f"   响应内容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ TTS API调用失败: {e}")
        return False

def test_available_endpoints(api_url):
    """测试可用的API端点"""
    print(f"\n🔄 测试可用的API端点...")
    
    endpoints = [
        "/",
        "/tts",
        "/health", 
        "/status",
        "/models",
        "/presets"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{api_url}{endpoint}", timeout=5)
            status = "✅" if response.status_code == 200 else "⚠️"
            print(f"  {status} {endpoint}: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"  ❌ {endpoint}: 连接失败")
        except Exception as e:
            print(f"  ❌ {endpoint}: {str(e)[:50]}...")

def main():
    """主函数"""
    print("="*60)
    print("GPT-SoVITS API连接测试")
    print("="*60)
    
    # 加载配置
    tts_config = load_config()
    if not tts_config:
        return
    
    api_url = tts_config['api_url']
    print(f"API地址: {api_url}")
    
    # 测试基础连接
    if not test_basic_connection(api_url):
        print("\n❌ 基础连接失败，请检查：")
        print("   1. GPT-SoVITS服务是否正在运行")
        print("   2. API地址是否正确")
        print("   3. 防火墙设置")
        return
    
    # 测试可用端点
    test_available_endpoints(api_url)
    
    # 测试简单TTS调用
    test_tts_api_simple(api_url)
    
    # 测试使用预设配置
    current_preset = tts_config.get('current_preset', 'default')
    presets = tts_config.get('presets', {})
    
    if current_preset in presets:
        print(f"\n使用预设: {current_preset}")
        test_tts_api_with_preset(api_url, presets[current_preset])
    else:
        print(f"\n⚠️ 当前预设 '{current_preset}' 不存在，使用默认参数")
        test_tts_api_with_preset(api_url, tts_config.get('default_params', {}))
    
    print("\n" + "="*60)
    print("测试完成")
    print("="*60)
    
    print("\n💡 说明:")
    print("   - 状态码200: API调用成功")
    print("   - 状态码400: 参数错误")
    print("   - 状态码404: 端点不存在（但服务正在运行）")
    print("   - 状态码500: 服务器内部错误")
    print("   - 连接失败: 服务未运行")

if __name__ == "__main__":
    main()
