#!/usr/bin/env python3
"""
简单的语音对话集成测试

测试语音对话功能的基本集成，不涉及Qt应用程序创建
"""

import sys
import os

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'dev'))

def test_imports():
    """测试所有必要的导入"""
    print("🔄 测试模块导入...")
    
    results = {}
    
    # 1. 测试语音设置对话框
    print("\n1. 测试语音设置对话框...")
    try:
        from dialogue_system.ui.voice_settings_dialog import VoiceSettingsDialog
        results['voice_settings_dialog'] = True
        print("✅ VoiceSettingsDialog导入成功")
    except Exception as e:
        results['voice_settings_dialog'] = False
        print(f"❌ VoiceSettingsDialog导入失败: {e}")
    
    # 2. 测试UI模块
    print("\n2. 测试UI模块...")
    try:
        from dialogue_system.ui import VoiceSettingsDialog as UIVoiceSettingsDialog
        results['ui_module'] = True
        print("✅ UI模块导入成功")
    except Exception as e:
        results['ui_module'] = False
        print(f"❌ UI模块导入失败: {e}")
    
    # 3. 测试配置管理器
    print("\n3. 测试配置管理器...")
    try:
        from settings_dialog import ConfigManager
        config_manager = ConfigManager()
        results['config_manager'] = True
        print("✅ ConfigManager创建成功")
    except Exception as e:
        results['config_manager'] = False
        print(f"❌ ConfigManager创建失败: {e}")
        return results
    
    # 4. 测试语音配置
    print("\n4. 测试语音配置...")
    try:
        from dialogue_system.config import VoiceDialogueConfig
        voice_config = VoiceDialogueConfig(config_manager)
        results['voice_config'] = True
        print("✅ VoiceDialogueConfig创建成功")
    except Exception as e:
        results['voice_config'] = False
        print(f"❌ VoiceDialogueConfig创建失败: {e}")
    
    # 5. 测试语音对话管理器（可能失败）
    print("\n5. 测试语音对话管理器...")
    try:
        from dialogue_system.voice import VoiceDialogueManager
        results['voice_manager'] = True
        print("✅ VoiceDialogueManager导入成功")
    except ImportError as e:
        if "pyaudio" in str(e).lower():
            results['voice_manager'] = 'pyaudio_missing'
            print("⚠️ VoiceDialogueManager导入失败（PyAudio未安装）")
        else:
            results['voice_manager'] = False
            print(f"❌ VoiceDialogueManager导入失败: {e}")
    except Exception as e:
        results['voice_manager'] = False
        print(f"❌ VoiceDialogueManager导入异常: {e}")
    
    return results

def test_main_window_integration():
    """测试主窗口集成"""
    print("\n🔄 测试主窗口集成...")
    
    try:
        # 检查主窗口文件
        main_window_path = os.path.join('dev', 'main_window.py')
        if not os.path.exists(main_window_path):
            print("❌ 主窗口文件不存在")
            return False
        
        with open(main_window_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查语音相关方法
        voice_methods = [
            'init_voice_dialogue',
            'start_key_triggered_voice', 
            'start_realtime_voice',
            'stop_voice_input',
            'show_voice_settings',
            'on_voice_input_received'
        ]
        
        missing_methods = []
        for method in voice_methods:
            if f"def {method}" not in content:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 主窗口缺少语音方法: {missing_methods}")
            return False
        else:
            print("✅ 主窗口包含所有语音方法")
        
        # 检查语音菜单
        if "🎤 语音对话" in content:
            print("✅ 主窗口包含语音菜单")
        else:
            print("❌ 主窗口缺少语音菜单")
            return False
        
        # 检查语音组件初始化
        if "self.voice_dialogue_manager = None" in content:
            print("✅ 主窗口包含语音组件初始化")
        else:
            print("❌ 主窗口缺少语音组件初始化")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 主窗口集成检查失败: {e}")
        return False

def test_config_file():
    """测试配置文件"""
    print("\n🔄 测试配置文件...")
    
    try:
        import json
        
        # 检查主配置文件
        config_path = 'config.json'
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            if 'voice_dialogue' in config:
                print("✅ 主配置文件包含语音对话配置")
                
                voice_config = config['voice_dialogue']
                required_sections = ['stt_config', 'tts_config', 'microphone_config']
                
                for section in required_sections:
                    if section in voice_config:
                        print(f"✅ 配置包含 {section}")
                    else:
                        print(f"⚠️ 配置缺少 {section}")
                
                return True
            else:
                print("❌ 主配置文件缺少语音对话配置")
                return False
        else:
            print("❌ 主配置文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件检查失败: {e}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("Live2D语音对话集成测试（简化版）")
    print("="*60)
    
    # 测试导入
    import_results = test_imports()
    
    # 测试主窗口集成
    main_window_ok = test_main_window_integration()
    
    # 测试配置文件
    config_ok = test_config_file()
    
    print("\n" + "="*60)
    print("测试总结:")
    print("="*60)
    
    # 导入测试结果
    print("模块导入测试:")
    for module, result in import_results.items():
        if result is True:
            print(f"  ✅ {module}: 成功")
        elif result == 'pyaudio_missing':
            print(f"  ⚠️ {module}: PyAudio未安装（预期）")
        else:
            print(f"  ❌ {module}: 失败")
    
    # 集成测试结果
    print(f"\n主窗口集成: {'✅ 成功' if main_window_ok else '❌ 失败'}")
    print(f"配置文件: {'✅ 成功' if config_ok else '❌ 失败'}")
    
    # 总体评估
    critical_modules = ['voice_settings_dialog', 'ui_module', 'config_manager', 'voice_config']
    critical_success = all(import_results.get(module, False) for module in critical_modules)
    
    voice_manager_ok = import_results.get('voice_manager') in [True, 'pyaudio_missing']
    
    overall_success = critical_success and main_window_ok and config_ok and voice_manager_ok
    
    print("\n" + "="*60)
    if overall_success:
        print("🎉 语音对话集成测试通过！")
        print("\n✅ 所有核心组件都已正确集成")
        print("✅ 主窗口包含语音功能菜单和方法")
        print("✅ 配置文件包含语音设置")
        
        if import_results.get('voice_manager') == 'pyaudio_missing':
            print("\n💡 安装PyAudio后即可使用完整语音功能:")
            print("   pip install pyaudio")
        
        print("\n🎤 现在可以在Live2D主窗口右键菜单中找到'语音对话'选项！")
        
    else:
        print("❌ 语音对话集成测试失败")
        print("\n请检查以下问题:")
        
        if not critical_success:
            print("- 核心模块导入失败")
        if not main_window_ok:
            print("- 主窗口集成不完整")
        if not config_ok:
            print("- 配置文件问题")
    
    print("="*60)

if __name__ == "__main__":
    main()
