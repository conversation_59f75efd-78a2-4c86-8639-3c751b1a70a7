#!/usr/bin/env python3
"""
Live2D语音对话系统 - faster-whisper客户端

这个模块提供了faster-whisper语音识别功能，包括：
- 本地模型加载和管理
- 音频文件和数据流识别
- 多语言支持
- 批量处理和实时识别

使用示例：
    from dialogue_system.stt.faster_whisper_client import FasterWhisperClient
    
    # 创建客户端
    client = FasterWhisperClient(
        model_path="D:/huggingface_cache/hub/models--Systran--faster-whisper-large-v3",
        device="auto"
    )
    
    # 识别音频文件
    result = client.transcribe_file("audio.wav")
    
    # 识别音频数据
    result = client.transcribe_audio_data(audio_array, sample_rate=16000)
"""

import os
import io
import wave
import tempfile
import numpy as np
from typing import Optional, List, Dict, Any, Union, Tuple
from pathlib import Path

try:
    from faster_whisper import WhisperModel
    FASTER_WHISPER_AVAILABLE = True
except ImportError:
    FASTER_WHISPER_AVAILABLE = False
    print("⚠️ faster-whisper未安装，STT功能将不可用")


class FasterWhisperClient:
    """faster-whisper语音识别客户端"""
    
    def __init__(self, model_path: str, device: str = "auto", compute_type: str = "float16"):
        """初始化faster-whisper客户端"""
        self.model_path = model_path
        self.device = device
        self.compute_type = compute_type
        
        # 模型对象
        self.model: Optional[WhisperModel] = None
        self.is_loaded = False
        
        # 默认参数
        self.default_params = {
            "language": "auto",
            "task": "transcribe",
            "beam_size": 5,
            "temperature": [0.0, 0.2, 0.4, 0.6, 0.8, 1.0],
            "compression_ratio_threshold": 2.4,
            "log_prob_threshold": -1.0,
            "no_speech_threshold": 0.6
        }
        
        print(f"🤖 faster-whisper客户端初始化完成")
        print(f"   模型路径: {model_path}")
        print(f"   设备: {device}")
        print(f"   计算类型: {compute_type}")
        
    def load_model(self) -> bool:
        """加载模型"""
        if not FASTER_WHISPER_AVAILABLE:
            print("❌ faster-whisper不可用")
            return False
            
        if self.is_loaded:
            print("✅ 模型已加载")
            return True
            
        try:
            print("🔄 正在加载faster-whisper模型...")
            
            # 检查模型路径
            if not os.path.exists(self.model_path):
                print(f"❌ 模型路径不存在: {self.model_path}")
                return False
                
            # 加载模型
            self.model = WhisperModel(
                self.model_path,
                device=self.device,
                compute_type=self.compute_type
            )
            
            self.is_loaded = True
            print("✅ faster-whisper模型加载成功")
            return True
            
        except Exception as e:
            print(f"❌ 加载faster-whisper模型失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    def transcribe_file(self, 
                       audio_file_path: str, 
                       language: Optional[str] = None,
                       **kwargs) -> Dict[str, Any]:
        """识别音频文件"""
        if not self.is_loaded:
            if not self.load_model():
                return {"error": "模型加载失败"}
                
        try:
            # 准备参数
            params = self.default_params.copy()
            if language:
                params["language"] = language
            params.update(kwargs)
            
            print(f"🎤 开始识别音频文件: {audio_file_path}")
            
            # 执行识别
            segments, info = self.model.transcribe(
                audio_file_path,
                language=params["language"] if params["language"] != "auto" else None,
                task=params["task"],
                beam_size=params["beam_size"],
                temperature=params["temperature"],
                compression_ratio_threshold=params["compression_ratio_threshold"],
                log_prob_threshold=params["log_prob_threshold"],
                no_speech_threshold=params["no_speech_threshold"]
            )
            
            # 收集结果
            text_segments = []
            full_text = ""
            
            for segment in segments:
                segment_info = {
                    "start": segment.start,
                    "end": segment.end,
                    "text": segment.text.strip(),
                    "avg_logprob": segment.avg_logprob,
                    "no_speech_prob": segment.no_speech_prob
                }
                text_segments.append(segment_info)
                full_text += segment.text.strip() + " "
                
            full_text = full_text.strip()
            
            result = {
                "text": full_text,
                "segments": text_segments,
                "language": info.language,
                "language_probability": info.language_probability,
                "duration": info.duration,
                "duration_after_vad": info.duration_after_vad,
                "success": True
            }
            
            print(f"✅ 识别完成: {full_text}")
            return result
            
        except Exception as e:
            print(f"❌ 音频识别失败: {e}")
            return {"error": str(e), "success": False}
            
    def transcribe_audio_data(self, 
                             audio_data: np.ndarray, 
                             sample_rate: int = 16000,
                             language: Optional[str] = None,
                             **kwargs) -> Dict[str, Any]:
        """识别音频数据"""
        if not self.is_loaded:
            if not self.load_model():
                return {"error": "模型加载失败"}
                
        try:
            # 创建临时WAV文件
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_path = temp_file.name
                
                # 写入WAV数据
                with wave.open(temp_path, 'wb') as wav_file:
                    wav_file.setnchannels(1)  # 单声道
                    wav_file.setsampwidth(2)  # 16位
                    wav_file.setframerate(sample_rate)
                    wav_file.writeframes(audio_data.astype(np.int16).tobytes())
                    
            try:
                # 识别临时文件
                result = self.transcribe_file(temp_path, language=language, **kwargs)
                return result
            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_path)
                except:
                    pass
                    
        except Exception as e:
            print(f"❌ 音频数据识别失败: {e}")
            return {"error": str(e), "success": False}
            
    def transcribe_audio_bytes(self, 
                              audio_bytes: bytes,
                              language: Optional[str] = None,
                              **kwargs) -> Dict[str, Any]:
        """识别音频字节数据"""
        if not self.is_loaded:
            if not self.load_model():
                return {"error": "模型加载失败"}
                
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_file.write(audio_bytes)
                temp_path = temp_file.name
                
            try:
                # 识别临时文件
                result = self.transcribe_file(temp_path, language=language, **kwargs)
                return result
            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_path)
                except:
                    pass
                    
        except Exception as e:
            print(f"❌ 音频字节识别失败: {e}")
            return {"error": str(e), "success": False}
            
    def set_default_params(self, **params):
        """设置默认参数"""
        self.default_params.update(params)
        print(f"🔧 已更新默认参数: {params}")
        
    def get_supported_languages(self) -> List[str]:
        """获取支持的语言列表"""
        # Whisper支持的语言
        return [
            "auto", "zh", "en", "ja", "ko", "es", "fr", "de", "it", "pt", "ru",
            "ar", "hi", "th", "vi", "id", "ms", "tl", "tr", "pl", "nl", "sv",
            "da", "no", "fi", "cs", "sk", "hu", "ro", "bg", "hr", "sl", "et",
            "lv", "lt", "mt", "ga", "cy", "is", "mk", "sq", "az", "be", "bs",
            "eu", "gl", "ka", "kk", "ky", "lb", "mn", "ne", "ps", "fa", "ta",
            "te", "ur", "uz", "bn", "gu", "kn", "ml", "mr", "pa", "si", "am",
            "as", "ay", "bm", "br", "dv", "fo", "fy", "gd", "gn", "ha", "haw",
            "he", "ht", "ig", "jw", "km", "la", "ln", "lo", "mg", "mi", "my",
            "oc", "om", "qu", "rw", "sa", "sd", "sn", "so", "su", "sw", "tg",
            "tk", "tt", "ug", "wo", "xh", "yi", "yo", "zu"
        ]
        
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_path": self.model_path,
            "device": self.device,
            "compute_type": self.compute_type,
            "is_loaded": self.is_loaded,
            "available": FASTER_WHISPER_AVAILABLE
        }
        
    def test_recognition(self) -> bool:
        """测试识别功能"""
        if not self.is_loaded:
            if not self.load_model():
                return False
                
        try:
            # 创建测试音频（1秒的静音）
            sample_rate = 16000
            duration = 1.0
            samples = int(sample_rate * duration)
            test_audio = np.zeros(samples, dtype=np.int16)
            
            # 测试识别
            result = self.transcribe_audio_data(test_audio, sample_rate)
            
            if result.get("success", False):
                print("✅ STT功能测试通过")
                return True
            else:
                print(f"❌ STT功能测试失败: {result.get('error', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"❌ STT功能测试异常: {e}")
            return False
            
    def cleanup(self):
        """清理资源"""
        if self.model:
            # faster-whisper模型通常不需要显式清理
            self.model = None
            self.is_loaded = False
            print("🧹 STT模型资源已清理")
            
    def __del__(self):
        """析构函数"""
        self.cleanup()
