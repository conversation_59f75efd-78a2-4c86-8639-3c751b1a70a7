{"Version": 3, "Meta": {"Duration": 5.933, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 244, "TotalSegmentCount": 12740, "TotalPointCount": 15140, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.5, 1, 0, 0.667, 0.9, 0, 1.067, 1, 0, 1.433, 0.7, 2, 2.967, 0.7, 0, 3.233, 0, 0, 3.467, 1, 2, 5.933, 1]}, {"Target": "Parameter", "Id": "ParamEyeSmileL", "Segments": [0, 0, 2, 2.867, 0, 0, 3.2, 1, 2, 3.4, 1, 0, 3.733, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.5, 1, 0, 0.667, 0.9, 0, 1.067, 1, 0, 1.433, 0.7, 2, 2.967, 0.7, 0, 3.233, 0, 0, 3.467, 1, 2, 5.933, 1]}, {"Target": "Parameter", "Id": "ParamEyeSmileR", "Segments": [0, 0, 2, 2.867, 0, 0, 3.2, 1, 2, 3.4, 1, 0, 3.733, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpen2", "Segments": [0, 0, 2, 3.067, 0, 0, 3.267, 1, 2, 3.7, 1, 0, 3.9, 0.5, 2, 4.367, 0.5, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamMouthType", "Segments": [0, 0, 2, 3.067, 0, 2, 3.233, 0, 2, 3.267, 1, 2, 5, 1, 2, 5.033, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 0, 0.2, -0.5, 2, 0.4, -0.5, 0, 0.567, 0, 0, 0.8, -0.5, 0, 0.9, -0.2, 0, 1.033, -0.6, 0, 1.133, -0.5, 0, 1.233, -1, 2, 1.267, -1, 2, 1.367, -1, 0, 1.533, -0.5, 0, 1.733, -0.7, 0, 1.967, -0.5, 2, 2.167, -0.5, 0, 2.4, 0.505, 2, 2.7, 0.505, 0, 2.933, 0, 2, 3.067, 0, 0, 3.267, -0.5, 0, 3.5, 0.619, 0, 3.667, -0.7, 0, 4.033, 1, 2, 4.633, 1, 1, 4.766, 1, 4.9, 0.655, 5.033, 0.4, 1, 5.211, 0.061, 5.389, 0, 5.567, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 0.2, 1, 0, 0.3, 0.7, 0, 0.4, 0.8, 0, 0.567, 0.5, 0, 0.8, 1, 0, 1.033, 0.3, 0, 1.167, 0.637, 0, 1.367, 0.6, 0, 1.533, 0.8, 1, 1.6, 0.8, 1.666, 0.642, 1.733, 0.5, 1, 1.811, 0.334, 1.889, 0.3, 1.967, 0.3, 0, 2.167, 1, 0, 2.367, 0, 2, 2.533, 0, 0, 2.7, 1, 0, 2.933, 0.6, 0, 3.067, 1, 0, 3.2, 0.892, 0, 3.333, 1, 0, 3.5, 0.6, 2, 3.667, 0.6, 0, 3.9, 0.7, 0, 4.1, 0.5, 2, 4.6, 0.5, 0, 4.833, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamMouthSize", "Segments": [0, 0, 0, 0.2, -0.5, 0, 0.6, 0, 0, 0.8, -0.7, 0, 1.067, 0, 0, 1.267, -0.5, 0, 1.533, -0.3, 2, 1.967, -0.3, 0, 2.1, -0.9, 0, 2.267, 0, 1, 2.434, 0, 2.6, -0.076, 2.767, -0.3, 1, 2.822, -0.375, 2.878, -0.475, 2.933, -0.475, 0, 3.133, 0, 0, 3.267, -0.475, 0, 3.767, 0, 2, 4.667, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamMouthY", "Segments": [0, 0, 2, 0.467, 0, 0, 0.933, 0.2, 0, 1.333, 0, 2, 2.833, 0, 0, 3.167, -0.2, 0, 4, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamEyeEmotion", "Segments": [0, 0, 2, 0.467, 0, 0, 0.933, 1, 0, 1.333, -1, 2, 2.833, -1, 1, 2.944, -1, 3.056, -0.827, 3.167, -0.3, 1, 3.334, 0.491, 3.5, 1, 3.667, 1, 0, 4.033, 0.9, 2, 4.533, 0.9, 0, 4.733, 1, 0, 5.467, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamTeethLight", "Segments": [0, 0, 2, 3.667, 0, 1, 3.767, 0, 3.867, 0.117, 3.967, 0.274, 1, 4.134, 0.537, 4.3, 0.704, 4.467, 1.001, 1, 4.6, 1.239, 4.734, 1.5, 4.867, 1.5, 2, 4.9, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLX", "Segments": [0, 0, 2, 0.7, 0, 0, 1.233, -0.301, 2, 2.467, -0.301, 0, 2.8, 0, 2, 3.233, 0, 0, 3.633, 0.5, 2, 4.6, 0.5, 0, 5.3, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRX", "Segments": [0, 0, 2, 0.7, 0, 0, 1.233, -0.5, 2, 2.467, -0.5, 0, 2.8, 0, 2, 3.233, 0, 0, 3.633, 0.301, 2, 4.6, 0.301, 0, 5.3, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPHYInputX", "Segments": [0, 0, 2, 0.233, 0, 2, 0.467, 0, 0, 0.667, -8, 0, 1.033, 4, 0, 1.4, -6.749, 0, 1.6, -4.233, 0, 1.867, -5, 0, 2.233, -4.312, 0, 2.633, -5, 0, 2.967, -4, 0, 3.133, -13, 0, 3.567, 11, 0, 3.867, 4, 0, 4.067, 11, 0, 4.5, 5.386, 0, 4.7, 9.498, 1, 4.767, 9.498, 4.833, 9.805, 4.9, 9.247, 1, 5.122, 7.387, 5.345, -2, 5.567, -2, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.367, 0, 2, 0.467, 0, 0, 1.267, 10, 0, 1.6, 8.637, 0, 2, 10.454, 1, 2.233, 10.454, 2.467, 10.585, 2.7, 10, 1, 2.956, 9.36, 3.211, -2.909, 3.467, -4.003, 1, 3.734, -5.145, 4, -5, 4.267, -5, 0, 5.1, 1, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.233, 0, 2, 0.467, 0, 0, 0.667, -8, 0, 1.033, 4, 0, 1.4, -6.749, 0, 1.6, -4.233, 0, 1.867, -5, 0, 2.233, -4.312, 0, 2.633, -5, 0, 2.967, -4, 0, 3.133, -13, 0, 3.567, 11, 0, 3.867, 4, 0, 4.067, 11, 0, 4.5, 5.386, 0, 4.7, 9.498, 1, 4.767, 9.498, 4.833, 9.805, 4.9, 9.247, 1, 5.122, 7.387, 5.345, -2, 5.567, -2, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.4, 0, 0, 0.633, -5, 0, 1.133, 18, 0, 1.433, 14, 0, 1.767, 16, 2, 2.967, 16, 0, 3.167, 17, 0, 3.633, 7, 0, 3.8, 10.504, 0, 4.1, 8.44, 0, 4.433, 10, 0, 5.3, -1.863, 0, 5.733, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamAngleH", "Segments": [0, 0, 2, 0.467, 0, 2, 3.033, 0, 0, 3.2, -3.551, 0, 3.5, 9, 0, 3.8, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamArm<PERSON>pperRAngle", "Segments": [0, 0, 2, 0.467, 0, 0, 0.9, -5, 0, 1.233, 2, 0, 1.533, 1.245, 2, 1.8, 1.245, 2, 2.133, 1.245, 2, 2.7, 1.245, 0, 3.067, 4, 0, 3.867, 0, 0, 4.633, 1, 0, 5.367, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRAngle", "Segments": [0, 7.4, 0, 0.267, 7.76, 1, 0.389, 7.76, 0.511, 7.791, 0.633, 7.4, 1, 0.733, 7.08, 0.833, 4.942, 0.933, 3, 1, 1.044, 0.843, 1.156, -1.683, 1.267, -1.683, 0, 1.567, -1, 0, 2.033, -1.398, 0, 2.533, -1, 2, 2.767, -1, 0, 3.167, 1, 1, 3.289, 1, 3.411, -3.709, 3.533, -4, 1, 3.644, -4.265, 3.756, -4.2, 3.867, -4.2, 1, 4.134, -4.2, 4.4, -4.225, 4.667, -4, 1, 4.856, -3.841, 5.044, 7.76, 5.233, 7.76, 0, 5.433, 7.4, 2, 5.933, 7.4]}, {"Target": "Parameter", "Id": "ParamArmHandRAngle", "Segments": [0, 0, 2, 0.467, 0, 0, 0.767, -1, 0, 1.033, 2.199, 0, 1.333, 1, 0, 1.733, 1.17, 0, 2.1, 0.732, 0, 2.533, 1.17, 0, 2.8, 1, 0, 3.167, 3, 0, 3.5, 2.199, 0, 3.733, 7, 1, 3.778, 7, 3.822, 6.054, 3.867, 6, 1, 4.178, 5.623, 4.489, 5.478, 4.8, 5, 1, 4.944, 4.778, 5.089, -1.388, 5.233, -1.388, 0, 5.4, 0.732, 0, 5.633, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerRH", "Segments": [0, 0, 2, 0.467, 0, 0, 0.9, 8, 0, 1.233, -21, 0, 1.733, -16, 2, 2.9, -16, 0, 3.133, -21, 0, 3.433, 11, 0, 3.633, 4.486, 0, 3.933, 6, 1, 4.044, 6, 4.156, 6.149, 4.267, 5.365, 1, 4.645, 2.698, 5.022, 0, 5.4, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperRH", "Segments": [0, 0, 2, 0.467, 0, 2, 0.933, 0, 0, 1.267, 3, 2, 1.7, 3, 2, 2.7, 3, 0, 3.1, 6, 1, 3.2, 6, 3.3, -4.005, 3.4, -6, 1, 3.544, -8.882, 3.689, -9, 3.833, -9, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRAngle", "Segments": [0, 0, 2, 0.467, 0, 0, 0.667, -10, 0, 0.9, -6, 2, 1.767, -6, 2, 2.767, -6, 0, 3.167, -8, 0, 3.367, 4, 0, 3.6, 0, 2, 4.633, 0, 0, 4.967, -4, 0, 5.3, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamHandT2R", "Segments": [0, 0, 2, 0.467, 0, 2, 0.667, 0, 0, 0.9, 1, 0, 1.233, 0.7, 2, 1.767, 0.7, 2, 2.767, 0.7, 0, 3.167, 0, 0, 3.367, 0.597, 0, 3.6, 0.5, 2, 4.633, 0.5, 0, 4.967, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamHandRCup", "Segments": [0, 0, 2, 0.467, 0, 2, 0.733, 0, 0, 0.967, 1, 2, 1.767, 1, 2, 2.767, 1, 2, 4.667, 1, 2, 4.8, 1, 0, 4.967, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY1", "Segments": [0, 0, 2, 0.467, 0, 2, 0.8, 0, 1, 0.856, 0, 0.911, 4.332, 0.967, 6.5, 1, 1.134, 13.005, 1.3, 15, 1.467, 15, 2, 1.5, 0, 2, 3, 0, 0, 3.3, 7.311, 0, 3.8, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY2", "Segments": [0, 0, 2, 0.467, 0, 2, 0.9, 0, 0, 1.033, 4.9, 0, 1.267, 0, 1, 1.356, 0, 1.444, 6.649, 1.533, 9, 1, 1.722, 13.996, 1.911, 15, 2.1, 15, 2, 2.133, 0, 2, 3.033, 0, 1, 3.166, 0, 3.3, 3.329, 3.433, 9.641, 1, 3.511, 13.323, 3.589, 15, 3.667, 15, 2, 3.7, 0, 0, 4.167, 15, 0, 4.2, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamHandLIQY3", "Segments": [0, 0, 2, 0.467, 0, 2, 0.967, 0, 2, 1.067, 0, 1, 1.122, 0, 1.178, 3.878, 1.233, 6, 1, 1.411, 12.79, 1.589, 15, 1.767, 15, 2, 1.8, 0, 2, 3.1, 0, 0, 3.5, 15, 2, 3.533, 0, 0, 4.033, 15, 0, 4.067, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamHandCupZ", "Segments": [0, 0, 2, 0.3, 0, 2, 0.467, 0, 0, 0.633, -0.185, 0, 0.967, 0.425, 0, 1.367, -0.405, 0, 1.833, 0.564, 0, 2.3, -0.474, 0, 2.733, 0.608, 0, 2.933, 0, 2, 3.167, 0, 0, 3.3, 0.425, 0, 3.7, -0.405, 0, 3.9, 0.564, 0, 4.367, -0.474, 0, 4.733, 0.608, 0, 5.333, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamHandCupY", "Segments": [0, 0, 2, 0.3, 0, 2, 0.567, 0, 0, 0.833, -0.198, 0, 1.167, 0.494, 0, 1.5, -0.484, 0, 1.967, 0.586, 0, 2.467, -0.544, 0, 2.933, 0.494, 0, 3.2, -1, 0, 3.467, 0.494, 0, 3.8, -0.484, 0, 4.067, 0.586, 0, 4.467, -0.544, 0, 4.933, 0.494, 0, 5.433, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 0.467, 5, 0, 1.067, -1, 0, 1.3, 0, 0, 1.733, -1.469, 0, 2.067, -0.762, 0, 2.567, -1.131, 2, 2.867, -1.131, 0, 3.167, -0.438, 0, 3.533, -9, 0, 3.767, -7.741, 0, 3.867, -8.489, 0, 4.533, -8.247, 0, 4.833, -8.964, 0, 5.433, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.033, 0, 0, 0.533, -6, 0, 0.967, 0.8, 0, 1.267, -7, 0, 1.5, -5.065, 0, 1.8, -6.34, 0, 2.067, -5.4, 0, 2.533, -6, 0, 2.733, -4, 0, 3.167, -5, 0, 3.467, 10, 0, 3.667, 5, 0, 3.867, 9.862, 0, 4.233, 5.575, 0, 4.533, 9, 2, 5, 9, 0, 5.5, -0.478, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.1, 0, 0, 0.6, -2, 0, 1.433, 2, 0, 2, 1.662, 2, 2.533, 1.662, 0, 3.167, 3.363, 0, 3.5, -3, 0, 4.033, -1.527, 2, 4.533, -1.527, 0, 4.8, -1.603, 0, 5.267, 1, 0, 5.667, -0.194, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX2", "Segments": [0, 0, 2, 0.467, 0, 0, 0.7, -2, 0, 1.267, 2.436, 0, 1.633, -1.19, 0, 1.933, 0, 2, 2.9, 0, 0, 3.167, 5, 0, 3.633, -2, 0, 3.967, 1, 0, 4.233, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY2", "Segments": [0, 0, 2, 0.1, 0, 0, 0.633, -4.776, 0, 1.033, 0.221, 0, 1.367, -8.342, 0, 1.667, -5.405, 0, 1.933, -6.548, 0, 2.167, -6, 2, 2.433, -6, 0, 2.767, -5.405, 0, 3.067, -6.119, 0, 3.433, 3.943, 0, 3.8, -2.748, 0, 4.067, -0.698, 1, 4.167, -0.698, 4.267, -1.898, 4.367, -2.113, 1, 4.622, -2.663, 4.878, -2.748, 5.133, -2.748, 0, 5.533, 0.982, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechX", "Segments": [0, 0, 2, 0.367, 0, 0, 0.667, 1, 0, 1.267, -8, 0, 1.567, -6.944, 0, 1.967, -7.423, 0, 2.6, -6.713, 0, 2.8, -8, 0, 3.3, 5, 0, 3.667, -2.621, 0, 3.9, -0.769, 1, 3.978, -0.769, 4.055, -1.881, 4.133, -2.113, 1, 4.378, -2.842, 4.622, -3, 4.867, -3, 0, 5.267, 1, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBodyBreechW", "Segments": [0, 0, 2, 0.467, 0, 2, 2.833, 0, 0, 3.1, 3, 0, 3.333, -6, 0, 3.767, 1.169, 0, 4.167, -0.442, 0, 4.6, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, 0, 2, 0.133, 0, 2, 0.467, 0, 0, 0.667, -1, 0, 1.267, 2.74, 0, 1.5, 2.42, 2, 2.467, 2.42, 0, 3.033, 3, 0, 3.4, -1, 0, 3.867, -0.527, 2, 4.433, -0.527, 0, 4.633, -0.634, 0, 5.1, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamShoulderR", "Segments": [0, 0, 2, 0.467, 0, 0, 0.767, -6.964, 0, 1.233, 4.976, 0, 1.667, 0, 2, 2.867, 0, 0, 3.133, -30, 0, 3.567, 30, 0, 3.833, 21.673, 2, 5.167, 21.673, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Z", "Segments": [0, 0, 2, 0.467, 0, 2, 2.933, 0, 0, 3.433, 1, 0, 3.8, -0.36, 0, 4.133, 0.314, 0, 4.5, -0.242, 0, 4.833, 0.18, 0, 5.167, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Y", "Segments": [0, 0, 2, 0.467, 0, 0, 0.767, 2, 0, 1.4, -5, 2, 2.8, -5, 0, 3.3, 1, 0, 3.667, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLegR2Y", "Segments": [0, 0, 2, 0.467, 0, 2, 2.867, 0, 0, 3.4, 5.514, 0, 3.7, -2, 0, 4.067, 1.096, 0, 4.433, -0.643, 0, 4.767, 0.381, 0, 5.133, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRInput", "Segments": [0, 0, 2, 2.867, 0, 0, 3.333, -30, 0, 4.233, 30, 0, 4.867, -30, 0, 5.7, 30, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamMRCupSet", "Segments": [0, 1, 2, 0.467, 1, 2, 2.867, 1, 0, 3.067, 0, 2, 4.933, 0, 0, 5, 1, 2, 5.933, 1]}, {"Target": "Parameter", "Id": "ParamManjuuREyeOpen", "Segments": [0, 0, 2, 3.3, 0, 0, 3.5, 1, 2, 4.733, 1, 0, 4.833, 0, 2, 5.367, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyY", "Segments": [0, 0, 2, 4.5, 0, 0, 4.8, -12, 0, 5.1, 2, 0, 5.367, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyZ", "Segments": [0, 0, 2, 0.467, 0, 0, 1.033, 18.387, 0, 1.5, -15.215, 1, 1.822, -15.215, 2.145, -13.737, 2.467, -8.98, 1, 2.556, -7.668, 2.644, -4.926, 2.733, 0, 1, 2.833, 5.542, 2.933, 30, 3.033, 30, 0, 3.7, -30, 1, 3.789, -30, 3.878, -25.162, 3.967, -25, 1, 4.211, -24.553, 4.456, -24.492, 4.7, -24, 1, 4.822, -23.754, 4.945, 5.498, 5.067, 5.498, 0, 5.367, 0, 2, 5.667, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRPositionZ", "Segments": [0, 0, 2, 0.3, 0, 0, 0.7, 18.645, 0, 1.3, -18, 1, 1.689, -18, 2.078, -16.89, 2.467, -13, 1, 2.556, -12.111, 2.644, -2.127, 2.733, 0, 1, 2.8, 1.596, 2.866, 2.373, 2.933, 3, 1, 3, 3.627, 3.066, 3.69, 3.133, 3.69, 1, 3.189, 3.69, 3.244, 1.961, 3.3, -4.762, 1, 3.411, -18.208, 3.522, -27.171, 3.633, -27.171, 0, 3.9, -26, 2, 4.533, -26, 0, 4.867, 1.594, 0, 5.1, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRArmB", "Segments": [0, 30, 2, 0.467, 30, 2, 0.7, 30, 0, 1.233, 4, 0, 1.4, 6.323, 0, 1.667, 3.932, 0, 1.9, 6.323, 0, 2.267, 4.74, 1, 2.367, 4.74, 2.467, 4.412, 2.567, 7.12, 1, 2.678, 10.129, 2.789, 30, 2.9, 30, 1, 2.978, 30, 3.055, 30, 3.133, 25.714, 1, 3.289, 14.929, 3.444, -10, 3.6, -10, 0, 3.8, -6, 2, 4.4, -6, 0, 4.533, -0.44, 0, 4.7, -6, 0, 5.1, 30, 2, 5.933, 30]}, {"Target": "Parameter", "Id": "ParamManjuuRMouth", "Segments": [0, 0, 2, 2.867, 0, 0, 3.133, 30, 1, 3.2, 30, 3.266, 30, 3.333, 26.58, 1, 3.422, 20.922, 3.511, -30, 3.6, -30, 0, 3.867, 27, 0, 4.1, -30, 0, 4.233, 30, 0, 4.433, -30, 0, 4.667, 23.103, 0, 4.767, -18, 0, 4.933, 5.118, 0, 5.1, 0, 2, 5.367, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow1", "Segments": [0, 0, 2, 3.3, 0, 1, 3.556, 3.333, 3.811, 6.667, 4.067, 10, 2, 4.1, 0, 2, 4.767, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow2", "Segments": [0, 0, 2, 3.633, 0, 1, 3.911, 3.333, 4.189, 6.667, 4.467, 10, 2, 4.5, 0, 2, 4.767, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow3", "Segments": [0, 0, 2, 3.833, 0, 1, 4.133, 3.333, 4.433, 6.667, 4.733, 10, 2, 4.767, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlow4", "Segments": [0, 0, 2, 3.433, 0, 1, 3.8, 3.333, 4.166, 6.667, 4.533, 10, 2, 4.567, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowB", "Segments": [0, 0, 2, 3.3, 0, 0, 3.833, 1, 2, 4.6, 1, 0, 4.733, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW", "Segments": [0, 0, 2, 2.833, 0, 0, 3.067, 1, 1, 3.134, 1, 3.2, 1, 3.267, 0.886, 1, 3.356, 0.674, 3.444, -1, 3.533, -1, 0, 3.8, 0.9, 0, 4.033, -1, 0, 4.233, 1, 0, 4.4, -1, 0, 4.6, 0.77, 0, 4.767, -0.6, 0, 4.867, 0.171, 0, 5.067, 0, 2, 5.133, 0, 0, 5.233, -0.5, 0, 5.433, 0.287, 0, 5.667, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyW2", "Segments": [0, 0, 2, 2.9, 0, 0, 3.133, 1, 2, 3.367, 1, 0, 3.633, -0.946, 0, 3.933, 1, 1, 4.033, 1, 4.133, 1, 4.233, 0.904, 1, 4.322, 0.801, 4.411, -1, 4.5, -1, 0, 4.767, 0.733, 0, 4.9, -0.6, 0, 5.267, 0.096, 0, 5.6, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamMRCupFZ", "Segments": [0, 0, 2, 4.967, 0, 0, 5.067, -23.275, 0, 5.133, 19.381, 0, 5.267, -15.952, 0, 5.4, 10.377, 0, 5.467, -7.255, 0, 5.567, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 2, 5.033, 0, 0, 5.133, -22.41, 1, 5.155, -22.41, 5.178, 20.275, 5.2, 20.275, 0, 5.333, -15.952, 0, 5.467, 10.377, 0, 5.533, -7.255, 0, 5.633, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX1", "Segments": [0, 0, 2, 3.5, 0, 0, 3.667, 1, 0, 4, -1, 0, 4.233, 1, 0, 4.533, -1, 0, 4.733, 1, 0, 4.767, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLiqFlowX2", "Segments": [0, 0, 2, 3.567, 0, 0, 3.767, 1, 0, 4.067, -1, 2, 4.233, -1, 0, 4.333, 1, 0, 4.567, -1, 0, 4.767, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLiqH", "Segments": [0, 0, 2, 3.3, 0, 0, 4.7, 0.3, 0, 4.867, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX1", "Segments": [0, 0, 2, 3, 0, 0, 3.167, 0.714, 0, 3.367, -0.625, 0, 3.6, 0.625, 0, 3.767, -0.714, 0, 4, 0.803, 0, 4.267, -0.9, 0, 4.433, 1, 0, 4.6, -1, 0, 4.667, 1, 0, 4.867, -1, 0, 5, 1, 0, 5.1, -1, 0, 5.233, 1, 0, 5.367, -1, 0, 5.667, 1, 0, 5.733, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX2", "Segments": [0, 0, 2, 3.067, 0, 0, 3.167, 1, 0, 3.433, -1, 0, 3.667, 1, 0, 3.8, -1, 0, 4.133, 1, 0, 4.333, -1, 0, 4.433, 1, 0, 4.633, -1, 0, 4.733, 1, 0, 4.933, -1, 0, 5.033, 1, 0, 5.167, -1, 0, 5.3, 1, 0, 5.433, -1, 0, 5.7, 1, 0, 5.833, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLiqBasicFlowX3", "Segments": [0, 0, 2, 3.133, 0, 0, 3.267, 0.418, 0, 3.5, -0.418, 0, 3.733, 0.418, 0, 3.867, -0.418, 0, 4.233, 0.418, 0, 4.4, -0.418, 0, 4.5, 0.418, 0, 4.667, -0.4, 0, 4.8, 0.418, 0, 5, -1, 0, 5.067, 1, 0, 5.233, -1, 0, 5.367, 1, 0, 5.533, -1, 0, 5.767, 0.418, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuL", "Segments": [0, 0, 2, 0.367, 0, 2, 2.533, 0, 0, 3, -0.05, 1, 3.022, -0.05, 3.045, 0.343, 3.067, 0.751, 1, 3.1, 1.364, 3.134, 1.549, 3.167, 1.549, 1, 3.211, 1.549, 3.256, 1.378, 3.3, 0.751, 1, 3.322, 0.437, 3.345, 0, 3.367, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLEyeOpen", "Segments": [0, 0, 2, 0.3, 0, 2, 0.933, 0, 0, 1.467, 1, 2, 3.4, 1, 0, 3.633, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyX", "Segments": [0, 0, 2, 0.367, 0, 0, 0.733, -2.265, 0, 1.267, 10, 0, 1.533, 1.614, 0, 2.233, 10, 0, 2.467, 3.76, 0, 2.933, 10, 0, 3.433, -8, 0, 3.6, 3.267, 0, 4.133, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyY", "Segments": [0, 0, 2, 0.167, 0, 0, 0.367, 4, 0, 0.867, -4, 0, 1.367, 8, 0, 1.733, 3.76, 0, 2.333, 8, 1, 2.489, 8, 2.644, 7.916, 2.8, 7, 1, 2.867, 6.607, 2.933, -5, 3, -5, 0, 3.167, 3.76, 0, 3.467, -3, 0, 3.7, 2.151, 0, 3.9, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyZ", "Segments": [0, 0, 2, 0.367, 0, 2, 0.467, 0, 0, 1.067, 30, 0, 1.333, 21, 0, 1.767, 30, 1, 1.989, 30, 2.211, 28.296, 2.433, 21, 1, 2.5, 18.811, 2.566, -18, 2.633, -18, 0, 2.9, 30, 0, 3.067, -10, 0, 3.333, 4.303, 0, 3.567, -7.012, 0, 3.8, 2.072, 0, 4, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLBodyW", "Segments": [0, 0, 2, 0.533, 0, 1, 0.578, 0, 0.622, -7.238, 0.667, -7.518, 1, 0.856, -8.707, 1.044, -9, 1.233, -9, 0, 1.467, 5.211, 0, 1.8, -3.684, 0, 1.967, 2.781, 0, 2.167, 0, 2, 2.567, 0, 0, 2.867, 15, 0, 3.167, -15, 0, 3.5, 9.3, 0, 3.833, -3.69, 0, 4.2, 3, 0, 4.733, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuL", "Segments": [0, 0, 2, 2.533, 0, 2, 3, 0, 0, 3.167, -1, 0, 3.367, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyX", "Segments": [0, 0, 2, 0.467, 0, 0, 0.833, -5, 0, 1.333, 10.18, 0, 1.633, 4.206, 0, 2.333, 11.391, 0, 2.533, 5.735, 0, 3.033, 10.18, 0, 3.3, 7, 0, 3.733, 9.817, 0, 4.033, -3, 0, 4.467, 2, 0, 4.967, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyY", "Segments": [0, 0, 2, 0.233, 0, 0, 0.467, 4, 0, 0.967, -20, 0, 1.467, 20.208, 0, 1.833, 17.101, 0, 2.333, 20.883, 0, 2.5, 17.101, 0, 2.767, 17.995, 0, 2.967, 13, 0, 3.2, 17.995, 0, 3.5, -30, 1, 3.6, -30, 3.7, -8.254, 3.8, -4, 1, 3.9, 0.254, 4, 0, 4.1, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyZ", "Segments": [0, 0, 2, 0.467, 0, 2, 0.567, 0, 0, 1.167, -5, 0, 1.433, 16, 0, 1.867, 7, 0, 2.133, 16.941, 0, 2.6, 7, 0, 2.9, 16, 0, 3.167, 7, 0, 3.367, 25.737, 0, 3.633, -7, 0, 3.933, 6, 0, 4.567, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUArmZ", "Segments": [0, 0, 2, 0.567, 0, 0, 0.967, -5, 0, 1.567, 9.993, 0, 1.933, 4, 0, 2.3, 6.673, 0, 2.533, 4.897, 0, 2.8, 6, 0, 3.067, 3, 0, 3.367, 12, 0, 3.633, -18, 0, 3.9, 2.009, 0, 4.3, -2.641, 0, 4.6, 0.924, 0, 5.1, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUBodyW", "Segments": [0, 0, 2, 0.667, 0, 1, 0.722, 0, 0.778, -0.238, 0.833, -0.251, 1, 1.011, -0.293, 1.189, -0.3, 1.367, -0.3, 0, 1.633, 0.174, 0, 1.933, -0.123, 0, 2.2, 0.093, 0, 2.467, 0, 2, 3, 0, 0, 3.267, 0.5, 0, 3.6, -0.5, 0, 3.9, 0.31, 0, 4.267, -0.123, 0, 4.6, 0.1, 0, 5.133, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPositionYPanda", "Segments": [0, 0, 2, 0.467, 0, 2, 2.533, 0, 2, 3.033, 0, 1, 3.044, 0, 3.056, 0.058, 3.067, 0.256, 1, 3.1, 0.849, 3.134, 1.195, 3.167, 1.195, 1, 3.211, 1.195, 3.256, 0.893, 3.3, 0.256, 1, 3.311, 0.097, 3.322, 0, 3.333, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyX", "Segments": [0, 0, 2, 0.467, 0, 0, 0.833, 2.265, 0, 1.333, -10, 0, 1.633, -1.614, 0, 2.467, -10, 0, 2.733, -3.76, 0, 3.267, -10, 0, 3.8, 8, 0, 3.933, -3.267, 0, 4.467, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyY", "Segments": [0, 0, 2, 0.233, 0, 0, 0.467, -4, 0, 0.967, 4, 0, 1.467, -8, 0, 1.833, -3.76, 0, 2.533, -8, 1, 2.744, -8, 2.956, -7.827, 3.167, -7, 1, 3.222, -6.782, 3.278, 5, 3.333, 5, 0, 3.533, -3.76, 0, 3.8, 3, 0, 4.033, -2.151, 0, 4.267, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2Panda", "Segments": [0, 0, 2, 2.7, 0, 0, 3, -20, 0, 3.233, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPandaBodyZ", "Segments": [0, 0, 2, 0.467, 0, 2, 0.567, 0, 0, 1.167, -30, 0, 1.433, -21, 0, 1.867, -30, 1, 2.145, -30, 2.422, -28.536, 2.7, -21, 1, 2.8, -18.287, 2.9, 18, 3, 18, 0, 3.233, -30, 0, 3.4, 10, 0, 3.7, -4.303, 0, 3.9, 7.012, 0, 4.133, -2.072, 0, 4.367, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegFZ", "Segments": [0, 0, 2, 0.467, 0, 2, 2.533, 0, 2, 2.7, 0, 0, 3, 0.3, 0, 3.233, -1, 0, 3.333, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPandaLegBZ", "Segments": [0, 0, 2, 0.467, 0, 2, 2.533, 0, 2, 2.7, 0, 0, 3, 0.3, 0, 3.233, -1, 0, 3.333, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyX", "Segments": [0, 0, 2, 0.467, 0, 0, 0.9, 2.265, 0, 1.6, -30, 2, 2.533, -30, 0, 2.967, -4, 0, 3.333, -30, 1, 3.422, -30, 3.511, -28.329, 3.6, -28, 1, 3.811, -27.219, 4.022, -27.328, 4.233, -26.215, 1, 4.444, -25.102, 4.656, 0, 4.867, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamCannonBodyY", "Segments": [0, 0, 2, 0.467, 0, 0, 0.967, 5, 0, 1.667, -15.617, 0, 2, -11.76, 2, 2.567, -11.76, 0, 3, -30, 0, 3.2, 30, 0, 3.533, 0, 0, 3.8, 6, 0, 4.2, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamCannonZ", "Segments": [0, 0, 2, 0.467, 0, 2, 1.233, 0, 2, 2.633, 0, 0, 2.933, 18, 0, 3.233, -30, 0, 3.567, 2.63, 0, 3.767, -0.478, 0, 4.133, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamCannonGaY", "Segments": [0, 0, 2, 0.467, 0, 2, 1.233, 0, 2, 2.633, 0, 0, 2.833, 15.936, 0, 3.067, -30, 0, 3.3, 30, 0, 3.567, -4.861, 0, 3.8, 3.602, 0, 4.133, -4.861, 0, 4.367, 2.072, 0, 4.567, -1.833, 0, 4.767, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamCannonHandZ", "Segments": [0, 0, 2, 0.467, 0, 2, 1.233, 0, 2, 2.7, 0, 0, 2.967, -10, 0, 3.367, 10, 0, 3.633, -3, 0, 3.867, 1.195, 0, 4.033, -1.753, 0, 4.267, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY1", "Segments": [0, 0, 2, 0.467, 0, 2, 3.1, 0, 1, 3.156, 0, 3.211, 2.297, 3.267, 4.741, 1, 3.434, 12.072, 3.6, 15, 3.767, 15, 2, 3.8, 0, 1, 3.878, 0, 3.955, 5.153, 4.033, 7.271, 1, 4.255, 13.322, 4.478, 15, 4.7, 15, 2, 4.733, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY2", "Segments": [0, 0, 2, 0.467, 0, 2, 3.2, 0, 1, 3.233, 0, 3.267, 1.911, 3.3, 4.9, 1, 3.378, 11.874, 3.455, 15, 3.533, 15, 2, 3.567, 0, 1, 3.678, 0, 3.789, 6.726, 3.9, 9, 1, 4.144, 14.002, 4.389, 15, 4.633, 15, 2, 4.667, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamCannonLIQY3", "Segments": [0, 0, 2, 0.467, 0, 2, 3.267, 0, 2, 3.333, 0, 1, 3.389, 0, 3.444, 3.738, 3.5, 6, 1, 3.667, 12.785, 3.833, 15, 4, 15, 2, 4.033, 0, 1, 4.166, 0, 4.3, 3.623, 4.433, 9, 1, 4.544, 13.481, 4.656, 15, 4.767, 15, 2, 4.8, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamParamStrongCatZ", "Segments": [0, 0, 2, 0.467, 0, 0, 0.8, 2, 0, 1.133, -11.745, 0, 1.6, -5.725, 0, 1.9, -7, 2, 2.533, -7, 0, 3, -17, 0, 3.2, 25, 2, 3.6, 25, 0, 3.9, -11, 0, 4.433, 0, 2, 4.867, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamParamSCBodyZ", "Segments": [0, 0, 2, 0.467, 0, 2, 0.567, 0, 0, 0.9, 2, 0, 1.233, -9.745, 0, 1.7, -5.725, 0, 1.967, -7, 2, 2.633, -7, 0, 2.767, -2, 0, 3.1, -18, 0, 3.367, 30, 2, 3.567, 30, 0, 3.967, -7.012, 0, 4.433, 6.773, 0, 4.967, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamSCDishY", "Segments": [0, 0, 2, 0.467, 0, 2, 0.633, 0, 0, 0.967, 2, 0, 1.267, -2.745, 0, 1.767, 1.275, 0, 2.033, 0, 2, 2.533, 0, 2, 2.833, 0, 0, 3.033, -19, 1, 3.089, -19, 3.144, -12.564, 3.2, 0, 1, 3.289, 20.102, 3.378, 30, 3.467, 30, 0, 4.167, -30, 0, 4.5, 8, 0, 4.9, -5.976, 0, 5.2, 3.586, 0, 5.467, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamSCDishZ", "Segments": [0, 0, 2, 0.467, 0, 2, 0.633, 0, 0, 1, 2, 0, 1.3, -2.745, 0, 1.8, 1.275, 0, 2.067, 0, 2, 2.533, 0, 2, 2.733, 0, 0, 2.933, 9, 0, 3.167, -19.522, 0, 3.467, 16.733, 0, 4, -5, 0, 4.533, 4.542, 0, 4.933, -5, 0, 5.267, 2.14, 0, 5.5, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamSCCupRO", "Segments": [0, 0, 2, 0.467, 0, 2, 2.533, 0, 2, 3.2, 0, 1, 3.278, 0.3, 3.355, 0.6, 3.433, 0.9, 2, 3.467, 0, 1, 3.556, 0.333, 3.644, 0.667, 3.733, 1, 2, 3.767, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamSCCupY", "Segments": [0, 0, 2, 0.467, 0, 2, 2.533, 0, 2, 3.167, 0, 1, 3.2, 0, 3.234, 13.373, 3.267, 18.801, 1, 3.322, 27.847, 3.378, 29.14, 3.433, 29.14, 1, 3.489, 29.14, 3.544, 28.542, 3.6, 20.688, 1, 3.644, 14.405, 3.689, 0, 3.733, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamSCCupZ", "Segments": [0, 0, 2, 0.467, 0, 2, 0.7, 0, 0, 1.1, 5.028, 0, 1.4, -5.773, 0, 1.9, 3.378, 0, 2.133, -1.036, 1, 2.266, -1.036, 2.4, -1.062, 2.533, 0, 1, 2.633, 0.796, 2.733, 5, 2.833, 5, 0, 3.233, -30, 1, 3.3, -30, 3.366, -8.38, 3.433, 0, 1, 3.622, 23.743, 3.811, 30, 4, 30, 0, 4.4, -10.916, 0, 4.667, 2.789, 0, 4.9, -2.63, 0, 5.067, 0, 2, 5.933, 0]}, {"Target": "Parameter", "Id": "MB_yanwubaozha", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "MB_DRFWXZKTMD", "Segments": [0, 1, 0, 5.933, 1]}, {"Target": "Parameter", "Id": "ParamAllSizeFix", "Segments": [0, 1, 0, 5.933, 1]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBGHide", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBG2Hide", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBGX", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBGY", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN2", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBlackIN3", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBlackY", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBlackCollar", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBlackOrder", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamWhiteIN", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamCHHide", "Segments": [0, 1, 0, 5.933, 1]}, {"Target": "Parameter", "Id": "ParamDeskHide", "Segments": [0, 1, 0, 5.933, 1]}, {"Target": "Parameter", "Id": "ParamStoolHide", "Segments": [0, 1, 0, 5.933, 1]}, {"Target": "Parameter", "Id": "ParamCupDesk", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamCHX", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamCHY", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamCHZ", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamChaSize", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamCcharacterZ", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionX", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamAllMalpositionY", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionX", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamAllPositionY", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamAllZ", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamAllSize", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamALLSize2", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamDeskShow", "Segments": [0, 10, 0, 5.933, 10]}, {"Target": "Parameter", "Id": "ParamStrongCatShow", "Segments": [0, 10, 0, 5.933, 10]}, {"Target": "Parameter", "Id": "ParamCannonShow", "Segments": [0, 10, 0, 5.933, 10]}, {"Target": "Parameter", "Id": "ParamLightPositionX", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamFixT", "Segments": [0, 1, 0, 5.933, 1]}, {"Target": "Parameter", "Id": "ParamFlap", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamScare", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPupilExp", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBlackFace", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamHeart2", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamMark", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamShameLine", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamMarkShake", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLY", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeL", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallRY", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallSizeR", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamEyeRLightOpen", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLightLine1", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLightLine2", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLightLine3", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLightShine", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo1", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo2", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLightLineRo3", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle1Y", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLightCycle2", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow1", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamTearFlow2", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamTearLight", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamTears", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamAngleS", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamArmUpperLAngle", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamArmHandLAngle", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamArmLowerLAngle", "Segments": [0, 10, 0, 5.933, 10]}, {"Target": "Parameter", "Id": "ParamArmLowerLH", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerLAngle", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamHandT2L", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamFanOpenR", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamChili", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamChiliX", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamHandR", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamArmFingerRY", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamHand_Cl", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamHandT1R", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamHandRMail", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "Segments": [0, 1, 0, 5.933, 1]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamShoulderL", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLegR1Z", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLegR1X", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Z", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLegR3Y", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamFootRX", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Z", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Z", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Z", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Y", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Y", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLegL3Y", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamFootLX", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamLegLF", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRHide", "Segments": [0, 1, 0, 5.933, 1]}, {"Target": "Parameter", "Id": "ParamMJRFlap", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuR", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuR", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPositionY2ManjuuR", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuR", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamMalpositionManjuuR", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRBodyX", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuRSigh", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuLHide", "Segments": [0, 1, 0, 5.933, 1]}, {"Target": "Parameter", "Id": "ParamMJLSigh", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuL", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuL", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamMjLFlip", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPositionZManjuuL", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamClawFX", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamClawFY", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamClawBX", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamClawBY", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUHide", "Segments": [0, 1, 0, 5.933, 1]}, {"Target": "Parameter", "Id": "ParamPositionXManjuuU", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPositionYManjuuU", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamSizeManjuuU", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamManjuuUEyesForm", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineU", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamSpeedLineD", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamPandaHide", "Segments": [0, 1, 0, 5.933, 1]}, {"Target": "Parameter", "Id": "ParamPositionXPanda", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamSizePanda", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupY", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamCannonCupZ", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupIce", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupZ", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamDeskCupInput", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamSCDishRO", "Segments": [0, 0, 0, 5.933, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.067, "Value": ""}, {"Time": 5.433, "Value": ""}]}