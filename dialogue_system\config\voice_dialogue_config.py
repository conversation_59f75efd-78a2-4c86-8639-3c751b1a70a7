#!/usr/bin/env python3
"""
Live2D语音对话系统 - 配置管理模块

这个模块提供语音对话系统的配置管理功能，包括：
- 配置验证和默认值
- 配置更新和保存
- 预设管理
- 配置迁移

使用示例：
    from dialogue_system.config.voice_dialogue_config import VoiceDialogueConfig
    
    # 创建配置管理器
    config = VoiceDialogueConfig(config_manager)
    
    # 获取配置
    stt_config = config.get_stt_config()
    tts_config = config.get_tts_config()
    
    # 更新配置
    config.update_stt_config({"model_path": "new_path"})
"""

import os
import json
from typing import Dict, Any, Optional, List
from pathlib import Path


class VoiceDialogueConfig:
    """语音对话配置管理器"""
    
    def __init__(self, config_manager=None):
        """初始化配置管理器"""
        self.config_manager = config_manager
        self.default_config = self._get_default_config()
        
        # 确保配置存在
        self._ensure_config_exists()
        
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "voice_dialogue": {
                "enabled": True,
                "microphone_config": {
                    "sample_rate": 16000,
                    "chunk_size": 1024,
                    "channels": 1,
                    "format": "int16",
                    "device_index": None,
                    "auto_select_device": True,
                    "volume_threshold": 0.01,
                    "noise_reduction": True
                },
                "key_triggered_config": {
                    "trigger_key": "space",
                    "hold_to_record": True,
                    "min_recording_duration": 0.5,
                    "max_recording_duration": 30.0,
                    "silence_timeout": 2.0,
                    "auto_stop_on_silence": True
                },
                "realtime_config": {
                    "voice_activity_threshold": 0.02,
                    "silence_duration": 1.5,
                    "min_speech_duration": 0.8,
                    "max_speech_duration": 30.0,
                    "energy_threshold": 300,
                    "dynamic_energy_threshold": True
                },
                "stt_config": {
                    "model_path": "D:/huggingface_cache/hub/models--Systran--faster-whisper-large-v3",
                    "device": "auto",
                    "compute_type": "float16",
                    "language": "auto",
                    "task": "transcribe",
                    "beam_size": 5,
                    "temperature": [0.0, 0.2, 0.4, 0.6, 0.8, 1.0],
                    "compression_ratio_threshold": 2.4,
                    "log_prob_threshold": -1.0,
                    "no_speech_threshold": 0.6,
                    "vad_filter": True,
                    "vad_parameters": {
                        "threshold": 0.5,
                        "min_speech_duration_ms": 250,
                        "max_speech_duration_s": 30,
                        "min_silence_duration_ms": 2000,
                        "speech_pad_ms": 400
                    }
                },
                "tts_config": {
                    "api_url": "http://localhost:9880",
                    "default_params": {
                        "text_lang": "zh",
                        "cut_punc": ",.;?!、，。？！；：",
                        "top_k": 20,
                        "top_p": 0.6,
                        "temperature": 0.6,
                        "speed_factor": 1.0
                    },
                    "audio_settings": {
                        "output_dir": "output/audio",
                        "auto_play": True,
                        "save_audio": False,
                        "format": "wav",
                        "volume": 1.0,
                        "playback_rate": 1.0
                    },
                    "current_preset": "default",
                    "presets": {
                        "default": {
                            "name": "默认语音",
                            "refer_wav_path": "",
                            "prompt_text": "",
                            "prompt_lang": "zh",
                            "text_lang": "zh",
                            "cut_punc": ",.;?!、，。？！；：",
                            "top_k": 20,
                            "top_p": 0.6,
                            "temperature": 0.6,
                            "speed_factor": 1.0
                        }
                    }
                },
                "ui_settings": {
                    "show_voice_indicator": True,
                    "show_volume_meter": True,
                    "show_status_text": True,
                    "voice_button_position": "bottom_right",
                    "hotkey_enabled": True,
                    "notification_enabled": True,
                    "auto_hide_ui": True,
                    "ui_timeout": 3.0
                },
                "integration_settings": {
                    "llm_integration": True,
                    "auto_send_to_llm": True,
                    "llm_response_tts": True,
                    "context_memory": True,
                    "conversation_history": True,
                    "max_history_length": 10
                }
            }
        }
        
    def _ensure_config_exists(self):
        """确保配置存在"""
        if self.config_manager:
            current_config = self.config_manager.config
            
            # 如果没有语音对话配置，添加默认配置
            if "voice_dialogue" not in current_config:
                current_config.update(self.default_config)
                self.config_manager.save_config()
                print("✅ 语音对话配置已添加到配置文件")
            else:
                # 合并缺失的配置项
                self._merge_missing_config(current_config["voice_dialogue"], 
                                         self.default_config["voice_dialogue"])
                
    def _merge_missing_config(self, current: Dict[str, Any], default: Dict[str, Any]):
        """合并缺失的配置项"""
        updated = False
        
        for key, value in default.items():
            if key not in current:
                current[key] = value
                updated = True
                print(f"✅ 添加缺失的配置项: voice_dialogue.{key}")
            elif isinstance(value, dict) and isinstance(current[key], dict):
                if self._merge_missing_config(current[key], value):
                    updated = True
                    
        if updated and self.config_manager:
            self.config_manager.save_config()
            
        return updated
        
    def get_voice_dialogue_config(self) -> Dict[str, Any]:
        """获取完整的语音对话配置"""
        if self.config_manager:
            return self.config_manager.config.get("voice_dialogue", 
                                                 self.default_config["voice_dialogue"])
        else:
            return self.default_config["voice_dialogue"]
            
    def get_microphone_config(self) -> Dict[str, Any]:
        """获取麦克风配置"""
        voice_config = self.get_voice_dialogue_config()
        return voice_config.get("microphone_config", 
                               self.default_config["voice_dialogue"]["microphone_config"])
                               
    def get_stt_config(self) -> Dict[str, Any]:
        """获取STT配置"""
        voice_config = self.get_voice_dialogue_config()
        return voice_config.get("stt_config", 
                               self.default_config["voice_dialogue"]["stt_config"])
                               
    def get_tts_config(self) -> Dict[str, Any]:
        """获取TTS配置"""
        voice_config = self.get_voice_dialogue_config()
        return voice_config.get("tts_config", 
                               self.default_config["voice_dialogue"]["tts_config"])
                               
    def get_ui_settings(self) -> Dict[str, Any]:
        """获取UI设置"""
        voice_config = self.get_voice_dialogue_config()
        return voice_config.get("ui_settings", 
                               self.default_config["voice_dialogue"]["ui_settings"])
                               
    def get_integration_settings(self) -> Dict[str, Any]:
        """获取集成设置"""
        voice_config = self.get_voice_dialogue_config()
        return voice_config.get("integration_settings", 
                               self.default_config["voice_dialogue"]["integration_settings"])
                               
    def update_microphone_config(self, updates: Dict[str, Any]):
        """更新麦克风配置"""
        self._update_config_section("microphone_config", updates)
        
    def update_stt_config(self, updates: Dict[str, Any]):
        """更新STT配置"""
        self._update_config_section("stt_config", updates)
        
    def update_tts_config(self, updates: Dict[str, Any]):
        """更新TTS配置"""
        self._update_config_section("tts_config", updates)
        
    def update_ui_settings(self, updates: Dict[str, Any]):
        """更新UI设置"""
        self._update_config_section("ui_settings", updates)
        
    def update_integration_settings(self, updates: Dict[str, Any]):
        """更新集成设置"""
        self._update_config_section("integration_settings", updates)
        
    def _update_config_section(self, section: str, updates: Dict[str, Any]):
        """更新配置节"""
        if self.config_manager:
            voice_config = self.config_manager.config.get("voice_dialogue", {})
            
            if section not in voice_config:
                voice_config[section] = {}
                
            voice_config[section].update(updates)
            self.config_manager.config["voice_dialogue"] = voice_config
            self.config_manager.save_config()
            
            print(f"✅ 已更新配置: voice_dialogue.{section}")
            
    def get_tts_presets(self) -> Dict[str, Any]:
        """获取TTS预设"""
        tts_config = self.get_tts_config()
        return tts_config.get("presets", {})
        
    def add_tts_preset(self, preset_name: str, preset_config: Dict[str, Any]):
        """添加TTS预设"""
        tts_config = self.get_tts_config()
        
        if "presets" not in tts_config:
            tts_config["presets"] = {}
            
        tts_config["presets"][preset_name] = preset_config
        self.update_tts_config(tts_config)
        
        print(f"✅ 已添加TTS预设: {preset_name}")
        
    def remove_tts_preset(self, preset_name: str):
        """删除TTS预设"""
        tts_config = self.get_tts_config()
        presets = tts_config.get("presets", {})
        
        if preset_name in presets:
            del presets[preset_name]
            self.update_tts_config(tts_config)
            print(f"✅ 已删除TTS预设: {preset_name}")
        else:
            print(f"⚠️ TTS预设不存在: {preset_name}")
            
    def set_current_tts_preset(self, preset_name: str):
        """设置当前TTS预设"""
        tts_config = self.get_tts_config()
        presets = tts_config.get("presets", {})
        
        if preset_name in presets:
            tts_config["current_preset"] = preset_name
            self.update_tts_config(tts_config)
            print(f"✅ 已切换到TTS预设: {preset_name}")
            return True
        else:
            print(f"⚠️ TTS预设不存在: {preset_name}")
            return False
            
    def validate_config(self) -> List[str]:
        """验证配置"""
        errors = []
        
        # 验证STT模型路径
        stt_config = self.get_stt_config()
        model_path = stt_config.get("model_path", "")
        if model_path and not os.path.exists(model_path):
            errors.append(f"STT模型路径不存在: {model_path}")
            
        # 验证TTS API URL
        tts_config = self.get_tts_config()
        api_url = tts_config.get("api_url", "")
        if not api_url:
            errors.append("TTS API URL未配置")
            
        # 验证音频输出目录
        audio_settings = tts_config.get("audio_settings", {})
        output_dir = audio_settings.get("output_dir", "")
        if output_dir:
            try:
                os.makedirs(output_dir, exist_ok=True)
            except Exception as e:
                errors.append(f"无法创建音频输出目录: {e}")
                
        return errors
        
    def export_config(self, filepath: str):
        """导出配置到文件"""
        try:
            config = self.get_voice_dialogue_config()
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            print(f"✅ 配置已导出到: {filepath}")
            return True
        except Exception as e:
            print(f"❌ 导出配置失败: {e}")
            return False
            
    def import_config(self, filepath: str):
        """从文件导入配置"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
                
            if self.config_manager:
                self.config_manager.config["voice_dialogue"] = imported_config
                self.config_manager.save_config()
                print(f"✅ 配置已从文件导入: {filepath}")
                return True
        except Exception as e:
            print(f"❌ 导入配置失败: {e}")
            return False
            
    def reset_to_default(self):
        """重置为默认配置"""
        if self.config_manager:
            self.config_manager.config["voice_dialogue"] = self.default_config["voice_dialogue"]
            self.config_manager.save_config()
            print("✅ 配置已重置为默认值")
            
    def get_status(self) -> Dict[str, Any]:
        """获取配置状态"""
        errors = self.validate_config()
        
        return {
            "config_exists": "voice_dialogue" in (self.config_manager.config if self.config_manager else {}),
            "validation_errors": errors,
            "is_valid": len(errors) == 0,
            "tts_presets_count": len(self.get_tts_presets()),
            "current_tts_preset": self.get_tts_config().get("current_preset", "default")
        }
